﻿Imports Microsoft.VisualBasic
Imports System
Imports System.Data
Imports System.Configuration
Imports System.Collections
Imports System.Data.SqlClient
Public Class Class1
    Public Function GetDataReader(ByVal sql As String) As SqlDataReader
        Try
            cm = New SqlCommand(sql, con)
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            dr = cm.ExecuteReader(CommandBehavior.CloseConnection)
        Catch ex As Exception
            'MessageBox.Show(ex.Message)
        End Try
        Return dr
    End Function
    Public Function GetDataset(ByVal sql As String) As DataSet
        If con.State = ConnectionState.Closed Then
            con.Open()
        End If
        cm = New SqlCommand(sql, con)
        da = New SqlDataAdapter(cm)
        ds = New DataSet
        da.Fill(ds)
        Return ds
    End Function
    Public Function GetDataTable(ByVal sql As String) As DataTable
        If con.State = ConnectionState.Closed Then
            con.Open()
        End If
        cm = New SqlCommand(sql, con)
        da = New SqlDataAdapter(cm)
        dt = New DataTable
        da.Fill(dt)
        Return dt
    End Function
    Public Function GetDataAdeptor(ByVal QueryString As String) As SqlDataAdapter
        Dim DataAdapter As New SqlDataAdapter
        Try
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm = New SqlCommand(QueryString, con)
            DataAdapter = New SqlDataAdapter(cm)
            Return DataAdapter
        Catch ex1 As SqlException
            Throw New Exception("Error Getting The Table", ex1)
        Catch ex As Exception
            Throw New Exception("Error Getting The DataAdapter", ex)
        End Try
    End Function
    Public Function Execute(ByVal sql As [String]) As Boolean
        Dim lngrecords As Integer
        cm = New SqlCommand(sql, con)
        If con.State = ConnectionState.Closed Then
            con.Open()
        End If
        lngrecords = cm.ExecuteNonQuery()
        con.Close()
        If lngrecords > 0 Then
            Return True
        Else
            Return False
        End If
    End Function

End Class
