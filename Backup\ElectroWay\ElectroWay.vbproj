﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="3.5" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DC65BC9F-7CE9-4E0A-BFB1-D18D5EDF5142}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>ElectroWay.My.MyApplication</StartupObject>
    <RootNamespace>ElectroWay</RootNamespace>
    <AssemblyName>ElectroWay</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <ApplicationIcon>ESLLOGO.ico</ApplicationIcon>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>ElectroWay.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>ElectroWay.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DocumentationFile>ElectroWay.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x86\Release\</OutputPath>
    <DocumentationFile>ElectroWay.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CrystalDecisions.CrystalReports.Engine, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="CrystalDecisions.Enterprise.Framework, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="CrystalDecisions.Enterprise.InfoStore, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="CrystalDecisions.ReportSource, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="CrystalDecisions.Shared, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="CrystalDecisions.Windows.Forms, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Office.Interop.Excel, Version=11.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Class1.vb" />
    <Compile Include="Form1.Designer.vb">
      <DependentUpon>Form1.vb</DependentUpon>
    </Compile>
    <Compile Include="Form1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCancelVehicle.Designer.vb">
      <DependentUpon>frmCancelVehicle.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCancelVehicle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangeGroupRef.Designer.vb">
      <DependentUpon>frmChangeGroupRef.vb</DependentUpon>
    </Compile>
    <Compile Include="frmChangeGroupRef.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmClearSecondWeighment.Designer.vb">
      <DependentUpon>frmClearSecondWeighment.vb</DependentUpon>
    </Compile>
    <Compile Include="frmClearSecondWeighment.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCompanymaster.Designer.vb">
      <DependentUpon>frmCompanymaster.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCompanymaster.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmContMaterialReturnDetails.Designer.vb">
      <DependentUpon>FrmContMaterialReturnDetails.vb</DependentUpon>
    </Compile>
    <Compile Include="FrmContMaterialReturnDetails.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmContractorMatApproval.Designer.vb">
      <DependentUpon>frmContractorMatApproval.vb</DependentUpon>
    </Compile>
    <Compile Include="frmContractorMatApproval.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmContractorMaterial.Designer.vb">
      <DependentUpon>frmContractorMaterial.vb</DependentUpon>
    </Compile>
    <Compile Include="frmContractorMaterial.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmContractorMaterialDetails_47KOutPass.Designer.vb">
      <DependentUpon>frmContractorMaterialDetails_47KOutPass.vb</DependentUpon>
    </Compile>
    <Compile Include="frmContractorMaterialDetails_47KOutPass.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmContractorMaterialDetails_OutPass.Designer.vb">
      <DependentUpon>frmContractorMaterialDetails_OutPass.vb</DependentUpon>
    </Compile>
    <Compile Include="frmContractorMaterialDetails_OutPass.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCustomer.Designer.vb">
      <DependentUpon>frmCustomer.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCustomer.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmDetailsReportNew.Designer.vb">
      <DependentUpon>frmDetailsReportNew.vb</DependentUpon>
    </Compile>
    <Compile Include="frmDetailsReportNew.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmDocumentDetails.Designer.vb">
      <DependentUpon>frmDocumentDetails.vb</DependentUpon>
    </Compile>
    <Compile Include="frmDocumentDetails.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\ECL_RptGateEntrySlip1.vb">
      <DependentUpon>ECL_RptGateEntrySlip1.rpt</DependentUpon>
      <AutoGen>True</AutoGen>
      <SubType>Component</SubType>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Reports\frmCheckPostReport.Designer.vb">
      <DependentUpon>frmCheckPostReport.vb</DependentUpon>
    </Compile>
    <Compile Include="Reports\frmCheckPostReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\frmDetailsReport.Designer.vb">
      <DependentUpon>frmDetailsReport.vb</DependentUpon>
    </Compile>
    <Compile Include="Reports\frmDetailsReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmDriver.Designer.vb">
      <DependentUpon>frmDriver.vb</DependentUpon>
    </Compile>
    <Compile Include="frmDriver.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmGateEntry.Designer.vb">
      <DependentUpon>frmGateEntry.vb</DependentUpon>
    </Compile>
    <Compile Include="frmGateEntry.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmGetMasterFromSAP.Designer.vb">
      <DependentUpon>frmGetMasterFromSAP.vb</DependentUpon>
    </Compile>
    <Compile Include="frmGetMasterFromSAP.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmGouping.Designer.vb">
      <DependentUpon>frmGouping.vb</DependentUpon>
    </Compile>
    <Compile Include="frmGouping.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmGrosstareNetWtUpdation.Designer.vb">
      <DependentUpon>frmGrosstareNetWtUpdation.vb</DependentUpon>
    </Compile>
    <Compile Include="frmGrosstareNetWtUpdation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmGroupingCancel.Designer.vb">
      <DependentUpon>frmGroupingCancel.vb</DependentUpon>
    </Compile>
    <Compile Include="frmGroupingCancel.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmHelp.Designer.vb">
      <DependentUpon>frmHelp.vb</DependentUpon>
    </Compile>
    <Compile Include="frmHelp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLocationMaster.Designer.vb">
      <DependentUpon>frmLocationMaster.vb</DependentUpon>
    </Compile>
    <Compile Include="frmLocationMaster.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLogin.Designer.vb">
      <DependentUpon>frmLogin.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmManualWeighmentAuthorisation.Designer.vb">
      <DependentUpon>frmManualWeighmentAuthorisation.vb</DependentUpon>
    </Compile>
    <Compile Include="frmManualWeighmentAuthorisation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMaterial.Designer.vb">
      <DependentUpon>frmMaterial.vb</DependentUpon>
    </Compile>
    <Compile Include="frmMaterial.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmNode.Designer.vb">
      <DependentUpon>frmNode.vb</DependentUpon>
    </Compile>
    <Compile Include="frmNode.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPassword.Designer.vb">
      <DependentUpon>frmPassword.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPassword.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPathSettings.Designer.vb">
      <DependentUpon>frmPathSettings.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPathSettings.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPlantmaster.Designer.vb">
      <DependentUpon>frmPlantmaster.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPlantmaster.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmReferenceMaster.Designer.vb">
      <DependentUpon>frmReferenceMaster.vb</DependentUpon>
    </Compile>
    <Compile Include="frmReferenceMaster.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmRequiredDate.Designer.vb">
      <DependentUpon>frmRequiredDate.vb</DependentUpon>
    </Compile>
    <Compile Include="frmRequiredDate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmRe_Grouping.Designer.vb">
      <DependentUpon>frmRe_Grouping.vb</DependentUpon>
    </Compile>
    <Compile Include="frmRe_Grouping.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmRouteMaster.Designer.vb">
      <DependentUpon>frmRouteMaster.vb</DependentUpon>
    </Compile>
    <Compile Include="frmRouteMaster.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSelectMaterial.Designer.vb">
      <DependentUpon>frmSelectMaterial.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSelectMaterial.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmServerConfig.Designer.vb">
      <DependentUpon>frmServerConfig.vb</DependentUpon>
    </Compile>
    <Compile Include="frmServerConfig.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSplitting.Designer.vb">
      <DependentUpon>frmSplitting.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSplitting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTareWthelp.Designer.vb">
      <DependentUpon>frmTareWthelp.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTareWthelp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTransaction.Designer.vb">
      <DependentUpon>frmTransaction.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTransaction.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTransactionMaster.Designer.vb">
      <DependentUpon>frmTransactionMaster.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTransactionMaster.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTransferToPlant.Designer.vb">
      <DependentUpon>frmTransferToPlant.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTransferToPlant.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTransporter1.Designer.vb">
      <DependentUpon>frmTransporter1.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTransporter1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUnloadingDOUpdation.Designer.vb">
      <DependentUpon>frmUnloadingDOUpdation.vb</DependentUpon>
    </Compile>
    <Compile Include="frmUnloadingDOUpdation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUpdate.Designer.vb">
      <DependentUpon>frmUpdate.vb</DependentUpon>
    </Compile>
    <Compile Include="frmUpdate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUpdateChallanTransporter.Designer.vb">
      <DependentUpon>frmUpdateChallanTransporter.vb</DependentUpon>
    </Compile>
    <Compile Include="frmUpdateChallanTransporter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUpdateLineItems.Designer.vb">
      <DependentUpon>frmUpdateLineItems.vb</DependentUpon>
    </Compile>
    <Compile Include="frmUpdateLineItems.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUser.Designer.vb">
      <DependentUpon>frmUser.vb</DependentUpon>
    </Compile>
    <Compile Include="frmUser.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVehicle.Designer.vb">
      <DependentUpon>frmVehicle.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVehicle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVehicleStatus.Designer.vb">
      <DependentUpon>frmVehicleStatus.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVehicleStatus.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVehicleTransporter.Designer.vb">
      <DependentUpon>frmVehicleTransporter.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVehicleTransporter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVehicleWt.Designer.vb">
      <DependentUpon>frmVehicleWt.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVehicleWt.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVendor.Designer.vb">
      <DependentUpon>frmVendor.vb</DependentUpon>
    </Compile>
    <Compile Include="frmVendor.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmWM.Designer.vb">
      <DependentUpon>frmWM.vb</DependentUpon>
    </Compile>
    <Compile Include="frmWM.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MDIForm1.Designer.vb">
      <DependentUpon>MDIForm1.vb</DependentUpon>
    </Compile>
    <Compile Include="MDIForm1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module1.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Reports\frmReport.Designer.vb">
      <DependentUpon>frmReport.vb</DependentUpon>
    </Compile>
    <Compile Include="Reports\frmReport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\frmSlipPrint.Designer.vb">
      <DependentUpon>frmSlipPrint.vb</DependentUpon>
    </Compile>
    <Compile Include="Reports\frmSlipPrint.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\frmSlipPrintDirect.Designer.vb">
      <DependentUpon>frmSlipPrintDirect.vb</DependentUpon>
    </Compile>
    <Compile Include="Reports\frmSlipPrintDirect.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\GateEntrySlip.Designer.vb">
      <DependentUpon>GateEntrySlip.vb</DependentUpon>
    </Compile>
    <Compile Include="Reports\GateEntrySlip.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\GateEntry_Slip.Designer.vb">
      <DependentUpon>GateEntry_Slip.vb</DependentUpon>
    </Compile>
    <Compile Include="Reports\GateEntry_Slip.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\RptGateEntrySlip.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RptGateEntrySlip.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\RptGateEntrySlip1.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RptGateEntrySlip1.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\RptGateEntrySlip2.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RptGateEntrySlip2.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\RptGateEntrySlip3.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RptGateEntrySlip3.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\RptWeighmentSlip.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RptWeighmentSlip.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\VIEW_Cont_Mat_Ret_ID.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>VIEW_Cont_Mat_Ret_ID.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCancelVehicle.resx">
      <DependentUpon>frmCancelVehicle.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmChangeGroupRef.resx">
      <DependentUpon>frmChangeGroupRef.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmClearSecondWeighment.resx">
      <DependentUpon>frmClearSecondWeighment.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCompanymaster.resx">
      <DependentUpon>frmCompanymaster.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmContMaterialReturnDetails.resx">
      <DependentUpon>FrmContMaterialReturnDetails.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmContractorMatApproval.resx">
      <DependentUpon>frmContractorMatApproval.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmContractorMaterial.resx">
      <DependentUpon>frmContractorMaterial.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmContractorMaterialDetails_47KOutPass.resx">
      <DependentUpon>frmContractorMaterialDetails_47KOutPass.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmContractorMaterialDetails_OutPass.resx">
      <DependentUpon>frmContractorMaterialDetails_OutPass.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCustomer.resx">
      <DependentUpon>frmCustomer.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmDetailsReportNew.resx">
      <DependentUpon>frmDetailsReportNew.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmDocumentDetails.resx">
      <DependentUpon>frmDocumentDetails.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\ECL_RptGateEntrySlip1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>ECL_RptGateEntrySlip1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\frmCheckPostReport.resx">
      <DependentUpon>frmCheckPostReport.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\frmDetailsReport.resx">
      <DependentUpon>frmDetailsReport.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmDriver.resx">
      <DependentUpon>frmDriver.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmGateEntry.resx">
      <DependentUpon>frmGateEntry.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmGetMasterFromSAP.resx">
      <DependentUpon>frmGetMasterFromSAP.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmGouping.resx">
      <DependentUpon>frmGouping.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmGrosstareNetWtUpdation.resx">
      <DependentUpon>frmGrosstareNetWtUpdation.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmGroupingCancel.resx">
      <DependentUpon>frmGroupingCancel.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmHelp.resx">
      <DependentUpon>frmHelp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLocationMaster.resx">
      <DependentUpon>frmLocationMaster.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLogin.resx">
      <DependentUpon>frmLogin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmManualWeighmentAuthorisation.resx">
      <DependentUpon>frmManualWeighmentAuthorisation.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmMaterial.resx">
      <DependentUpon>frmMaterial.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmNode.resx">
      <DependentUpon>frmNode.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPassword.resx">
      <DependentUpon>frmPassword.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPathSettings.resx">
      <DependentUpon>frmPathSettings.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPlantmaster.resx">
      <DependentUpon>frmPlantmaster.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmReferenceMaster.resx">
      <DependentUpon>frmReferenceMaster.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmRequiredDate.resx">
      <DependentUpon>frmRequiredDate.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmRe_Grouping.resx">
      <DependentUpon>frmRe_Grouping.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmRouteMaster.resx">
      <DependentUpon>frmRouteMaster.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSelectMaterial.resx">
      <DependentUpon>frmSelectMaterial.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmServerConfig.resx">
      <DependentUpon>frmServerConfig.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSplitting.resx">
      <DependentUpon>frmSplitting.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTareWthelp.resx">
      <DependentUpon>frmTareWthelp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTransaction.resx">
      <DependentUpon>frmTransaction.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTransactionMaster.resx">
      <DependentUpon>frmTransactionMaster.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTransferToPlant.resx">
      <DependentUpon>frmTransferToPlant.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTransporter1.resx">
      <DependentUpon>frmTransporter1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmUnloadingDOUpdation.resx">
      <DependentUpon>frmUnloadingDOUpdation.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmUpdate.resx">
      <DependentUpon>frmUpdate.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmUpdateChallanTransporter.resx">
      <DependentUpon>frmUpdateChallanTransporter.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmUpdateLineItems.resx">
      <DependentUpon>frmUpdateLineItems.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmUser.resx">
      <DependentUpon>frmUser.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVehicle.resx">
      <DependentUpon>frmVehicle.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVehicleStatus.resx">
      <DependentUpon>frmVehicleStatus.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVehicleTransporter.resx">
      <DependentUpon>frmVehicleTransporter.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVehicleWt.resx">
      <DependentUpon>frmVehicleWt.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVendor.resx">
      <DependentUpon>frmVendor.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmWM.resx">
      <DependentUpon>frmWM.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="MDIForm1.resx">
      <DependentUpon>MDIForm1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\frmReport.resx">
      <DependentUpon>frmReport.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\frmSlipPrint.resx">
      <DependentUpon>frmSlipPrint.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\frmSlipPrintDirect.resx">
      <DependentUpon>frmSlipPrintDirect.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\GateEntrySlip.resx">
      <DependentUpon>GateEntrySlip.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\GateEntry_Slip.resx">
      <DependentUpon>GateEntry_Slip.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\RptGateEntrySlip.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>RptGateEntrySlip.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\RptGateEntrySlip1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>RptGateEntrySlip1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\RptGateEntrySlip2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>RptGateEntrySlip2.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\RptGateEntrySlip3.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>RptGateEntrySlip3.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\RptWeighmentSlip.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>RptWeighmentSlip.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\VIEW_Cont_Mat_Ret_ID.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>VIEW_Cont_Mat_Ret_ID.vb</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="ESLLOGO.ico" />
    <Content Include="Icon1.ico" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="MSCommLib">
      <Guid>{648A5603-2C6E-101B-82B6-000000000014}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>1</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="SAPFunctionsOCX">
      <Guid>{5B076C00-2F26-11CF-9AE5-0800096E19F4}</Guid>
      <VersionMajor>5</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="SAPLogonCtrl">
      <Guid>{B24944D9-1501-11CF-8981-0000E8A49FA0}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>1</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="SAPTableFactoryCtrl">
      <Guid>{87D28511-6B43-101C-92CE-10005AF5DF4D}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>1</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="stdole">
      <Guid>{00020430-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{967B4E0D-AD0C-4609-AB67-0FA40C0206D8}" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>