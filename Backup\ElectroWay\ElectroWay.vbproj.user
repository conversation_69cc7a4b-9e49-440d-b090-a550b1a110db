﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectView>ShowAllFiles</ProjectView>
    <ReferencePath>C:\Program Files (x86)\Business Objects\Common\2.8\managed\</ReferencePath>
    <PublishUrlHistory>publish\</PublishUrlHistory>
    <InstallUrlHistory>
    </InstallUrlHistory>
    <SupportUrlHistory>
    </SupportUrlHistory>
    <UpdateUrlHistory>
    </UpdateUrlHistory>
    <BootstrapperUrlHistory>
    </BootstrapperUrlHistory>
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
</Project>