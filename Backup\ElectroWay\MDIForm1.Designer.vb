﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MDIForm1
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub


    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MDIForm1))
        Me.MenuStrip1 = New System.Windows.Forms.MenuStrip
        Me.MasterToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.CompanyMasterToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.PlantMasterToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.LocationToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.TransactionToolStripMenuItem1 = New System.Windows.Forms.ToolStripMenuItem
        Me.ScannedDocumentPathSettingsToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ImportMasterDatafromSAPToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleTareWeightManualModeToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleTareWeightAutoToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleTareWTValidityExtensionToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.DriverDetailsToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.SAPServerConfigToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.NodeClientConfigurationToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.UserMasterToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.MaterialDetailsToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VendorDetailsToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.CustomerDetailsToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.TransporterDetailsToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.RakeGroupingReferenceToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleWithTransporterToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ChangePasswordToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleStatusBlacklistWarnedToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.DocumentsValidityToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ExitToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.TransactionToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.GateEntryToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.WeighmentToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.WeighmentManualModeToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.CheckPostEntryToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.WeighmentDataSplittingToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleTransferToOtherLocationToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ChangeGroupingReferenceToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.CancelVehicleEntryToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.CancelGroupingDataToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleNetWTSplitForGroupingToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.UpdateDONoBeforeFinalWeighmentToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.UpdateLineItemsToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ContractorItemStoresApprovalToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ContractorItemOUTPassToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ModifyChallanNoTransporterToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleActivityChangeToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.VehicleNumberChangeToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ClearSecondWeighmentToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ContractorItemOUTPASSToolStripMenuItem1 = New System.Windows.Forms.ToolStripMenuItem
        Me.CheckContractorItem47KhataOUTPASSToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.ReportsToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.SlipPrintReportToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.GatePassSlipToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.WeighmentSlipToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.WeighmentSlipDirectToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.WeighmentSlipViewToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.DetailsReportToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.GateCheckPostINToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.DetailsReportNewToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.MenuStrip1.SuspendLayout()
        Me.SuspendLayout()
        '
        'MenuStrip1
        '
        Me.MenuStrip1.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.MasterToolStripMenuItem, Me.TransactionToolStripMenuItem, Me.ReportsToolStripMenuItem})
        Me.MenuStrip1.Location = New System.Drawing.Point(0, 0)
        Me.MenuStrip1.Name = "MenuStrip1"
        Me.MenuStrip1.Size = New System.Drawing.Size(1280, 24)
        Me.MenuStrip1.TabIndex = 9
        Me.MenuStrip1.Text = "MenuStrip1"
        '
        'MasterToolStripMenuItem
        '
        Me.MasterToolStripMenuItem.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.CompanyMasterToolStripMenuItem, Me.PlantMasterToolStripMenuItem, Me.LocationToolStripMenuItem, Me.TransactionToolStripMenuItem1, Me.ScannedDocumentPathSettingsToolStripMenuItem, Me.ImportMasterDatafromSAPToolStripMenuItem, Me.VehicleTareWeightManualModeToolStripMenuItem, Me.VehicleTareWeightAutoToolStripMenuItem, Me.VehicleTareWTValidityExtensionToolStripMenuItem, Me.DriverDetailsToolStripMenuItem, Me.SAPServerConfigToolStripMenuItem, Me.NodeClientConfigurationToolStripMenuItem, Me.UserMasterToolStripMenuItem, Me.MaterialDetailsToolStripMenuItem, Me.VendorDetailsToolStripMenuItem, Me.CustomerDetailsToolStripMenuItem, Me.TransporterDetailsToolStripMenuItem, Me.RakeGroupingReferenceToolStripMenuItem, Me.VehicleWithTransporterToolStripMenuItem, Me.ChangePasswordToolStripMenuItem, Me.VehicleStatusBlacklistWarnedToolStripMenuItem, Me.DocumentsValidityToolStripMenuItem, Me.ExitToolStripMenuItem})
        Me.MasterToolStripMenuItem.Name = "MasterToolStripMenuItem"
        Me.MasterToolStripMenuItem.Size = New System.Drawing.Size(55, 20)
        Me.MasterToolStripMenuItem.Text = "&Master"
        '
        'CompanyMasterToolStripMenuItem
        '
        Me.CompanyMasterToolStripMenuItem.Name = "CompanyMasterToolStripMenuItem"
        Me.CompanyMasterToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.CompanyMasterToolStripMenuItem.Text = "&Company Master"
        '
        'PlantMasterToolStripMenuItem
        '
        Me.PlantMasterToolStripMenuItem.Name = "PlantMasterToolStripMenuItem"
        Me.PlantMasterToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.PlantMasterToolStripMenuItem.Text = "&Plant Master"
        '
        'LocationToolStripMenuItem
        '
        Me.LocationToolStripMenuItem.Name = "LocationToolStripMenuItem"
        Me.LocationToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.LocationToolStripMenuItem.Text = "&Location Master"
        '
        'TransactionToolStripMenuItem1
        '
        Me.TransactionToolStripMenuItem1.Name = "TransactionToolStripMenuItem1"
        Me.TransactionToolStripMenuItem1.Size = New System.Drawing.Size(261, 22)
        Me.TransactionToolStripMenuItem1.Text = "&Transaction Master"
        '
        'ScannedDocumentPathSettingsToolStripMenuItem
        '
        Me.ScannedDocumentPathSettingsToolStripMenuItem.Name = "ScannedDocumentPathSettingsToolStripMenuItem"
        Me.ScannedDocumentPathSettingsToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.ScannedDocumentPathSettingsToolStripMenuItem.Text = "&Scanned Document Path Settings"
        '
        'ImportMasterDatafromSAPToolStripMenuItem
        '
        Me.ImportMasterDatafromSAPToolStripMenuItem.Name = "ImportMasterDatafromSAPToolStripMenuItem"
        Me.ImportMasterDatafromSAPToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.ImportMasterDatafromSAPToolStripMenuItem.Text = "&Import Master Data from SAP"
        '
        'VehicleTareWeightManualModeToolStripMenuItem
        '
        Me.VehicleTareWeightManualModeToolStripMenuItem.Name = "VehicleTareWeightManualModeToolStripMenuItem"
        Me.VehicleTareWeightManualModeToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.VehicleTareWeightManualModeToolStripMenuItem.Text = "Vehicle Tare Weight (Manual Mode)"
        '
        'VehicleTareWeightAutoToolStripMenuItem
        '
        Me.VehicleTareWeightAutoToolStripMenuItem.Name = "VehicleTareWeightAutoToolStripMenuItem"
        Me.VehicleTareWeightAutoToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.VehicleTareWeightAutoToolStripMenuItem.Text = "Vehicle Tare Weight (Auto Mode)"
        '
        'VehicleTareWTValidityExtensionToolStripMenuItem
        '
        Me.VehicleTareWTValidityExtensionToolStripMenuItem.Name = "VehicleTareWTValidityExtensionToolStripMenuItem"
        Me.VehicleTareWTValidityExtensionToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.VehicleTareWTValidityExtensionToolStripMenuItem.Text = "Vehicle Tare WT Validity Extension"
        '
        'DriverDetailsToolStripMenuItem
        '
        Me.DriverDetailsToolStripMenuItem.Name = "DriverDetailsToolStripMenuItem"
        Me.DriverDetailsToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.DriverDetailsToolStripMenuItem.Text = "&Driver Details"
        '
        'SAPServerConfigToolStripMenuItem
        '
        Me.SAPServerConfigToolStripMenuItem.Name = "SAPServerConfigToolStripMenuItem"
        Me.SAPServerConfigToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.SAPServerConfigToolStripMenuItem.Text = "SAP Server Configuration"
        '
        'NodeClientConfigurationToolStripMenuItem
        '
        Me.NodeClientConfigurationToolStripMenuItem.Name = "NodeClientConfigurationToolStripMenuItem"
        Me.NodeClientConfigurationToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.NodeClientConfigurationToolStripMenuItem.Text = "Node/Client Configuration"
        '
        'UserMasterToolStripMenuItem
        '
        Me.UserMasterToolStripMenuItem.Name = "UserMasterToolStripMenuItem"
        Me.UserMasterToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.UserMasterToolStripMenuItem.Text = "&User Master"
        '
        'MaterialDetailsToolStripMenuItem
        '
        Me.MaterialDetailsToolStripMenuItem.Name = "MaterialDetailsToolStripMenuItem"
        Me.MaterialDetailsToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.MaterialDetailsToolStripMenuItem.Text = "&Material Details"
        '
        'VendorDetailsToolStripMenuItem
        '
        Me.VendorDetailsToolStripMenuItem.Name = "VendorDetailsToolStripMenuItem"
        Me.VendorDetailsToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.VendorDetailsToolStripMenuItem.Text = "&Vendor Details"
        '
        'CustomerDetailsToolStripMenuItem
        '
        Me.CustomerDetailsToolStripMenuItem.Name = "CustomerDetailsToolStripMenuItem"
        Me.CustomerDetailsToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.CustomerDetailsToolStripMenuItem.Text = "&Customer Details"
        '
        'TransporterDetailsToolStripMenuItem
        '
        Me.TransporterDetailsToolStripMenuItem.Name = "TransporterDetailsToolStripMenuItem"
        Me.TransporterDetailsToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.TransporterDetailsToolStripMenuItem.Text = "&Transporter Details"
        '
        'RakeGroupingReferenceToolStripMenuItem
        '
        Me.RakeGroupingReferenceToolStripMenuItem.Name = "RakeGroupingReferenceToolStripMenuItem"
        Me.RakeGroupingReferenceToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.RakeGroupingReferenceToolStripMenuItem.Text = "&Rake / Grouping Reference"
        '
        'VehicleWithTransporterToolStripMenuItem
        '
        Me.VehicleWithTransporterToolStripMenuItem.Name = "VehicleWithTransporterToolStripMenuItem"
        Me.VehicleWithTransporterToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.VehicleWithTransporterToolStripMenuItem.Text = "&Vehicle with Transporter"
        '
        'ChangePasswordToolStripMenuItem
        '
        Me.ChangePasswordToolStripMenuItem.Name = "ChangePasswordToolStripMenuItem"
        Me.ChangePasswordToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.ChangePasswordToolStripMenuItem.Text = "&Change Password"
        Me.ChangePasswordToolStripMenuItem.Visible = False
        '
        'VehicleStatusBlacklistWarnedToolStripMenuItem
        '
        Me.VehicleStatusBlacklistWarnedToolStripMenuItem.Name = "VehicleStatusBlacklistWarnedToolStripMenuItem"
        Me.VehicleStatusBlacklistWarnedToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.VehicleStatusBlacklistWarnedToolStripMenuItem.Text = "&Vehicle Status (Blacklist / Warned)"
        '
        'DocumentsValidityToolStripMenuItem
        '
        Me.DocumentsValidityToolStripMenuItem.Name = "DocumentsValidityToolStripMenuItem"
        Me.DocumentsValidityToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.DocumentsValidityToolStripMenuItem.Text = "Documents Validity"
        '
        'ExitToolStripMenuItem
        '
        Me.ExitToolStripMenuItem.Name = "ExitToolStripMenuItem"
        Me.ExitToolStripMenuItem.Size = New System.Drawing.Size(261, 22)
        Me.ExitToolStripMenuItem.Text = "&Exit"
        '
        'TransactionToolStripMenuItem
        '
        Me.TransactionToolStripMenuItem.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.GateEntryToolStripMenuItem, Me.WeighmentToolStripMenuItem, Me.WeighmentManualModeToolStripMenuItem, Me.CheckPostEntryToolStripMenuItem, Me.WeighmentDataSplittingToolStripMenuItem, Me.WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem, Me.VehicleTransferToOtherLocationToolStripMenuItem, Me.ChangeGroupingReferenceToolStripMenuItem, Me.CancelVehicleEntryToolStripMenuItem, Me.CancelGroupingDataToolStripMenuItem, Me.VehicleNetWTSplitForGroupingToolStripMenuItem, Me.UpdateDONoBeforeFinalWeighmentToolStripMenuItem, Me.UpdateLineItemsToolStripMenuItem, Me.ContractorItemStoresApprovalToolStripMenuItem, Me.ContractorItemOUTPassToolStripMenuItem, Me.ModifyChallanNoTransporterToolStripMenuItem, Me.VehicleActivityChangeToolStripMenuItem, Me.VehicleNumberChangeToolStripMenuItem, Me.ClearSecondWeighmentToolStripMenuItem, Me.ContractorItemOUTPASSToolStripMenuItem1, Me.CheckContractorItem47KhataOUTPASSToolStripMenuItem})
        Me.TransactionToolStripMenuItem.Name = "TransactionToolStripMenuItem"
        Me.TransactionToolStripMenuItem.Size = New System.Drawing.Size(79, 20)
        Me.TransactionToolStripMenuItem.Text = "&Transaction"
        '
        'GateEntryToolStripMenuItem
        '
        Me.GateEntryToolStripMenuItem.Name = "GateEntryToolStripMenuItem"
        Me.GateEntryToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.GateEntryToolStripMenuItem.Text = "&Gate Entry"
        '
        'WeighmentToolStripMenuItem
        '
        Me.WeighmentToolStripMenuItem.Name = "WeighmentToolStripMenuItem"
        Me.WeighmentToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.WeighmentToolStripMenuItem.Text = "&Weighment"
        '
        'WeighmentManualModeToolStripMenuItem
        '
        Me.WeighmentManualModeToolStripMenuItem.Name = "WeighmentManualModeToolStripMenuItem"
        Me.WeighmentManualModeToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.WeighmentManualModeToolStripMenuItem.Text = "&Weighment (Manual Mode)"
        '
        'CheckPostEntryToolStripMenuItem
        '
        Me.CheckPostEntryToolStripMenuItem.Name = "CheckPostEntryToolStripMenuItem"
        Me.CheckPostEntryToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.CheckPostEntryToolStripMenuItem.Text = "Check Post Entry"
        '
        'WeighmentDataSplittingToolStripMenuItem
        '
        Me.WeighmentDataSplittingToolStripMenuItem.Name = "WeighmentDataSplittingToolStripMenuItem"
        Me.WeighmentDataSplittingToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.WeighmentDataSplittingToolStripMenuItem.Text = "Weighment Data Splitting"
        '
        'WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem
        '
        Me.WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem.Name = "WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem"
        Me.WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem.Text = "&Weighment Data Grouping_on_the_basis of Reference"
        '
        'VehicleTransferToOtherLocationToolStripMenuItem
        '
        Me.VehicleTransferToOtherLocationToolStripMenuItem.Name = "VehicleTransferToOtherLocationToolStripMenuItem"
        Me.VehicleTransferToOtherLocationToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.VehicleTransferToOtherLocationToolStripMenuItem.Text = "&Vehicle Transfer to Other Location"
        Me.VehicleTransferToOtherLocationToolStripMenuItem.Visible = False
        '
        'ChangeGroupingReferenceToolStripMenuItem
        '
        Me.ChangeGroupingReferenceToolStripMenuItem.Name = "ChangeGroupingReferenceToolStripMenuItem"
        Me.ChangeGroupingReferenceToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.ChangeGroupingReferenceToolStripMenuItem.Text = "&Change Grouping Reference"
        '
        'CancelVehicleEntryToolStripMenuItem
        '
        Me.CancelVehicleEntryToolStripMenuItem.Name = "CancelVehicleEntryToolStripMenuItem"
        Me.CancelVehicleEntryToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.CancelVehicleEntryToolStripMenuItem.Text = "&Cancel Vehicle Entry"
        '
        'CancelGroupingDataToolStripMenuItem
        '
        Me.CancelGroupingDataToolStripMenuItem.Name = "CancelGroupingDataToolStripMenuItem"
        Me.CancelGroupingDataToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.CancelGroupingDataToolStripMenuItem.Text = "&Cancel Grouping Data"
        '
        'VehicleNetWTSplitForGroupingToolStripMenuItem
        '
        Me.VehicleNetWTSplitForGroupingToolStripMenuItem.Name = "VehicleNetWTSplitForGroupingToolStripMenuItem"
        Me.VehicleNetWTSplitForGroupingToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.VehicleNetWTSplitForGroupingToolStripMenuItem.Text = "&Vehicle Net WT Split for Grouping"
        Me.VehicleNetWTSplitForGroupingToolStripMenuItem.Visible = False
        '
        'UpdateDONoBeforeFinalWeighmentToolStripMenuItem
        '
        Me.UpdateDONoBeforeFinalWeighmentToolStripMenuItem.Name = "UpdateDONoBeforeFinalWeighmentToolStripMenuItem"
        Me.UpdateDONoBeforeFinalWeighmentToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.UpdateDONoBeforeFinalWeighmentToolStripMenuItem.Text = "&Update DO No before Final Weighment"
        '
        'UpdateLineItemsToolStripMenuItem
        '
        Me.UpdateLineItemsToolStripMenuItem.Name = "UpdateLineItemsToolStripMenuItem"
        Me.UpdateLineItemsToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.UpdateLineItemsToolStripMenuItem.Text = "Update &line Items"
        '
        'ContractorItemStoresApprovalToolStripMenuItem
        '
        Me.ContractorItemStoresApprovalToolStripMenuItem.Name = "ContractorItemStoresApprovalToolStripMenuItem"
        Me.ContractorItemStoresApprovalToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.ContractorItemStoresApprovalToolStripMenuItem.Text = "&Contractor Item Stores Approval"
        '
        'ContractorItemOUTPassToolStripMenuItem
        '
        Me.ContractorItemOUTPassToolStripMenuItem.Name = "ContractorItemOUTPassToolStripMenuItem"
        Me.ContractorItemOUTPassToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.ContractorItemOUTPassToolStripMenuItem.Text = "Contractor Item (&OUT Pass)"
        '
        'ModifyChallanNoTransporterToolStripMenuItem
        '
        Me.ModifyChallanNoTransporterToolStripMenuItem.Name = "ModifyChallanNoTransporterToolStripMenuItem"
        Me.ModifyChallanNoTransporterToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.ModifyChallanNoTransporterToolStripMenuItem.Text = "&Modify Challan No Transporter"
        '
        'VehicleActivityChangeToolStripMenuItem
        '
        Me.VehicleActivityChangeToolStripMenuItem.Name = "VehicleActivityChangeToolStripMenuItem"
        Me.VehicleActivityChangeToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.VehicleActivityChangeToolStripMenuItem.Text = "Vehicle Activity Change"
        Me.VehicleActivityChangeToolStripMenuItem.Visible = False
        '
        'VehicleNumberChangeToolStripMenuItem
        '
        Me.VehicleNumberChangeToolStripMenuItem.Name = "VehicleNumberChangeToolStripMenuItem"
        Me.VehicleNumberChangeToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.VehicleNumberChangeToolStripMenuItem.Text = "Vehicle Number Change"
        Me.VehicleNumberChangeToolStripMenuItem.Visible = False
        '
        'ClearSecondWeighmentToolStripMenuItem
        '
        Me.ClearSecondWeighmentToolStripMenuItem.Name = "ClearSecondWeighmentToolStripMenuItem"
        Me.ClearSecondWeighmentToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.ClearSecondWeighmentToolStripMenuItem.Text = "Clear Weight Entry"
        '
        'ContractorItemOUTPASSToolStripMenuItem1
        '
        Me.ContractorItemOUTPASSToolStripMenuItem1.Name = "ContractorItemOUTPASSToolStripMenuItem1"
        Me.ContractorItemOUTPASSToolStripMenuItem1.Size = New System.Drawing.Size(357, 22)
        Me.ContractorItemOUTPASSToolStripMenuItem1.Text = "Check Contractor Item (OUT PASS)"
        '
        'CheckContractorItem47KhataOUTPASSToolStripMenuItem
        '
        Me.CheckContractorItem47KhataOUTPASSToolStripMenuItem.Name = "CheckContractorItem47KhataOUTPASSToolStripMenuItem"
        Me.CheckContractorItem47KhataOUTPASSToolStripMenuItem.Size = New System.Drawing.Size(357, 22)
        Me.CheckContractorItem47KhataOUTPASSToolStripMenuItem.Text = "Check Contractor Item (47 Khata OUT PASS)"
        '
        'ReportsToolStripMenuItem
        '
        Me.ReportsToolStripMenuItem.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.SlipPrintReportToolStripMenuItem, Me.DetailsReportToolStripMenuItem, Me.GateCheckPostINToolStripMenuItem, Me.DetailsReportNewToolStripMenuItem})
        Me.ReportsToolStripMenuItem.Name = "ReportsToolStripMenuItem"
        Me.ReportsToolStripMenuItem.Size = New System.Drawing.Size(59, 20)
        Me.ReportsToolStripMenuItem.Text = "&Reports"
        '
        'SlipPrintReportToolStripMenuItem
        '
        Me.SlipPrintReportToolStripMenuItem.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.GatePassSlipToolStripMenuItem, Me.WeighmentSlipToolStripMenuItem, Me.WeighmentSlipDirectToolStripMenuItem, Me.WeighmentSlipViewToolStripMenuItem})
        Me.SlipPrintReportToolStripMenuItem.Name = "SlipPrintReportToolStripMenuItem"
        Me.SlipPrintReportToolStripMenuItem.Size = New System.Drawing.Size(241, 22)
        Me.SlipPrintReportToolStripMenuItem.Text = "Slip Print Report"
        '
        'GatePassSlipToolStripMenuItem
        '
        Me.GatePassSlipToolStripMenuItem.Name = "GatePassSlipToolStripMenuItem"
        Me.GatePassSlipToolStripMenuItem.Size = New System.Drawing.Size(200, 22)
        Me.GatePassSlipToolStripMenuItem.Text = "&Gate Pass Slip"
        '
        'WeighmentSlipToolStripMenuItem
        '
        Me.WeighmentSlipToolStripMenuItem.Name = "WeighmentSlipToolStripMenuItem"
        Me.WeighmentSlipToolStripMenuItem.Size = New System.Drawing.Size(200, 22)
        Me.WeighmentSlipToolStripMenuItem.Text = "&Weighment Slip"
        '
        'WeighmentSlipDirectToolStripMenuItem
        '
        Me.WeighmentSlipDirectToolStripMenuItem.Name = "WeighmentSlipDirectToolStripMenuItem"
        Me.WeighmentSlipDirectToolStripMenuItem.Size = New System.Drawing.Size(200, 22)
        Me.WeighmentSlipDirectToolStripMenuItem.Text = "Weighment Slip (&Direct)"
        '
        'WeighmentSlipViewToolStripMenuItem
        '
        Me.WeighmentSlipViewToolStripMenuItem.Name = "WeighmentSlipViewToolStripMenuItem"
        Me.WeighmentSlipViewToolStripMenuItem.Size = New System.Drawing.Size(200, 22)
        Me.WeighmentSlipViewToolStripMenuItem.Text = "Weighment Slip &View"
        '
        'DetailsReportToolStripMenuItem
        '
        Me.DetailsReportToolStripMenuItem.Name = "DetailsReportToolStripMenuItem"
        Me.DetailsReportToolStripMenuItem.Size = New System.Drawing.Size(241, 22)
        Me.DetailsReportToolStripMenuItem.Text = "&Details Report"
        '
        'GateCheckPostINToolStripMenuItem
        '
        Me.GateCheckPostINToolStripMenuItem.Name = "GateCheckPostINToolStripMenuItem"
        Me.GateCheckPostINToolStripMenuItem.Size = New System.Drawing.Size(241, 22)
        Me.GateCheckPostINToolStripMenuItem.Text = "Gate/Check Post IN OUT Report"
        '
        'DetailsReportNewToolStripMenuItem
        '
        Me.DetailsReportNewToolStripMenuItem.Name = "DetailsReportNewToolStripMenuItem"
        Me.DetailsReportNewToolStripMenuItem.Size = New System.Drawing.Size(241, 22)
        Me.DetailsReportNewToolStripMenuItem.Text = "&Details Report New"
        '
        'Timer1
        '
        Me.Timer1.Enabled = True
        Me.Timer1.Interval = 1000
        '
        'MDIForm1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackgroundImage = CType(resources.GetObject("$this.BackgroundImage"), System.Drawing.Image)
        Me.ClientSize = New System.Drawing.Size(1280, 701)
        Me.Controls.Add(Me.MenuStrip1)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.IsMdiContainer = True
        Me.Name = "MDIForm1"
        Me.Text = "ElectroWay - e Application by ESL STEEL LIMITED   Ver. - 18.02.24"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        Me.MenuStrip1.ResumeLayout(False)
        Me.MenuStrip1.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents MenuStrip1 As System.Windows.Forms.MenuStrip
    Friend WithEvents MasterToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ReportsToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents CompanyMasterToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents PlantMasterToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents LocationToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents TransactionToolStripMenuItem1 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents Timer1 As System.Windows.Forms.Timer
    Friend WithEvents ScannedDocumentPathSettingsToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ImportMasterDatafromSAPToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleTareWeightManualModeToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleTareWeightAutoToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleTareWTValidityExtensionToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents DriverDetailsToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents SAPServerConfigToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents NodeClientConfigurationToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents UserMasterToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents MaterialDetailsToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VendorDetailsToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents CustomerDetailsToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents TransporterDetailsToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents RakeGroupingReferenceToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleWithTransporterToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ChangePasswordToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleStatusBlacklistWarnedToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ExitToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents TransactionToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents GateEntryToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents WeighmentToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents WeighmentManualModeToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents CheckPostEntryToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents WeighmentDataSplittingToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleTransferToOtherLocationToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ChangeGroupingReferenceToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents CancelVehicleEntryToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents CancelGroupingDataToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleNetWTSplitForGroupingToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents UpdateDONoBeforeFinalWeighmentToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents UpdateLineItemsToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ContractorItemStoresApprovalToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ContractorItemOUTPassToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ModifyChallanNoTransporterToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents SlipPrintReportToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents GatePassSlipToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents WeighmentSlipToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents WeighmentSlipDirectToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents WeighmentSlipViewToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents DetailsReportToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleActivityChangeToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents VehicleNumberChangeToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents GateCheckPostINToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ClearSecondWeighmentToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ContractorItemOUTPASSToolStripMenuItem1 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents CheckContractorItem47KhataOUTPASSToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents DetailsReportNewToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents DocumentsValidityToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem

End Class
