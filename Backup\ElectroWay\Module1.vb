﻿Imports System
Imports System.Data
Imports System.Data.SqlClient
Module Module1
    Public Database As String = "ELECTROWAYESL"
    '' ''------------------------
    Public UserId As String = "sdadmin"
    Public Password As String = "***********"
    Public Server As String = "192.1.88.10"
    Public con As SqlConnection = New SqlConnection("Server =192.1.88.10;database=ELECTROWAYESL;user id=sdadmin; Password=***********;")
    ' ''--------------------------------------------------------
    ''------------------------
    'Public UserId As String = "ESL"
    'Public Password As String = "********"
    'Public Server As String = "************"
    'Public con As SqlConnection = New SqlConnection("Server =************;database=ELECTROWAYESL;user id=ESL; Password=********;")


    Public cm As SqlCommand
    Public ds As DataSet
    Public dr As SqlDataReader
    Public da As SqlDataAdapter
    Public dt As DataTable
    Public dc As DataColumn
    '------------------------
    Public ipaddress As String
    Public CallFromVehTareWtMst As Integer
    '------------------------------------
    Public User_ID As String
    Public Sys_loc_IP As String
    Public OperatMode As String
    Public OperationType As String
    Public vehicle_sel As Integer
    Public FirstLevelAuth As Integer
    Public SecondLevelAuth As Integer
    Public selrec1 As String
    Public ApplicationPath As String
    Public dataRep_close As Integer
    Public AuthForOUTBOUNDSplitting As String
    '------------SAP-------------------
    Public SAPUsere_ID As String
    Public SAPUsere_Pass As String
    Public SAPSys_name As String
    Public SAPApp_Server As String
    Public SAPSys_No As String
    Public SAP_Client As String
    Public SAP_Lang As String
    Public SAP_CodePage As String
    '--------------------------------
    ''OOOOOOOOOOOOOOOOOOOOOOOO
    Public VehicleImagePathCamera As String
    Public VehicleImagePathServer As String
    Public BlueBookPathServer As String
    Public DLICPathServer As String
    Public Help_callfrom As String
    Public Call_From_Veh_WT As String
    Public PLANT_CODE_UPL_ZWT_BG As String
    Public Call_From_Check_Post As String
End Module
