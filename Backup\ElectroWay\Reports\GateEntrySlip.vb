﻿Imports System.Data.SqlClient
Imports System.Data
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Public Class GateEntrySlip
    Dim cc As New Class1
    Dim CrystalReportViewer As CrystalDecisions.Windows.Forms.CrystalReportViewer = New CrystalDecisions.Windows.Forms.CrystalReportViewer
    Friend Sub ViewReport(ByVal ReportName As String, ByVal TableName() As String, ByVal QueryString() As String, Optional ByVal [Parameter] As String = "")
        'Me.MdiParent = MainForm

        If Not UBound(TableName).Equals(UBound(QueryString)) Then MessageBox.Show("Passed Variable Are Not Correct", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information) : Exit Sub

        Dim Report As CrystalDecisions.CrystalReports.Engine.ReportDocument = New CrystalDecisions.CrystalReports.Engine.ReportDocument

        'Dim CrystalReportViewer As CrystalDecisions.Windows.Forms.CrystalReportViewer = New CrystalDecisions.Windows.Forms.CrystalReportViewer

        CrystalReportViewer.ActiveViewIndex = 0

        CrystalReportViewer.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle

        CrystalReportViewer.DisplayGroupTree = False

        CrystalReportViewer.Dock = System.Windows.Forms.DockStyle.Fill

        CrystalReportViewer.Location = New System.Drawing.Point(0, 0)

        CrystalReportViewer.Name = "CrystalReportViewer"
        Dim Adapter As New SqlDataAdapter

        Dim DataSet As New DataSet
        Try
            For I As Integer = 0 To UBound(TableName)
                Adapter = cc.GetDataAdeptor(QueryString(I))
                Adapter.Fill(DataSet, TableName(I))
            Next
        Catch ex As Exception

        End Try
        '---------------------
        Report.Load(Application.StartupPath & "\" & ReportName)

        Report.SetDataSource(DataSet)

        CrystalReportViewer.ReportSource = Report
        Dim connectionInfo As ConnectionInfo = New ConnectionInfo()
        connectionInfo.DatabaseName = Database
        connectionInfo.ServerName = Server
        connectionInfo.UserID = UserId
        connectionInfo.Password = Password
        SetDBLogonForReport(connectionInfo, Report)
        CrystalReportViewer.ReportSource = Report
        'Report.PrintOptions.PaperSize = CrystalDecisions.Shared.PaperSize.PaperA4Small
        'Report.PrintOptions.ApplyPageMargins(New CrystalDecisions.Shared.PageMargins(0, 0, 0, 25))
        Me.Panel1.Controls.Add(CrystalReportViewer)
        'Report.PrintToPrinter(1, False, 1, 1)
    End Sub
    Private Sub SetDBLogonForReport(ByVal connectionInfo As ConnectionInfo, ByVal reportDocument As ReportDocument)
        Dim tables As Tables = reportDocument.Database.Tables
        For Each table As CrystalDecisions.CrystalReports.Engine.Table In tables
            Dim tableLogonInfo As TableLogOnInfo = table.LogOnInfo
            tableLogonInfo.ConnectionInfo = connectionInfo
            table.ApplyLogOnInfo(tableLogonInfo)
        Next
    End Sub

    Private Sub GateEntrySlip_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyDown
        If e.KeyCode = Keys.P Then
            CrystalReportViewer.PrintReport()
        End If
    End Sub
End Class