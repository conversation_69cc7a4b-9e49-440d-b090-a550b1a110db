﻿Imports Excel = Microsoft.Office.Interop.Excel
Imports Microsoft.Office.Interop.Excel
Public Class frmCheckPostReport
    Dim cc As New Class1
    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        Dim dtFrom1 As Date = dtFrom.Text
        Dim dtTo1 As Date = dtTo.Text
        Dim TotalVehicle As Integer = 0

        Dim str As String = "select * , (DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime) ) as   'TATMinuteDiff' from tbl_VIEW_GE_HDR_Allowed_Route_CheckPost_Det where vehicle_No like '%" & Trim(txtVehicleNo.Text) & "%' and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'  and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999'  order by EntryDateTime"
        Try
            ds = cc.GetDataset(str)
            gvReport.DataSource = ds.Tables(0)
        Catch ex As Exception

        End Try
    End Sub
    Private Function SearchData(ByVal Str As String) As String
        Dim ValueName As String = ""
        dr = cc.GetDataReader(Str)
        Try
            While dr.Read
                If ValueName = "" Then
                    ValueName = dr(0).ToString
                Else
                    ValueName = ValueName & "," & dr(0).ToString
                End If
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        Return ValueName
    End Function

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        Dim excel1 As Microsoft.Office.Interop.Excel.Application
        Dim wb As Microsoft.Office.Interop.Excel.Workbook

        Dim xlsheet As Excel.Worksheet

        'Dim xlwbook As Excel.Workbook

        excel1 = New Microsoft.Office.Interop.Excel.Application
        wb = excel1.Workbooks.Open(ApplicationPath & "\DetailsReport.xls", True, True, , "jnan")
        excel1.Visible = True
        wb.Activate()
        Try
            Dim row, col As Integer
            row = 2

            xlsheet = wb.Sheets.Item(1)

            For i As Integer = 0 To gvReport.Columns.Count - 1
                xlsheet.Cells(1, i + 1) = gvReport.Columns(i).HeaderText
                '.Cells(1, i) = ListView1.ColumnHeaders(i).Text

            Next i
            For j As Integer = 0 To gvReport.Rows.Count - 1
                xlsheet.Cells(row + j, 1) = gvReport.Rows(j).Cells(0).Value
                xlsheet.Cells(row + j, 2) = gvReport.Rows(j).Cells(1).Value
                xlsheet.Cells(row + j, 3) = gvReport.Rows(j).Cells(2).Value
                xlsheet.Cells(row + j, 4) = gvReport.Rows(j).Cells(3).Value
                xlsheet.Cells(row + j, 5) = gvReport.Rows(j).Cells(4).Value
                xlsheet.Cells(row + j, 6) = gvReport.Rows(j).Cells(5).Value
                xlsheet.Cells(row + j, 7) = gvReport.Rows(j).Cells(6).Value
                xlsheet.Cells(row + j, 8) = gvReport.Rows(j).Cells(7).Value
                xlsheet.Cells(row + j, 9) = gvReport.Rows(j).Cells(8).Value
                xlsheet.Cells(row + j, 10) = gvReport.Rows(j).Cells(9).Value
                xlsheet.Cells(row + j, 11) = gvReport.Rows(j).Cells(10).Value
                xlsheet.Cells(row + j, 12) = gvReport.Rows(j).Cells(11).Value
                xlsheet.Cells(row + j, 13) = gvReport.Rows(j).Cells(12).Value
                xlsheet.Cells(row + j, 14) = gvReport.Rows(j).Cells(13).Value
                xlsheet.Cells(row + j, 15) = gvReport.Rows(j).Cells(14).Value
                xlsheet.Cells(row + j, 16) = gvReport.Rows(j).Cells(15).Value
                xlsheet.Cells(row + j, 17) = gvReport.Rows(j).Cells(16).Value
                xlsheet.Cells(row + j, 18) = gvReport.Rows(j).Cells(17).Value
                xlsheet.Cells(row + j, 19) = gvReport.Rows(j).Cells(18).Value
                xlsheet.Cells(row + j, 20) = gvReport.Rows(j).Cells(19).Value
                xlsheet.Cells(row + j, 21) = gvReport.Rows(j).Cells(20).Value
                xlsheet.Cells(row + j, 22) = gvReport.Rows(j).Cells(21).Value
                xlsheet.Cells(row + j, 23) = gvReport.Rows(j).Cells(22).Value
                xlsheet.Cells(row + j, 24) = gvReport.Rows(j).Cells(23).Value
                xlsheet.Cells(row + j, 25) = gvReport.Rows(j).Cells(24).Value
                xlsheet.Cells(row + j, 26) = gvReport.Rows(j).Cells(25).Value
                xlsheet.Cells(row + j, 27) = gvReport.Rows(j).Cells(26).Value
                xlsheet.Cells(row + j, 28) = gvReport.Rows(j).Cells(27).Value
                xlsheet.Cells(row + j, 29) = gvReport.Rows(j).Cells(28).Value
                xlsheet.Cells(row + j, 30) = gvReport.Rows(j).Cells(29).Value
                xlsheet.Cells(row + j, 31) = gvReport.Rows(j).Cells(30).Value
                xlsheet.Cells(row + j, 32) = gvReport.Rows(j).Cells(31).Value
                xlsheet.Cells(row + j, 33) = gvReport.Rows(j).Cells(32).Value
                xlsheet.Cells(row + j, 34) = gvReport.Rows(j).Cells(33).Value
                xlsheet.Cells(row + j, 35) = gvReport.Rows(j).Cells(34).Value
               
             
            Next
        Catch ex As Exception

        End Try
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Hide()
    End Sub
End Class