﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmReport
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmReport))
        Me.GroupBox1 = New System.Windows.Forms.GroupBox
        Me.btnECL = New System.Windows.Forms.Button
        Me.btnWeighmentSlip = New System.Windows.Forms.Button
        Me.btnGateEntrySlip = New System.Windows.Forms.Button
        Me.gbRB = New System.Windows.Forms.GroupBox
        Me.rbSales = New System.Windows.Forms.RadioButton
        Me.rbPurchase = New System.Windows.Forms.RadioButton
        Me.txtTransactionNo = New System.Windows.Forms.TextBox
        Me.Label1 = New System.Windows.Forms.Label
        Me.GroupBox2 = New System.Windows.Forms.GroupBox
        Me.btnExit = New System.Windows.Forms.Button
        Me.GroupBox1.SuspendLayout()
        Me.gbRB.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.btnECL)
        Me.GroupBox1.Controls.Add(Me.btnWeighmentSlip)
        Me.GroupBox1.Controls.Add(Me.btnGateEntrySlip)
        Me.GroupBox1.Controls.Add(Me.gbRB)
        Me.GroupBox1.Controls.Add(Me.txtTransactionNo)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Location = New System.Drawing.Point(13, 3)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(259, 281)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'btnECL
        '
        Me.btnECL.Location = New System.Drawing.Point(153, 189)
        Me.btnECL.Name = "btnECL"
        Me.btnECL.Size = New System.Drawing.Size(97, 40)
        Me.btnECL.TabIndex = 5
        Me.btnECL.Text = "ECL Gate Entry Slip"
        Me.btnECL.UseVisualStyleBackColor = True
        Me.btnECL.Visible = False
        '
        'btnWeighmentSlip
        '
        Me.btnWeighmentSlip.Location = New System.Drawing.Point(12, 235)
        Me.btnWeighmentSlip.Name = "btnWeighmentSlip"
        Me.btnWeighmentSlip.Size = New System.Drawing.Size(241, 40)
        Me.btnWeighmentSlip.TabIndex = 4
        Me.btnWeighmentSlip.Text = "Weighment Slip"
        Me.btnWeighmentSlip.UseVisualStyleBackColor = True
        '
        'btnGateEntrySlip
        '
        Me.btnGateEntrySlip.Location = New System.Drawing.Point(12, 190)
        Me.btnGateEntrySlip.Name = "btnGateEntrySlip"
        Me.btnGateEntrySlip.Size = New System.Drawing.Size(133, 40)
        Me.btnGateEntrySlip.TabIndex = 3
        Me.btnGateEntrySlip.Text = "Gate Entry Slip"
        Me.btnGateEntrySlip.UseVisualStyleBackColor = True
        '
        'gbRB
        '
        Me.gbRB.Controls.Add(Me.rbSales)
        Me.gbRB.Controls.Add(Me.rbPurchase)
        Me.gbRB.Location = New System.Drawing.Point(12, 73)
        Me.gbRB.Name = "gbRB"
        Me.gbRB.Size = New System.Drawing.Size(241, 111)
        Me.gbRB.TabIndex = 2
        Me.gbRB.TabStop = False
        '
        'rbSales
        '
        Me.rbSales.AutoSize = True
        Me.rbSales.Location = New System.Drawing.Point(161, 20)
        Me.rbSales.Name = "rbSales"
        Me.rbSales.Size = New System.Drawing.Size(59, 17)
        Me.rbSales.TabIndex = 1
        Me.rbSales.Text = "SALES"
        Me.rbSales.UseVisualStyleBackColor = True
        '
        'rbPurchase
        '
        Me.rbPurchase.AutoSize = True
        Me.rbPurchase.Checked = True
        Me.rbPurchase.Location = New System.Drawing.Point(7, 20)
        Me.rbPurchase.Name = "rbPurchase"
        Me.rbPurchase.Size = New System.Drawing.Size(148, 17)
        Me.rbPurchase.TabIndex = 0
        Me.rbPurchase.TabStop = True
        Me.rbPurchase.Text = "PURCHASE/CONT ITEM"
        Me.rbPurchase.UseVisualStyleBackColor = True
        '
        'txtTransactionNo
        '
        Me.txtTransactionNo.Location = New System.Drawing.Point(12, 37)
        Me.txtTransactionNo.Name = "txtTransactionNo"
        Me.txtTransactionNo.Size = New System.Drawing.Size(241, 20)
        Me.txtTransactionNo.TabIndex = 1
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(9, 20)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(136, 13)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "Gate Entry Transaction No."
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.btnExit)
        Me.GroupBox2.Location = New System.Drawing.Point(12, 290)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(260, 56)
        Me.GroupBox2.TabIndex = 1
        Me.GroupBox2.TabStop = False
        '
        'btnExit
        '
        Me.btnExit.Location = New System.Drawing.Point(166, 19)
        Me.btnExit.Name = "btnExit"
        Me.btnExit.Size = New System.Drawing.Size(85, 29)
        Me.btnExit.TabIndex = 5
        Me.btnExit.Text = "Exit"
        Me.btnExit.UseVisualStyleBackColor = True
        '
        'frmReport
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(284, 350)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.GroupBox1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmReport"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "REPORT"
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.gbRB.ResumeLayout(False)
        Me.gbRB.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents txtTransactionNo As System.Windows.Forms.TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents gbRB As System.Windows.Forms.GroupBox
    Friend WithEvents rbSales As System.Windows.Forms.RadioButton
    Friend WithEvents rbPurchase As System.Windows.Forms.RadioButton
    Friend WithEvents btnGateEntrySlip As System.Windows.Forms.Button
    Friend WithEvents btnWeighmentSlip As System.Windows.Forms.Button
    Friend WithEvents btnExit As System.Windows.Forms.Button
    Friend WithEvents btnECL As System.Windows.Forms.Button
End Class
