﻿Imports Microsoft.Office.Interop

Public Class frmReport
    Dim xl As New Excel.Application
    Dim xlsheet As Excel.Worksheet
    Dim xlwbook As Excel.Workbook
    Dim cc As New Class1
    Private Sub frmReport_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnGateEntrySlip_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGateEntrySlip.Click
        Dim Type_Of_Vehicle As String = Nothing
        Dim str As String = "SELECT Type_Of_Vehicle FROM tbl_GE_HDR WHERE GE_HDR_ID = '" & Trim(txtTransactionNo.Text.Trim) & "'"
        dr = cc.GetDataReader(str)
        Try
            While dr.Read
                Type_Of_Vehicle = dr(0).ToString
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        If Type_Of_Vehicle = Nothing Then
            MessageBox.Show("Type Of Vehicle is not Found!")
            Exit Sub
        End If

        'CONTITEM','GATEPASS','PURCH','SALES','SALESRET','STKTROUT'
        If Type_Of_Vehicle = "PURCH" Or Type_Of_Vehicle = "CONTITEM" Or Type_Of_Vehicle = "INTRDEPT" Or Type_Of_Vehicle = "GATEPASS" Or Type_Of_Vehicle = "PURCHRET" Then
            'Dim ReportForm As New GateEntry_Slip
            'Dim TableName(0) As String
            'Dim QueryString(0) As String
            'TableName(0) = "tbl_GE_HDR"
            ''TableName(1) = "tbl_GE_DET"
            'QueryString(0) = "Select a.* ,b.* from tbl_GE_HDR a, tbl_GE_DET b where a.TRN_ID  = b.GE_HDR_TRAN_ID and  a.GE_HDR_ID = '" & Trim(txtTransactionNo.Text.Trim) & "'"   '''''a.GE_HDR_ID ='" & Trim(Transaction_No_hdr_ID) & "'", con, adOpenKeyset '' where convert(varchar, EntryDateTime, 105) between '" & Format(dtValidity.Value, "dd-MM-yyyy") & "' and '" & Format(DTPicker2.Value, "dd-MM-yyyy") & "'"
            'ReportForm.ViewReport("RptGateEntrySlip.rpt", TableName, QueryString, )
            'ReportForm.Show()
            'ReportForm.BringToFront()
            ''ReportForm.MaximizeBox = True
            Dim ReportForm As New GateEntry_Slip
            Dim TableName(0) As String
            Dim QueryString(0) As String
            TableName(0) = "tbl_VIEW_GE_HDR_Details"
            'TableName(1) = "tbl_GE_DET"
            QueryString(0) = "Select * from tbl_VIEW_GE_HDR_Details where GE_HDR_ID = '" & Trim(txtTransactionNo.Text.Trim) & "'"   '''''a.GE_HDR_ID ='" & Trim(Transaction_No_hdr_ID) & "'", con, adOpenKeyset '' where convert(varchar, EntryDateTime, 105) between '" & Format(dtValidity.Value, "dd-MM-yyyy") & "' and '" & Format(DTPicker2.Value, "dd-MM-yyyy") & "'"
            ReportForm.ViewReport("RptGateEntrySlip3.rpt", TableName, QueryString, )
            ReportForm.Show()
            ReportForm.BringToFront()
        Else
            Dim ReportForm As New GateEntry_Slip
            Dim TableName(0) As String
            Dim QueryString(0) As String
            TableName(0) = "tbl_VIEW_GE_HDR_Details"
            'TableName(1) = "tbl_GE_DET"
            QueryString(0) = "Select * from tbl_VIEW_GE_HDR_Details where GE_HDR_ID = '" & Trim(txtTransactionNo.Text.Trim) & "'"   '''''a.GE_HDR_ID ='" & Trim(Transaction_No_hdr_ID) & "'", con, adOpenKeyset '' where convert(varchar, EntryDateTime, 105) between '" & Format(dtValidity.Value, "dd-MM-yyyy") & "' and '" & Format(DTPicker2.Value, "dd-MM-yyyy") & "'"
            ReportForm.ViewReport("RptGateEntrySlip1.rpt", TableName, QueryString, )
            ReportForm.Show()
            ReportForm.BringToFront()
            'ReportForm.MaximizeBox = True
        End If

        
    End Sub

    Private Sub btnWeighmentSlip_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnWeighmentSlip.Click
        Dim ReportForm As New GateEntry_Slip
        Dim TableName(0) As String
        Dim QueryString(0) As String
        TableName(0) = "tbl_GE_HDR"
        'TableName(1) = "tbl_GE_DET"
        QueryString(0) = "Select a.*,b.* from tbl_GE_HDR a, tbl_GE_DET b where a.TRN_ID  = b.GE_HDR_TRAN_ID and a.GE_HDR_ID ='" & Trim(txtTransactionNo.Text.Trim) & "' and b.F_WT > 0 and b.S_WT > 0 and  b.NET_WT > 0"
        ds = cc.GetDataset(QueryString(0))
        If ds.Tables(0).Rows.Count > 0 Then
            ReportForm.ViewReport("RptWeighmentSlip.rpt", TableName, QueryString, )
            ReportForm.Show()
            ReportForm.BringToFront()
        Else
            Dim ansp = MsgBox("Weighment Not Completed .......Do you still want to take First Weighment Slip Print ?", vbYesNo, "ElectroWay")

            If ansp = vbYes Then
                QueryString(0) = "Select a.*,b.* from tbl_GE_HDR a, tbl_GE_DET b where a.TRN_ID  = b.GE_HDR_TRAN_ID and a.GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "' and b.F_WT > 0" '' where convert(varchar, EntryDateTime, 105) between '" & Format(DTPicker1.Value, "dd-MM-yyyy") & "' and '" & Format(DTPicker2.Value, "dd-MM-yyyy") & "'", con, adOpenKeyset
                ds = cc.GetDataset(QueryString(0))

                If ds.Tables(0).Rows.Count > 0 Then
                    ReportForm.ViewReport("RptWeighmentSlip.rpt", TableName, QueryString, )
                    ReportForm.Show()
                    ReportForm.BringToFront()
                Else
                    MsgBox("First Weighment Not done .....", vbInformation, "ElectroWay")
                End If

            End If
        End If
      
    End Sub

    Private Sub txtTransactionNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransactionNo.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "TRANSACTION_NO"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub txtTransactionNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTransactionNo.TextChanged

    End Sub

    Private Sub btnECL_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnECL.Click
        Dim ReportForm As New GateEntry_Slip
        Dim TableName(0) As String
        Dim QueryString(0) As String
        TableName(0) = "tbl_VIEW_GE_HDR_Details"
        'TableName(1) = "tbl_GE_DET"
        QueryString(0) = "Select * from tbl_VIEW_GE_HDR_Details where GE_HDR_ID = '" & Trim(txtTransactionNo.Text.Trim) & "'"   '''''a.GE_HDR_ID ='" & Trim(Transaction_No_hdr_ID) & "'", con, adOpenKeyset '' where convert(varchar, EntryDateTime, 105) between '" & Format(dtValidity.Value, "dd-MM-yyyy") & "' and '" & Format(DTPicker2.Value, "dd-MM-yyyy") & "'"
        ReportForm.ViewReport("ECL_RptGateEntrySlip1.rpt", TableName, QueryString, )
        ReportForm.Show()
        ReportForm.BringToFront()
    End Sub
End Class