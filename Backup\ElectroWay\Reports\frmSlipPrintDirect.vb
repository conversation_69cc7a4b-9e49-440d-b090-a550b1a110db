﻿Imports System.Drawing.Printing
Imports Microsoft.Win32.SafeHandles
Imports System.Runtime.InteropServices
Imports System.IO

Public Class frmSlipPrintDirect
    Dim cc As New Class1
    Private Sub frmSlipPrintDirect_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Private Sub txtTransactionNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransactionNo.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "SLIP_DIRECT_TRANSACTION_NO"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub txtTransactionNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo.KeyPress
        If AscW(e.KeyChar) = 13 And Trim(txtTransactionNo.Text) <> "" Then
            Dim Vehicle_No_Print, Plant_Code_Print, Seal_No_Print, PartyNEET_WT, TransporterName_Print, GE_HDR_ID_Print, PO_NO_print, DO_No_Print, Mat_Code_Print, Mat_Name_Print, Type_Of_Vehicle_Print, F_WT_Print, S_WT_Print, NET_WT_Print, F_WT_Note_Print, S_WT_Note_Print, F_WT_DateTime_Print, S_WT_DateTime_Print, Chaln_nu_Print, Chaln_Qty_Print, Chaln_UOM_Print, DO_nu_Print, VenCustName As String
            Dim Chaln_Qty_Print_D As Double
            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "'")
            If dr.Read Then
                Vehicle_No_Print = dr("Vehicle_No")
                Plant_Code_Print = dr("Plant_Code")
                Seal_No_Print = dr("Seal_No")
                PartyNEET_WT = dr("Party_Net_WT")
                TransporterName_Print = dr("TransporterName")
               
            End If
            dr.Close()

            dr = cc.GetDataReader("select * from tbl_Split_DET where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' order by WB_Count_ID Desc")
            If dr.Read Then

                GE_HDR_ID_Print = dr("GE_HDR_ID")

                PO_NO_print = dr("PO_No")
                DO_No_Print = dr("DO_no")
                Mat_Code_Print = dr("Mat_Code")
                Mat_Name_Print = dr("Mat_Desc")
                Type_Of_Vehicle_Print = dr("Type_Of_Vehicle")

                F_WT_Print = dr("F_WT")
                S_WT_Print = dr("S_WT")
                NET_WT_Print = dr("NET_WT")
                F_WT_Note_Print = dr("F_WT_Note")
                S_WT_Note_Print = dr("S_WT_Note")

                F_WT_DateTime_Print = dr("F_WT_DateTime")
                S_WT_DateTime_Print = dr("S_WT_DateTime")

                Chaln_nu_Print = dr("Challan_No")
                'Chaln_Qty_Print = dr("DO_Challan_QTY")
                Chaln_Qty_Print_D = dr("DO_Challan_QTY")
                Chaln_UOM_Print = dr("UOM")
                DO_nu_Print = dr("DO_NO")
                VenCustName = dr("Vendor_Name")
                If VenCustName.Trim = "" Then
                    VenCustName = dr("Customer_Name")
                End If

                ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
                'Dim nPrinter As Long
                'nPrinter = FreeFile()
                Dim lines As String = ""
                'Dim printer As New myPrinter
                ''Open "LPT1:" For Output As nPrinter
                Try
                    lines = "             ELECTROSTEEL STEELS LIMITED ( " & Trim(Plant_Code_Print) & " - WORKS )"
                    lines = lines & vbCrLf & "                           WEIGHMENT SLIP  -  ( " & Type_Of_Vehicle_Print & " )"
                    lines = lines & vbCrLf & "--------------------------------------------------------------------------------"

                    lines = lines & vbCrLf & " Gate Pass No              : " & GE_HDR_ID_Print & ""
                    lines = lines & vbCrLf & " VEHICLE NO                : " & Vehicle_No_Print & ""
                    lines = lines & vbCrLf & " Vendor/Customer           : " & VenCustName & ""
                    lines = lines & vbCrLf & " Material                  : " & Mat_Name_Print & ""

                    lines = lines & vbCrLf & " Challan/DO No.            : " & Chaln_nu_Print & " " & DO_nu_Print & ""
                    'lines = lines & vbCrLf & " Challan Qty.              : " & CInt(Chaln_Qty_Print) & "" & Chaln_UOM_Print
                    lines = lines & vbCrLf & " Challan Qty.              : " & Chaln_Qty_Print_D.ToString("0.##", New System.Globalization.CultureInfo("en-US")) & "" & Chaln_UOM_Print

                    lines = lines & vbCrLf & " Transporter Name          : " & TransporterName_Print
                    lines = lines & vbCrLf & Chr(13) & Chr(5)
                    'printer.prt(Chr(13) & Chr(5))

                    lines = lines & vbCrLf & " 1st WT. Date & Time       : " & F_WT_DateTime_Print
                    lines = lines & vbCrLf & " 2nd WT. Date & Time       : " & S_WT_DateTime_Print ' Tab(72); S_WT_Print; ""

                    lines = lines & vbCrLf & Chr(13) & Chr(5)

                    lines = lines & vbCrLf & " 1st WT. (KG)              : " & CInt(F_WT_Print) ' Tab(72); F_WT_Print; ""
                    lines = lines & vbCrLf & " 2nd WT. (KG)              : " & CInt(S_WT_Print) ' Tab(72); S_WT_Print; ""
                    lines = lines & vbCrLf & " Net WT. (KG)              : " & CInt(NET_WT_Print) ' Tab(72); S_WT_Print; ""


                    lines = lines & vbCrLf & "                                                          (Signature)"
                    lines = lines & vbCrLf & "-------------------------------------------------------------------------------"
                    lines = lines & vbCrLf & Chr(13) & Chr(5)
                    'printer.prt(lines)
                    Print2LPT.Print(lines)
                Catch ex As Exception

                End Try
                ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
                'Print2LPT.Print("             ELECTROSTEEL STEELS LIMITED ( " & Trim(Plant_Code_Print) & " - WORKS )")
                'Print2LPT.Print("                           WEIGHMENT SLIP  -  ( " & Type_Of_Vehicle_Print & "  )")
                'Print2LPT.Print("-----------------------------------------------------------------------------")
                'Print2LPT.Print("")
                'Print2LPT.Print(" Gate Pass No                                  : " & GE_HDR_ID_Print & "")
                'Print2LPT.Print(" VEHICLE NO                                    :" & Vehicle_No_Print & "")
                'Print2LPT.Print(" Vendor/Customer                               :" & VenCustName & "")
                'Print2LPT.Print(" Material                                      :" & Mat_Name_Print & "")
                'Print2LPT.Print(" Challan/DO No.                                : " & Chaln_nu_Print & " " & DO_nu_Print & "")
                'Print2LPT.Print(" Challan Qty.                                  : " & CInt(Chaln_Qty_Print) & "" & Chaln_UOM_Print)
                'Print2LPT.Print(" Transporter Name                              : " & TransporterName_Print)
                'Print2LPT.Print(" 1st WT. Date & Time                           : " & F_WT_DateTime_Print)
                'Print2LPT.Print(" 2nd WT. Date & Time                           : " & S_WT_DateTime_Print & "")
                'Print2LPT.Print(" 1st WT. (KG)                                  : " & CInt(F_WT_Print) & "")
                'Print2LPT.Print(" 2nd WT. (KG)                                  : " & CInt(S_WT_Print) & "")
                'Print2LPT.Print(" Net WT. (KG)                                  : " & CInt(NET_WT_Print) & "")
                'Print2LPT.Print("                                                          (Signature)")
                'Print2LPT.Print("---------------------------------------------------------------------------")

            Else
                MsgBox("No weighment record found for this Transaction No.", vbInformation, "ElectroWay")
            End If

            dr.Close()

        End If

    End Sub
    Public Class myPrinter
        Friend TextToBePrinted As String
        Dim settings As PrinterSettings = New PrinterSettings()
        Public Sub prt(ByVal text As String)
            TextToBePrinted = text
            Dim prn As New Printing.PrintDocument
            Using (prn)
                'prn.PrinterSettings.PrinterName _
                '   = "Kyocera FS-1035MFP KX"
                prn.PrinterSettings.PrinterName = settings.PrinterName
                AddHandler prn.PrintPage, _
                   AddressOf Me.PrintPageHandler
                prn.Print()
                RemoveHandler prn.PrintPage, _
                   AddressOf Me.PrintPageHandler
            End Using
        End Sub

        Private Sub PrintPageHandler(ByVal sender As Object, _
           ByVal args As Printing.PrintPageEventArgs)
            Dim myFont As New Font("Microsoft San Serif", 10)
            args.Graphics.DrawString(TextToBePrinted, _
               New Font(myFont, FontStyle.Regular), _
               Brushes.Black, 50, 50)
        End Sub
    End Class

    Public NotInheritable Class Print2LPT
        Private Sub New()
        End Sub
        <DllImport("kernel32.dll", SetLastError:=True)> _
        Private Shared Function CreateFile(ByVal lpFileName As String, ByVal dwDesiredAccess As FileAccess, ByVal dwShareMode As UInteger, ByVal lpSecurityAttributes As IntPtr, ByVal dwCreationDisposition As FileMode, ByVal dwFlagsAndAttributes As UInteger, _
         ByVal hTemplateFile As IntPtr) As SafeFileHandle
        End Function

        Public Shared Function Print(ByVal SText As String) As Boolean
            Dim nl As String = Convert.ToChar(13).ToString() + Convert.ToChar(10).ToString()
            Dim IsConnected As Boolean = False

            Dim sampleText As String = (Convert.ToString(SText) & nl)
            Try
                Dim buffer As [Byte]() = New Byte(sampleText.Length - 1) {}
                buffer = System.Text.Encoding.ASCII.GetBytes(sampleText)

                Dim fh As SafeFileHandle = CreateFile("LPT1:", FileAccess.Write, 0, IntPtr.Zero, FileMode.OpenOrCreate, 0, _
                 IntPtr.Zero)
                If Not fh.IsInvalid Then
                    IsConnected = True
                    Dim lpt1 As New FileStream(fh, FileAccess.ReadWrite)
                    lpt1.Write(buffer, 0, buffer.Length)
                    lpt1.Close()

                End If
            Catch ex As Exception
                MessageBox.Show(ex.Message)
            End Try

            Return IsConnected
        End Function
    End Class

    Private Sub txtTransactionNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTransactionNo.TextChanged

    End Sub
End Class