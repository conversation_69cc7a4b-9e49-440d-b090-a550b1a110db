﻿Imports Microsoft.Office.Interop

Public Class frmCancelVehicle
    Dim cc As New Class1

    '-------------------
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object
    Dim functionCtr2 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object

    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object
    Dim iii As Integer
    '----------------------
    Private Sub frmCancelVehicle_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        If dr.Read Then
            txtPlant.Text = dr("Plant_Name")
            ''Text7.Text = dr("Node_No")
            txtCompany.Text = dr("Company_Code")

            txtPlant1.Text = dr("Plant_Name")
            txtCompany1.Text = dr("Company_Code")

        Else
            MsgBox("IP number not mapped as ElectroWay Node ....or You are not connected with ElectroWay Server.")

        End If
        dr.Close()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtTransactionNo.Text = ""
        txtVehicleNo.Text = ""
        txtReasonforCancellation.Text = ""

    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        Dim SalesNetF As Boolean = False
        Dim str As String = "select count(*) from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and H.GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "' and D.F_WT > 0"
        dr = cc.GetDataReader(str)
        Try
            While dr.Read
                If CInt(dr(0)) >= 1 Then
                    SalesNetF = True
                End If
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        ''--------------------------
        If Trim(txtTransactionNo.Text) <> "" And Trim(txtVehicleNo.Text) <> "" Then

            Dim ans = MsgBox("Are you sure, you want to cancel this vehicle ?", vbYesNo, "ElectroWay")
            If ans = vbYes Then
                If SalesNetF = True Then
                    Dim str1 As String = "insert into tbl_GE_Hdr_Cancellation select * from tbl_GE_HDR where GE_HDR_ID  ='" & Trim(txtTransactionNo.Text) & "'"
                    Dim str2 As String = "insert into tbl_GE_Det_Cancellation select * from tbl_GE_Det where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'"
                    Dim str3 As String = "Update tbl_GE_Det_Cancellation set F_WT = 0,S_WT = 0,NET_WT = 0, F_WT_DoneBy = '',S_WT_DoneBy = '',F_WT_DateTime = '',S_WT_DateTime='' where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'"
                    Dim str4 As String = "update tbl_GE_HDR set Vehicle_Status = 'CANCEL',Remarks_cancellation = '" & Trim(txtReasonforCancellation.Text) & "' ,OUT_DateTime = getdate(),OUT_DoneBy = '" & User_ID & "' where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'"
                    Try
                        cc.Execute(str1)
                        cc.Execute(str2)
                        cc.Execute(str3)
                        cc.Execute(str4)
                        MsgBox("Vehicle cancelled successfully !", vbInformation, "ElectroWay")
                        txtReasonforCancellation.Text = ""
                        txtVehicleNo.Text = ""
                        txtTransactionNo.Text = ""
                    Catch ex As Exception

                    End Try
                Else
                    Try
                        cm.Connection = con
                        cm.CommandType = CommandType.StoredProcedure
                        cm.CommandText = "sp_upd_tbl_GE_HDR_Cancellation"
                        cm.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(txtTransactionNo.Text) & "")
                        cm.Parameters.AddWithValue("@val_Cancellation_Reason", Trim(txtReasonforCancellation.Text) & "")
                        cm.Parameters.AddWithValue("@val_User_ID", User_ID)
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        cm.ExecuteNonQuery()

                        MsgBox("Vehicle cancelled successfully !", vbInformation, "ElectroWay")

                        txtReasonforCancellation.Text = ""
                        txtVehicleNo.Text = ""
                        txtTransactionNo.Text = ""
                    Catch ex As Exception
                        MessageBox.Show(ex.Message)
                    End Try
                End If
            End If
        Else
            MsgBox("Invalid Transaction No. ....", vbInformation, "ElectroWay")
        End If
    End Sub

    Private Sub txtTransactionNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransactionNo.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "CANCEL_VEHICLE_ENTRY"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Owner = Me
            frmHelp1.gvHelp.DataSource = Nothing
            frmHelp1.ShowDialog()
        End If
    End Sub

    Private Sub txtTransactionNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo.KeyPress

        On Error GoTo err

        If AscW(e.KeyChar) = 13 Then
            txtVehicleNo.Text = ""
            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' and Vehicle_Status  = 'IN' and Company_Code = '" & Trim(txtCompany.Text) & "'") ''' and Plant_Code  = '" & Trim(Text5.Text) & "' "
            If dr.Read Then
                txtVehicleNo.Text = dr("Vehicle_No")
            Else
                MsgBox("Invalid Transaction No....", vbInformation, "ElectroWay")
                txtTransactionNo.Text = ""
                txtTransactionNo.Focus()

            End If
            dr.Close()

        End If
err:
        ''MsgBox err.Number
        If Err.Description <> "" Then
            MsgBox(Err.Number & "  " & Err.Description)
        End If
    End Sub

    Private Sub txtTransactionNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTransactionNo.TextChanged

    End Sub

    Private Sub btnOut_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOut.Click
        If Trim(txtTransactionNo1.Text) <> "" And Trim(txtVehicleNo1.Text) <> "" Then
            Dim ans = MsgBox("Are you sure, you want to OUT this vehicle ?", vbYesNo, "ElectroWay")
            If ans = vbYes Then
                If Check_DO(txtTransactionNo1.Text.Trim) = True Then
                    MessageBox.Show("DO has not been cancelled !!")
                    Exit Sub
                End If
                Try
                    'Dim str As String = "update tbl_GE_Hdr_Cancellation set Vehicle_Status = 'OUT',OUT_DateTime = getdate() where GE_HDR_ID = '" & txtTransactionNo1.Text.Trim & "'"
                    'cc.Execute(str)
                    MessageBox.Show("Successfully Updated")
                Catch ex As Exception
                    MessageBox.Show(ex.Message)
                End Try

            End If
        End If
    End Sub
    Private Sub SAP_Con2()
        functionCtr2 = CreateObject("SAP.Functions")
        sapConnection = functionCtr2.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
    End Sub
    Private Function Check_DO(ByVal Trn_No As String) As Boolean
        Dim DOF As Boolean = False
        Dim DO_NO As String = ""
        Dim str As String = "select DO_No from tbl_GE_Det where GE_HDR_ID = '" & Trn_No & "'"
        dr = cc.GetDataReader(str)
        Try
            While dr.Read
                DO_NO = dr(0).ToString
            End While
        Catch ex As Exception

        End Try
        dr.Close()

        If DO_NO = "" Then
            DOF = False
            Return DOF
            Exit Function
        End If


        Dim ShipToParty, SoldToParty As String

        Call SAP_Con2()

        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            Exit Function
        End If

        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
        objQueryTab1.Value = "LIKP"
        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
        objFldTab1 = objRfcFunc1.Tables("FIELDS")
        objDatTab1 = objRfcFunc1.Tables("DATA")
        objOptTab1.FreeTable()
        objOptTab1.Rows.Add()
        objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & DO_No & "'"

        objFldTab1.FreeTable()

        objFldTab1.Rows.Add()
        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
        objFldTab1.Rows.Add()
        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNAG"   ''''
        objFldTab1.Rows.Add()
        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNNR"   ''''KUNNR

        If objRfcFunc1.Call = False Then
            MsgBox(objRfcFunc1.Exception)
        End If

        iii = 0
        If objDatTab1.Rows.Count = 0 Then
            DOF = False
            'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
        Else
            For Each objDatRec1 In objDatTab1.Rows
                For Each objFldRec1 In objFldTab1.Rows
                    'iii = iii + 1
                    'If iii = 2 Then
                    '    SoldToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                    'End If
                    'If iii = 3 Then
                    '    ShipToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                    'End If
                    DOF = True
                Next
            Next
        End If
        Return DOF
    End Function
    Private Sub txtTransactionNo1_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo1.KeyPress
        On Error GoTo err

        If AscW(e.KeyChar) = 13 Then
            txtVehicleNo.Text = ""
            dr = cc.GetDataReader("select * from tbl_GE_Hdr_Cancellation where GE_HDR_ID = '" & Trim(txtTransactionNo1.Text) & "' and Company_Code = '" & Trim(txtCompany1.Text) & "'")
            If dr.Read Then
                txtVehicleNo1.Text = dr("Vehicle_No")
            Else
                MsgBox("Invalid Transaction No....", vbInformation, "ElectroWay")
                txtTransactionNo.Text = ""
                txtTransactionNo.Focus()

            End If
            dr.Close()

        End If
err:
        ''MsgBox err.Number
        If Err.Description <> "" Then
            MsgBox(Err.Number & "  " & Err.Description)
        End If
    End Sub

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        lblMessage.Text = "Please Wait...."
        lblMessage.Refresh()
        Dim excel1 As Microsoft.Office.Interop.Excel.Application
        Dim wb As Microsoft.Office.Interop.Excel.Workbook

        Dim xlsheet As Excel.Worksheet

        'Dim xlwbook As Excel.Workbook

        excel1 = New Microsoft.Office.Interop.Excel.Application
        wb = excel1.Workbooks.Open(ApplicationPath & "\DetailsReport.xls", True, True, , "jnan")
        excel1.Visible = True
        wb.Activate()
        Try
            Dim ds As DataSet = cc.GetDataset("select top 1 GE_HDR_ID,Vehicle_No,Type_Of_Vehicle,Driver_name,Driver_LIC_no,TransporterName,Vehicle_Status,Remarks_cancellation,Entry_DoneBy,EntryDateTime,OUT_DateTime,OUT_DoneBy from tbl_GE_Hdr_Cancellation where GE_HDR_ID = '" & txtTransactionNo1.Text.Trim & "'")

            Dim row, col As Integer
            row = 2

            xlsheet = wb.Sheets.Item(1)
            For i As Integer = 0 To ds.Tables(0).Columns.Count - 1
                xlsheet.Cells(1, i + 1) = ds.Tables(0).Columns(i).ColumnName
                '.Cells(1, i) = ListView1.ColumnHeaders(i).Text

            Next i

            For j As Integer = 0 To ds.Tables(0).Rows.Count - 1
                xlsheet.Cells(row + j, 1) = ds.Tables(0).Rows(j).Item(0).ToString
                xlsheet.Cells(row + j, 2) = ds.Tables(0).Rows(j).Item(1).ToString
                xlsheet.Cells(row + j, 3) = ds.Tables(0).Rows(j).Item(2).ToString
                xlsheet.Cells(row + j, 4) = ds.Tables(0).Rows(j).Item(3).ToString
                xlsheet.Cells(row + j, 5) = ds.Tables(0).Rows(j).Item(4).ToString
                xlsheet.Cells(row + j, 6) = ds.Tables(0).Rows(j).Item(5).ToString
                xlsheet.Cells(row + j, 7) = ds.Tables(0).Rows(j).Item(6).ToString
                xlsheet.Cells(row + j, 8) = ds.Tables(0).Rows(j).Item(7).ToString
                xlsheet.Cells(row + j, 9) = ds.Tables(0).Rows(j).Item(8).ToString
                xlsheet.Cells(row + j, 10) = ds.Tables(0).Rows(j).Item(9).ToString
                xlsheet.Cells(row + j, 11) = ds.Tables(0).Rows(j).Item(10).ToString
                xlsheet.Cells(row + j, 12) = ds.Tables(0).Rows(j).Item(11).ToString
            Next
        Catch ex As Exception

        End Try
        Try
            Dim ds As DataSet = cc.GetDataset("select top 1 GE_HDR_ID,Type_Of_Vehicle,DO_No,DO_Line_Item,SO_No,SO_Line_Item,Mat_Code,Mat_Desc,DO_Challan_Qty,Customer_Name,F_WT,F_WT_DateTime,F_WT_DoneBy,S_WT,NET_WT from tbl_GE_Det_Cancellation where GE_HDR_ID = '" & txtTransactionNo1.Text.Trim & "' and F_WT > 0 order by GE_DET_Tran_ID desc")

            Dim row, col As Integer
            row = 5

            xlsheet = wb.Sheets.Item(1)
            For i As Integer = 0 To ds.Tables(0).Columns.Count - 1
                xlsheet.Cells(4, i + 1) = ds.Tables(0).Columns(i).ColumnName
                '.Cells(1, i) = ListView1.ColumnHeaders(i).Text

            Next i

            For j As Integer = 0 To ds.Tables(0).Rows.Count - 1
                xlsheet.Cells(row + j, 1) = ds.Tables(0).Rows(j).Item(0).ToString
                xlsheet.Cells(row + j, 2) = ds.Tables(0).Rows(j).Item(1).ToString
                xlsheet.Cells(row + j, 3) = ds.Tables(0).Rows(j).Item(2).ToString
                xlsheet.Cells(row + j, 4) = ds.Tables(0).Rows(j).Item(3).ToString
                xlsheet.Cells(row + j, 5) = ds.Tables(0).Rows(j).Item(4).ToString
                xlsheet.Cells(row + j, 6) = ds.Tables(0).Rows(j).Item(5).ToString
                xlsheet.Cells(row + j, 7) = ds.Tables(0).Rows(j).Item(6).ToString
                xlsheet.Cells(row + j, 8) = ds.Tables(0).Rows(j).Item(7).ToString
                xlsheet.Cells(row + j, 9) = ds.Tables(0).Rows(j).Item(8).ToString
                xlsheet.Cells(row + j, 10) = ds.Tables(0).Rows(j).Item(9).ToString
                xlsheet.Cells(row + j, 11) = ds.Tables(0).Rows(j).Item(10).ToString
                xlsheet.Cells(row + j, 12) = ds.Tables(0).Rows(j).Item(11).ToString
                xlsheet.Cells(row + j, 13) = ds.Tables(0).Rows(j).Item(12).ToString
                xlsheet.Cells(row + j, 14) = ds.Tables(0).Rows(j).Item(13).ToString
                xlsheet.Cells(row + j, 15) = ds.Tables(0).Rows(j).Item(14).ToString
            Next
        Catch ex As Exception

        End Try

        lblMessage.Text = ""
        lblMessage.Refresh()
    End Sub
End Class