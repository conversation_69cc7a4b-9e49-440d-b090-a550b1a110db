﻿Imports System.ComponentModel
Public Class frmChangeGroupRef
    Dim cc As New Class1
    Private Sub frmChangeGroupRef_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Disabled = '0' order by Reference_Code")
        While dr.Read = True
            ddlToGroupRefCode.Items.Add(dr("Reference_Code"))
        End While

        dr.Close()

        Dim str As String = "select * from TempTransaction"
        ds = cc.GetDataset(str)
        datagrid.DataSource = ds.Tables(0)
    End Sub
    Private Sub ddlToGroupRefCode_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlToGroupRefCode.SelectedIndexChanged
        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlToGroupRefCode.Text) & "'")
        If dr.Read = True Then
            txtToGroupRefName.Text = dr("Reference_Name")
            ''Text28.Text = dr("Company_Code")
        End If
        dr.Close()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        'ESL\ES01\2016\392169
        If txtTransactionNo.Text.Trim = "" Then
            MessageBox.Show("Please Enter the Transaction No.!")
            Exit Sub
        End If
        Dim Flag1 As Boolean = False
        Dim InsertFlag As Boolean = False
        If Trim(txtTransactionNo.Text) <> "" And Trim(txtVehicleNo.Text) <> "" Then
            dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "' and Grouping_Id  = 0")
            If dr.Read = True Then
                InsertFlag = True
                'Text1.Text = ""
            Else
                MsgBox("Grouping Already done !, you cannot change the Grouping Ref....", vbInformation, "ElectroWay")
            End If
            dr.Close()
        Else
            MsgBox("Please input Transaction No which you want to change the Reference code !", vbInformation, "ElectroWay")
        End If
        If InsertFlag = True Then
            Try
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_upd_tbl_GE_DET_Grp_Ref_code"
                cm.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(txtTransactionNo.Text) & "")
                cm.Parameters.AddWithValue("@val_Grp_Ref_Code", Trim(ddlToGroupRefCode.Text) & "")
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.ExecuteNonQuery()
                Flag1 = True
            Catch ex As Exception

            End Try
        End If
        If Flag1 = True Then
            Dim PO_NO, PO_Line_Item, Mat_Code, Mat_Desc, Vendor_Code, Vendor_Name As String
            dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlToGroupRefCode.Text) & "'")
            If dr.Read Then
                'If dr("Auto_Update") = 1 Then
                PO_NO = dr("PO_No").ToString
                PO_Line_Item = dr("PO_Line_Item").ToString
                Mat_Code = dr("Mat_Code").ToString
                Mat_Desc = dr("Mat_Desc").ToString
                Vendor_Code = dr("Vendor_Code").ToString
                Vendor_Name = dr("Vendor_Name").ToString
            End If
            dr.Close()
            '-------------------
            cm.Connection = con
            cm.CommandType = CommandType.Text
            cm.CommandText = "update tbl_GE_DET set PO_No = '" & PO_NO & "' , PO_Line_Item = '" & PO_Line_Item & "' , Mat_Code  ='" & Mat_Code & "' , Mat_Desc = '" & Mat_Desc & "'  , Vendor_Code = '" & Vendor_Code & "' , Vendor_Name  = '" & Vendor_Name & "'  where GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "'"
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.ExecuteNonQuery()
            '-----------------------
            MsgBox("Grouping Reference has been changed successfully !", vbInformation, "ElectroWay")
            txtTransactionNo.Text = ""
            txtVehicleNo.Text = ""
            txtFromVehicleRefCode.Text = ""
        End If
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub txtTransactionNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransactionNo.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "CHANGEGRREF"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Owner = Me
            frmHelp1.gvHelp.DataSource = Nothing
            frmHelp1.ShowDialog()
        End If
    End Sub

    Private Sub txtTransactionNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo.KeyPress
        On Error GoTo err

        If AscW(e.KeyChar) = 13 Then
            txtVehicleNo.Text = ""
            txtFromVehicleRefCode.Text = ""
            dr = cc.GetDataReader("select a.* , b.Grouping_Ref_Code from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and  a.GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "'")
            If dr.Read = True Then
                txtVehicleNo.Text = dr("Vehicle_No")
                txtFromVehicleRefCode.Text = dr("Grouping_Ref_Code")

            Else
                MsgBox("Invalid Transaction No ....", vbInformation, "ElectroWay")
                txtTransactionNo.Text = ""
                txtTransactionNo.Focus()

            End If
            dr.Close()

        End If
err:
        ''MsgBox err.Number
        If Err.Description <> "" Then
            MsgBox(Err.Number & "  " & Err.Description)
        End If

    End Sub

    Private Sub txtTransactionNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTransactionNo.TextChanged

    End Sub

    Private Sub btnS_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

    End Sub
    Private Sub PasteUnboundRecords()

        Try
            Dim rowLines As String() = Clipboard.GetText(TextDataFormat.Text).Split(New String(0) {vbCr & vbLf}, StringSplitOptions.None)
            Dim currentRowIndex As Integer = (If(datagrid.CurrentRow IsNot Nothing, datagrid.CurrentRow.Index, 0))
            Dim currentColumnIndex As Integer = (If(datagrid.CurrentCell IsNot Nothing, datagrid.CurrentCell.ColumnIndex, 0))
            Dim currentColumnCount As Integer = datagrid.Columns.Count

            datagrid.AllowUserToAddRows = False
            For rowLine As Integer = 0 To rowLines.Length - 1

                If rowLine = rowLines.Length - 1 AndAlso String.IsNullOrEmpty(rowLines(rowLine)) Then
                    Exit For
                End If

                Dim columnsData As String() = rowLines(rowLine).Split(New String(0) {vbTab}, StringSplitOptions.None)
                If (currentColumnIndex + columnsData.Length) > datagrid.Columns.Count Then
                    For columnCreationCounter As Integer = 0 To ((currentColumnIndex + columnsData.Length) - currentColumnCount) - 1
                        If columnCreationCounter = rowLines.Length - 1 Then
                            Exit For
                        End If
                    Next
                End If
                If datagrid.Rows.Count > (currentRowIndex + rowLine) Then
                    For columnsDataIndex As Integer = 0 To columnsData.Length - 1
                        If currentColumnIndex + columnsDataIndex <= datagrid.Columns.Count - 1 Then
                            datagrid.Rows(currentRowIndex + rowLine).Cells(currentColumnIndex + columnsDataIndex).Value = columnsData(columnsDataIndex)
                        End If
                    Next
                Else
                    Dim pasteCells As String() = New String(datagrid.Columns.Count - 1) {}
                    For cellStartCounter As Integer = currentColumnIndex To datagrid.Columns.Count - 1
                        If columnsData.Length > (cellStartCounter - currentColumnIndex) Then
                            pasteCells(cellStartCounter) = columnsData(cellStartCounter - currentColumnIndex)
                        End If
                    Next
                End If
            Next

        Catch ex As Exception
            'Log Exception
        End Try

    End Sub

    Private Sub datagrid_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles datagrid.CellContentClick

    End Sub

    Private Sub datagrid_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles datagrid.KeyDown
        Try
            If e.Control And (e.KeyCode = Keys.C) Then
                Dim d As DataObject = datagrid.GetClipboardContent()
                Clipboard.SetDataObject(d)
                e.Handled = True
            ElseIf (e.Control And e.KeyCode = Keys.V) Then
                PasteUnboundRecords()
            End If
        Catch ex As Exception
            'Log Exception
        End Try

    End Sub

    Private Sub PasteToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PasteToolStripMenuItem.Click
        PasteUnboundRecords()
    End Sub

    Private Sub btnUpdateAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdateAll.Click
        Dim ans = MsgBox("Are you sure you want to Update?", vbYesNo, "ElectroWay")
        If ans = vbNo Then
            Exit Sub
        End If

        dt = datagrid.DataSource

        Dim UpdatedItem As Integer = 0
       
        For Each row As DataRow In dt.Rows
            If IsDBNull(row.Item(1)) = False Then
                If row.Item(1).ToString.Trim <> "" Then
                    Dim Flag1 As Boolean = False
                    Dim InsertFlag As Boolean = False
                    txtTransactionNo.Text = row.Item(1).ToString
                    '-----------------
                    dr = cc.GetDataReader("select a.* , b.Grouping_Ref_Code from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and  a.GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "'")
                    Try
                        If dr.Read = True Then
                            txtVehicleNo.Text = dr("Vehicle_No")
                            txtFromVehicleRefCode.Text = dr("Grouping_Ref_Code")
                        End If
                    Catch ex As Exception

                    End Try
                    dr.Close()
                    '------------------
                    If Trim(txtTransactionNo.Text) <> "" And Trim(txtVehicleNo.Text) <> "" Then
                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "' and Grouping_Id  = 0")
                        If dr.Read = True Then
                            InsertFlag = True
                            'Text1.Text = ""
                        Else
                            'MsgBox("Grouping Already done !, you cannot change the Grouping Ref....", vbInformation, "ElectroWay")
                        End If
                        dr.Close()
                    Else
                        'MsgBox("Please input Transaction No which you want to change the Reference code !", vbInformation, "ElectroWay")
                    End If
                    If InsertFlag = True Then
                        Try
                            cm.Connection = con
                            cm.CommandType = CommandType.StoredProcedure
                            cm.CommandText = "sp_upd_tbl_GE_DET_Grp_Ref_code"
                            cm.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(txtTransactionNo.Text) & "")
                            cm.Parameters.AddWithValue("@val_Grp_Ref_Code", Trim(ddlToGroupRefCode.Text) & "")
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            cm.ExecuteNonQuery()
                            Flag1 = True
                        Catch ex As Exception

                        End Try
                    End If
                    If Flag1 = True Then
                        Dim PO_NO, PO_Line_Item, Mat_Code, Mat_Desc, Vendor_Code, Vendor_Name As String
                        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlToGroupRefCode.Text) & "'")
                        Try
                            If dr.Read Then
                                'If dr("Auto_Update") = 1 Then
                                PO_NO = dr("PO_No").ToString
                                PO_Line_Item = dr("PO_Line_Item").ToString
                                Mat_Code = dr("Mat_Code").ToString
                                Mat_Desc = dr("Mat_Desc").ToString
                                Vendor_Code = dr("Vendor_Code").ToString
                                Vendor_Name = dr("Vendor_Name").ToString
                            End If
                        Catch ex As Exception

                        End Try
                        dr.Close()
                        '-------------------
                        Try
                            cm.Connection = con
                            cm.CommandType = CommandType.Text
                            cm.CommandText = "update tbl_GE_DET set PO_No = '" & PO_NO & "' , PO_Line_Item = '" & PO_Line_Item & "' , Mat_Code  ='" & Mat_Code & "' , Mat_Desc = '" & Mat_Desc & "'  , Vendor_Code = '" & Vendor_Code & "' , Vendor_Name  = '" & Vendor_Name & "'  where GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "'"
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            cm.ExecuteNonQuery()
                            UpdatedItem = UpdatedItem + 1
                        Catch ex As Exception

                        End Try
                        '-----------------------
                        'MsgBox("Grouping Reference has been changed successfully !", vbInformation, "ElectroWay")
                        txtTransactionNo.Text = ""
                        txtVehicleNo.Text = ""
                        txtFromVehicleRefCode.Text = ""
                    End If
                End If
            End If
        Next row
        MsgBox("Grouping Reference has been changed successfully for " & UpdatedItem & " Items!", vbInformation, "ElectroWay")
    End Sub
End Class