﻿Public Class frmCompanymaster
    Dim cc As New Class1
    Private Sub frmCompanymaster_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_Company_mst"
            ds = cc.GetDataset(str)
            gvCompany.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtCompanyCode.Text) <> "" And Trim(txtCompanyName.Text) <> "" Then
            ds = cc.GetDataset("select * from tbl_Company_mst where company_code = '" & Trim(txtCompanyCode.Text) & "'")
            If ds.Tables(0).Rows.Count = 0 Then
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_Company_mst"
                cm.Parameters.Clear()
                cm.Parameters.AddWithValue("@val_Compnay_Code", txtCompanyCode.Text.Trim)
                cm.Parameters.AddWithValue("@val_Company_Name", txtCompanyName.Text.Trim)
                cm.Parameters.AddWithValue("@val_Compnay_Address", txtAddress.Text.Trim)

                cm.ExecuteNonQuery()
                loadGrid()
                MsgBox("Company created successfully !", vbInformation, "ElectroWay")

                txtCompanyCode.Text = ""
                txtCompanyName.Text = ""
                txtAddress.Text = ""
            Else
                MsgBox("Company already exists !", vbInformation, "ElectroWay")
                txtCompanyCode.Text = ""
                txtCompanyName.Text = ""
                txtAddress.Text = ""
                txtCompanyCode.Focus()
            End If
            ds.Dispose()
            ds.Clear()
        Else
            MsgBox("Blank Company Code & Name Not allowed .", vbInformation, "ElectroWay")
        End If
        loadGrid()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtCompanyCode.Text = ""
        txtCompanyName.Text = ""
        txtAddress.Text = ""
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub gvCompany_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvCompany.CellMouseClick
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvCompany.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvCompany.SelectedRows(i).Index)

                    txtCompanyCode.Text = Convert.ToString(gvCompany.Rows(index).Cells(1).Value)
                    txtCompanyName.Text = Convert.ToString(gvCompany.Rows(index).Cells(2).Value)
                    txtAddress.Text = Convert.ToString(gvCompany.Rows(index).Cells(3).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
End Class