﻿Public Class frmCustomer
    Dim cc As New Class1
    Private Sub frmCustomer_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_Customer_Mst"
            ds = cc.GetDataset(str)
            gvCustomer.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtCustomerCode.Text) <> "" And Trim(txtCustomerName.Text) <> "" Then
            ds = cc.GetDataset("select * from tbl_Customer_Mst where Customer_Code = '" & Trim(txtCustomerCode.Text) & "'")
            If ds.Tables(0).Rows.Count = 0 Then
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_Customer_Mst"
                cm.Parameters.AddWithValue("@val_Customer_Code", Trim(txtCustomerCode.Text))
                cm.Parameters.AddWithValue("@val_Customer_Name", Trim(txtCustomerName.Text))
                cm.Parameters.AddWithValue("@val_Customer_Address", Trim(txtAddress.Text))
                cm.Parameters.AddWithValue("@val_NON_SAP_Item", "1")
                cm.ExecuteNonQuery()
                loadGrid()
                MsgBox("Customer Master updated successfully !", vbInformation, "ElectroWay")
                txtCustomerCode.Text = ""
                txtCustomerName.Text = ""
                txtAddress.Text = ""
            Else
                Dim ans = MsgBox("Customer Already Exists ! Do you want to Update..", vbYesNo, "ElectroWay")
                If ans = vbYes Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_Customer_Mst"
                    cm.Parameters.AddWithValue("@val_Customer_Code", Trim(txtCustomerCode.Text))
                    cm.Parameters.AddWithValue("@val_Customer_Name", Trim(txtCustomerName.Text))
                    cm.Parameters.AddWithValue("@val_Customer_Address", Trim(txtAddress.Text))
                    cm.Parameters.AddWithValue("@val_NON_SAP_Item", "1")
                    cm.ExecuteNonQuery()
                    loadGrid()
                    MsgBox("Customer Master updated successfully !", vbInformation, "ElectroWay")
                    txtCustomerCode.Text = ""
                    txtCustomerName.Text = ""
                    txtAddress.Text = ""
                End If
            End If
            ds.Dispose()

        Else
            MsgBox("Blank Customer record cannot be updated !", vbInformation, "ElectroWay")

        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtCustomerCode.Text = ""
        txtCustomerName.Text = ""
        txtAddress.Text = ""
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub txtCustomerCode_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtCustomerCode.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "CUSTOMER"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub txtCustomerCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtCustomerCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtCustomerName.Focus()
            dr = cc.GetDataReader("select * from tbl_Customer_Mst where Customer_Code = '" & Trim(txtCustomerCode.Text) & "'")
            Try
                While dr.Read
                    txtCustomerName.Text = dr("Customer_Name")
                    txtAddress.Text = dr("Customer_Address")
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Try
            Dim str As String = "SELECT * FROM tbl_Customer_Mst where Customer_code  like '%" & txtSearch.Text & "%' or Customer_Name like '%" & txtSearch.Text & "%'"
            ds = cc.GetDataset(str)
            gvCustomer.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub

    Private Sub gvCustomer_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvCustomer.CellMouseClick
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvCustomer.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvCustomer.SelectedRows(i).Index)

                    txtCustomerCode.Text = Convert.ToString(gvCustomer.Rows(index).Cells(1).Value)
                    txtCustomerName.Text = Convert.ToString(gvCustomer.Rows(index).Cells(2).Value)
                    txtAddress.Text = Convert.ToString(gvCustomer.Rows(index).Cells(3).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
End Class