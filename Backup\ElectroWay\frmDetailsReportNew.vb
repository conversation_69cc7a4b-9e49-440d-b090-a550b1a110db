﻿Imports Excel = Microsoft.Office.Interop.Excel
Imports Microsoft.Office.Interop.Excel
Imports System.Data
Imports System.Data.SqlClient
Imports Microsoft.Office.Interop

Imports System.Runtime.InteropServices
Imports Microsoft.Office
Imports System.Globalization
Imports System.IO
Imports System.Drawing
Public Class frmDetailsReportNew
    Dim tot_veh As Integer
    Dim IN_veh As Integer
    Dim OUT_veh As Integer
    Dim CANC_veh As Integer

    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    Dim i As Double


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object
    Dim m As Integer
    Dim TRN_ID As Double
    Dim SAP_CON_NOT_AVAIL As Integer

    Dim cc As New Class1
    Dim strWB As String

    Private Sub frmDetailsReportNew_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            dtFrom.Value = Today.Date
            dtTo.Value = Today.Date
            ddlTypeOfVehicle.Items.Add("-")
            dr = cc.GetDataReader("select * from  tbl_User_Mst where User_ID  = '" & User_ID & "'")
            Try
                If dr.Read Then
                    If dr("Per_INBOUND_Report") = "1" Then
                        ddlTypeOfVehicle.Items.Add("PURCH")
                        ddlTypeOfVehicle.Items.Add("GATEPASS")
                        ddlTypeOfVehicle.Items.Add("CONTITEM")
                    End If
                End If
            Catch ex As Exception

            End Try

            dr.Close()

            dr = cc.GetDataReader("select * from  tbl_User_Mst where User_ID  = '" & User_ID & "'")
            Try
                While dr.Read
                    If dr("Per_OUTBOUND_Report") = "1" Then
                        ddlTypeOfVehicle.Items.Add("SALES")
                        ddlTypeOfVehicle.Items.Add("STKTROUT")
                    End If
                End While
            Catch ex As Exception

            End Try

            dr.Close()


            '''''
            '''''        PURCH
            '''''        GATEPASS
            '''''        SALES
            '''''        STKTROUT
            '''''        CONTITEM
            dr = cc.GetDataReader("select distinct Transpoter_Code from tbl_GE_HDR order by Transpoter_Code")
            While dr.Read
                ddlTransporterCode.Items.Add(dr("Transpoter_Code"))
            End While
            dr.Close()

            dr = cc.GetDataReader("select distinct TransporterName from tbl_GE_HDR order by TransporterName")
            While dr.Read
                ddlTransporterName.Items.Add(dr("TransporterName"))
            End While
            dr.Close()
            ddlTransporterCode.Items.Add("")
            ddlTypeOfVehicle.SelectedIndex = 0
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Hide()
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        btnExport.Visible = True
        btnExportSales.Visible = False
        lblWait.Text = "Please Wait .."
        lblWait.Refresh()

        Dim dtFrom1 As Date = dtFrom.Text
        Dim dtTo1 As Date = dtTo.Text
        Dim TotalVehicle As Integer = 0
        If ddlVehicleStatus.Text.Trim = "" And (ddlTypeOfVehicle.Text.Trim = "" Or ddlTypeOfVehicle.Text.Trim = "-") And ddlTransporterCode.Text.Trim = "" Then
            Dim str As String = "select T1.*,"
            str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
            str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
            str = str & " from "
            str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
            str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
            str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
            str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
            str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
            str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
            str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
            str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
            str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
            str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
            str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
            str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
            str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
            str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%')T1"
            str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID"
            Try
                ds = cc.GetDataset(str)
                gvReport.DataSource = ds.Tables(0)
                TotalVehicle = ds.Tables(0).Rows.Count
                txtTotWt.Text = TotalVehicle
                'txtVehicleIN.Text = (SearchData("select count(Vehicle_Status) from tbl_GE_HDR  where EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'IN' "))
                txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN'"))
                txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT'"))
                txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'C'"))
                txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' "))
            Catch ex As Exception

            End Try

            lblWait.Text = ""
            lblWait.Refresh()
            Exit Sub
        End If
        If ddlVehicleStatus.Text.Trim = "" And (ddlTypeOfVehicle.Text.Trim <> "" And ddlTypeOfVehicle.Text.Trim <> "-") And ddlTransporterCode.Text.Trim = "" Then
            Try
                Dim str As String = "select T1.*,"
                str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
                str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
                str = str & " from "
                str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
                str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
                str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
                str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
                str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
                str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
                str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
                str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
                str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
                str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
                str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
                str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
                str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
                str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = '" & ddlTypeOfVehicle.Text.Trim & "')T1"
                str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID "
                ds = cc.GetDataset(str)
                gvReport.DataSource = ds.Tables(0)
                TotalVehicle = ds.Tables(0).Rows.Count
                txtTotWt.Text = TotalVehicle
                txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' "))
                txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' "))
                txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' "))
                txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "'"))
            Catch ex As Exception

            End Try

            lblWait.Text = ""
            lblWait.Refresh()
            Exit Sub
        End If
        If ddlVehicleStatus.Text.Trim = "" And (ddlTypeOfVehicle.Text.Trim <> "" And ddlTypeOfVehicle.Text.Trim <> "-") And ddlTransporterCode.Text.Trim <> "" Then
            Try
                Try
                    Dim str As String = "select T1.*,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
                    str = str & " from "
                    str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
                    str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
                    str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
                    str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
                    str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
                    str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
                    str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
                    str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
                    str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
                    str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
                    str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = '" & ddlTypeOfVehicle.Text.Trim & "' and a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "')T1"
                    str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID "
                    ds = cc.GetDataset(str)
                    gvReport.DataSource = ds.Tables(0)
                    TotalVehicle = ds.Tables(0).Rows.Count
                    txtTotWt.Text = TotalVehicle
                    txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                Catch ex As Exception

                End Try
            Catch ex As Exception

            End Try

            lblWait.Text = ""
            lblWait.Refresh()
            Exit Sub
        End If
        If ddlVehicleStatus.Text.Trim <> "" And (ddlTypeOfVehicle.Text.Trim <> "-") And ddlTransporterCode.Text.Trim = "" Then
            Try
                Try

                    Dim str As String = "select T1.*,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
                    str = str & " from "
                    str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
                    str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
                    str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
                    str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
                    str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
                    str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
                    str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
                    str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
                    str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
                    str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
                    str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "' and a.Type_Of_Vehicle = '" & ddlTypeOfVehicle.Text.Trim & "')T1"
                    str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID"
                    ds = cc.GetDataset(str)
                    gvReport.DataSource = ds.Tables(0)
                    TotalVehicle = ds.Tables(0).Rows.Count
                    txtTotWt.Text = TotalVehicle
                    txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and   a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "'"))
                    txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and   a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "'"))
                    txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and   a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "'"))
                    txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "'"))
                Catch ex As Exception

                End Try
            Catch ex As Exception

            End Try

            lblWait.Text = ""
            lblWait.Refresh()
            Exit Sub
        End If

        If ddlVehicleStatus.Text.Trim = "" And (ddlTypeOfVehicle.Text.Trim = "-") And ddlTransporterCode.Text.Trim <> "" Then
            Try
                Try
                    Dim str As String = "select T1.*,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
                    str = str & " from "
                    str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
                    str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
                    str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
                    str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
                    str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
                    str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
                    str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
                    str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
                    str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
                    str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
                    str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "')T1"
                    str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID"
                    ds = cc.GetDataset(str)
                    gvReport.DataSource = ds.Tables(0)
                    TotalVehicle = ds.Tables(0).Rows.Count
                    txtTotWt.Text = TotalVehicle
                    txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                Catch ex As Exception

                End Try
            Catch ex As Exception

            End Try

            lblWait.Text = ""
            lblWait.Refresh()
            Exit Sub
        End If
        If ddlVehicleStatus.Text.Trim <> "" And (ddlTypeOfVehicle.Text.Trim <> "-") And ddlTransporterCode.Text.Trim <> "" Then
            Try
                Try

                    Dim str As String = "select T1.*,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
                    str = str & " from "
                    str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
                    str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
                    str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
                    str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
                    str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
                    str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
                    str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
                    str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
                    str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
                    str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
                    str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "' and a.Type_Of_Vehicle = '" & ddlTypeOfVehicle.Text.Trim & "' and a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "')T1"
                    str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID"
                    ds = cc.GetDataset(str)
                    gvReport.DataSource = ds.Tables(0)
                    TotalVehicle = ds.Tables(0).Rows.Count
                    txtTotWt.Text = TotalVehicle
                    txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                    txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = '" & Trim(ddlTypeOfVehicle.Text.Trim) & "' and a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "' and  a.Transpoter_Code = '" & ddlTransporterCode.Text.Trim & "'"))
                Catch ex As Exception

                End Try
            Catch ex As Exception

            End Try

            lblWait.Text = ""
            lblWait.Refresh()
            Exit Sub
        End If
        If ddlVehicleStatus.Text.Trim <> "" And (ddlTypeOfVehicle.Text.Trim = "-") And ddlTransporterCode.Text.Trim = "" Then
            Try
                Try
                    Dim str As String = "select T1.*,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
                    str = str & " from "
                    str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
                    str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
                    str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
                    str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
                    str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
                    str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
                    str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
                    str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
                    str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
                    str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
                    str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
                    str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "')T1"

                    str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID "

                    ds = cc.GetDataset(str)
                    gvReport.DataSource = ds.Tables(0)
                    TotalVehicle = ds.Tables(0).Rows.Count
                    txtTotWt.Text = TotalVehicle
                    txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' "))
                    txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' "))
                    txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' "))
                    txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = '" & ddlVehicleStatus.Text.Trim & "'"))
                Catch ex As Exception

                End Try
            Catch ex As Exception

            End Try

            lblWait.Text = ""
            lblWait.Refresh()
            Exit Sub
        End If
       
    End Sub
    Private Function SearchData(ByVal Str As String) As String
        Dim ValueName As String = ""
        dr = cc.GetDataReader(Str)
        Try
            While dr.Read
                If ValueName = "" Then
                    ValueName = dr(0).ToString
                Else
                    ValueName = ValueName & "," & dr(0).ToString
                End If

            End While
        Catch ex As Exception

        End Try
        dr.Close()
        Return ValueName
    End Function
    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        lblMessage.Text = "Please Wait...."
        lblMessage.Refresh()
        Dim excel1 As Microsoft.Office.Interop.Excel.Application
        Dim wb As Microsoft.Office.Interop.Excel.Workbook

        Dim xlsheet As Excel.Worksheet

        'Dim xlwbook As Excel.Workbook

        excel1 = New Microsoft.Office.Interop.Excel.Application
        wb = excel1.Workbooks.Open(ApplicationPath & "\DetailsReport.xls", True, True, , "jnan")
        excel1.Visible = True
        wb.Activate()
        Try
            Dim row, col As Integer
            row = 2

            xlsheet = wb.Sheets.Item(1)
            xlsheet.Cells(1, 65) = "InvoiceNo"
            xlsheet.Cells(1, 66) = "InvoiceDate"
            xlsheet.Cells(1, 67) = "InvoiceTime"
            For i As Integer = 0 To gvReport.Columns.Count - 1
                xlsheet.Cells(1, i + 1) = gvReport.Columns(i).HeaderText
                '.Cells(1, i) = ListView1.ColumnHeaders(i).Text

            Next i
            xlsheet.Cells(1, 68) = "Invoice_Out_TAT"
            xlsheet.Cells(1, 69) = "Invoice_SWT_TAT"
            xlsheet.Cells(1, 70) = "CheckPost_IN"
            xlsheet.Cells(1, 71) = "CheckPost_OUT"
            xlsheet.Cells(1, 72) = "CheckPost_TAT(HH:MM)"
            xlsheet.Cells(1, 73) = "SPART"

            For j As Integer = 0 To gvReport.Rows.Count - 1
                xlsheet.Cells(row + j, 1) = gvReport.Rows(j).Cells(0).Value
                xlsheet.Cells(row + j, 2) = gvReport.Rows(j).Cells(1).Value
                xlsheet.Cells(row + j, 3) = gvReport.Rows(j).Cells(2).Value
                xlsheet.Cells(row + j, 4) = gvReport.Rows(j).Cells(3).Value
                xlsheet.Cells(row + j, 5) = gvReport.Rows(j).Cells(4).Value
                xlsheet.Cells(row + j, 6) = gvReport.Rows(j).Cells(5).Value
                xlsheet.Cells(row + j, 7) = gvReport.Rows(j).Cells(6).Value
                xlsheet.Cells(row + j, 8) = gvReport.Rows(j).Cells(7).Value
                xlsheet.Cells(row + j, 9) = gvReport.Rows(j).Cells(8).Value
                xlsheet.Cells(row + j, 10) = gvReport.Rows(j).Cells(9).Value
                xlsheet.Cells(row + j, 11) = gvReport.Rows(j).Cells(10).Value
                xlsheet.Cells(row + j, 12) = gvReport.Rows(j).Cells(11).Value
                xlsheet.Cells(row + j, 13) = gvReport.Rows(j).Cells(12).Value
                xlsheet.Cells(row + j, 14) = gvReport.Rows(j).Cells(13).Value
                xlsheet.Cells(row + j, 15) = gvReport.Rows(j).Cells(14).Value
                xlsheet.Cells(row + j, 16) = gvReport.Rows(j).Cells(15).Value
                xlsheet.Cells(row + j, 17) = gvReport.Rows(j).Cells(16).Value
                xlsheet.Cells(row + j, 18) = gvReport.Rows(j).Cells(17).Value
                xlsheet.Cells(row + j, 19) = gvReport.Rows(j).Cells(18).Value
                xlsheet.Cells(row + j, 20) = gvReport.Rows(j).Cells(19).Value
                xlsheet.Cells(row + j, 21) = gvReport.Rows(j).Cells(20).Value
                xlsheet.Cells(row + j, 22) = gvReport.Rows(j).Cells(21).Value
                xlsheet.Cells(row + j, 23) = gvReport.Rows(j).Cells(22).Value
                xlsheet.Cells(row + j, 24) = gvReport.Rows(j).Cells(23).Value
                xlsheet.Cells(row + j, 25) = gvReport.Rows(j).Cells(24).Value
                xlsheet.Cells(row + j, 26) = gvReport.Rows(j).Cells(25).Value
                xlsheet.Cells(row + j, 27) = gvReport.Rows(j).Cells(26).Value
                xlsheet.Cells(row + j, 28) = gvReport.Rows(j).Cells(27).Value
                xlsheet.Cells(row + j, 29) = gvReport.Rows(j).Cells(28).Value
                xlsheet.Cells(row + j, 30) = gvReport.Rows(j).Cells(29).Value
                xlsheet.Cells(row + j, 31) = gvReport.Rows(j).Cells(30).Value
                xlsheet.Cells(row + j, 32) = gvReport.Rows(j).Cells(31).Value
                xlsheet.Cells(row + j, 33) = gvReport.Rows(j).Cells(32).Value
                Try
                    xlsheet.Cells(row + j, 34) = CDate(gvReport.Rows(j).Cells(33).Value)
                Catch ex As Exception

                End Try
                'xlsheet.Cells(row + j, 34) = gvReport.Rows(j).Cells(33).Value
                xlsheet.Cells(row + j, 35) = gvReport.Rows(j).Cells(34).Value
                xlsheet.Cells(row + j, 36) = gvReport.Rows(j).Cells(35).Value
                xlsheet.Cells(row + j, 37) = gvReport.Rows(j).Cells(36).Value
                xlsheet.Cells(row + j, 38) = gvReport.Rows(j).Cells(37).Value
                xlsheet.Cells(row + j, 39) = gvReport.Rows(j).Cells(38).Value
                xlsheet.Cells(row + j, 40) = gvReport.Rows(j).Cells(39).Value
                xlsheet.Cells(row + j, 41) = gvReport.Rows(j).Cells(40).Value
                xlsheet.Cells(row + j, 42) = gvReport.Rows(j).Cells(41).Value
                xlsheet.Cells(row + j, 43) = gvReport.Rows(j).Cells(42).Value
                xlsheet.Cells(row + j, 44) = gvReport.Rows(j).Cells(43).Value
                xlsheet.Cells(row + j, 45) = gvReport.Rows(j).Cells(44).Value
                xlsheet.Cells(row + j, 46) = gvReport.Rows(j).Cells(45).Value
                xlsheet.Cells(row + j, 47) = gvReport.Rows(j).Cells(46).Value
                xlsheet.Cells(row + j, 48) = gvReport.Rows(j).Cells(47).Value
                xlsheet.Cells(row + j, 49) = gvReport.Rows(j).Cells(48).Value
                xlsheet.Cells(row + j, 50) = gvReport.Rows(j).Cells(49).Value
                xlsheet.Cells(row + j, 51) = gvReport.Rows(j).Cells(50).Value
                xlsheet.Cells(row + j, 52) = gvReport.Rows(j).Cells(51).Value
                xlsheet.Cells(row + j, 53) = gvReport.Rows(j).Cells(52).Value
                xlsheet.Cells(row + j, 54) = gvReport.Rows(j).Cells(53).Value
                xlsheet.Cells(row + j, 55) = gvReport.Rows(j).Cells(54).Value
                xlsheet.Cells(row + j, 56) = gvReport.Rows(j).Cells(55).Value
                Try
                    xlsheet.Cells(row + j, 57) = CDate(gvReport.Rows(j).Cells(56).Value)
                    xlsheet.Cells(row + j, 58) = CDate(gvReport.Rows(j).Cells(57).Value)
                Catch ex As Exception

                End Try
                xlsheet.Cells(row + j, 59) = gvReport.Rows(j).Cells(58).Value
                xlsheet.Cells(row + j, 60) = gvReport.Rows(j).Cells(59).Value
                xlsheet.Cells(row + j, 61) = gvReport.Rows(j).Cells(60).Value
                xlsheet.Cells(row + j, 62) = gvReport.Rows(j).Cells(61).Value
                xlsheet.Cells(row + j, 63) = gvReport.Rows(j).Cells(62).Value
                xlsheet.Cells(row + j, 64) = gvReport.Rows(j).Cells(63).Value
                If gvReport.Rows(j).Cells(4).Value = "SALES" Then
                    xlsheet.Cells(row + j, 65) = SearchData("select InvoiceNo from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 66) = SearchData("select InvoiceDate from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 67) = SearchData("select InvoiceTime from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 68) = SearchData("select Invoice_Out_TAT from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 69) = SearchData("select Invoice_SWT_TAT  from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 73) = SearchData("select SPART  from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                End If
               
                xlsheet.Cells(row + j, 70) = SearchData("select IN_Date_Time from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID = '" & gvReport.Rows(j).Cells(1).Value & "'")
                xlsheet.Cells(row + j, 71) = SearchData("select OUT_Date_Time from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID = '" & gvReport.Rows(j).Cells(1).Value & "'")
                xlsheet.Cells(row + j, 72) = SearchData("select CASE WHEN (DATEDIFF(MINUTE, IN_Date_Time ,OUT_Date_Time)) > 0 THEN concat((DATEDIFF(MINUTE, IN_Date_Time, OUT_Date_Time)/60),':',(DATEDIFF(minute, IN_Date_Time, OUT_Date_Time)%60)) else '' end as   'TATDiff(HH:MM)'  from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID = '" & gvReport.Rows(j).Cells(1).Value & "'")
            Next
        Catch ex As Exception

        End Try
        lblMessage.Text = ""
        lblMessage.Refresh()
    End Sub

 
    Private Sub btnSalesShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSalesShow.Click
        If ddlItems.Text = "" Then
            MessageBox.Show("Please select the Items !!")
            Exit Sub
        End If

        btnExport.Visible = False
        btnExportSales.Visible = True
        lblWait.Text = "Please Wait .."
        lblWait.Refresh()
        gvReport.DataSource = Nothing
        Dim dtFrom1 As Date = dtSalesFrom.Text
        Dim dtTo1 As Date = dtSalesTo.Text
        Dim TotalVehicle As Integer = 0
        If txtSalesVehicleStatus.Text.Trim <> "" And (txtSalesTypeVehicle.Text.Trim) <> "-" And ddlItems.Text <> "FLYASH" Then
            Try
                Try
                    Try
                        'World:
                        Dim str As String = "select T1.*,"
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
                        str = str & " from "
                        str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
                        str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
                        str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
                        str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
                        str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
                        str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
                        str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
                        str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
                        str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
                        str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
                        str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = 'SALES' and a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "' and a.GE_HDR_ID in (select GE_HDR_ID from Electroway_Invoice_details where SPART =  '" & ddlItems.Text & "'))T1"

                        str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID "

                        ds = cc.GetDataset(str)
                        gvReport.DataSource = ds.Tables(0)
                        TotalVehicle = ds.Tables(0).Rows.Count
                        txtTotWt.Text = TotalVehicle
                        txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' and a.Type_Of_Vehicle = 'SALES' and a.GE_HDR_ID in (select GE_HDR_ID from Electroway_Invoice_details where SPART =  '" & ddlItems.Text & "')"))
                        txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' and a.Type_Of_Vehicle = 'SALES' and a.GE_HDR_ID in (select GE_HDR_ID from Electroway_Invoice_details where SPART =  '" & ddlItems.Text & "')"))
                        txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' and a.Type_Of_Vehicle = 'SALES' and a.GE_HDR_ID in (select GE_HDR_ID from Electroway_Invoice_details where SPART =  '" & ddlItems.Text & "')"))
                        txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "' and a.Type_Of_Vehicle = 'SALES' and a.GE_HDR_ID in (select GE_HDR_ID from Electroway_Invoice_details where SPART =  '" & ddlItems.Text & "')"))
                    Catch ex As Exception
                        'GoTo World
                        MessageBox.Show(ex.Message)
                    End Try
                Catch ex As Exception

                End Try

                lblWait.Text = ""
                lblWait.Refresh()
            Catch ex As Exception

            End Try
        End If
        'Type_Of_Vehicle = 'FLYASH'
        If txtSalesVehicleStatus.Text.Trim <> "" And (txtSalesTypeVehicle.Text.Trim <> "-") And ddlItems.Text = "FLYASH" Then
            Try
                Try
                    Try
                        Dim str As String = "select T1.*,"
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
                        str = str & " from "
                        str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
                        str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
                        str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
                        str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
                        str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
                        str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
                        str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
                        str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
                        str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
                        str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
                        str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
                        str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = 'FLYASH' and a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "')T1"

                        str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID "

                        ds = cc.GetDataset(str)
                        gvReport.DataSource = ds.Tables(0)
                        TotalVehicle = ds.Tables(0).Rows.Count
                        txtTotWt.Text = TotalVehicle
                        txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' and a.Type_Of_Vehicle = 'FLYASH' "))
                        txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' and a.Type_Of_Vehicle = 'FLYASH' "))
                        txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' and a.Type_Of_Vehicle = 'FLYASH' "))
                        txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "' and a.Type_Of_Vehicle = 'FLYASH' "))
                    Catch ex As Exception

                    End Try
                Catch ex As Exception

                End Try

                lblWait.Text = ""
                lblWait.Refresh()
            Catch ex As Exception

            End Try
        End If
        'If txtSalesVehicleStatus.Text.Trim <> "" And (txtSalesTypeVehicle.Text.Trim <> "-") Then
        '    Try
        '        Try

        '            Dim str As String = "select T1.*,"
        '            str = str & " CASE WHEN (DATEDIFF(MINUTE, T1.F_WT_DateTime ,T2.IN_Date_Time) ) > 0 THEN concat((DATEDIFF(MINUTE, T1.F_WT_DateTime, T2.IN_Date_Time)/60),':',(DATEDIFF(minute, T1.F_WT_DateTime, T2.IN_Date_Time)%60))  Else ''       END as   'TATDiff_FWT_CHK_IN(HH:MM)',"
        '            str = str & " CASE WHEN (DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, T2.OUT_Date_Time,T1.S_WT_DateTime)/60),':',(DATEDIFF(minute, T2.OUT_Date_Time,T1.S_WT_DateTime)%60))  Else ''       END as   'TATDiff_CHK_OUT_SWT(HH:MM)'"
        '            str = str & " from "
        '            str = str & " (select CONCAT(b.GE_DET_Tran_ID, '\ES01')as Tran_ID,a.GE_HDR_ID as TransactionNo, a.EntryDateTime as EntryDateTime, a.Vehicle_No as VehicleNo,"
        '            str = str & " a.Type_Of_Vehicle as VehicleType , a.Transpoter_Code as TrptrCode, a.TransporterName, b.PO_No ,  b.PO_Line_Item  , b.SO_No , b.SO_Line_Item ,"
        '            str = str & " b.DO_No , b.DO_Line_Item, b.Mat_Code , b.Mat_Desc, DO_Challan_Qty , b.UOM,b.Unloading_No , b.Challan_No , b.Challan_Date, b.CN_No as LR_NO,"
        '            str = str & " b.CN_Date as LR_DATE , b.WayBill_No, b.Unloading_Remarks, b.GatePass_No, b.Vendor_Code , b.Vendor_Name, b.Customer_Code , b.Customer_Name,"
        '            str = str & " b.F_WT , b.S_WT , case when (b.F_WT = 0 or b.S_WT = 0 ) then '0' else b.Net_WT  end as Net_WT, a.Vehicle_Status, a.out_DateTime, a.Remarks_IN ,"
        '            str = str & " a.Remarks_OUT, a.Remarks_Cancellation, b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code, a.Gate_NO, a.Driver_Name, a.Driver_LIC_No ,"
        '            str = str & " a.Entry_DoneBy, a.out_DoneBy ,  a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT ,"
        '            str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),':',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' ,"
        '            str = str & " CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)/60),':',(DATEDIFF(MINUTE, a.EntryDateTime ,b.F_WT_DateTime)%60)) Else ''  END as  'GIN_F_WT_Diff' , "
        '            str = str & " CASE WHEN (DATEDIFF(MINUTE, b.F_WT_DateTime,b.S_WT_DateTime) ) > 0 THEN concat((DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)/60),':',(DATEDIFF(MINUTE, b.F_WT_DateTime, b.S_WT_DateTime)%60)) Else ''  END as  'FWT_S_WT_Diff' ,"
        '            str = str & "  CASE WHEN b.S_WT_DateTime <> '' and (DATEDIFF(MINUTE, b.S_WT_DateTime,a.OUT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)/60),':',(DATEDIFF(MINUTE, b.S_WT_DateTime , a.OUT_DateTime)%60)) Else '' END as  'S_WT_GOUT_Diff' , "
        '            str = str & "           b.WB_Count_ID, b.F_WT_DateTime, b.S_WT_DAtetime, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.F_WT_DoneBy, b.S_WT_DoneBy"
        '            str = str & " from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'"
        '            str = str & "   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "' and a.Type_Of_Vehicle = '" & txtSalesTypeVehicle.Text.Trim & "')T1"
        '            str = str & "  LEFT JOIN tbl_Allowed_Route_CheckPost_Det T2  ON T1.TransactionNo = T2.GE_HDR_ID"
        '            ds = cc.GetDataset(str)
        '            gvReport.DataSource = ds.Tables(0)
        '            TotalVehicle = ds.Tables(0).Rows.Count
        '            txtTotWt.Text = TotalVehicle
        '            txtVehicleIN.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'IN' and a.Type_Of_Vehicle = '" & Trim(txtSalesTypeVehicle.Text.Trim) & "' and   a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "'"))
        '            txtVehicleOUT.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Vehicle_Status = 'OUT' and a.Type_Of_Vehicle = '" & Trim(txtSalesTypeVehicle.Text.Trim) & "' and   a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "'"))
        '            txtVehicleCancel.Text = (SearchData("select count(a.Vehicle_Status) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000'   and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and Vehicle_Status = 'C' and a.Type_Of_Vehicle = '" & Trim(txtSalesTypeVehicle.Text.Trim) & "' and   a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "'"))
        '            txtTOTnetWT.Text = (SearchData("select sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and EntryDateTime >  '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and   EntryDateTime < '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999' and a.Remarks_IN not like '%Auto%IN%' and a.Type_Of_Vehicle = '" & Trim(txtSalesTypeVehicle.Text.Trim) & "' and a.Vehicle_Status = '" & txtSalesVehicleStatus.Text.Trim & "'"))
        '        Catch ex As Exception

        '        End Try
        '    Catch ex As Exception

        '    End Try
        'End If
        lblWait.Text = ""
        lblWait.Refresh()
    End Sub

    Private Sub btnExportSales_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExportSales.Click
        ''Hours_TAT()
        lblMessage.Text = "Please Wait...."
        lblMessage.Refresh()

        '-------------------Amit L Mohan------------------------------
        Dim excel1 As Microsoft.Office.Interop.Excel.Application
        Dim wb As Microsoft.Office.Interop.Excel.Workbook

        Dim xlsheet As Excel.Worksheet

        'Dim xlwbook As Excel.Workbook

        excel1 = New Microsoft.Office.Interop.Excel.Application
        wb = excel1.Workbooks.Open(ApplicationPath & "\DetailsReport.xls", True, True, , "jnan")
        excel1.Visible = True
        wb.Activate()
        Try
            Dim row, col As Integer
            row = 2

            xlsheet = wb.Sheets.Item(1)
            xlsheet.Cells(1, 65) = "InvoiceNo"
            xlsheet.Cells(1, 66) = "InvoiceDate"
            xlsheet.Cells(1, 67) = "InvoiceTime"
            For i As Integer = 0 To gvReport.Columns.Count - 1
                xlsheet.Cells(1, i + 1) = gvReport.Columns(i).HeaderText
                '.Cells(1, i) = ListView1.ColumnHeaders(i).Text

            Next i
            xlsheet.Cells(1, 68) = "Invoice_Out_TAT"
            xlsheet.Cells(1, 69) = "Invoice_SWT_TAT"
            xlsheet.Cells(1, 70) = "CheckPost_IN"
            xlsheet.Cells(1, 71) = "CheckPost_OUT"
            xlsheet.Cells(1, 72) = "CheckPost_TAT(HH:MM)"
            xlsheet.Cells(1, 73) = "SPART"

            For j As Integer = 0 To gvReport.Rows.Count - 1
                xlsheet.Cells(row + j, 1) = gvReport.Rows(j).Cells(0).Value
                xlsheet.Cells(row + j, 2) = gvReport.Rows(j).Cells(1).Value
                xlsheet.Cells(row + j, 3) = gvReport.Rows(j).Cells(2).Value
                xlsheet.Cells(row + j, 4) = gvReport.Rows(j).Cells(3).Value
                xlsheet.Cells(row + j, 5) = gvReport.Rows(j).Cells(4).Value
                xlsheet.Cells(row + j, 6) = gvReport.Rows(j).Cells(5).Value
                xlsheet.Cells(row + j, 7) = gvReport.Rows(j).Cells(6).Value
                xlsheet.Cells(row + j, 8) = gvReport.Rows(j).Cells(7).Value
                xlsheet.Cells(row + j, 9) = gvReport.Rows(j).Cells(8).Value
                xlsheet.Cells(row + j, 10) = gvReport.Rows(j).Cells(9).Value
                xlsheet.Cells(row + j, 11) = gvReport.Rows(j).Cells(10).Value
                xlsheet.Cells(row + j, 12) = gvReport.Rows(j).Cells(11).Value
                xlsheet.Cells(row + j, 13) = gvReport.Rows(j).Cells(12).Value
                xlsheet.Cells(row + j, 14) = gvReport.Rows(j).Cells(13).Value
                xlsheet.Cells(row + j, 15) = gvReport.Rows(j).Cells(14).Value
                xlsheet.Cells(row + j, 16) = gvReport.Rows(j).Cells(15).Value
                xlsheet.Cells(row + j, 17) = gvReport.Rows(j).Cells(16).Value
                xlsheet.Cells(row + j, 18) = gvReport.Rows(j).Cells(17).Value
                xlsheet.Cells(row + j, 19) = gvReport.Rows(j).Cells(18).Value
                xlsheet.Cells(row + j, 20) = gvReport.Rows(j).Cells(19).Value
                xlsheet.Cells(row + j, 21) = gvReport.Rows(j).Cells(20).Value
                xlsheet.Cells(row + j, 22) = gvReport.Rows(j).Cells(21).Value
                xlsheet.Cells(row + j, 23) = gvReport.Rows(j).Cells(22).Value
                xlsheet.Cells(row + j, 24) = gvReport.Rows(j).Cells(23).Value
                xlsheet.Cells(row + j, 25) = gvReport.Rows(j).Cells(24).Value
                xlsheet.Cells(row + j, 26) = gvReport.Rows(j).Cells(25).Value
                xlsheet.Cells(row + j, 27) = gvReport.Rows(j).Cells(26).Value
                xlsheet.Cells(row + j, 28) = gvReport.Rows(j).Cells(27).Value
                xlsheet.Cells(row + j, 29) = gvReport.Rows(j).Cells(28).Value
                xlsheet.Cells(row + j, 30) = gvReport.Rows(j).Cells(29).Value
                xlsheet.Cells(row + j, 31) = gvReport.Rows(j).Cells(30).Value
                xlsheet.Cells(row + j, 32) = gvReport.Rows(j).Cells(31).Value
                xlsheet.Cells(row + j, 33) = gvReport.Rows(j).Cells(32).Value
                Try
                    xlsheet.Cells(row + j, 34) = CDate(gvReport.Rows(j).Cells(33).Value)
                Catch ex As Exception

                End Try
                'xlsheet.Cells(row + j, 34) = gvReport.Rows(j).Cells(33).Value
                xlsheet.Cells(row + j, 35) = gvReport.Rows(j).Cells(34).Value
                xlsheet.Cells(row + j, 36) = gvReport.Rows(j).Cells(35).Value
                xlsheet.Cells(row + j, 37) = gvReport.Rows(j).Cells(36).Value
                xlsheet.Cells(row + j, 38) = gvReport.Rows(j).Cells(37).Value
                xlsheet.Cells(row + j, 39) = gvReport.Rows(j).Cells(38).Value
                xlsheet.Cells(row + j, 40) = gvReport.Rows(j).Cells(39).Value
                xlsheet.Cells(row + j, 41) = gvReport.Rows(j).Cells(40).Value
                xlsheet.Cells(row + j, 42) = gvReport.Rows(j).Cells(41).Value
                xlsheet.Cells(row + j, 43) = gvReport.Rows(j).Cells(42).Value
                xlsheet.Cells(row + j, 44) = gvReport.Rows(j).Cells(43).Value
                xlsheet.Cells(row + j, 45) = gvReport.Rows(j).Cells(44).Value
                xlsheet.Cells(row + j, 46) = gvReport.Rows(j).Cells(45).Value
                xlsheet.Cells(row + j, 47) = gvReport.Rows(j).Cells(46).Value
                xlsheet.Cells(row + j, 48) = gvReport.Rows(j).Cells(47).Value
                xlsheet.Cells(row + j, 49) = gvReport.Rows(j).Cells(48).Value
                xlsheet.Cells(row + j, 50) = gvReport.Rows(j).Cells(49).Value
                xlsheet.Cells(row + j, 51) = gvReport.Rows(j).Cells(50).Value
                xlsheet.Cells(row + j, 52) = gvReport.Rows(j).Cells(51).Value
                xlsheet.Cells(row + j, 53) = gvReport.Rows(j).Cells(52).Value
                xlsheet.Cells(row + j, 54) = gvReport.Rows(j).Cells(53).Value
                xlsheet.Cells(row + j, 55) = gvReport.Rows(j).Cells(54).Value
                xlsheet.Cells(row + j, 56) = gvReport.Rows(j).Cells(55).Value
                Try
                    xlsheet.Cells(row + j, 57) = CDate(gvReport.Rows(j).Cells(56).Value)
                    xlsheet.Cells(row + j, 58) = CDate(gvReport.Rows(j).Cells(57).Value)
                Catch ex As Exception

                End Try
                xlsheet.Cells(row + j, 59) = gvReport.Rows(j).Cells(58).Value
                xlsheet.Cells(row + j, 60) = gvReport.Rows(j).Cells(59).Value
                xlsheet.Cells(row + j, 61) = gvReport.Rows(j).Cells(60).Value
                xlsheet.Cells(row + j, 62) = gvReport.Rows(j).Cells(61).Value
                xlsheet.Cells(row + j, 63) = gvReport.Rows(j).Cells(62).Value
                xlsheet.Cells(row + j, 64) = gvReport.Rows(j).Cells(63).Value
                If gvReport.Rows(j).Cells(4).Value = "SALES" Then
                    xlsheet.Cells(row + j, 65) = SearchData("select InvoiceNo from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 66) = SearchData("select InvoiceDate from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 67) = SearchData("select InvoiceTime from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 68) = SearchData("select Invoice_Out_TAT from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 69) = SearchData("select Invoice_SWT_TAT  from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                    xlsheet.Cells(row + j, 73) = SearchData("select SPART  from Electroway_Invoice_details where Tran_ID = '" & gvReport.Rows(j).Cells(0).Value & "'")
                End If

                xlsheet.Cells(row + j, 70) = SearchData("select IN_Date_Time from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID = '" & gvReport.Rows(j).Cells(1).Value & "'")
                xlsheet.Cells(row + j, 71) = SearchData("select OUT_Date_Time from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID = '" & gvReport.Rows(j).Cells(1).Value & "'")
                xlsheet.Cells(row + j, 72) = SearchData("select CASE WHEN (DATEDIFF(MINUTE, IN_Date_Time ,OUT_Date_Time)) > 0 THEN concat((DATEDIFF(MINUTE, IN_Date_Time, OUT_Date_Time)/60),':',(DATEDIFF(minute, IN_Date_Time, OUT_Date_Time)%60)) else '' end as   'TATDiff(HH:MM)'  from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID = '" & gvReport.Rows(j).Cells(1).Value & "'")
            Next
        Catch ex As Exception

        End Try

        ''------------for GRAPH ------------------
        'Dim excel1 As Microsoft.Office.Interop.Excel.Application
        'Dim wb As Microsoft.Office.Interop.Excel.Workbook

        'Dim xlsheet As Excel.Worksheet

        ''Dim xlwbook As Excel.Workbook

        'excel1 = New Microsoft.Office.Interop.Excel.Application
        'wb = excel1.Workbooks.Open(ApplicationPath & "\TAT_MATERAILS.xls", True, True, , "jnan")
        'excel1.ActiveWindow.WindowState = XlWindowState.xlMaximized
        'excel1.Visible = True
        'wb.Activate()

        'Try
        '    Dim row, col As Integer
        '    row = 2

        '    xlsheet = wb.Sheets.Item(1)
        '    xlsheet.Cells(2, 2) = dtSalesFrom.Text
        '    xlsheet.Cells(2, 4) = dtSalesTo.Text
        '    '--------------------------------------------------------TAT IN OUT
        '    '-----------PIG IRON--------------
        '    Dim strHrsPigIronSales As String = "select "
        '    strHrsPigIronSales = strHrsPigIronSales & " sum("
        '    strHrsPigIronSales = strHrsPigIronSales & " convert(int,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsPigIronSales = strHrsPigIronSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsPigIronSales = strHrsPigIronSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
        '    strHrsPigIronSales = strHrsPigIronSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
        '    strHrsPigIronSales = strHrsPigIronSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
        '    strHrsPigIronSales = strHrsPigIronSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
        '    strHrsPigIronSales = strHrsPigIronSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
        '    strHrsPigIronSales = strHrsPigIronSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
        '    strHrsPigIronSales = strHrsPigIronSales & " where  Mat_Desc like  '%Pig%Iron%'))) A1"

        '    dr = cc.GetDataReader(strHrsPigIronSales)
        '    Try
        '        While dr.Read
        '            If dr.HasRows Then
        '                Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '                If CDec(x1) > 0 Then
        '                    Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                    xlsheet.Cells(6, 10) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '                Else
        '                    xlsheet.Cells(6, 10) = 0
        '                End If
        '            Else
        '                xlsheet.Cells(6, 10) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(6, 10) = 0
        '    End Try
        '    dr.Close()
        '    '--------Billet-----------
        '    Dim strHrsBilletSales As String = "select "
        '    strHrsBilletSales = strHrsBilletSales & " sum("
        '    strHrsBilletSales = strHrsBilletSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsBilletSales = strHrsBilletSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsBilletSales = strHrsBilletSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
        '    strHrsBilletSales = strHrsBilletSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
        '    strHrsBilletSales = strHrsBilletSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
        '    strHrsBilletSales = strHrsBilletSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
        '    strHrsBilletSales = strHrsBilletSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
        '    strHrsBilletSales = strHrsBilletSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
        '    strHrsBilletSales = strHrsBilletSales & " where  Mat_Desc like  '%Billet%'))) A1"

        '    dr = cc.GetDataReader(strHrsBilletSales)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(7, 10) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(7, 10) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(7, 10) = 0
        '    End Try
        '    dr.Close()
        '    '------------'%Rebar%'--------------------
        '    Dim strHrsRebarSales As String = "select "
        '    strHrsRebarSales = strHrsRebarSales & " sum("
        '    strHrsRebarSales = strHrsRebarSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsRebarSales = strHrsRebarSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsRebarSales = strHrsRebarSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
        '    strHrsRebarSales = strHrsRebarSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
        '    strHrsRebarSales = strHrsRebarSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
        '    strHrsRebarSales = strHrsRebarSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
        '    strHrsRebarSales = strHrsRebarSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
        '    strHrsRebarSales = strHrsRebarSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
        '    strHrsRebarSales = strHrsRebarSales & " where Mat_Desc like  '%Rebar%')))A1"
        '    dr = cc.GetDataReader(strHrsRebarSales)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(8, 10) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(8, 10) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(8, 10) = 0
        '    End Try
        '    dr.Close()
        '    '---------Wire Rod Mill----------
        '    Dim strHrsWireSales As String = "select "
        '    strHrsWireSales = strHrsWireSales & " sum("
        '    strHrsWireSales = strHrsWireSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsWireSales = strHrsWireSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsWireSales = strHrsWireSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
        '    strHrsWireSales = strHrsWireSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
        '    strHrsWireSales = strHrsWireSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
        '    strHrsWireSales = strHrsWireSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
        '    strHrsWireSales = strHrsWireSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
        '    strHrsWireSales = strHrsWireSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
        '    strHrsWireSales = strHrsWireSales & " where Mat_Desc like  '%Wire%')))A1"
        '    dr = cc.GetDataReader(strHrsWireSales)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(9, 10) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(9, 10) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(9, 10) = 0
        '    End Try
        '    dr.Close()
        '    '--------DI---------
        '    Dim strHrsDISales As String = "select "
        '    strHrsDISales = strHrsDISales & " sum("
        '    strHrsDISales = strHrsDISales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsDISales = strHrsDISales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsDISales = strHrsDISales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
        '    strHrsDISales = strHrsDISales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
        '    strHrsDISales = strHrsDISales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
        '    strHrsDISales = strHrsDISales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
        '    strHrsDISales = strHrsDISales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
        '    strHrsDISales = strHrsDISales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
        '    strHrsDISales = strHrsDISales & " where Mat_Desc like '%DI%PIP%')))A1"
        '    dr = cc.GetDataReader(strHrsDISales)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(10, 10) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(10, 10) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(10, 10) = 0
        '    End Try
        '    dr.Close()

        '    '---------------------'%Gran%Slag%'-----------------------------------------------
        '    Dim strHrsGrnSlagSales As String = "select "
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " sum("
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
        '    strHrsGrnSlagSales = strHrsGrnSlagSales & " where Mat_Desc like  '%Gran%Slag%')))A1"
        '    dr = cc.GetDataReader(strHrsGrnSlagSales)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(11, 10) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(11, 10) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(11, 10) = 0
        '    End Try
        '    dr.Close()
        '    '-----------------------'%Liquid%'-------------------------------
        '    Dim strHrsLiquidSales As String = "select "
        '    strHrsLiquidSales = strHrsLiquidSales & " sum("
        '    strHrsLiquidSales = strHrsLiquidSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsLiquidSales = strHrsLiquidSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsLiquidSales = strHrsLiquidSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
        '    strHrsLiquidSales = strHrsLiquidSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
        '    strHrsLiquidSales = strHrsLiquidSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
        '    strHrsLiquidSales = strHrsLiquidSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
        '    strHrsLiquidSales = strHrsLiquidSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
        '    strHrsLiquidSales = strHrsLiquidSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
        '    strHrsLiquidSales = strHrsLiquidSales & " where Mat_Desc like  '%Liquid%')))A1"
        '    dr = cc.GetDataReader(strHrsLiquidSales)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(12, 10) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(12, 10) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(12, 10) = 0
        '    End Try
        '    dr.Close()
        '    '----------------------------------------------------------------------------------TAT SWT_OUT
        '    '-----------PIG IRON--------------
        '    'Dim strHrsPigIronSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
        '    Dim strHrsPigIronSalesSWT_OUT As String = "select "
        '    strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " sum("
        '    strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
        '    strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
        '    strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
        '    strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsPigIronSalesSWT_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(6, 9) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(6, 9) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(6, 9) = 0
        '    End Try
        '    dr.Close()
        '    ''--------Billet-----------
        '    'Dim strHrsBilletSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
        '    Dim strHrsBilletSalesSWT_OUT As String = "select "
        '    strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " sum("
        '    strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
        '    strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
        '    strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
        '    strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A "
        '    dr = cc.GetDataReader(strHrsBilletSalesSWT_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(7, 9) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(7, 9) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(7, 9) = 0
        '    End Try
        '    dr.Close()
        '    ''------------'%Rebar%'--------------------
        '    'Dim strHrsRebarSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
        '    Dim strHrsRebarSalesSWT_OUT As String = "select "
        '    strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " sum("
        '    strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
        '    strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
        '    strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
        '    strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsRebarSalesSWT_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(8, 9) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(8, 9) = 0
        '            End If

        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(8, 9) = 0
        '    End Try
        '    dr.Close()
        '    ''---------Wire Rod Mill----------
        '    'Dim strHrsWireSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
        '    Dim strHrsWireSalesSWT_OUT As String = "select "
        '    strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " sum("
        '    strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
        '    strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
        '    strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
        '    strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsWireSalesSWT_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(9, 9) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(9, 9) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(9, 9) = 0
        '    End Try
        '    dr.Close()
        '    ''--------DI---------
        '    'Dim strHrsDISalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
        '    Dim strHrsDISalesSWT_OUT As String = "select "
        '    strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " sum("
        '    strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
        '    strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
        '    strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
        '    strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsDISalesSWT_OUT)
        '    Try
        '        While dr.Read

        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(10, 9) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(10, 9) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(10, 9) = 0
        '    End Try
        '    dr.Close()

        '    ''---------------------'%Gran%Slag%'-----------------------------------------------
        '    'Dim strHrsGrnSlagSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
        '    Dim strHrsGrnSlagSalesSWT_OUT As String = "select "
        '    strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " sum("
        '    strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
        '    strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
        '    strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
        '    strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Gran%Slag%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsGrnSlagSalesSWT_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(11, 9) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(11, 9) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(11, 9) = 0
        '    End Try
        '    dr.Close()
        '    ''-----------------------'%Liquid%'-------------------------------
        '    'Dim strHrsLiquidSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
        '    Dim strHrsLiquidSalesSWT_OUT As String = "select "
        '    strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " sum("
        '    strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
        '    strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
        '    strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
        '    strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
        '    strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
        '    strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A "
        '    'strHrsLiquidSales = strHrsLiquidSales & " where convert(decimal, a.[TATDiff(HH:MM)]) >= 12) and Mat_Desc like  '%Liquid%'))A1"
        '    dr = cc.GetDataReader(strHrsLiquidSalesSWT_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(12, 9) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(12, 9) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(12, 9) = 0
        '    End Try
        '    dr.Close()
        '    '----------------------------IN & T_WT------------------------------------------------------
        '    '-----------PIG IRON--------------
        '    Dim strHrsPigIronSalesIN_TWT As String = "select "
        '    strHrsPigIronSalesIN_TWT = strHrsPigIronSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsPigIronSalesIN_TWT = strHrsPigIronSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsPigIronSalesIN_TWT = strHrsPigIronSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsPigIronSalesIN_TWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(6, 3) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(6, 3) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(6, 3) = 0
        '    End Try
        '    dr.Close()
        '    ''--------Billet-----------
        '    Dim strHrsBilletSalesIN_TWT As String = "select "
        '    strHrsBilletSalesIN_TWT = strHrsBilletSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsBilletSalesIN_TWT = strHrsBilletSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsBilletSalesIN_TWT = strHrsBilletSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsBilletSalesIN_TWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(7, 3) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(7, 3) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(7, 3) = 0
        '    End Try
        '    dr.Close()
        '    ''------------'%Rebar%'--------------------
        '    Dim strHrsRebarSalesIN_TWT As String = "select "
        '    strHrsRebarSalesIN_TWT = strHrsRebarSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsRebarSalesIN_TWT = strHrsRebarSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsRebarSalesIN_TWT = strHrsRebarSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsRebarSalesIN_TWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(8, 3) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(8, 3) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(8, 3) = 0
        '    End Try
        '    dr.Close()
        '    ''---------Wire Rod Mill----------
        '    Dim strHrsWireSalesIN_TWT As String = "select "
        '    strHrsWireSalesIN_TWT = strHrsWireSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsWireSalesIN_TWT = strHrsWireSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsWireSalesIN_TWT = strHrsWireSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsWireSalesIN_TWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(9, 3) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(9, 3) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(9, 3) = 0
        '    End Try
        '    dr.Close()
        '    ''--------DI---------
        '    Dim strHrsDISalesIN_TWT As String = "select "
        '    strHrsDISalesIN_TWT = strHrsDISalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsDISalesIN_TWT = strHrsDISalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsDISalesIN_TWT = strHrsDISalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsDISalesIN_TWT)
        '    Try
        '        While dr.Read

        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(10, 3) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(10, 3) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(10, 3) = 0
        '    End Try
        '    dr.Close()

        '    ''---------------------'%Gran%Slag%'-----------------------------------------------
        '    Dim strHrsGrnSlagSalesIN_TWT As String = "select "
        '    strHrsGrnSlagSalesIN_TWT = strHrsGrnSlagSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsGrnSlagSalesIN_TWT = strHrsGrnSlagSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsGrnSlagSalesIN_TWT = strHrsGrnSlagSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like   '%Gran%Slag%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsGrnSlagSalesIN_TWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(11, 3) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(11, 3) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(11, 3) = 0
        '    End Try
        '    dr.Close()
        '    ''-----------------------'%Liquid%'-------------------------------
        '    Dim strHrsLiquidSalesIN_TWT As String = "select "
        '    strHrsLiquidSalesIN_TWT = strHrsLiquidSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsLiquidSalesIN_TWT = strHrsLiquidSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsLiquidSalesIN_TWT = strHrsLiquidSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsLiquidSalesIN_TWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(12, 3) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(12, 3) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(12, 3) = 0
        '    End Try
        '    dr.Close()
        '    '----------------------------T_WT & CHK IN------------------------------------------------------
        '    '-----------PIG IRON--------------
        '    Dim strHrsPigIronSalesTWT_chkIN As String = "select "
        '    strHrsPigIronSalesTWT_chkIN = strHrsPigIronSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsPigIronSalesTWT_chkIN = strHrsPigIronSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsPigIronSalesTWT_chkIN = strHrsPigIronSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsPigIronSalesTWT_chkIN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(6, 4) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(6, 4) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(6, 4) = 0
        '    End Try
        '    dr.Close()
        '    ''--------Billet-----------
        '    Dim strHrsBilletSalesTWT_chkIN As String = "select "
        '    strHrsBilletSalesTWT_chkIN = strHrsBilletSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsBilletSalesTWT_chkIN = strHrsBilletSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsBilletSalesTWT_chkIN = strHrsBilletSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsBilletSalesTWT_chkIN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(7, 4) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(7, 4) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(7, 4) = 0
        '    End Try
        '    dr.Close()
        '    ''------------'%Rebar%'--------------------
        '    Dim strHrsRebarSalesTWT_chkIN As String = "select "
        '    strHrsRebarSalesTWT_chkIN = strHrsRebarSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsRebarSalesTWT_chkIN = strHrsRebarSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsRebarSalesTWT_chkIN = strHrsRebarSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsRebarSalesTWT_chkIN)

        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(8, 4) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(8, 4) = 0
        '            End If

        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(8, 4) = 0
        '    End Try
        '    dr.Close()
        '    ''---------Wire Rod Mill----------
        '    Dim strHrsWireSalesTWT_chkIN As String = "select "
        '    strHrsWireSalesTWT_chkIN = strHrsWireSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsWireSalesTWT_chkIN = strHrsWireSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsWireSalesTWT_chkIN = strHrsWireSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsWireSalesTWT_chkIN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(9, 4) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(9, 4) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(9, 4) = 0
        '    End Try
        '    dr.Close()
        '    ''--------DI---------
        '    Dim strHrsDISalesTWT_chkIN As String = "select "
        '    strHrsDISalesTWT_chkIN = strHrsDISalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsDISalesTWT_chkIN = strHrsDISalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsDISalesTWT_chkIN = strHrsDISalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsDISalesTWT_chkIN)
        '    Try
        '        While dr.Read

        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(10, 4) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(10, 4) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(10, 4) = 0
        '    End Try
        '    dr.Close()

        '    ''---------------------'%Gran%Slag%'-----------------------------------------------
        '    Dim strHrsGrnSlagSalesTWT_chkIN As String = "select "
        '    strHrsGrnSlagSalesTWT_chkIN = strHrsGrnSlagSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsGrnSlagSalesTWT_chkIN = strHrsGrnSlagSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsGrnSlagSalesTWT_chkIN = strHrsGrnSlagSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Gran%Slag%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsGrnSlagSalesTWT_chkIN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(11, 4) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(11, 4) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(11, 4) = 0
        '    End Try
        '    dr.Close()
        '    ''-----------------------'%Liquid%'-------------------------------
        '    Dim strHrsLiquidSalesTWT_chkIN As String = "select "
        '    strHrsLiquidSalesTWT_chkIN = strHrsLiquidSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsLiquidSalesTWT_chkIN = strHrsLiquidSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsLiquidSalesTWT_chkIN = strHrsLiquidSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsLiquidSalesTWT_chkIN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(12, 4) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(12, 4) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(12, 4) = 0
        '    End Try
        '    dr.Close()

        '    '----------------------------CHK OUT & S_WT------------------------------------------------------
        '    '-----------PIG IRON--------------
        '    Dim strHrsPigIronSales_chkOUT_SWT As String = "select "
        '    strHrsPigIronSales_chkOUT_SWT = strHrsPigIronSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsPigIronSales_chkOUT_SWT = strHrsPigIronSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsPigIronSales_chkOUT_SWT = strHrsPigIronSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A  "

        '    dr = cc.GetDataReader(strHrsPigIronSales_chkOUT_SWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(6, 5) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(6, 5) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(6, 5) = 0
        '    End Try
        '    dr.Close()
        '    ''--------Billet-----------
        '    Dim strHrsBilletSales_chkOUT_SWT As String = "select "
        '    strHrsBilletSales_chkOUT_SWT = strHrsBilletSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsBilletSales_chkOUT_SWT = strHrsBilletSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsBilletSales_chkOUT_SWT = strHrsBilletSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A  "

        '    dr = cc.GetDataReader(strHrsBilletSales_chkOUT_SWT)

        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(7, 5) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(7, 5) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(7, 5) = 0
        '    End Try
        '    dr.Close()
        '    ''------------'%Rebar%'--------------------
        '    Dim strHrsRebarSales_chkOUT_SWT As String = "select "
        '    strHrsRebarSales_chkOUT_SWT = strHrsRebarSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsRebarSales_chkOUT_SWT = strHrsRebarSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsRebarSales_chkOUT_SWT = strHrsRebarSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A  "

        '    dr = cc.GetDataReader(strHrsRebarSales_chkOUT_SWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(8, 5) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(8, 5) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(8, 5) = 0
        '    End Try
        '    dr.Close()
        '    ''---------Wire Rod Mill----------
        '    Dim strHrsWireSales_chkOUT_SWT As String = "select "
        '    strHrsWireSales_chkOUT_SWT = strHrsWireSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsWireSales_chkOUT_SWT = strHrsWireSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsWireSales_chkOUT_SWT = strHrsWireSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A  "

        '    dr = cc.GetDataReader(strHrsWireSales_chkOUT_SWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(9, 5) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(9, 5) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(9, 5) = 0
        '    End Try
        '    dr.Close()
        '    ''--------DI---------
        '    Dim strHrsDISales_chkOUT_SWT As String = "select "
        '    strHrsDISales_chkOUT_SWT = strHrsDISales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsDISales_chkOUT_SWT = strHrsDISales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsDISales_chkOUT_SWT = strHrsDISales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A  "

        '    dr = cc.GetDataReader(strHrsDISales_chkOUT_SWT)
        '    Try
        '        While dr.Read

        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(10, 5) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(10, 5) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(10, 5) = 0
        '    End Try
        '    dr.Close()

        '    ''---------------------'%Gran%Slag%'-----------------------------------------------
        '    Dim strHrsGrnSlagSales_chkOUT_SWT As String = "select "
        '    strHrsGrnSlagSales_chkOUT_SWT = strHrsGrnSlagSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsGrnSlagSales_chkOUT_SWT = strHrsGrnSlagSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsGrnSlagSales_chkOUT_SWT = strHrsGrnSlagSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Gran%Slag%' and S_WT > 0)A  "

        '    dr = cc.GetDataReader(strHrsGrnSlagSales_chkOUT_SWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(11, 5) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(11, 5) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(11, 5) = 0
        '    End Try
        '    dr.Close()
        '    ''-----------------------'%Liquid%'-------------------------------
        '    Dim strHrsLiquidSales_chkOUT_SWT As String = "select "
        '    strHrsLiquidSales_chkOUT_SWT = strHrsLiquidSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsLiquidSales_chkOUT_SWT = strHrsLiquidSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsLiquidSales_chkOUT_SWT = strHrsLiquidSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A  "

        '    dr = cc.GetDataReader(strHrsLiquidSales_chkOUT_SWT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(12, 5) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(12, 5) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(12, 5) = 0
        '    End Try
        '    dr.Close()

        '    '----------------------------CHK IN & CHK OUT------------------------------------------------------
        '    '-----------PIG IRON--------------
        '    Dim strHrsPigIronSales_chkOUT_IN As String = "select "
        '    strHrsPigIronSales_chkOUT_IN = strHrsPigIronSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsPigIronSales_chkOUT_IN = strHrsPigIronSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsPigIronSales_chkOUT_IN = strHrsPigIronSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsPigIronSales_chkOUT_IN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(6, 6) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(6, 6) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(6, 6) = 0
        '    End Try
        '    dr.Close()
        '    ''--------Billet-----------
        '    Dim strHrsBilletSales_chkOUT_IN As String = "select "
        '    strHrsBilletSales_chkOUT_IN = strHrsBilletSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsBilletSales_chkOUT_IN = strHrsBilletSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsBilletSales_chkOUT_IN = strHrsBilletSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsBilletSales_chkOUT_IN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(7, 6) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(7, 6) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(7, 6) = 0
        '    End Try
        '    dr.Close()
        '    ''------------'%Rebar%'--------------------
        '    Dim strHrsRebarSales_chkOUT_IN As String = "select "
        '    strHrsRebarSales_chkOUT_IN = strHrsRebarSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsRebarSales_chkOUT_IN = strHrsRebarSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsRebarSales_chkOUT_IN = strHrsRebarSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsRebarSales_chkOUT_IN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(8, 6) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(8, 6) = 0
        '            End If

        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(8, 6) = 0
        '    End Try
        '    dr.Close()
        '    ''---------Wire Rod Mill----------
        '    Dim strHrsWireSSales_chkOUT_IN As String = "select "
        '    strHrsWireSSales_chkOUT_IN = strHrsWireSSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsWireSSales_chkOUT_IN = strHrsWireSSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsWireSSales_chkOUT_IN = strHrsWireSSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsWireSSales_chkOUT_IN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(9, 6) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(9, 6) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(9, 6) = 0
        '    End Try
        '    dr.Close()
        '    ''--------DI---------
        '    Dim strHrsDISSales_chkOUT_IN As String = "select "
        '    strHrsDISSales_chkOUT_IN = strHrsDISSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsDISSales_chkOUT_IN = strHrsDISSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsDISSales_chkOUT_IN = strHrsDISSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsDISSales_chkOUT_IN)
        '    Try
        '        While dr.Read

        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(10, 6) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(10, 6) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(10, 6) = 0
        '    End Try
        '    dr.Close()

        '    ''---------------------'%Gran%Slag%'-----------------------------------------------
        '    Dim strHrsGrnSlagSales_chkOUT_IN As String = "select "
        '    strHrsGrnSlagSales_chkOUT_IN = strHrsGrnSlagSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsGrnSlagSales_chkOUT_IN = strHrsGrnSlagSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsGrnSlagSales_chkOUT_IN = strHrsGrnSlagSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Gran%Slag%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsGrnSlagSales_chkOUT_IN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(11, 6) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(11, 6) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(11, 6) = 0
        '    End Try
        '    dr.Close()
        '    ''-----------------------'%Liquid%'-------------------------------
        '    Dim strHrsLiquidSales_chkOUT_IN As String = "select "
        '    strHrsLiquidSales_chkOUT_IN = strHrsLiquidSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
        '    strHrsLiquidSales_chkOUT_IN = strHrsLiquidSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
        '    strHrsLiquidSales_chkOUT_IN = strHrsLiquidSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A "

        '    dr = cc.GetDataReader(strHrsLiquidSales_chkOUT_IN)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(12, 6) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(12, 6) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(12, 6) = 0
        '    End Try
        '    dr.Close()

        '    '            select
        '    'sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from
        '    '(select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Pig%Iron%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A
        '    '----------------------------Invoice & Out ------------------------------------------------------
        '    '-----------PIG IRON--------------
        '    Dim strHrsPigIronSales_Inv_OUT As String = "select "
        '    strHrsPigIronSales_Inv_OUT = strHrsPigIronSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsPigIronSales_Inv_OUT = strHrsPigIronSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Pig%Iron%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsPigIronSales_Inv_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(6, 8) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(6, 8) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(6, 8) = 0
        '    End Try
        '    dr.Close()
        '    ''--------Billet-----------
        '    Dim strHrsBilletSales_Inv_OUT As String = "select "
        '    strHrsBilletSales_Inv_OUT = strHrsBilletSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsBilletSales_Inv_OUT = strHrsBilletSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Billet%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsBilletSales_Inv_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(7, 8) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(7, 8) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(7, 8) = 0
        '    End Try
        '    dr.Close()
        '    ''------------'%Rebar%'--------------------
        '    Dim strHrsRebarSales_Inv_OUT As String = "select "
        '    strHrsRebarSales_Inv_OUT = strHrsRebarSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsRebarSales_Inv_OUT = strHrsRebarSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Rebar%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsRebarSales_Inv_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(8, 8) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(8, 8) = 0
        '            End If

        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(8, 8) = 0
        '    End Try
        '    dr.Close()
        '    ''---------Wire Rod Mill----------
        '    Dim strHrsWireSales_Inv_OUT As String = "select "
        '    strHrsWireSales_Inv_OUT = strHrsWireSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsWireSales_Inv_OUT = strHrsWireSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Wire%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsWireSales_Inv_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(9, 8) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(9, 8) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(9, 8) = 0
        '    End Try
        '    dr.Close()
        '    ''--------DI---------
        '    Dim strHrsDISales_Inv_OUT As String = "select "
        '    strHrsDISales_Inv_OUT = strHrsDISales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsDISales_Inv_OUT = strHrsDISales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%DI%PIP%'and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsDISales_Inv_OUT)
        '    Try
        '        While dr.Read

        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(10, 8) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(10, 8) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(10, 8) = 0
        '    End Try
        '    dr.Close()

        '    ''---------------------'%Gran%Slag%'-----------------------------------------------
        '    ''--------DI---------
        '    Dim strHrsGrnSlagSales_Inv_OUT As String = "select "
        '    strHrsGrnSlagSales_Inv_OUT = strHrsGrnSlagSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsGrnSlagSales_Inv_OUT = strHrsGrnSlagSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like  '%Gran%Slag%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsGrnSlagSales_Inv_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(11, 8) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(11, 8) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(11, 8) = 0
        '    End Try
        '    dr.Close()
        '    ''-----------------------'%Liquid%'-------------------------------
        '    Dim strHrsLiquidSales_Inv_OUT As String = "select "
        '    strHrsLiquidSales_Inv_OUT = strHrsLiquidSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsLiquidSales_Inv_OUT = strHrsLiquidSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like  '%Liquid%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsLiquidSales_Inv_OUT)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(12, 8) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(12, 8) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(12, 8) = 0
        '    End Try
        '    dr.Close()

        '    '            select
        '    'sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from
        '    '(select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Pig%Iron%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A
        '    '--------------------------SWT_Invoice------------------------------------------------------
        '    '-----------PIG IRON--------------
        '    Dim strHrsPigIronSales_SWT_INV As String = "select "
        '    strHrsPigIronSales_SWT_INV = strHrsPigIronSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsPigIronSales_SWT_INV = strHrsPigIronSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Pig%Iron%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsPigIronSales_SWT_INV)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(6, 7) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(6, 7) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(6, 7) = 0
        '    End Try
        '    dr.Close()
        '    ''--------Billet-----------
        '    Dim strHrsBilletSales_SWT_INV As String = "select "
        '    strHrsBilletSales_SWT_INV = strHrsBilletSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsBilletSales_SWT_INV = strHrsBilletSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Billet%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsBilletSales_SWT_INV)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(7, 7) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(7, 7) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(7, 7) = 0
        '    End Try
        '    dr.Close()
        '    ''------------'%Rebar%'--------------------
        '    Dim strHrsRebarSales_SWT_INV As String = "select "
        '    strHrsRebarSales_SWT_INV = strHrsRebarSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsRebarSales_SWT_INV = strHrsRebarSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Rebar%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsRebarSales_SWT_INV)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(8, 7) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(8, 7) = 0
        '            End If

        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(8, 7) = 0
        '    End Try
        '    dr.Close()
        '    ''---------Wire Rod Mill----------
        '    Dim strHrsWireSales_SWT_INV As String = "select "
        '    strHrsWireSales_SWT_INV = strHrsWireSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsWireSales_SWT_INV = strHrsWireSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Wire%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsWireSales_SWT_INV)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(9, 7) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(9, 7) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(9, 7) = 0
        '    End Try
        '    dr.Close()
        '    ''--------DI---------
        '    Dim strHrsDISales_SWT_INV As String = "select "
        '    strHrsDISales_SWT_INV = strHrsDISales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsDISales_SWT_INV = strHrsDISales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%DI%PIP%'and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsDISales_SWT_INV)
        '    Try
        '        While dr.Read

        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(10, 7) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(10, 7) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(10, 7) = 0
        '    End Try
        '    dr.Close()

        '    ''---------------------'%Gran%Slag%'-----------------------------------------------
        '    ''--------DI---------
        '    Dim strHrsGrnSlagSales_SWT_INV As String = "select "
        '    strHrsGrnSlagSales_SWT_INV = strHrsGrnSlagSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsGrnSlagSales_SWT_INV = strHrsGrnSlagSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like  '%Gran%Slag%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsGrnSlagSales_SWT_INV)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(11, 7) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(11, 7) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(11, 7) = 0
        '    End Try
        '    dr.Close()
        '    ''-----------------------'%Liquid%'-------------------------------
        '    Dim strHrsLiquidSales_SWT_INV As String = "select "
        '    strHrsLiquidSales_SWT_INV = strHrsLiquidSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
        '    strHrsLiquidSales_SWT_INV = strHrsLiquidSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like  '%Liquid%' and convert(date, H.OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"

        '    dr = cc.GetDataReader(strHrsLiquidSales_SWT_INV)
        '    Try
        '        While dr.Read
        '            Dim x1 As Integer = CInt(CInt(dr(0).ToString / dr(1).ToString))
        '            If CDec(x1) > 0 Then
        '                Dim x As Integer = Math.Truncate((CInt(x1) / 60))
        '                xlsheet.Cells(12, 7) = x & "." & (CInt(x1) Mod 60).ToString.PadLeft(2, "0")
        '            Else
        '                xlsheet.Cells(12, 7) = 0
        '            End If
        '        End While
        '    Catch ex As Exception
        '        xlsheet.Cells(12, 7) = 0
        '    End Try
        '    dr.Close()

        'Catch ex As Exception

        'End Try
        lblMessage.Text = ""
        lblMessage.Refresh()
    End Sub
    Private Sub Hours_TAT()
        lblMessage.Text = "Please Wait...."
        lblMessage.Refresh()
        Dim excel1 As Microsoft.Office.Interop.Excel.Application
        Dim wb As Microsoft.Office.Interop.Excel.Workbook

        Dim xlsheet As Excel.Worksheet

        'Dim xlwbook As Excel.Workbook

        excel1 = New Microsoft.Office.Interop.Excel.Application
        wb = excel1.Workbooks.Open(ApplicationPath & "\TAT_MATERAILS.xls", True, True, , "jnan")
        excel1.ActiveWindow.WindowState = XlWindowState.xlMaximized
        excel1.Visible = True
        wb.Activate()

        Try
            Dim row, col As Integer
            row = 2

            xlsheet = wb.Sheets.Item(1)
            xlsheet.Cells(2, 2) = dtSalesFrom.Text
            xlsheet.Cells(2, 4) = dtSalesTo.Text
            '--------------------------------------------------------TAT IN OUT
            '-----------PIG IRON--------------
            Dim strHrsPigIronSales As String = "select "
            strHrsPigIronSales = strHrsPigIronSales & " sum("
            strHrsPigIronSales = strHrsPigIronSales & " convert(int,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsPigIronSales = strHrsPigIronSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsPigIronSales = strHrsPigIronSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
            strHrsPigIronSales = strHrsPigIronSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
            strHrsPigIronSales = strHrsPigIronSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
            strHrsPigIronSales = strHrsPigIronSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
            strHrsPigIronSales = strHrsPigIronSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
            strHrsPigIronSales = strHrsPigIronSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
            strHrsPigIronSales = strHrsPigIronSales & " where  Mat_Desc like  '%Pig%Iron%'))) A1"

            dr = cc.GetDataReader(strHrsPigIronSales)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(6, 10) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(6, 10) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(6, 10) = 0
            End Try
            dr.Close()
            '--------Billet-----------
            Dim strHrsBilletSales As String = "select "
            strHrsBilletSales = strHrsBilletSales & " sum("
            strHrsBilletSales = strHrsBilletSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsBilletSales = strHrsBilletSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsBilletSales = strHrsBilletSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
            strHrsBilletSales = strHrsBilletSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
            strHrsBilletSales = strHrsBilletSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
            strHrsBilletSales = strHrsBilletSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
            strHrsBilletSales = strHrsBilletSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
            strHrsBilletSales = strHrsBilletSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
            strHrsBilletSales = strHrsBilletSales & " where  Mat_Desc like  '%Billet%'))) A1"

            dr = cc.GetDataReader(strHrsBilletSales)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(7, 10) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(7, 10) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(7, 10) = 0
            End Try
            dr.Close()
            '------------'%Rebar%'--------------------
            Dim strHrsRebarSales As String = "select "
            strHrsRebarSales = strHrsRebarSales & " sum("
            strHrsRebarSales = strHrsRebarSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsRebarSales = strHrsRebarSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsRebarSales = strHrsRebarSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
            strHrsRebarSales = strHrsRebarSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
            strHrsRebarSales = strHrsRebarSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
            strHrsRebarSales = strHrsRebarSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
            strHrsRebarSales = strHrsRebarSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
            strHrsRebarSales = strHrsRebarSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
            strHrsRebarSales = strHrsRebarSales & " where Mat_Desc like  '%Rebar%')))A1"
            dr = cc.GetDataReader(strHrsRebarSales)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(8, 10) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(8, 10) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(8, 10) = 0
            End Try
            dr.Close()
            '---------Wire Rod Mill----------
            Dim strHrsWireSales As String = "select "
            strHrsWireSales = strHrsWireSales & " sum("
            strHrsWireSales = strHrsWireSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsWireSales = strHrsWireSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsWireSales = strHrsWireSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
            strHrsWireSales = strHrsWireSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
            strHrsWireSales = strHrsWireSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
            strHrsWireSales = strHrsWireSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
            strHrsWireSales = strHrsWireSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
            strHrsWireSales = strHrsWireSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
            strHrsWireSales = strHrsWireSales & " where Mat_Desc like  '%Wire%')))A1"
            dr = cc.GetDataReader(strHrsWireSales)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(9, 10) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(9, 10) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(9, 10) = 0
            End Try
            dr.Close()
            '--------DI---------
            Dim strHrsDISales As String = "select "
            strHrsDISales = strHrsDISales & " sum("
            strHrsDISales = strHrsDISales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsDISales = strHrsDISales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsDISales = strHrsDISales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
            strHrsDISales = strHrsDISales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
            strHrsDISales = strHrsDISales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
            strHrsDISales = strHrsDISales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
            strHrsDISales = strHrsDISales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
            strHrsDISales = strHrsDISales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
            strHrsDISales = strHrsDISales & " where Mat_Desc like '%DI%PIP%')))A1"
            dr = cc.GetDataReader(strHrsDISales)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(10, 10) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(10, 10) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(10, 10) = 0
            End Try
            dr.Close()

            '---------------------'%Gran%Slag%'-----------------------------------------------
            Dim strHrsGrnSlagSales As String = "select "
            strHrsGrnSlagSales = strHrsGrnSlagSales & " sum("
            strHrsGrnSlagSales = strHrsGrnSlagSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsGrnSlagSales = strHrsGrnSlagSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsGrnSlagSales = strHrsGrnSlagSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
            strHrsGrnSlagSales = strHrsGrnSlagSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
            strHrsGrnSlagSales = strHrsGrnSlagSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
            strHrsGrnSlagSales = strHrsGrnSlagSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
            strHrsGrnSlagSales = strHrsGrnSlagSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
            strHrsGrnSlagSales = strHrsGrnSlagSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
            strHrsGrnSlagSales = strHrsGrnSlagSales & " where Mat_Desc like  '%Gran%Slag%')))A1"
            dr = cc.GetDataReader(strHrsGrnSlagSales)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(11, 10) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(11, 10) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(11, 10) = 0
            End Try
            dr.Close()
            '-----------------------'%Liquid%'-------------------------------
            Dim strHrsLiquidSales As String = "select "
            strHrsLiquidSales = strHrsLiquidSales & " sum("
            strHrsLiquidSales = strHrsLiquidSales & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsLiquidSales = strHrsLiquidSales & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsLiquidSales = strHrsLiquidSales & " (select GE_HDR_ID,CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0   THEN"
            strHrsLiquidSales = strHrsLiquidSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60)),2))  Else ''  END as   'TATDiff'"
            strHrsLiquidSales = strHrsLiquidSales & " from tbl_GE_Hdr  where GE_HDR_ID in (select distinct GE_HDR_ID from tbl_GE_Det where GE_HDR_ID in"
            strHrsLiquidSales = strHrsLiquidSales & " (select GE_HDR_ID from (select GE_HDR_ID,  CASE WHEN (DATEDIFF(MINUTE, EntryDateTime ,OUT_DateTime) ) > 0  THEN"
            strHrsLiquidSales = strHrsLiquidSales & " concat((DATEDIFF(MINUTE, EntryDateTime, OUT_DateTime)/60),':',(DATEDIFF(minute, EntryDateTime, OUT_DateTime)%60))  Else ''  END as   'TATDiff'"
            strHrsLiquidSales = strHrsLiquidSales & " from tbl_GE_Hdr where Type_Of_Vehicle = 'SALES' and convert(date, OUT_DateTime) between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "') A"
            strHrsLiquidSales = strHrsLiquidSales & " where Mat_Desc like  '%Liquid%')))A1"
            dr = cc.GetDataReader(strHrsLiquidSales)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(12, 10) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(12, 10) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(12, 10) = 0
            End Try
            dr.Close()
            '----------------------------------------------------------------------------------TAT SWT_OUT
            '-----------PIG IRON--------------
            'Dim strHrsPigIronSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
            Dim strHrsPigIronSalesSWT_OUT As String = "select "
            strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " sum("
            strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
            strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
            strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
            strHrsPigIronSalesSWT_OUT = strHrsPigIronSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsPigIronSalesSWT_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(6, 9) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(6, 9) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(6, 9) = 0
            End Try
            dr.Close()
            ''--------Billet-----------
            'Dim strHrsBilletSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
            Dim strHrsBilletSalesSWT_OUT As String = "select "
            strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " sum("
            strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
            strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
            strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
            strHrsBilletSalesSWT_OUT = strHrsBilletSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A "
            dr = cc.GetDataReader(strHrsBilletSalesSWT_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(7, 9) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(7, 9) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(7, 9) = 0
            End Try
            dr.Close()
            ''------------'%Rebar%'--------------------
            'Dim strHrsRebarSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
            Dim strHrsRebarSalesSWT_OUT As String = "select "
            strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " sum("
            strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
            strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
            strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
            strHrsRebarSalesSWT_OUT = strHrsRebarSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsRebarSalesSWT_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(8, 9) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(8, 9) = 0
                    End If

                End While
            Catch ex As Exception
                xlsheet.Cells(8, 9) = 0
            End Try
            dr.Close()
            ''---------Wire Rod Mill----------
            'Dim strHrsWireSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
            Dim strHrsWireSalesSWT_OUT As String = "select "
            strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " sum("
            strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
            strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
            strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
            strHrsWireSalesSWT_OUT = strHrsWireSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsWireSalesSWT_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(9, 9) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(9, 9) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(9, 9) = 0
            End Try
            dr.Close()
            ''--------DI---------
            'Dim strHrsDISalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
            Dim strHrsDISalesSWT_OUT As String = "select "
            strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " sum("
            strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
            strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
            strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
            strHrsDISalesSWT_OUT = strHrsDISalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsDISalesSWT_OUT)
            Try
                While dr.Read

                    If dr.HasRows Then
                        xlsheet.Cells(10, 9) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(10, 9) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(10, 9) = 0
            End Try
            dr.Close()

            ''---------------------'%Gran%Slag%'-----------------------------------------------
            'Dim strHrsGrnSlagSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
            Dim strHrsGrnSlagSalesSWT_OUT As String = "select "
            strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " sum("
            strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
            strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
            strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
            strHrsGrnSlagSalesSWT_OUT = strHrsGrnSlagSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Gran%Slag%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsGrnSlagSalesSWT_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(11, 9) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(11, 9) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(11, 9) = 0
            End Try
            dr.Close()
            ''-----------------------'%Liquid%'-------------------------------
            'Dim strHrsLiquidSalesSWT_OUT As String = "select count(*) as Tot,cast(dateadd(MINUTE,sum(datediff(MINUTE,0,cast(TATDiff as time))),0) as time(0)) as total_time from"
            Dim strHrsLiquidSalesSWT_OUT As String = "select "
            strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " sum("
            strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+ "
            strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from "
            strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " (select H.GE_HDR_ID,S_WT_DateTime,OUT_DateTime, CASE WHEN (DATEDIFF(MINUTE,S_WT_DateTime, OUT_DateTime) ) > 0 "
            strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " THEN concat((DATEDIFF(MINUTE, S_WT_DateTime,OUT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, S_WT_DateTime, OUT_DateTime)%60)),2))"
            strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & " Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES' and D.S_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) "
            strHrsLiquidSalesSWT_OUT = strHrsLiquidSalesSWT_OUT & "  between '" & dtSalesFrom.Text & "' and '" & dtSalesTo.Text & "' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A "
            'strHrsLiquidSales = strHrsLiquidSales & " where convert(decimal, a.[TATDiff(HH:MM)]) >= 12) and Mat_Desc like  '%Liquid%'))A1"
            dr = cc.GetDataReader(strHrsLiquidSalesSWT_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(12, 9) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(12, 9) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(12, 9) = 0
            End Try
            dr.Close()
            '----------------------------IN & T_WT------------------------------------------------------
            '-----------PIG IRON--------------
            Dim strHrsPigIronSalesIN_TWT As String = "select "
            strHrsPigIronSalesIN_TWT = strHrsPigIronSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsPigIronSalesIN_TWT = strHrsPigIronSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsPigIronSalesIN_TWT = strHrsPigIronSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsPigIronSalesIN_TWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(6, 3) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(6, 3) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(6, 3) = 0
            End Try
            dr.Close()
            ''--------Billet-----------
            Dim strHrsBilletSalesIN_TWT As String = "select "
            strHrsBilletSalesIN_TWT = strHrsBilletSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsBilletSalesIN_TWT = strHrsBilletSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsBilletSalesIN_TWT = strHrsBilletSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsBilletSalesIN_TWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(7, 3) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(7, 3) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(7, 3) = 0
            End Try
            dr.Close()
            ''------------'%Rebar%'--------------------
            Dim strHrsRebarSalesIN_TWT As String = "select "
            strHrsRebarSalesIN_TWT = strHrsRebarSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsRebarSalesIN_TWT = strHrsRebarSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsRebarSalesIN_TWT = strHrsRebarSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsRebarSalesIN_TWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(8, 3) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(8, 3) = 0
                    End If

                End While
            Catch ex As Exception
                xlsheet.Cells(8, 3) = 0
            End Try
            dr.Close()
            ''---------Wire Rod Mill----------
            Dim strHrsWireSalesIN_TWT As String = "select "
            strHrsWireSalesIN_TWT = strHrsWireSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsWireSalesIN_TWT = strHrsWireSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsWireSalesIN_TWT = strHrsWireSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsWireSalesIN_TWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(9, 3) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(9, 3) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(9, 3) = 0
            End Try
            dr.Close()
            ''--------DI---------
            Dim strHrsDISalesIN_TWT As String = "select "
            strHrsDISalesIN_TWT = strHrsDISalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsDISalesIN_TWT = strHrsDISalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsDISalesIN_TWT = strHrsDISalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsDISalesIN_TWT)
            Try
                While dr.Read

                    If dr.HasRows Then
                        xlsheet.Cells(10, 3) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(10, 3) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(10, 3) = 0
            End Try
            dr.Close()

            ''---------------------'%Gran%Slag%'-----------------------------------------------
            Dim strHrsGrnSlagSalesIN_TWT As String = "select "
            strHrsGrnSlagSalesIN_TWT = strHrsGrnSlagSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsGrnSlagSalesIN_TWT = strHrsGrnSlagSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsGrnSlagSalesIN_TWT = strHrsGrnSlagSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like   '%Gran%Slag%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsGrnSlagSalesIN_TWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(11, 3) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(11, 3) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(11, 3) = 0
            End Try
            dr.Close()
            ''-----------------------'%Liquid%'-------------------------------
            Dim strHrsLiquidSalesIN_TWT As String = "select "
            strHrsLiquidSalesIN_TWT = strHrsLiquidSalesIN_TWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsLiquidSalesIN_TWT = strHrsLiquidSalesIN_TWT & " (select H.GE_HDR_ID,F_WT_DateTime,EntryDateTime, CASE WHEN (DATEDIFF(MINUTE,EntryDateTime,F_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,EntryDateTime, F_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute, EntryDateTime,F_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff'  from tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsLiquidSalesIN_TWT = strHrsLiquidSalesIN_TWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsLiquidSalesIN_TWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(12, 3) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(12, 3) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(12, 3) = 0
            End Try
            dr.Close()
            '----------------------------T_WT & CHK IN------------------------------------------------------
            '-----------PIG IRON--------------
            Dim strHrsPigIronSalesTWT_chkIN As String = "select "
            strHrsPigIronSalesTWT_chkIN = strHrsPigIronSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsPigIronSalesTWT_chkIN = strHrsPigIronSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsPigIronSalesTWT_chkIN = strHrsPigIronSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsPigIronSalesTWT_chkIN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(6, 4) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(6, 4) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(6, 4) = 0
            End Try
            dr.Close()
            ''--------Billet-----------
            Dim strHrsBilletSalesTWT_chkIN As String = "select "
            strHrsBilletSalesTWT_chkIN = strHrsBilletSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsBilletSalesTWT_chkIN = strHrsBilletSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsBilletSalesTWT_chkIN = strHrsBilletSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsBilletSalesTWT_chkIN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(7, 4) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(7, 4) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(7, 4) = 0
            End Try
            dr.Close()
            ''------------'%Rebar%'--------------------
            Dim strHrsRebarSalesTWT_chkIN As String = "select "
            strHrsRebarSalesTWT_chkIN = strHrsRebarSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsRebarSalesTWT_chkIN = strHrsRebarSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsRebarSalesTWT_chkIN = strHrsRebarSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsRebarSalesTWT_chkIN)

            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(8, 4) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(8, 4) = 0
                    End If

                End While
            Catch ex As Exception
                xlsheet.Cells(8, 4) = 0
            End Try
            dr.Close()
            ''---------Wire Rod Mill----------
            Dim strHrsWireSalesTWT_chkIN As String = "select "
            strHrsWireSalesTWT_chkIN = strHrsWireSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsWireSalesTWT_chkIN = strHrsWireSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsWireSalesTWT_chkIN = strHrsWireSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsWireSalesTWT_chkIN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(9, 4) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(9, 4) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(9, 4) = 0
            End Try
            dr.Close()
            ''--------DI---------
            Dim strHrsDISalesTWT_chkIN As String = "select "
            strHrsDISalesTWT_chkIN = strHrsDISalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsDISalesTWT_chkIN = strHrsDISalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsDISalesTWT_chkIN = strHrsDISalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsDISalesTWT_chkIN)
            Try
                While dr.Read

                    If dr.HasRows Then
                        xlsheet.Cells(10, 4) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(10, 4) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(10, 4) = 0
            End Try
            dr.Close()

            ''---------------------'%Gran%Slag%'-----------------------------------------------
            Dim strHrsGrnSlagSalesTWT_chkIN As String = "select "
            strHrsGrnSlagSalesTWT_chkIN = strHrsGrnSlagSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsGrnSlagSalesTWT_chkIN = strHrsGrnSlagSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsGrnSlagSalesTWT_chkIN = strHrsGrnSlagSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Gran%Slag%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsGrnSlagSalesTWT_chkIN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(11, 4) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(11, 4) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(11, 4) = 0
            End Try
            dr.Close()
            ''-----------------------'%Liquid%'-------------------------------
            Dim strHrsLiquidSalesTWT_chkIN As String = "select "
            strHrsLiquidSalesTWT_chkIN = strHrsLiquidSalesTWT_chkIN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsLiquidSalesTWT_chkIN = strHrsLiquidSalesTWT_chkIN & " (select H.GE_HDR_ID,F_WT_DateTime,IN_Date_Time, CASE WHEN (DATEDIFF(MINUTE,F_WT_DateTime,IN_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE, F_WT_DateTime,IN_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,F_WT_DateTime,IN_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsLiquidSalesTWT_chkIN = strHrsLiquidSalesTWT_chkIN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsLiquidSalesTWT_chkIN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(12, 4) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(12, 4) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(12, 4) = 0
            End Try
            dr.Close()

            '----------------------------CHK OUT & S_WT------------------------------------------------------
            '-----------PIG IRON--------------
            Dim strHrsPigIronSales_chkOUT_SWT As String = "select "
            strHrsPigIronSales_chkOUT_SWT = strHrsPigIronSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsPigIronSales_chkOUT_SWT = strHrsPigIronSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsPigIronSales_chkOUT_SWT = strHrsPigIronSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A  "

            dr = cc.GetDataReader(strHrsPigIronSales_chkOUT_SWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(6, 5) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(6, 5) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(6, 5) = 0
            End Try
            dr.Close()
            ''--------Billet-----------
            Dim strHrsBilletSales_chkOUT_SWT As String = "select "
            strHrsBilletSales_chkOUT_SWT = strHrsBilletSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsBilletSales_chkOUT_SWT = strHrsBilletSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsBilletSales_chkOUT_SWT = strHrsBilletSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A  "

            dr = cc.GetDataReader(strHrsBilletSales_chkOUT_SWT)

            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(7, 5) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(7, 5) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(7, 5) = 0
            End Try
            dr.Close()
            ''------------'%Rebar%'--------------------
            Dim strHrsRebarSales_chkOUT_SWT As String = "select "
            strHrsRebarSales_chkOUT_SWT = strHrsRebarSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsRebarSales_chkOUT_SWT = strHrsRebarSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsRebarSales_chkOUT_SWT = strHrsRebarSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A  "

            dr = cc.GetDataReader(strHrsRebarSales_chkOUT_SWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(8, 5) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(8, 5) = 0
                    End If

                End While
            Catch ex As Exception
                xlsheet.Cells(8, 5) = 0
            End Try
            dr.Close()
            ''---------Wire Rod Mill----------
            Dim strHrsWireSales_chkOUT_SWT As String = "select "
            strHrsWireSales_chkOUT_SWT = strHrsWireSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsWireSales_chkOUT_SWT = strHrsWireSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsWireSales_chkOUT_SWT = strHrsWireSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A  "

            dr = cc.GetDataReader(strHrsWireSales_chkOUT_SWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(9, 5) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(9, 5) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(9, 5) = 0
            End Try
            dr.Close()
            ''--------DI---------
            Dim strHrsDISales_chkOUT_SWT As String = "select "
            strHrsDISales_chkOUT_SWT = strHrsDISales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsDISales_chkOUT_SWT = strHrsDISales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsDISales_chkOUT_SWT = strHrsDISales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A  "

            dr = cc.GetDataReader(strHrsDISales_chkOUT_SWT)
            Try
                While dr.Read

                    If dr.HasRows Then
                        xlsheet.Cells(10, 5) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(10, 5) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(10, 5) = 0
            End Try
            dr.Close()

            ''---------------------'%Gran%Slag%'-----------------------------------------------
            Dim strHrsGrnSlagSales_chkOUT_SWT As String = "select "
            strHrsGrnSlagSales_chkOUT_SWT = strHrsGrnSlagSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsGrnSlagSales_chkOUT_SWT = strHrsGrnSlagSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsGrnSlagSales_chkOUT_SWT = strHrsGrnSlagSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Gran%Slag%' and S_WT > 0)A  "

            dr = cc.GetDataReader(strHrsGrnSlagSales_chkOUT_SWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(11, 5) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(11, 5) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(11, 5) = 0
            End Try
            dr.Close()
            ''-----------------------'%Liquid%'-------------------------------
            Dim strHrsLiquidSales_chkOUT_SWT As String = "select "
            strHrsLiquidSales_chkOUT_SWT = strHrsLiquidSales_chkOUT_SWT & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsLiquidSales_chkOUT_SWT = strHrsLiquidSales_chkOUT_SWT & " (select H.GE_HDR_ID,S_WT_DateTime,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.OUT_Date_Time,S_WT_DateTime)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.OUT_Date_Time,S_WT_DateTime)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsLiquidSales_chkOUT_SWT = strHrsLiquidSales_chkOUT_SWT & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A  "

            dr = cc.GetDataReader(strHrsLiquidSales_chkOUT_SWT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(12, 5) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(12, 5) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(12, 5) = 0
            End Try
            dr.Close()

            '----------------------------CHK IN & CHK OUT------------------------------------------------------
            '-----------PIG IRON--------------
            Dim strHrsPigIronSales_chkOUT_IN As String = "select "
            strHrsPigIronSales_chkOUT_IN = strHrsPigIronSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsPigIronSales_chkOUT_IN = strHrsPigIronSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsPigIronSales_chkOUT_IN = strHrsPigIronSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Pig%Iron%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsPigIronSales_chkOUT_IN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(6, 6) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(6, 6) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(6, 6) = 0
            End Try
            dr.Close()
            ''--------Billet-----------
            Dim strHrsBilletSales_chkOUT_IN As String = "select "
            strHrsBilletSales_chkOUT_IN = strHrsBilletSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsBilletSales_chkOUT_IN = strHrsBilletSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsBilletSales_chkOUT_IN = strHrsBilletSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Billet%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsBilletSales_chkOUT_IN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(7, 6) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(7, 6) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(7, 6) = 0
            End Try
            dr.Close()
            ''------------'%Rebar%'--------------------
            Dim strHrsRebarSales_chkOUT_IN As String = "select "
            strHrsRebarSales_chkOUT_IN = strHrsRebarSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsRebarSales_chkOUT_IN = strHrsRebarSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsRebarSales_chkOUT_IN = strHrsRebarSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Rebar%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsRebarSales_chkOUT_IN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(8, 6) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(8, 6) = 0
                    End If

                End While
            Catch ex As Exception
                xlsheet.Cells(8, 6) = 0
            End Try
            dr.Close()
            ''---------Wire Rod Mill----------
            Dim strHrsWireSSales_chkOUT_IN As String = "select "
            strHrsWireSSales_chkOUT_IN = strHrsWireSSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsWireSSales_chkOUT_IN = strHrsWireSSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsWireSSales_chkOUT_IN = strHrsWireSSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Wire%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsWireSSales_chkOUT_IN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(9, 6) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(9, 6) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(9, 6) = 0
            End Try
            dr.Close()
            ''--------DI---------
            Dim strHrsDISSales_chkOUT_IN As String = "select "
            strHrsDISSales_chkOUT_IN = strHrsDISSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsDISSales_chkOUT_IN = strHrsDISSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsDISSales_chkOUT_IN = strHrsDISSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%DI%PIP%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsDISSales_chkOUT_IN)
            Try
                While dr.Read

                    If dr.HasRows Then
                        xlsheet.Cells(10, 6) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(10, 6) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(10, 6) = 0
            End Try
            dr.Close()

            ''---------------------'%Gran%Slag%'-----------------------------------------------
            Dim strHrsGrnSlagSales_chkOUT_IN As String = "select "
            strHrsGrnSlagSales_chkOUT_IN = strHrsGrnSlagSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsGrnSlagSales_chkOUT_IN = strHrsGrnSlagSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsGrnSlagSales_chkOUT_IN = strHrsGrnSlagSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Gran%Slag%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsGrnSlagSales_chkOUT_IN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(11, 6) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(11, 6) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(11, 6) = 0
            End Try
            dr.Close()
            ''-----------------------'%Liquid%'-------------------------------
            Dim strHrsLiquidSales_chkOUT_IN As String = "select "
            strHrsLiquidSales_chkOUT_IN = strHrsLiquidSales_chkOUT_IN & " sum( convert(bigint,(cast(left(right(TATDiff,2),2) AS bigint)*60+  cast(left(TATDiff,len(TATDiff)-3) AS bigint)*3600)/60.0)),count(*) from"
            strHrsLiquidSales_chkOUT_IN = strHrsLiquidSales_chkOUT_IN & " (select H.GE_HDR_ID,CHK.IN_Date_Time,CHK.OUT_Date_Time, CASE WHEN (DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time) ) > 0  THEN concat((DATEDIFF(MINUTE,CHK.IN_Date_Time,CHK.OUT_Date_Time)/60),':',right('00' + convert(varchar(2),(DATEDIFF(minute,CHK.IN_Date_Time,CHK.OUT_Date_Time)%60)),2)) Else ''  END as   'TATDiff' from tbl_GE_Hdr H,tbl_GE_Det D,tbl_Allowed_Route_CheckPost_Det CHK where H.GE_HDR_ID = D.GE_HDR_ID and H.GE_HDR_ID = CHK.GE_HDR_ID and H.Type_Of_Vehicle = 'SALES'"
            strHrsLiquidSales_chkOUT_IN = strHrsLiquidSales_chkOUT_IN & " and D.F_WT_DateTime <> '' and  convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019' and D.Mat_Desc like  '%Liquid%' and S_WT > 0)A "

            dr = cc.GetDataReader(strHrsLiquidSales_chkOUT_IN)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(12, 6) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(12, 6) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(12, 6) = 0
            End Try
            dr.Close()

            '            select
            'sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from
            '(select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Pig%Iron%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A
            '----------------------------Invoice & Out ------------------------------------------------------
            '-----------PIG IRON--------------
            Dim strHrsPigIronSales_Inv_OUT As String = "select "
            strHrsPigIronSales_Inv_OUT = strHrsPigIronSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsPigIronSales_Inv_OUT = strHrsPigIronSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Pig%Iron%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsPigIronSales_Inv_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(6, 8) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(6, 8) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(6, 8) = 0
            End Try
            dr.Close()
            ''--------Billet-----------
            Dim strHrsBilletSales_Inv_OUT As String = "select "
            strHrsBilletSales_Inv_OUT = strHrsBilletSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsBilletSales_Inv_OUT = strHrsBilletSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Billet%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsBilletSales_Inv_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(7, 8) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(7, 8) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(7, 8) = 0
            End Try
            dr.Close()
            ''------------'%Rebar%'--------------------
            Dim strHrsRebarSales_Inv_OUT As String = "select "
            strHrsRebarSales_Inv_OUT = strHrsRebarSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsRebarSales_Inv_OUT = strHrsRebarSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Rebar%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsRebarSales_Inv_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(8, 8) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(8, 8) = 0
                    End If

                End While
            Catch ex As Exception
                xlsheet.Cells(8, 8) = 0
            End Try
            dr.Close()
            ''---------Wire Rod Mill----------
            Dim strHrsWireSales_Inv_OUT As String = "select "
            strHrsWireSales_Inv_OUT = strHrsWireSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsWireSales_Inv_OUT = strHrsWireSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Wire%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsWireSales_Inv_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(9, 8) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(9, 8) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(9, 8) = 0
            End Try
            dr.Close()
            ''--------DI---------
            Dim strHrsDISales_Inv_OUT As String = "select "
            strHrsDISales_Inv_OUT = strHrsDISales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsDISales_Inv_OUT = strHrsDISales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%DI%PIP%'and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsDISales_Inv_OUT)
            Try
                While dr.Read

                    If dr.HasRows Then
                        xlsheet.Cells(10, 8) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(10, 8) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(10, 8) = 0
            End Try
            dr.Close()

            ''---------------------'%Gran%Slag%'-----------------------------------------------
            ''--------DI---------
            Dim strHrsGrnSlagSales_Inv_OUT As String = "select "
            strHrsGrnSlagSales_Inv_OUT = strHrsGrnSlagSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsGrnSlagSales_Inv_OUT = strHrsGrnSlagSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like  '%Gran%Slag%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsGrnSlagSales_Inv_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(11, 8) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(11, 8) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(11, 8) = 0
            End Try
            dr.Close()
            ''-----------------------'%Liquid%'-------------------------------
            Dim strHrsLiquidSales_Inv_OUT As String = "select "
            strHrsLiquidSales_Inv_OUT = strHrsLiquidSales_Inv_OUT & " sum( convert(bigint,(cast(left(Invoice_Out_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_Out_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsLiquidSales_Inv_OUT = strHrsLiquidSales_Inv_OUT & " (select Invoice_Out_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like  '%Liquid%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsLiquidSales_Inv_OUT)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(12, 8) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(12, 8) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(12, 8) = 0
            End Try
            dr.Close()

            '            select
            'sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from
            '(select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Pig%Iron%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A
            '--------------------------SWT_Invoice------------------------------------------------------
            '-----------PIG IRON--------------
            Dim strHrsPigIronSales_SWT_INV As String = "select "
            strHrsPigIronSales_SWT_INV = strHrsPigIronSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsPigIronSales_SWT_INV = strHrsPigIronSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Pig%Iron%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsPigIronSales_SWT_INV)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(6, 7) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(6, 7) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(6, 7) = 0
            End Try
            dr.Close()
            ''--------Billet-----------
            Dim strHrsBilletSales_SWT_INV As String = "select "
            strHrsBilletSales_SWT_INV = strHrsBilletSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsBilletSales_SWT_INV = strHrsBilletSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Billet%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsBilletSales_SWT_INV)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(7, 7) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(7, 7) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(7, 7) = 0
            End Try
            dr.Close()
            ''------------'%Rebar%'--------------------
            Dim strHrsRebarSales_SWT_INV As String = "select "
            strHrsRebarSales_SWT_INV = strHrsRebarSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsRebarSales_SWT_INV = strHrsRebarSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Rebar%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsRebarSales_SWT_INV)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(8, 7) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(8, 7) = 0
                    End If

                End While
            Catch ex As Exception
                xlsheet.Cells(8, 7) = 0
            End Try
            dr.Close()
            ''---------Wire Rod Mill----------
            Dim strHrsWireSales_SWT_INV As String = "select "
            strHrsWireSales_SWT_INV = strHrsWireSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsWireSales_SWT_INV = strHrsWireSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%Wire%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsWireSales_SWT_INV)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(9, 7) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(9, 7) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(9, 7) = 0
            End Try
            dr.Close()
            ''--------DI---------
            Dim strHrsDISales_SWT_INV As String = "select "
            strHrsDISales_SWT_INV = strHrsDISales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsDISales_SWT_INV = strHrsDISales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like '%DI%PIP%'and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsDISales_SWT_INV)
            Try
                While dr.Read

                    If dr.HasRows Then
                        xlsheet.Cells(10, 7) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)

                    Else
                        xlsheet.Cells(10, 7) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(10, 7) = 0
            End Try
            dr.Close()

            ''---------------------'%Gran%Slag%'-----------------------------------------------
            ''--------DI---------
            Dim strHrsGrnSlagSales_SWT_INV As String = "select "
            strHrsGrnSlagSales_SWT_INV = strHrsGrnSlagSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsGrnSlagSales_SWT_INV = strHrsGrnSlagSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like  '%Gran%Slag%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsGrnSlagSales_SWT_INV)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(11, 7) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(11, 7) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(11, 7) = 0
            End Try
            dr.Close()
            ''-----------------------'%Liquid%'-------------------------------
            Dim strHrsLiquidSales_SWT_INV As String = "select "
            strHrsLiquidSales_SWT_INV = strHrsLiquidSales_SWT_INV & " sum( convert(bigint,(cast(left(Invoice_SWT_TAT,2) AS bigint)*60)) + convert(bigint,(cast(left(right(Invoice_SWT_TAT, 5),2) AS bigint)))) as Min,count(*) as Tot from"
            strHrsLiquidSales_SWT_INV = strHrsLiquidSales_SWT_INV & " (select Invoice_SWT_TAT  from Electroway_Invoice_details INV,tbl_GE_Hdr H,tbl_GE_Det D where H.GE_HDR_ID = D.GE_HDR_ID and  H.GE_HDR_ID = INV.GE_HDR_ID and D.Mat_Desc like  '%Liquid%' and convert(date, H.OUT_DateTime) between '1 September, 2019' and '1 September, 2019') A"

            dr = cc.GetDataReader(strHrsLiquidSales_SWT_INV)
            Try
                While dr.Read
                    If dr.HasRows Then
                        xlsheet.Cells(12, 7) = CInt(CInt(dr(0).ToString / dr(1).ToString) / 60)
                    Else
                        xlsheet.Cells(12, 7) = 0
                    End If
                End While
            Catch ex As Exception
                xlsheet.Cells(12, 7) = 0
            End Try
            dr.Close()

        Catch ex As Exception

        End Try
        lblMessage.Text = ""
        lblMessage.Refresh()
    End Sub
End Class