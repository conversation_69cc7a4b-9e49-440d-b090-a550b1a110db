﻿Public Class frmDocumentDetails
    Dim cc As New Class1
    Private Sub frmDocumentDetails_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub
    Private Sub txtVehicleNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVehicleNo.KeyPress
        Dim ReadF As Boolean = False
        If AscW(e.KeyChar) = 13 Then
            dr = cc.GetDataReader("select * from tbl_DocumentsValidity where Vehicle_No = '" & Trim(txtVehicleNo.Text.Trim) & "'")
            Try
                While dr.Read
                    ReadF = True
                    dtRC.Text = dr(1).ToString
                    dtTax.Text = dr(2).ToString
                    dtInsurance.Text = dr(3).ToString
                    dtFitness.Text = dr(4).ToString
                    dtPollution.Text = dr(5).ToString
                    dtDL.Text = dr(6).ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
        If Convert.ToInt32(e.<PERSON><PERSON>har) = Keys.Enter Then
            If ReadF = False Then
                MessageBox.Show("Not Found!!")
            Else
                dtRC.Focus()
            End If
        End If
    End Sub

    Private Sub txtVehicleNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVehicleNo.TextChanged
        txtVehicleNo.Text = UCase(Trim(txtVehicleNo.Text))
        txtVehicleNo.SelectionStart = Len(txtVehicleNo.Text)
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        Dim str As String = "insert into tbl_DocumentsValidity values('" & txtVehicleNo.Text.Trim & "','" & dtRC.Text & "','" & dtTax.Text & "','" & dtInsurance.Text & "','" & dtFitness.Text & "','" & dtPollution.Text & "','" & dtDL.Text & "','" & UserId & "',getdate())"
        Try
            cc.Execute(str)
            MessageBox.Show("Successfully Updated!!")
        Catch ex As Exception
            str = "update tbl_DocumentsValidity set RC_Validity = '" & dtRC.Text & "',Tax_Validity='" & dtTax.Text & "',Insurance_Validity='" & dtInsurance.Text & "',Fitness_Validity='" & dtFitness.Text & "',Pollution_Validity='" & dtPollution.Text & "',DL_Validity='" & dtDL.Text & "',Updated_BY='" & UserId & "',Updated_DATE=getdate() where Vehicle_No = '" & txtVehicleNo.Text.Trim & "'"
            cc.Execute(str)
            MessageBox.Show("Successfully Updated!!")
        End Try
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtVehicleNo.Clear()
        dtRC.Text = Today.Date
        dtTax.Text = Today.Date
        dtInsurance.Text = Today.Date
        dtFitness.Text = Today.Date
        dtPollution.Text = Today.Date
        dtDL.Text = Today.Date
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub dtRC_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtRC.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtTax.Focus()
        End If
    End Sub

    Private Sub dtTax_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtTax.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtInsurance.Focus()
        End If
    End Sub

    Private Sub dtInsurance_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtInsurance.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtFitness.Focus()
        End If
    End Sub

    Private Sub dtFitness_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtFitness.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtPollution.Focus()
        End If
    End Sub

    Private Sub dtPollution_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtPollution.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtDL.Focus()
        End If
    End Sub

    Private Sub dtDL_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtDL.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            btnUpdate.Focus()
        End If
    End Sub
End Class