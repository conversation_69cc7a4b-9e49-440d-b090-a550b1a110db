﻿Public Class frmDriver
    Dim cc As New Class1
    Private Sub frmDriver_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        txtUpdatedBy.Text = User_ID
        txtLastUpdatedOn.Text = Format(Today.Date, "dd-MM-yyyy")
        loadGrid()
    End Sub
    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_Driver_mst"
            ds = cc.GetDataset(str)
            gvDriver.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtDriverName.Enabled = True
        Call Clear_all()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtDriverLicNo.Text) <> "" And Trim(txtDriverName.Text) <> "" Then
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_ins_tbl_Driver_mst"
            cm.Parameters.AddWithValue("@val_Driv_Lic_no", txtDriverLicNo.Text.Trim)
            cm.Parameters.AddWithValue("@val_Driv_Name", txtDriverName.Text.Trim)
            cm.Parameters.AddWithValue("@val_Blacklisted", cbBlackListed.Checked)
            cm.Parameters.AddWithValue("@val_Warning", cbWarned.Checked)
            cm.Parameters.AddWithValue("@val_Remarks", txtRemarks.Text.Trim)
            cm.Parameters.AddWithValue("@val_UpdateDate", Format(Today.Date, "dd-MMM-yyyy"))
            cm.Parameters.AddWithValue("@val_UpdatedBy", User_ID)
            cm.ExecuteNonQuery()
            loadGrid()
            MsgBox("Driver master updated successfully !", vbInformation, "Electrosteel Castings Limited.")
            txtDriverName.Enabled = True
            Call Clear_all()
        Else
            MsgBox("Invalid Driver Details data for master updation", vbInformation, "Electrosteel Castings Limited.")
        End If
    End Sub

    Private Sub txtDriverLicNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtDriverLicNo.KeyPress
        If AscW(e.KeyChar) = 13 Then
            ds = cc.GetDataset("select * from tbl_Driver_mst where Driv_LIC_no = '" & Trim(txtDriverLicNo.Text) & "'")
            Dim count As Integer = ds.Tables(0).Rows.Count
            For i As Integer = 0 To count - 1
                txtDriverName.Text = ds.Tables(0).Rows(i).Item("Driv_Name")
                txtLastUpdatedOn.Text = ds.Tables(0).Rows(i).Item("UpdateDate")
                txtUpdatedBy.Text = ds.Tables(0).Rows(i).Item("UpdatedBy")
                txtRemarks.Text = ds.Tables(0).Rows(i).Item("Remarks")
                cbBlackListed.Checked = ds.Tables(0).Rows(i).Item("BlackListed")
                cbWarned.Checked = ds.Tables(0).Rows(i).Item("Warning")
            Next
            If count = 0 Then
                MsgBox("Driver Details Not found !", vbInformation, "Electrosteel castings Limited.")
            End If
            ds.Dispose()
        End If
    End Sub
    Private Sub Clear_all()
        txtDriverLicNo.Text = ""
        txtLastUpdatedOn.Text = ""
        txtRemarks.Text = ""
        txtUpdatedBy.Text = ""
        txtDriverName.Text = ""
        cbBlackListed.Checked = 0
        cbWarned.Checked = 0
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Dim str As String = "select * from tbl_Driver_mst where Driv_LIC_no like '%" & Trim(txtSearch.Text) & "%' or Driv_Lic_ID like '%" & Trim(txtSearch.Text) & "%' or Driv_Name like '%" & Trim(txtSearch.Text) & "%'"
        Try
            ds = cc.GetDataset(str)
            gvDriver.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try

    End Sub

    Private Sub gvDriver_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvDriver.CellMouseClick
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvDriver.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvDriver.SelectedRows(i).Index)

                    txtDriverLicNo.Text = Convert.ToString(gvDriver.Rows(index).Cells(1).Value)
                    txtDriverName.Text = Convert.ToString(gvDriver.Rows(index).Cells(2).Value)
                    cbBlackListed.Checked = Convert.ToString(gvDriver.Rows(index).Cells(3).Value)
                    cbWarned.Checked = Convert.ToString(gvDriver.Rows(index).Cells(4).Value)
                    txtRemarks.Text = Convert.ToString(gvDriver.Rows(index).Cells(5).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
End Class