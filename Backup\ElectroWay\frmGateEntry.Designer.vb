﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmGateEntry
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim ListViewItem1 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("PO / SO No.")
        Dim ListViewItem2 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("DO No.")
        Dim ListViewItem3 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("DO/PO Line Item")
        Dim ListViewItem4 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("Material Code")
        Dim ListViewItem5 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("Material Description")
        Dim ListViewItem6 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("DO/Ch. Qty")
        Dim ListViewItem7 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("Unit")
        Dim ListViewItem8 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("SAP Gate Entry No.")
        Dim ListViewItem9 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("Ch. No.")
        Dim ListViewItem10 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("Challan Date")
        Dim ListViewItem11 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("RR No.")
        Dim ListViewItem12 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("RR. Date")
        Dim ListViewItem13 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("LR No.")
        Dim ListViewItem14 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("LR Date")
        Dim ListViewItem15 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("Rake No.")
        Dim ListViewItem16 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("SO Line Item")
        Dim ListViewItem17 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("Customer/Vendor")
        Dim ListViewItem18 As System.Windows.Forms.ListViewItem = New System.Windows.Forms.ListViewItem("Customer/Vendor Name")
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmGateEntry))
        Me.gbVehicleno = New System.Windows.Forms.GroupBox
        Me.TextBox18 = New System.Windows.Forms.TextBox
        Me.TextBox17 = New System.Windows.Forms.TextBox
        Me.Label25 = New System.Windows.Forms.Label
        Me.lblOut_EntryTime = New System.Windows.Forms.Label
        Me.lblOut_EntryDate = New System.Windows.Forms.Label
        Me.lblOutTime = New System.Windows.Forms.Label
        Me.lblOutDate = New System.Windows.Forms.Label
        Me.Label4 = New System.Windows.Forms.Label
        Me.txtCompany = New System.Windows.Forms.TextBox
        Me.Label3 = New System.Windows.Forms.Label
        Me.txtPlant = New System.Windows.Forms.TextBox
        Me.Label2 = New System.Windows.Forms.Label
        Me.txtGateNo = New System.Windows.Forms.TextBox
        Me.Label1 = New System.Windows.Forms.Label
        Me.txtVehicleNo = New System.Windows.Forms.TextBox
        Me.lblEntryTime = New System.Windows.Forms.Label
        Me.Label8 = New System.Windows.Forms.Label
        Me.gbActivity = New System.Windows.Forms.GroupBox
        Me.rbFLYASH = New System.Windows.Forms.RadioButton
        Me.rbPurchaseReturn = New System.Windows.Forms.RadioButton
        Me.rbSalesReturn = New System.Windows.Forms.RadioButton
        Me.rbInterDept = New System.Windows.Forms.RadioButton
        Me.rbGatePass = New System.Windows.Forms.RadioButton
        Me.rbCONTRACTORITEM = New System.Windows.Forms.RadioButton
        Me.RBSTOCKTRANSFEROUT = New System.Windows.Forms.RadioButton
        Me.rbSales = New System.Windows.Forms.RadioButton
        Me.rbPurchase = New System.Windows.Forms.RadioButton
        Me.gbSAPgateEntry = New System.Windows.Forms.GroupBox
        Me.txtChQty = New System.Windows.Forms.TextBox
        Me.Label21 = New System.Windows.Forms.Label
        Me.txtChNo = New System.Windows.Forms.TextBox
        Me.lblChNo = New System.Windows.Forms.Label
        Me.txtRRNo = New System.Windows.Forms.TextBox
        Me.Label19 = New System.Windows.Forms.Label
        Me.txtRakeNoGroupingRefName = New System.Windows.Forms.TextBox
        Me.ddlRakeNoGroupingRefCode = New System.Windows.Forms.ComboBox
        Me.Label18 = New System.Windows.Forms.Label
        Me.Label17 = New System.Windows.Forms.Label
        Me.Label16 = New System.Windows.Forms.Label
        Me.Label15 = New System.Windows.Forms.Label
        Me.txtTransporterName = New System.Windows.Forms.TextBox
        Me.Label14 = New System.Windows.Forms.Label
        Me.txtTransporterCode = New System.Windows.Forms.TextBox
        Me.lblSAPGateEntryNo = New System.Windows.Forms.Label
        Me.txtSAPGateEntryNo = New System.Windows.Forms.TextBox
        Me.GroupBox4 = New System.Windows.Forms.GroupBox
        Me.ListView1 = New System.Windows.Forms.ListView
        Me.ColumnHeader1 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader2 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader3 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader4 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader5 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader6 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader7 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader8 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader9 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader10 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader11 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader12 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader13 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader14 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader15 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader16 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader17 = New System.Windows.Forms.ColumnHeader
        Me.ColumnHeader18 = New System.Windows.Forms.ColumnHeader
        Me.GroupBox5 = New System.Windows.Forms.GroupBox
        Me.lblGateOut = New System.Windows.Forms.Label
        Me.lblRemarksGateOut = New System.Windows.Forms.Label
        Me.txtRemarksGateOut = New System.Windows.Forms.TextBox
        Me.lblGateIn = New System.Windows.Forms.Label
        Me.lblRemarksGateIn = New System.Windows.Forms.Label
        Me.txtRemarksGateIn = New System.Windows.Forms.TextBox
        Me.Label24 = New System.Windows.Forms.Label
        Me.txtDriverName = New System.Windows.Forms.TextBox
        Me.dtValidity = New System.Windows.Forms.DateTimePicker
        Me.Label23 = New System.Windows.Forms.Label
        Me.lblDL = New System.Windows.Forms.Label
        Me.txtDLNo = New System.Windows.Forms.TextBox
        Me.GroupBox6 = New System.Windows.Forms.GroupBox
        Me.gvRouteCheckPost = New System.Windows.Forms.DataGridView
        Me.Label29 = New System.Windows.Forms.Label
        Me.Label30 = New System.Windows.Forms.Label
        Me.GroupBox7 = New System.Windows.Forms.GroupBox
        Me.gvSearch = New System.Windows.Forms.DataGridView
        Me.GroupBox8 = New System.Windows.Forms.GroupBox
        Me.btnSearch = New System.Windows.Forms.Button
        Me.Label6 = New System.Windows.Forms.Label
        Me.Label5 = New System.Windows.Forms.Label
        Me.txtVehiclenoSearch = New System.Windows.Forms.TextBox
        Me.GroupBox9 = New System.Windows.Forms.GroupBox
        Me.btnViewGP = New System.Windows.Forms.Button
        Me.btnExit = New System.Windows.Forms.Button
        Me.btnCancel = New System.Windows.Forms.Button
        Me.btnUpdate = New System.Windows.Forms.Button
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Label22 = New System.Windows.Forms.Label
        Me.gbVehicleno.SuspendLayout()
        Me.gbActivity.SuspendLayout()
        Me.gbSAPgateEntry.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        Me.GroupBox5.SuspendLayout()
        Me.GroupBox6.SuspendLayout()
        CType(Me.gvRouteCheckPost, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox7.SuspendLayout()
        CType(Me.gvSearch, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox8.SuspendLayout()
        Me.GroupBox9.SuspendLayout()
        Me.SuspendLayout()
        '
        'gbVehicleno
        '
        Me.gbVehicleno.Controls.Add(Me.TextBox18)
        Me.gbVehicleno.Controls.Add(Me.TextBox17)
        Me.gbVehicleno.Controls.Add(Me.Label25)
        Me.gbVehicleno.Controls.Add(Me.lblOut_EntryTime)
        Me.gbVehicleno.Controls.Add(Me.lblOut_EntryDate)
        Me.gbVehicleno.Controls.Add(Me.lblOutTime)
        Me.gbVehicleno.Controls.Add(Me.lblOutDate)
        Me.gbVehicleno.Controls.Add(Me.Label4)
        Me.gbVehicleno.Controls.Add(Me.txtCompany)
        Me.gbVehicleno.Controls.Add(Me.Label3)
        Me.gbVehicleno.Controls.Add(Me.txtPlant)
        Me.gbVehicleno.Controls.Add(Me.Label2)
        Me.gbVehicleno.Controls.Add(Me.txtGateNo)
        Me.gbVehicleno.Controls.Add(Me.Label1)
        Me.gbVehicleno.Controls.Add(Me.txtVehicleNo)
        Me.gbVehicleno.Controls.Add(Me.lblEntryTime)
        Me.gbVehicleno.Controls.Add(Me.Label8)
        Me.gbVehicleno.Location = New System.Drawing.Point(13, 7)
        Me.gbVehicleno.Name = "gbVehicleno"
        Me.gbVehicleno.Size = New System.Drawing.Size(737, 85)
        Me.gbVehicleno.TabIndex = 0
        Me.gbVehicleno.TabStop = False
        '
        'TextBox18
        '
        Me.TextBox18.Location = New System.Drawing.Point(625, 73)
        Me.TextBox18.Name = "TextBox18"
        Me.TextBox18.Size = New System.Drawing.Size(103, 20)
        Me.TextBox18.TabIndex = 57
        Me.TextBox18.Visible = False
        '
        'TextBox17
        '
        Me.TextBox17.Location = New System.Drawing.Point(625, 47)
        Me.TextBox17.Name = "TextBox17"
        Me.TextBox17.Size = New System.Drawing.Size(103, 20)
        Me.TextBox17.TabIndex = 56
        Me.TextBox17.Visible = False
        '
        'Label25
        '
        Me.Label25.AutoSize = True
        Me.Label25.Location = New System.Drawing.Point(464, 59)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(10, 13)
        Me.Label25.TabIndex = 55
        Me.Label25.Text = "."
        '
        'lblOut_EntryTime
        '
        Me.lblOut_EntryTime.AutoSize = True
        Me.lblOut_EntryTime.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.lblOut_EntryTime.Location = New System.Drawing.Point(396, 58)
        Me.lblOut_EntryTime.Name = "lblOut_EntryTime"
        Me.lblOut_EntryTime.Size = New System.Drawing.Size(14, 13)
        Me.lblOut_EntryTime.TabIndex = 54
        Me.lblOut_EntryTime.Text = "#"
        '
        'lblOut_EntryDate
        '
        Me.lblOut_EntryDate.AutoSize = True
        Me.lblOut_EntryDate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.lblOut_EntryDate.Location = New System.Drawing.Point(76, 58)
        Me.lblOut_EntryDate.Name = "lblOut_EntryDate"
        Me.lblOut_EntryDate.Size = New System.Drawing.Size(14, 13)
        Me.lblOut_EntryDate.TabIndex = 53
        Me.lblOut_EntryDate.Text = "#"
        '
        'lblOutTime
        '
        Me.lblOutTime.AutoSize = True
        Me.lblOutTime.Location = New System.Drawing.Point(298, 59)
        Me.lblOutTime.Name = "lblOutTime"
        Me.lblOutTime.Size = New System.Drawing.Size(50, 13)
        Me.lblOutTime.TabIndex = 51
        Me.lblOutTime.Text = "Out Time"
        Me.lblOutTime.Visible = False
        '
        'lblOutDate
        '
        Me.lblOutDate.AutoSize = True
        Me.lblOutDate.Location = New System.Drawing.Point(14, 59)
        Me.lblOutDate.Name = "lblOutDate"
        Me.lblOutDate.Size = New System.Drawing.Size(50, 13)
        Me.lblOutDate.TabIndex = 49
        Me.lblOutDate.Text = "Out Date"
        Me.lblOutDate.Visible = False
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label4.Location = New System.Drawing.Point(555, 22)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(67, 17)
        Me.Label4.TabIndex = 48
        Me.Label4.Text = "Company"
        '
        'txtCompany
        '
        Me.txtCompany.Enabled = False
        Me.txtCompany.Location = New System.Drawing.Point(625, 21)
        Me.txtCompany.Name = "txtCompany"
        Me.txtCompany.Size = New System.Drawing.Size(103, 20)
        Me.txtCompany.TabIndex = 47
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label3.Location = New System.Drawing.Point(412, 25)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(40, 17)
        Me.Label3.TabIndex = 46
        Me.Label3.Text = "Plant"
        '
        'txtPlant
        '
        Me.txtPlant.Enabled = False
        Me.txtPlant.Location = New System.Drawing.Point(458, 22)
        Me.txtPlant.Name = "txtPlant"
        Me.txtPlant.Size = New System.Drawing.Size(90, 20)
        Me.txtPlant.TabIndex = 45
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label2.Location = New System.Drawing.Point(236, 25)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(65, 17)
        Me.Label2.TabIndex = 44
        Me.Label2.Text = "Gate No."
        '
        'txtGateNo
        '
        Me.txtGateNo.Enabled = False
        Me.txtGateNo.Location = New System.Drawing.Point(301, 22)
        Me.txtGateNo.Name = "txtGateNo"
        Me.txtGateNo.Size = New System.Drawing.Size(96, 20)
        Me.txtGateNo.TabIndex = 43
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label1.Location = New System.Drawing.Point(1, 22)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(80, 17)
        Me.Label1.TabIndex = 42
        Me.Label1.Text = "Vehicle No."
        '
        'txtVehicleNo
        '
        Me.txtVehicleNo.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtVehicleNo.Location = New System.Drawing.Point(86, 17)
        Me.txtVehicleNo.Name = "txtVehicleNo"
        Me.txtVehicleNo.Size = New System.Drawing.Size(149, 31)
        Me.txtVehicleNo.TabIndex = 41
        Me.ToolTip1.SetToolTip(Me.txtVehicleNo, "Input the Vehicle No")
        '
        'lblEntryTime
        '
        Me.lblEntryTime.AutoSize = True
        Me.lblEntryTime.Location = New System.Drawing.Point(329, 59)
        Me.lblEntryTime.Name = "lblEntryTime"
        Me.lblEntryTime.Size = New System.Drawing.Size(57, 13)
        Me.lblEntryTime.TabIndex = 52
        Me.lblEntryTime.Text = "Entry Time"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(13, 59)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(57, 13)
        Me.Label8.TabIndex = 50
        Me.Label8.Text = "Entry Date"
        '
        'gbActivity
        '
        Me.gbActivity.Controls.Add(Me.rbFLYASH)
        Me.gbActivity.Controls.Add(Me.rbPurchaseReturn)
        Me.gbActivity.Controls.Add(Me.rbSalesReturn)
        Me.gbActivity.Controls.Add(Me.rbInterDept)
        Me.gbActivity.Controls.Add(Me.rbGatePass)
        Me.gbActivity.Controls.Add(Me.rbCONTRACTORITEM)
        Me.gbActivity.Controls.Add(Me.RBSTOCKTRANSFEROUT)
        Me.gbActivity.Controls.Add(Me.rbSales)
        Me.gbActivity.Controls.Add(Me.rbPurchase)
        Me.gbActivity.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.gbActivity.Location = New System.Drawing.Point(13, 97)
        Me.gbActivity.Name = "gbActivity"
        Me.gbActivity.Size = New System.Drawing.Size(737, 65)
        Me.gbActivity.TabIndex = 1
        Me.gbActivity.TabStop = False
        Me.gbActivity.Text = "ACTIVITY"
        Me.ToolTip1.SetToolTip(Me.gbActivity, "Select the appropriate Activity")
        '
        'rbFLYASH
        '
        Me.rbFLYASH.AutoSize = True
        Me.rbFLYASH.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rbFLYASH.Location = New System.Drawing.Point(13, 36)
        Me.rbFLYASH.Name = "rbFLYASH"
        Me.rbFLYASH.Size = New System.Drawing.Size(69, 17)
        Me.rbFLYASH.TabIndex = 8
        Me.rbFLYASH.TabStop = True
        Me.rbFLYASH.Text = "FLY ASH"
        Me.rbFLYASH.UseVisualStyleBackColor = True
        '
        'rbPurchaseReturn
        '
        Me.rbPurchaseReturn.AutoSize = True
        Me.rbPurchaseReturn.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rbPurchaseReturn.Location = New System.Drawing.Point(584, 38)
        Me.rbPurchaseReturn.Name = "rbPurchaseReturn"
        Me.rbPurchaseReturn.Size = New System.Drawing.Size(140, 17)
        Me.rbPurchaseReturn.TabIndex = 7
        Me.rbPurchaseReturn.TabStop = True
        Me.rbPurchaseReturn.Text = "PUERCHASE RETURN"
        Me.rbPurchaseReturn.UseVisualStyleBackColor = True
        '
        'rbSalesReturn
        '
        Me.rbSalesReturn.AutoSize = True
        Me.rbSalesReturn.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rbSalesReturn.Location = New System.Drawing.Point(416, 38)
        Me.rbSalesReturn.Name = "rbSalesReturn"
        Me.rbSalesReturn.Size = New System.Drawing.Size(108, 17)
        Me.rbSalesReturn.TabIndex = 6
        Me.rbSalesReturn.TabStop = True
        Me.rbSalesReturn.Text = "SALES RETURN"
        Me.rbSalesReturn.UseVisualStyleBackColor = True
        '
        'rbInterDept
        '
        Me.rbInterDept.AutoSize = True
        Me.rbInterDept.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rbInterDept.Location = New System.Drawing.Point(244, 38)
        Me.rbInterDept.Name = "rbInterDept"
        Me.rbInterDept.Size = New System.Drawing.Size(136, 17)
        Me.rbInterDept.TabIndex = 5
        Me.rbInterDept.TabStop = True
        Me.rbInterDept.Text = "INTER DEPARTMENT"
        Me.rbInterDept.UseVisualStyleBackColor = True
        '
        'rbGatePass
        '
        Me.rbGatePass.AutoSize = True
        Me.rbGatePass.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rbGatePass.Location = New System.Drawing.Point(585, 13)
        Me.rbGatePass.Name = "rbGatePass"
        Me.rbGatePass.Size = New System.Drawing.Size(85, 17)
        Me.rbGatePass.TabIndex = 4
        Me.rbGatePass.TabStop = True
        Me.rbGatePass.Text = "GATE PASS"
        Me.rbGatePass.UseVisualStyleBackColor = True
        '
        'rbCONTRACTORITEM
        '
        Me.rbCONTRACTORITEM.AutoSize = True
        Me.rbCONTRACTORITEM.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rbCONTRACTORITEM.Location = New System.Drawing.Point(416, 13)
        Me.rbCONTRACTORITEM.Name = "rbCONTRACTORITEM"
        Me.rbCONTRACTORITEM.Size = New System.Drawing.Size(129, 17)
        Me.rbCONTRACTORITEM.TabIndex = 3
        Me.rbCONTRACTORITEM.TabStop = True
        Me.rbCONTRACTORITEM.Text = "CONTRACTOR ITEM"
        Me.rbCONTRACTORITEM.UseVisualStyleBackColor = True
        '
        'RBSTOCKTRANSFEROUT
        '
        Me.RBSTOCKTRANSFEROUT.AutoSize = True
        Me.RBSTOCKTRANSFEROUT.ForeColor = System.Drawing.SystemColors.ControlText
        Me.RBSTOCKTRANSFEROUT.Location = New System.Drawing.Point(244, 13)
        Me.RBSTOCKTRANSFEROUT.Name = "RBSTOCKTRANSFEROUT"
        Me.RBSTOCKTRANSFEROUT.Size = New System.Drawing.Size(154, 17)
        Me.RBSTOCKTRANSFEROUT.TabIndex = 2
        Me.RBSTOCKTRANSFEROUT.TabStop = True
        Me.RBSTOCKTRANSFEROUT.Text = "STOCK TRANSFER - OUT"
        Me.RBSTOCKTRANSFEROUT.UseVisualStyleBackColor = True
        '
        'rbSales
        '
        Me.rbSales.AutoSize = True
        Me.rbSales.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rbSales.Location = New System.Drawing.Point(129, 13)
        Me.rbSales.Name = "rbSales"
        Me.rbSales.Size = New System.Drawing.Size(59, 17)
        Me.rbSales.TabIndex = 1
        Me.rbSales.TabStop = True
        Me.rbSales.Text = "SALES"
        Me.rbSales.UseVisualStyleBackColor = True
        '
        'rbPurchase
        '
        Me.rbPurchase.AutoSize = True
        Me.rbPurchase.ForeColor = System.Drawing.SystemColors.ControlText
        Me.rbPurchase.Location = New System.Drawing.Point(13, 13)
        Me.rbPurchase.Name = "rbPurchase"
        Me.rbPurchase.Size = New System.Drawing.Size(84, 17)
        Me.rbPurchase.TabIndex = 0
        Me.rbPurchase.TabStop = True
        Me.rbPurchase.Text = "PURCHASE"
        Me.rbPurchase.UseVisualStyleBackColor = True
        '
        'gbSAPgateEntry
        '
        Me.gbSAPgateEntry.Controls.Add(Me.txtChQty)
        Me.gbSAPgateEntry.Controls.Add(Me.Label21)
        Me.gbSAPgateEntry.Controls.Add(Me.txtChNo)
        Me.gbSAPgateEntry.Controls.Add(Me.lblChNo)
        Me.gbSAPgateEntry.Controls.Add(Me.txtRRNo)
        Me.gbSAPgateEntry.Controls.Add(Me.Label19)
        Me.gbSAPgateEntry.Controls.Add(Me.txtRakeNoGroupingRefName)
        Me.gbSAPgateEntry.Controls.Add(Me.ddlRakeNoGroupingRefCode)
        Me.gbSAPgateEntry.Controls.Add(Me.Label18)
        Me.gbSAPgateEntry.Controls.Add(Me.Label17)
        Me.gbSAPgateEntry.Controls.Add(Me.Label16)
        Me.gbSAPgateEntry.Controls.Add(Me.Label15)
        Me.gbSAPgateEntry.Controls.Add(Me.txtTransporterName)
        Me.gbSAPgateEntry.Controls.Add(Me.Label14)
        Me.gbSAPgateEntry.Controls.Add(Me.txtTransporterCode)
        Me.gbSAPgateEntry.Controls.Add(Me.lblSAPGateEntryNo)
        Me.gbSAPgateEntry.Controls.Add(Me.txtSAPGateEntryNo)
        Me.gbSAPgateEntry.Location = New System.Drawing.Point(13, 164)
        Me.gbSAPgateEntry.Name = "gbSAPgateEntry"
        Me.gbSAPgateEntry.Size = New System.Drawing.Size(737, 117)
        Me.gbSAPgateEntry.TabIndex = 2
        Me.gbSAPgateEntry.TabStop = False
        '
        'txtChQty
        '
        Me.txtChQty.Location = New System.Drawing.Point(646, 86)
        Me.txtChQty.Name = "txtChQty"
        Me.txtChQty.Size = New System.Drawing.Size(85, 20)
        Me.txtChQty.TabIndex = 64
        '
        'Label21
        '
        Me.Label21.AutoSize = True
        Me.Label21.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label21.Location = New System.Drawing.Point(648, 64)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(98, 17)
        Me.Label21.TabIndex = 63
        Me.Label21.Text = "Ch. Qty. (Ton)"
        '
        'txtChNo
        '
        Me.txtChNo.Location = New System.Drawing.Point(573, 86)
        Me.txtChNo.Name = "txtChNo"
        Me.txtChNo.Size = New System.Drawing.Size(70, 20)
        Me.txtChNo.TabIndex = 62
        '
        'lblChNo
        '
        Me.lblChNo.AutoSize = True
        Me.lblChNo.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.lblChNo.Location = New System.Drawing.Point(575, 64)
        Me.lblChNo.Name = "lblChNo"
        Me.lblChNo.Size = New System.Drawing.Size(55, 17)
        Me.lblChNo.TabIndex = 61
        Me.lblChNo.Text = "Ch. No."
        '
        'txtRRNo
        '
        Me.txtRRNo.Location = New System.Drawing.Point(484, 86)
        Me.txtRRNo.Name = "txtRRNo"
        Me.txtRRNo.Size = New System.Drawing.Size(85, 20)
        Me.txtRRNo.TabIndex = 60
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label19.Location = New System.Drawing.Point(486, 64)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(54, 17)
        Me.Label19.TabIndex = 59
        Me.Label19.Text = "RR No."
        '
        'txtRakeNoGroupingRefName
        '
        Me.txtRakeNoGroupingRefName.Location = New System.Drawing.Point(234, 86)
        Me.txtRakeNoGroupingRefName.Name = "txtRakeNoGroupingRefName"
        Me.txtRakeNoGroupingRefName.Size = New System.Drawing.Size(244, 20)
        Me.txtRakeNoGroupingRefName.TabIndex = 58
        '
        'ddlRakeNoGroupingRefCode
        '
        Me.ddlRakeNoGroupingRefCode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.ddlRakeNoGroupingRefCode.FormattingEnabled = True
        Me.ddlRakeNoGroupingRefCode.Location = New System.Drawing.Point(14, 86)
        Me.ddlRakeNoGroupingRefCode.Name = "ddlRakeNoGroupingRefCode"
        Me.ddlRakeNoGroupingRefCode.Size = New System.Drawing.Size(208, 21)
        Me.ddlRakeNoGroupingRefCode.TabIndex = 57
        '
        'Label18
        '
        Me.Label18.AutoSize = True
        Me.Label18.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label18.Location = New System.Drawing.Point(241, 64)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(216, 17)
        Me.Label18.TabIndex = 56
        Me.Label18.Text = "RAKE NO. / Grouping Ref. Name"
        '
        'Label17
        '
        Me.Label17.AutoSize = True
        Me.Label17.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label17.Location = New System.Drawing.Point(13, 64)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(212, 17)
        Me.Label17.TabIndex = 55
        Me.Label17.Text = "RAKE NO. / Grouping Ref. Code"
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(192, Byte), Integer))
        Me.Label16.Location = New System.Drawing.Point(159, 39)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(14, 13)
        Me.Label16.TabIndex = 54
        Me.Label16.Text = "#"
        '
        'Label15
        '
        Me.Label15.AutoSize = True
        Me.Label15.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label15.Location = New System.Drawing.Point(477, 14)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(124, 17)
        Me.Label15.TabIndex = 48
        Me.Label15.Text = "Transporter Name"
        '
        'txtTransporterName
        '
        Me.txtTransporterName.Location = New System.Drawing.Point(480, 37)
        Me.txtTransporterName.Name = "txtTransporterName"
        Me.txtTransporterName.Size = New System.Drawing.Size(244, 20)
        Me.txtTransporterName.TabIndex = 47
        '
        'Label14
        '
        Me.Label14.AutoSize = True
        Me.Label14.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label14.Location = New System.Drawing.Point(342, 14)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(120, 17)
        Me.Label14.TabIndex = 46
        Me.Label14.Text = "Transporter Code"
        '
        'txtTransporterCode
        '
        Me.txtTransporterCode.Location = New System.Drawing.Point(345, 37)
        Me.txtTransporterCode.Name = "txtTransporterCode"
        Me.txtTransporterCode.Size = New System.Drawing.Size(129, 20)
        Me.txtTransporterCode.TabIndex = 45
        '
        'lblSAPGateEntryNo
        '
        Me.lblSAPGateEntryNo.AutoSize = True
        Me.lblSAPGateEntryNo.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.lblSAPGateEntryNo.Location = New System.Drawing.Point(10, 14)
        Me.lblSAPGateEntryNo.Name = "lblSAPGateEntryNo"
        Me.lblSAPGateEntryNo.Size = New System.Drawing.Size(133, 17)
        Me.lblSAPGateEntryNo.TabIndex = 44
        Me.lblSAPGateEntryNo.Text = "SAP Gate Entry No."
        '
        'txtSAPGateEntryNo
        '
        Me.txtSAPGateEntryNo.Location = New System.Drawing.Point(14, 37)
        Me.txtSAPGateEntryNo.Name = "txtSAPGateEntryNo"
        Me.txtSAPGateEntryNo.Size = New System.Drawing.Size(129, 20)
        Me.txtSAPGateEntryNo.TabIndex = 43
        Me.ToolTip1.SetToolTip(Me.txtSAPGateEntryNo, "Input the Unloading No")
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.ListView1)
        Me.GroupBox4.Location = New System.Drawing.Point(12, 281)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(737, 119)
        Me.GroupBox4.TabIndex = 3
        Me.GroupBox4.TabStop = False
        Me.ToolTip1.SetToolTip(Me.GroupBox4, "Fetch records from SAP")
        '
        'ListView1
        '
        Me.ListView1.Columns.AddRange(New System.Windows.Forms.ColumnHeader() {Me.ColumnHeader1, Me.ColumnHeader2, Me.ColumnHeader3, Me.ColumnHeader4, Me.ColumnHeader5, Me.ColumnHeader6, Me.ColumnHeader7, Me.ColumnHeader8, Me.ColumnHeader9, Me.ColumnHeader10, Me.ColumnHeader11, Me.ColumnHeader12, Me.ColumnHeader13, Me.ColumnHeader14, Me.ColumnHeader15, Me.ColumnHeader16, Me.ColumnHeader17, Me.ColumnHeader18})
        Me.ListView1.GridLines = True
        Me.ListView1.Items.AddRange(New System.Windows.Forms.ListViewItem() {ListViewItem1, ListViewItem2, ListViewItem3, ListViewItem4, ListViewItem5, ListViewItem6, ListViewItem7, ListViewItem8, ListViewItem9, ListViewItem10, ListViewItem11, ListViewItem12, ListViewItem13, ListViewItem14, ListViewItem15, ListViewItem16, ListViewItem17, ListViewItem18})
        Me.ListView1.Location = New System.Drawing.Point(6, 13)
        Me.ListView1.Name = "ListView1"
        Me.ListView1.Size = New System.Drawing.Size(725, 94)
        Me.ListView1.TabIndex = 8
        Me.ListView1.UseCompatibleStateImageBehavior = False
        '
        'ColumnHeader1
        '
        Me.ColumnHeader1.Text = "PO / SO No."
        '
        'ColumnHeader2
        '
        Me.ColumnHeader2.Text = "DO No."
        '
        'ColumnHeader3
        '
        Me.ColumnHeader3.Text = "DO/PO Line Item"
        '
        'ColumnHeader4
        '
        Me.ColumnHeader4.Text = "Material Code"
        '
        'ColumnHeader5
        '
        Me.ColumnHeader5.Text = "Material Description"
        '
        'ColumnHeader6
        '
        Me.ColumnHeader6.Text = "DO/Ch. Qty"
        '
        'ColumnHeader7
        '
        Me.ColumnHeader7.Text = "Unit"
        '
        'ColumnHeader8
        '
        Me.ColumnHeader8.Text = "SAP Gate Entry No."
        '
        'ColumnHeader9
        '
        Me.ColumnHeader9.Text = "Ch. No."
        '
        'ColumnHeader10
        '
        Me.ColumnHeader10.Text = "Challan Date"
        '
        'ColumnHeader11
        '
        Me.ColumnHeader11.Text = "RR No."
        '
        'ColumnHeader12
        '
        Me.ColumnHeader12.Text = "RR. Date"
        '
        'ColumnHeader13
        '
        Me.ColumnHeader13.Text = "LR No."
        '
        'ColumnHeader14
        '
        Me.ColumnHeader14.Text = "LR Date"
        '
        'ColumnHeader15
        '
        Me.ColumnHeader15.Text = "Rake No."
        '
        'ColumnHeader16
        '
        Me.ColumnHeader16.Text = "SO Line Item"
        '
        'ColumnHeader17
        '
        Me.ColumnHeader17.Text = "Customer/Vendor"
        '
        'ColumnHeader18
        '
        Me.ColumnHeader18.Text = "Customer/Vendor Name"
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.lblGateOut)
        Me.GroupBox5.Controls.Add(Me.lblRemarksGateOut)
        Me.GroupBox5.Controls.Add(Me.txtRemarksGateOut)
        Me.GroupBox5.Controls.Add(Me.lblGateIn)
        Me.GroupBox5.Controls.Add(Me.lblRemarksGateIn)
        Me.GroupBox5.Controls.Add(Me.txtRemarksGateIn)
        Me.GroupBox5.Controls.Add(Me.Label24)
        Me.GroupBox5.Controls.Add(Me.txtDriverName)
        Me.GroupBox5.Controls.Add(Me.dtValidity)
        Me.GroupBox5.Controls.Add(Me.Label23)
        Me.GroupBox5.Controls.Add(Me.lblDL)
        Me.GroupBox5.Controls.Add(Me.txtDLNo)
        Me.GroupBox5.Location = New System.Drawing.Point(13, 400)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(737, 92)
        Me.GroupBox5.TabIndex = 4
        Me.GroupBox5.TabStop = False
        '
        'lblGateOut
        '
        Me.lblGateOut.AutoSize = True
        Me.lblGateOut.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.lblGateOut.Location = New System.Drawing.Point(365, 64)
        Me.lblGateOut.Name = "lblGateOut"
        Me.lblGateOut.Size = New System.Drawing.Size(92, 17)
        Me.lblGateOut.TabIndex = 54
        Me.lblGateOut.Text = "(Gate - OUT)"
        '
        'lblRemarksGateOut
        '
        Me.lblRemarksGateOut.AutoSize = True
        Me.lblRemarksGateOut.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.lblRemarksGateOut.Location = New System.Drawing.Point(383, 47)
        Me.lblRemarksGateOut.Name = "lblRemarksGateOut"
        Me.lblRemarksGateOut.Size = New System.Drawing.Size(64, 17)
        Me.lblRemarksGateOut.TabIndex = 53
        Me.lblRemarksGateOut.Text = "Remarks"
        '
        'txtRemarksGateOut
        '
        Me.txtRemarksGateOut.Location = New System.Drawing.Point(462, 46)
        Me.txtRemarksGateOut.Multiline = True
        Me.txtRemarksGateOut.Name = "txtRemarksGateOut"
        Me.txtRemarksGateOut.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.txtRemarksGateOut.Size = New System.Drawing.Size(262, 35)
        Me.txtRemarksGateOut.TabIndex = 52
        '
        'lblGateIn
        '
        Me.lblGateIn.AutoSize = True
        Me.lblGateIn.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.lblGateIn.Location = New System.Drawing.Point(14, 64)
        Me.lblGateIn.Name = "lblGateIn"
        Me.lblGateIn.Size = New System.Drawing.Size(75, 17)
        Me.lblGateIn.TabIndex = 51
        Me.lblGateIn.Text = "(Gate - IN)"
        '
        'lblRemarksGateIn
        '
        Me.lblRemarksGateIn.AutoSize = True
        Me.lblRemarksGateIn.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.lblRemarksGateIn.Location = New System.Drawing.Point(14, 47)
        Me.lblRemarksGateIn.Name = "lblRemarksGateIn"
        Me.lblRemarksGateIn.Size = New System.Drawing.Size(64, 17)
        Me.lblRemarksGateIn.TabIndex = 50
        Me.lblRemarksGateIn.Text = "Remarks"
        '
        'txtRemarksGateIn
        '
        Me.txtRemarksGateIn.Location = New System.Drawing.Point(93, 46)
        Me.txtRemarksGateIn.Multiline = True
        Me.txtRemarksGateIn.Name = "txtRemarksGateIn"
        Me.txtRemarksGateIn.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.txtRemarksGateIn.Size = New System.Drawing.Size(262, 35)
        Me.txtRemarksGateIn.TabIndex = 49
        '
        'Label24
        '
        Me.Label24.AutoSize = True
        Me.Label24.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label24.Location = New System.Drawing.Point(451, 21)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(82, 17)
        Me.Label24.TabIndex = 48
        Me.Label24.Text = "Diver Name"
        '
        'txtDriverName
        '
        Me.txtDriverName.Location = New System.Drawing.Point(535, 20)
        Me.txtDriverName.Name = "txtDriverName"
        Me.txtDriverName.Size = New System.Drawing.Size(189, 20)
        Me.txtDriverName.TabIndex = 47
        '
        'dtValidity
        '
        Me.dtValidity.Location = New System.Drawing.Point(278, 20)
        Me.dtValidity.Name = "dtValidity"
        Me.dtValidity.Size = New System.Drawing.Size(145, 20)
        Me.dtValidity.TabIndex = 46
        '
        'Label23
        '
        Me.Label23.AutoSize = True
        Me.Label23.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label23.Location = New System.Drawing.Point(218, 19)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(53, 17)
        Me.Label23.TabIndex = 45
        Me.Label23.Text = "Validity"
        '
        'lblDL
        '
        Me.lblDL.AutoSize = True
        Me.lblDL.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.lblDL.Location = New System.Drawing.Point(13, 19)
        Me.lblDL.Name = "lblDL"
        Me.lblDL.Size = New System.Drawing.Size(56, 17)
        Me.lblDL.TabIndex = 44
        Me.lblDL.Text = "D.L No."
        '
        'txtDLNo
        '
        Me.txtDLNo.Location = New System.Drawing.Point(75, 16)
        Me.txtDLNo.Name = "txtDLNo"
        Me.txtDLNo.Size = New System.Drawing.Size(129, 20)
        Me.txtDLNo.TabIndex = 43
        '
        'GroupBox6
        '
        Me.GroupBox6.Controls.Add(Me.gvRouteCheckPost)
        Me.GroupBox6.Controls.Add(Me.Label29)
        Me.GroupBox6.Controls.Add(Me.Label30)
        Me.GroupBox6.Location = New System.Drawing.Point(13, 493)
        Me.GroupBox6.Name = "GroupBox6"
        Me.GroupBox6.Size = New System.Drawing.Size(423, 80)
        Me.GroupBox6.TabIndex = 5
        Me.GroupBox6.TabStop = False
        '
        'gvRouteCheckPost
        '
        Me.gvRouteCheckPost.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.gvRouteCheckPost.Location = New System.Drawing.Point(104, 9)
        Me.gvRouteCheckPost.Name = "gvRouteCheckPost"
        Me.gvRouteCheckPost.Size = New System.Drawing.Size(251, 63)
        Me.gvRouteCheckPost.TabIndex = 54
        '
        'Label29
        '
        Me.Label29.AutoSize = True
        Me.Label29.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label29.Location = New System.Drawing.Point(14, 36)
        Me.Label29.Name = "Label29"
        Me.Label29.Size = New System.Drawing.Size(89, 17)
        Me.Label29.TabIndex = 53
        Me.Label29.Text = "(Check Post)"
        '
        'Label30
        '
        Me.Label30.AutoSize = True
        Me.Label30.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label30.Location = New System.Drawing.Point(14, 19)
        Me.Label30.Name = "Label30"
        Me.Label30.Size = New System.Drawing.Size(46, 17)
        Me.Label30.TabIndex = 52
        Me.Label30.Text = "Route"
        '
        'GroupBox7
        '
        Me.GroupBox7.Controls.Add(Me.gvSearch)
        Me.GroupBox7.Controls.Add(Me.GroupBox8)
        Me.GroupBox7.Location = New System.Drawing.Point(756, 12)
        Me.GroupBox7.Name = "GroupBox7"
        Me.GroupBox7.Size = New System.Drawing.Size(352, 578)
        Me.GroupBox7.TabIndex = 6
        Me.GroupBox7.TabStop = False
        Me.GroupBox7.Text = "Vehicles Ready For OUT"
        '
        'gvSearch
        '
        Me.gvSearch.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.gvSearch.Location = New System.Drawing.Point(6, 73)
        Me.gvSearch.MultiSelect = False
        Me.gvSearch.Name = "gvSearch"
        Me.gvSearch.ReadOnly = True
        Me.gvSearch.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.gvSearch.Size = New System.Drawing.Size(335, 485)
        Me.gvSearch.TabIndex = 2
        '
        'GroupBox8
        '
        Me.GroupBox8.Controls.Add(Me.btnSearch)
        Me.GroupBox8.Controls.Add(Me.Label6)
        Me.GroupBox8.Controls.Add(Me.Label5)
        Me.GroupBox8.Controls.Add(Me.txtVehiclenoSearch)
        Me.GroupBox8.Location = New System.Drawing.Point(6, 16)
        Me.GroupBox8.Name = "GroupBox8"
        Me.GroupBox8.Size = New System.Drawing.Size(335, 51)
        Me.GroupBox8.TabIndex = 1
        Me.GroupBox8.TabStop = False
        '
        'btnSearch
        '
        Me.btnSearch.Location = New System.Drawing.Point(274, 19)
        Me.btnSearch.Name = "btnSearch"
        Me.btnSearch.Size = New System.Drawing.Size(51, 23)
        Me.btnSearch.TabIndex = 46
        Me.btnSearch.Text = "Search"
        Me.btnSearch.UseVisualStyleBackColor = True
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(257, 22)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(15, 13)
        Me.Label6.TabIndex = 45
        Me.Label6.Text = "%"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label5.Location = New System.Drawing.Point(8, 19)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(100, 17)
        Me.Label5.TabIndex = 44
        Me.Label5.Text = "Vehicle No.  %"
        '
        'txtVehiclenoSearch
        '
        Me.txtVehiclenoSearch.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.txtVehiclenoSearch.Font = New System.Drawing.Font("Microsoft Sans Serif", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.txtVehiclenoSearch.Location = New System.Drawing.Point(110, 12)
        Me.txtVehiclenoSearch.Name = "txtVehiclenoSearch"
        Me.txtVehiclenoSearch.Size = New System.Drawing.Size(143, 31)
        Me.txtVehiclenoSearch.TabIndex = 43
        '
        'GroupBox9
        '
        Me.GroupBox9.Controls.Add(Me.btnViewGP)
        Me.GroupBox9.Controls.Add(Me.btnExit)
        Me.GroupBox9.Controls.Add(Me.btnCancel)
        Me.GroupBox9.Controls.Add(Me.btnUpdate)
        Me.GroupBox9.Location = New System.Drawing.Point(477, 492)
        Me.GroupBox9.Name = "GroupBox9"
        Me.GroupBox9.Size = New System.Drawing.Size(273, 92)
        Me.GroupBox9.TabIndex = 7
        Me.GroupBox9.TabStop = False
        Me.GroupBox9.Text = "Action"
        '
        'btnViewGP
        '
        Me.btnViewGP.Location = New System.Drawing.Point(9, 50)
        Me.btnViewGP.Name = "btnViewGP"
        Me.btnViewGP.Size = New System.Drawing.Size(116, 30)
        Me.btnViewGP.TabIndex = 12
        Me.btnViewGP.Text = "View G.P. Details"
        Me.btnViewGP.UseVisualStyleBackColor = True
        '
        'btnExit
        '
        Me.btnExit.Location = New System.Drawing.Point(140, 50)
        Me.btnExit.Name = "btnExit"
        Me.btnExit.Size = New System.Drawing.Size(116, 30)
        Me.btnExit.TabIndex = 11
        Me.btnExit.Text = "Exit"
        Me.btnExit.UseVisualStyleBackColor = True
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(140, 16)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(116, 30)
        Me.btnCancel.TabIndex = 10
        Me.btnCancel.Text = "Cancel"
        Me.btnCancel.UseVisualStyleBackColor = True
        '
        'btnUpdate
        '
        Me.btnUpdate.Location = New System.Drawing.Point(9, 16)
        Me.btnUpdate.Name = "btnUpdate"
        Me.btnUpdate.Size = New System.Drawing.Size(116, 30)
        Me.btnUpdate.TabIndex = 9
        Me.btnUpdate.Text = "Update"
        Me.btnUpdate.UseVisualStyleBackColor = True
        '
        'Timer1
        '
        Me.Timer1.Enabled = True
        Me.Timer1.Interval = 1000
        '
        'Label22
        '
        Me.Label22.AutoSize = True
        Me.Label22.Location = New System.Drawing.Point(445, 531)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(15, 13)
        Me.Label22.TabIndex = 46
        Me.Label22.Text = "%"
        Me.Label22.Visible = False
        '
        'frmGateEntry
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(1115, 596)
        Me.Controls.Add(Me.Label22)
        Me.Controls.Add(Me.GroupBox9)
        Me.Controls.Add(Me.GroupBox7)
        Me.Controls.Add(Me.GroupBox6)
        Me.Controls.Add(Me.GroupBox5)
        Me.Controls.Add(Me.GroupBox4)
        Me.Controls.Add(Me.gbSAPgateEntry)
        Me.Controls.Add(Me.gbActivity)
        Me.Controls.Add(Me.gbVehicleno)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmGateEntry"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "ESL STEEL LIMITED - Gate Entry Application"
        Me.gbVehicleno.ResumeLayout(False)
        Me.gbVehicleno.PerformLayout()
        Me.gbActivity.ResumeLayout(False)
        Me.gbActivity.PerformLayout()
        Me.gbSAPgateEntry.ResumeLayout(False)
        Me.gbSAPgateEntry.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox5.ResumeLayout(False)
        Me.GroupBox5.PerformLayout()
        Me.GroupBox6.ResumeLayout(False)
        Me.GroupBox6.PerformLayout()
        CType(Me.gvRouteCheckPost, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox7.ResumeLayout(False)
        CType(Me.gvSearch, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox8.ResumeLayout(False)
        Me.GroupBox8.PerformLayout()
        Me.GroupBox9.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents gbVehicleno As System.Windows.Forms.GroupBox
    Friend WithEvents gbActivity As System.Windows.Forms.GroupBox
    Friend WithEvents gbSAPgateEntry As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox6 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox7 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox8 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox9 As System.Windows.Forms.GroupBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents txtGateNo As System.Windows.Forms.TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents txtVehicleNo As System.Windows.Forms.TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents txtCompany As System.Windows.Forms.TextBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents txtPlant As System.Windows.Forms.TextBox
    Friend WithEvents btnSearch As System.Windows.Forms.Button
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents txtVehiclenoSearch As System.Windows.Forms.TextBox
    Friend WithEvents lblOut_EntryTime As System.Windows.Forms.Label
    Friend WithEvents lblOut_EntryDate As System.Windows.Forms.Label
    Friend WithEvents lblEntryTime As System.Windows.Forms.Label
    Friend WithEvents lblOutTime As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents lblOutDate As System.Windows.Forms.Label
    Friend WithEvents rbGatePass As System.Windows.Forms.RadioButton
    Friend WithEvents rbCONTRACTORITEM As System.Windows.Forms.RadioButton
    Friend WithEvents RBSTOCKTRANSFEROUT As System.Windows.Forms.RadioButton
    Friend WithEvents rbSales As System.Windows.Forms.RadioButton
    Friend WithEvents rbPurchase As System.Windows.Forms.RadioButton
    Friend WithEvents rbPurchaseReturn As System.Windows.Forms.RadioButton
    Friend WithEvents rbSalesReturn As System.Windows.Forms.RadioButton
    Friend WithEvents rbInterDept As System.Windows.Forms.RadioButton
    Friend WithEvents lblSAPGateEntryNo As System.Windows.Forms.Label
    Friend WithEvents txtSAPGateEntryNo As System.Windows.Forms.TextBox
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents Label15 As System.Windows.Forms.Label
    Friend WithEvents txtTransporterName As System.Windows.Forms.TextBox
    Friend WithEvents Label14 As System.Windows.Forms.Label
    Friend WithEvents txtTransporterCode As System.Windows.Forms.TextBox
    Friend WithEvents ddlRakeNoGroupingRefCode As System.Windows.Forms.ComboBox
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents txtRakeNoGroupingRefName As System.Windows.Forms.TextBox
    Friend WithEvents txtChQty As System.Windows.Forms.TextBox
    Friend WithEvents Label21 As System.Windows.Forms.Label
    Friend WithEvents txtChNo As System.Windows.Forms.TextBox
    Friend WithEvents lblChNo As System.Windows.Forms.Label
    Friend WithEvents txtRRNo As System.Windows.Forms.TextBox
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents Label24 As System.Windows.Forms.Label
    Friend WithEvents txtDriverName As System.Windows.Forms.TextBox
    Friend WithEvents dtValidity As System.Windows.Forms.DateTimePicker
    Friend WithEvents Label23 As System.Windows.Forms.Label
    Friend WithEvents lblDL As System.Windows.Forms.Label
    Friend WithEvents txtDLNo As System.Windows.Forms.TextBox
    Friend WithEvents lblGateOut As System.Windows.Forms.Label
    Friend WithEvents lblRemarksGateOut As System.Windows.Forms.Label
    Friend WithEvents txtRemarksGateOut As System.Windows.Forms.TextBox
    Friend WithEvents lblGateIn As System.Windows.Forms.Label
    Friend WithEvents lblRemarksGateIn As System.Windows.Forms.Label
    Friend WithEvents txtRemarksGateIn As System.Windows.Forms.TextBox
    Friend WithEvents Label29 As System.Windows.Forms.Label
    Friend WithEvents Label30 As System.Windows.Forms.Label
    Friend WithEvents gvRouteCheckPost As System.Windows.Forms.DataGridView
    Friend WithEvents gvSearch As System.Windows.Forms.DataGridView
    Friend WithEvents btnViewGP As System.Windows.Forms.Button
    Friend WithEvents btnExit As System.Windows.Forms.Button
    Friend WithEvents btnCancel As System.Windows.Forms.Button
    Friend WithEvents btnUpdate As System.Windows.Forms.Button
    Friend WithEvents Timer1 As System.Windows.Forms.Timer
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents Label25 As System.Windows.Forms.Label
    Friend WithEvents TextBox17 As System.Windows.Forms.TextBox
    Friend WithEvents TextBox18 As System.Windows.Forms.TextBox
    Friend WithEvents ListView1 As System.Windows.Forms.ListView
    Friend WithEvents ColumnHeader1 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader2 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader3 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader4 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader5 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader6 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader7 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader8 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader9 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader10 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader11 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader12 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader13 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader14 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader15 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader16 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader17 As System.Windows.Forms.ColumnHeader
    Friend WithEvents ColumnHeader18 As System.Windows.Forms.ColumnHeader
    Friend WithEvents Label22 As System.Windows.Forms.Label
    Friend WithEvents rbFLYASH As System.Windows.Forms.RadioButton
End Class
