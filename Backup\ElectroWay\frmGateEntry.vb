﻿Imports System.Data
Imports System.Data.SqlClient
Imports System.Net.Mail

Public Class frmGateEntry
    Dim cc As New Class1
    Dim POO_NOO As String
    Dim POO_LIN_ITEMM As String
    Dim MateCode As String
    Dim MateName As String
    Dim Vend_cde As String
    Dim Vend_Name As String
    '--------------------------------

    ''*******************************
    Dim SAP_CON_NOT_AVAIL As Integer
    Dim j As Double

    Dim VehiclePictureName As String

    Dim Date_v As String
    Dim Time_v As String


    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    'Dim i As Double
    Dim i As Integer


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object
    Dim m As Integer
    Dim TRN_ID As Double
    ''*******************************
    Private Sub frmGateEntry_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
        txtVehicleNo.Focus()
    End Sub

    Private Sub frmGateEntry_Disposed(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Disposed
        'FreeLibrary(hLib)
        Try
            functionCtrl.Connection.Logoff()
            'functionCtr2.Connection.Logoff()
            sapConnection = Nothing
            functionCtrl = Nothing
            functionCtr2 = Nothing
        Catch ex As Exception

        End Try
       
    End Sub
    Private Sub frmGateEntry_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'sendMail("ESL\ES01\2016\162")
        ''------------------Practice---------------------
        'Dim XX As String
        'Call SAP_Con1()

        'If sapConnection.Logon(0, True) <> True Then
        '    MsgBox("No connection to SAP System .......")
        '    SAP_CON_NOT_AVAIL = 1
        '    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
        '    Exit Sub

        'Else

        '    Label25.Text = ""

        '    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
        '    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
        '    objQueryTab.Value = "LIPS"

        '    objOptTab = objRfcFunc.Tables("OPTIONS")
        '    objFldTab = objRfcFunc.Tables("FIELDS")
        '    objDatTab = objRfcFunc.Tables("DATA")

        '    objOptTab.FreeTable()

        '    objOptTab.Rows.Add()

        '    objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080032279'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

        '    objFldTab.FreeTable()

        '    objFldTab.Rows.Add()
        '    objFldTab(objFldTab.RowCount, "FIELDNAME") = "VTWEG"

        '    If objRfcFunc.Call = False Then
        '        MsgBox(objRfcFunc.Exception)
        '    End If

        '    i = 5

        '    If objDatTab.Rows.Count = 0 Then
        '        ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
        '    Else

        '        For Each objDatRec In objDatTab.Rows
        '            i = i + 1
        '            For Each objFldRec In objFldTab.Rows
        '                XX = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
        '            Next
        '        Next
        '    End If
        'End If

        '--------------------------------------------
        ds = cc.GetDataset("select * from tbl_node_mst where Node_name  = 'ROUTE/CHECK POST' order by Node_no")
        gvRouteCheckPost.DataSource = ds.Tables(0)
        ds.Dispose()
        ds.Clear()

        lblOut_EntryDate.Text = Format(Today.Date, "dd-MM-yyyy")
        lblOut_EntryTime.Text = TimeOfDay.ToString("hh:mm:ss tt")
        rbPurchase.Checked = True
        dtValidity.Value = Format(Today.Date, "dd-MMM-yyyy")
        '-------------------------------------------------------
        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        Try
            While dr.Read
                txtPlant.Text = dr("Plant_Name")
                txtGateNo.Text = dr("Node_No")
                txtCompany.Text = dr("Company_Code")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        lblChNo.Visible = True
        txtChNo.Visible = True
        '-------------------------------------------------
        'dt = cc.GetDataTable("select * from tbl_Reference_Mst where Disabled = '0' order by Reference_Code")
        dt = cc.GetDataTable("select '' as Reference_Code union select Reference_Code from tbl_Reference_Mst where Disabled = '0' order by Reference_Code")
        ddlRakeNoGroupingRefCode.DataSource = dt
        ddlRakeNoGroupingRefCode.DisplayMember = "Reference_Code"
        ddlRakeNoGroupingRefCode.ValueMember = "Reference_Code"
        '------------------------------
        filldllRakeNoGroupingRefNo()
        ''-----------ListView--------------
        Dim lvwItem As New ListViewItem()

        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False

        'Headings
        ListView1.Columns.Add("PO / SO No.", 250, HorizontalAlignment.Left)
        ListView1.Columns.Add("DO No.")
        ListView1.Columns.Add("DO/PO Line Item")
        ListView1.Columns.Add("Material Code")
        ListView1.Columns.Add("Material Description")
        ListView1.Columns.Add("DO/Ch Qty.")
        ListView1.Columns.Add("Unit")
        ListView1.Columns.Add("SAP Gate Entry No.")
        ListView1.Columns.Add("Ch. No.")
        ListView1.Columns.Add("Challan Date")
        ListView1.Columns.Add("RR No.")
        ListView1.Columns.Add("RR Date")
        ListView1.Columns.Add("LR No.")
        ListView1.Columns.Add("LR Date")
        ListView1.Columns.Add("Rake No.")
        ListView1.Columns.Add("SO Line Item")
        ListView1.Columns.Add("Customer/Vendor")
        ListView1.Columns.Add("Customer/Vendor Name")
        'ListView1.Items.Clear()


        '-----------------------------

        ''Dim lvwItem As New ListViewItem()
        'lvwItem.Text = ""
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")
        'lvwItem.SubItems.Add("")

        'ListView1.Items.Add(lvwItem)

        For i As Integer = 0 To ListView1.Columns.Count - 1
            ListView1.Columns(i).Width = -2
        Next
        ''ListView1.Items(0).SubItems(1).Text = "Bhram"
    End Sub
    Private Sub filldllRakeNoGroupingRefNo()
        Try
            txtRakeNoGroupingRefName.Text = ""

            ListView1.Items.Clear()
            txtRRNo.Text = ""

            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlRakeNoGroupingRefCode.Text) & "'")
            Try
                While dr.Read
                    txtRakeNoGroupingRefName.Text = dr("Reference_Name") & ""
                    txtRRNo.Text = dr("RR_NO") & ""

                    POO_NOO = dr("PO_NO")
                    POO_LIN_ITEMM = dr("PO_Line_Item")
                    MateCode = dr("Mat_Code")
                    MateName = dr("Mat_Desc")
                    Vend_cde = dr("Vendor_Code")
                    Vend_Name = dr("Vendor_Name")
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        Catch ex As Exception
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End Try
    End Sub

    Private Sub ddlRakeNoGroupingRefCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles ddlRakeNoGroupingRefCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtChNo.Focus()
        End If
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        SAP_Close1()
        '-----------------------------------
        Dim PrintFront As Boolean = False
        Dim Vehicle_NoF As String = txtVehicleNo.Text.Trim
        Dim GP_Veh_NO As String
        If ListView1.Items.Count < 1 Then
            Dim ans = MsgBox("Blank entry ... Are you sure you want to proceed?", vbYesNo, "ElectroWay")
            If ans = vbNo Then
                Exit Sub
                txtSAPGateEntryNo.Focus()
            End If

        End If

        If Trim(txtDLNo.Text) = "" Then
            MsgBox("Driver License No cannot be left blank.", vbInformation, "ElectroWay")
            txtDLNo.Text = ""
            txtDLNo.Focus()
            'Exit Sub
        End If
        'If Trim(txtDLNo.Text) <> "" Then
        '    Dim BlackListedFlag As Boolean = False
        '    Dim DriverBlackListstr As String = "select BlackListed from tbl_Driver_Mst where Driv_Lic_No = '" & txtDLNo.Text.Trim & "'"
        '    dr = cc.GetDataReader(DriverBlackListstr)
        '    Try
        '        While dr.Read
        '            BlackListedFlag = dr(0).ToString
        '        End While
        '    Catch ex As Exception

        '    End Try
        '    dr.Close()
        '    If BlackListedFlag = True Then
        '        MsgBox("Driver is Black Listed!", vbInformation, "ElectroWay")
        '        txtDLNo.Text = ""
        '        txtDLNo.Focus()
        '        Exit Sub
        '    End If
        'End If

        If Trim(txtTransporterName.Text) = "" Then
            Dim anss = MsgBox("Transporter Name not mentioned. Do you want to proceed ?", vbYesNo)
            If anss = vbNo Then
                txtTransporterCode.Text = ""
                txtTransporterName.Text = ""
                txtTransporterCode.Focus()
                Exit Sub
            End If

        End If

        '------------------------Used for Haldia----------------------
        'If Trim(txtPlant.Text) = "HLWK" Then
        '    ds = gvGateEntry.DataSource

        '    dr = cc.GetDataReader("select * from tbl_Vehicle_Transporter where Vehicle_no ='" & Trim(txtVehicleNo.Text) & "'")
        '    Try
        '        While dr.Read
        '            If dr("Vendor_Code") = Trim(ds.Tables(0).Rows(0).Item(16)) Or dr("Customer_Code") = Trim(ds.Tables(0).Rows(0).Item(16)) Then
        '                If dr("Transporter_Code") <> Trim(txtTransporterCode.Text) Then
        '                    Dim ans = MsgBox("This Vehicle is not mapped with the Specified Transporter, Do you want to proceed", vbYesNo, "ElectroWay")

        '                    If ans = vbNo Then
        '                        Exit Sub
        '                    End If
        '                End If
        '            End If
        '        End While
        '    Catch ex As Exception

        '    End Try
        '    dr.Close()
        'End If
        '-------------------------------------------------------------------

        If lblOutDate.Visible = False Then
            If Trim(txtVehicleNo.Text.Trim) = "" Then
                MsgBox("Vehicle Number cannot be blank !", vbInformation, "ElectroWay")
                txtVehicleNo.Text = ""
                txtVehicleNo.Focus()
                Exit Sub
            End If

            Dim TRN_ID As Double
            Dim DLPicName_Path As String
            Dim VehiclePicName_Path As String
            Dim BlueBookName_path As String

            DLPicName_Path = "C:\DLPicNameFolderName\"
            VehiclePicName_Path = "C:\VehiclePicNameFolderName\"
            BlueBookName_path = "C:\BlueBookNameFolderName\"

            ''UUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUU
            dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN'  and company_code = '" & Trim(txtCompany.Text) & "'")
            'and Plant_Code ='" & Trim(txtPlant.Text) & "'
            Try
                While dr.Read
                    MsgBox("Vehicle already  IN !", vbInformation, "Electrosteel Steels Limited.")
                    txtSAPGateEntryNo.Text = ""
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            ''UUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUU

            Dim TRANS_NAME, Trans_year As String
            dr = cc.GetDataReader("select TRAN_name , TRAN_YEAR from tbl_Trans_Mst where Plant_Code  = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' order by TRAN_YEAR Desc ")
            Try
                While dr.Read
                    TRANS_NAME = dr(0)
                    Trans_year = dr(1)

                End While
            Catch ex As Exception

            End Try
            dr.Close()

            Dim cm As New SqlCommand
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_ins_tbl_GE_Hdr"
            cm.Parameters.AddWithValue("@val_GE_HDR_ID", "") ''Trim(TRANS_NAME) & Trim(cStr(Trans_year)) & "\" & Trim(cStr(rec_no))
            cm.Parameters.AddWithValue("@val_Gate_No", Trim(txtGateNo.Text))
            cm.Parameters.AddWithValue("@val_Vehicle_No", Trim(txtVehicleNo.Text))
            If rbPurchase.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "PURCH")
            ElseIf rbSales.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "SALES")
            ElseIf RBSTOCKTRANSFEROUT.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "STKTROUT")
            ElseIf rbInterDept.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "INTRDEPT")
            ElseIf rbCONTRACTORITEM.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "CONTITEM")

            ElseIf rbSalesReturn.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "SALESRET")
            ElseIf rbPurchaseReturn.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "PURCHRET")
            ElseIf rbGatePass.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "GATEPASS")
            ElseIf rbFLYASH.Checked = True Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "FLYASH")
            End If

            cm.Parameters.AddWithValue("@val_Driver_name", Trim(txtDriverName.Text))
            cm.Parameters.AddWithValue("@val_Driver_LIC_no", Trim(txtDLNo.Text))
            'cm.Parameters.AddWithValue("@val_Driver_LIC_ValidUpto", Format(dtValidity.Value, "MM/dd/yyyy"))
            cm.Parameters.AddWithValue("@val_Driver_LIC_ValidUpto", Format(dtValidity.Value, "yyyy-MM-dd"))
            cm.Parameters.AddWithValue("@val_DL_Pic_name_Patch", DLPicName_Path & Trim(txtDLNo.Text))
            cm.Parameters.AddWithValue("@val_Vehicle_Pic_name_path", VehiclePicName_Path & Format(Today.Date, "ddMMyyyy"))
            cm.Parameters.AddWithValue("@val_Blue_book_Name_path", BlueBookName_path & Trim(txtVehicleNo.Text))
            cm.Parameters.AddWithValue("@val_Transpoter_Code", UCase(Trim(txtTransporterCode.Text)))
            cm.Parameters.AddWithValue("@val_TransporterName", UCase(Trim(txtTransporterName.Text)))
            cm.Parameters.AddWithValue("@val_Remarks_IN", Trim(txtRemarksGateIn.Text))
            cm.Parameters.AddWithValue("@val_Remarks_OUT", "")
            cm.Parameters.AddWithValue("@val_Vehicle_Status", "IN")
            cm.Parameters.AddWithValue("@val_Remarks_cancellation", "")
            cm.Parameters.AddWithValue("@val_Entry_DoneBy", User_ID)
            cm.Parameters.AddWithValue("@val_EntryDateTime", Format(Today.Date, "yyyy-MM-dd"))
            cm.Parameters.AddWithValue("@val_OUT_DateTime", "")
            cm.Parameters.AddWithValue("@val_Out_DoneBy", "")
            cm.Parameters.AddWithValue("@val_Plant", Trim(txtPlant.Text))
            cm.Parameters.AddWithValue("@val_Company_Code", Trim(txtCompany.Text))
            cm.Parameters.AddWithValue("@val_Transfer_REF_GE_HDR_ID", "")
            cm.Parameters.AddWithValue("@val_Seal_No", "")
            cm.Parameters.AddWithValue("@val_Party_Gross_WT", 0)
            cm.Parameters.AddWithValue("@val_Party_Tare_WT", 0)
            cm.Parameters.AddWithValue("@val_Party_Net_WT", 0)
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.ExecuteNonQuery()
            cm.Dispose()

            dr = cc.GetDataReader("select MAX(TRN_ID) from tbl_GE_Hdr where Vehicle_No ='" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN'")
            Try
                While dr.Read
                    TRN_ID = dr(0)
                End While
            Catch ex As Exception

            End Try
            dr.Close()

            Dim Transaction_No_hdr_ID As String = Trim(TRANS_NAME) & Trim((Trans_year)) & "\" & Trim((TRN_ID))
            Dim str As String = "update tbl_GE_HDR set GE_HDR_ID= '" & Trim(Transaction_No_hdr_ID) & "'   where TRN_ID ='" & TRN_ID & "'"
            cc.Execute(str)

            For i = 0 To ListView1.Items.Count - 1
                Dim cm1 As New SqlCommand
                cm1.Connection = con
                cm1.CommandType = CommandType.StoredProcedure
                cm1.CommandText = "sp_ins_tbl_GE_Det"
                cm1.Parameters.AddWithValue("@GE_HDR_TRAN_ID", TRN_ID)
                cm1.Parameters.AddWithValue("@val_GE_HDR_ID", Transaction_No_hdr_ID)
                If rbPurchase.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "PURCH")
                ElseIf rbSales.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "SALES")
                ElseIf RBSTOCKTRANSFEROUT.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "STKTROUT")
                ElseIf rbInterDept.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "INTRDEPT")
                ElseIf rbCONTRACTORITEM.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "CONTITEM")
                ElseIf rbSalesReturn.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "SALESRET")
                ElseIf rbPurchaseReturn.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "PURCHRET")
                ElseIf rbGatePass.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "GATEPASS")
                ElseIf rbFLYASH.Checked = True Then
                    cm1.Parameters.AddWithValue("@val_Type_Of_Vehicle", "FLYASH")

                End If
                cm1.Parameters.AddWithValue("@val_Unloading_No", ListView1.Items(i).SubItems(7).Text)

                If (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then
                    cm1.Parameters.AddWithValue("@val_PO_No", ListView1.Items(i).SubItems(0).Text)
                Else
                    cm1.Parameters.AddWithValue("@val_PO_No", "")
                End If
                If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True) Then
                    cm1.Parameters.AddWithValue("@val_PO_Line_Item", "")
                Else
                    cm1.Parameters.AddWithValue("@val_PO_Line_Item", ListView1.Items(i).SubItems(2).Text)
                End If

                cm1.Parameters.AddWithValue("@val_DO_No", ListView1.Items(i).SubItems(1).Text)

                If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True) Then
                    cm1.Parameters.AddWithValue("@val_DO_Line_Item", ListView1.Items(i).SubItems(2).Text)
                Else
                    cm1.Parameters.AddWithValue("@val_DO_Line_Item", "")
                End If

                If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True) Then
                    cm1.Parameters.AddWithValue("@val_SO_No", ListView1.Items(i).SubItems(0).Text)
                Else
                    cm1.Parameters.AddWithValue("@val_SO_No", "")
                End If

                cm1.Parameters.AddWithValue("@val_SO_Line_Item", ListView1.Items(i).SubItems(15).Text)
                cm1.Parameters.AddWithValue("@val_Mat_Code", ListView1.Items(i).SubItems(3).Text)
                cm1.Parameters.AddWithValue("@val_Mat_Desc", ListView1.Items(i).SubItems(4).Text)
                cm1.Parameters.AddWithValue("@val_Challan_Date", ListView1.Items(i).SubItems(9).Text)
                cm1.Parameters.AddWithValue("@val_Challan_No", ListView1.Items(i).SubItems(8).Text)
                cm1.Parameters.AddWithValue("@val_Challan_Qty", ListView1.Items(i).SubItems(5).Text)
                cm1.Parameters.AddWithValue("@val_UOM", ListView1.Items(i).SubItems(6).Text)
                cm1.Parameters.AddWithValue("@val_WayBill_No", ListView1.Items(i).SubItems(12).Text)
                If (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then
                    cm1.Parameters.AddWithValue("@val_Vendor_Code", ListView1.Items(i).SubItems(16).Text)
                Else
                    cm1.Parameters.AddWithValue("@val_Vendor_Code", "")
                End If
                If (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then
                    cm1.Parameters.AddWithValue("@val_Vendor_Name", ListView1.Items(i).SubItems(17).Text)
                Else
                    cm1.Parameters.AddWithValue("@val_Vendor_Name", "")
                End If

                If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True Or rbFLYASH.Checked = True) Then
                    cm1.Parameters.AddWithValue("@val_Customer_Code", ListView1.Items(i).SubItems(16).Text)
                Else
                    cm1.Parameters.AddWithValue("@val_Customer_Code", "")
                End If
                If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True Or rbFLYASH.Checked = True) Then
                    cm1.Parameters.AddWithValue("@val_Customer_Name", ListView1.Items(i).SubItems(17).Text)
                Else
                    cm1.Parameters.AddWithValue("@val_Customer_Name", "")
                End If
                cm1.Parameters.AddWithValue("@val_GatePass_No", ListView1.Items(0).SubItems(14).Text)
                cm1.Parameters.AddWithValue("@val_Unloading_Remarks", ListView1.Items(i).SubItems(13).Text)
                cm1.Parameters.AddWithValue("@val_CN_No", ListView1.Items(i).SubItems(10).Text)
                cm1.Parameters.AddWithValue("@val_CN_Date", ListView1.Items(i).SubItems(11).Text)
                cm1.Parameters.AddWithValue("@val_Grouping_Ref_Code", Trim(ddlRakeNoGroupingRefCode.Text))
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm1.ExecuteNonQuery()
                cm1.Dispose()
            Next
            Dim rec_no As String
            If ListView1.Items.Count = 0 Or SAP_CON_NOT_AVAIL = 1 Then
                Dim cm2 As New SqlCommand
                cm2.Connection = con
                cm2.CommandType = CommandType.StoredProcedure
                cm2.CommandText = "sp_ins_tbl_GE_Det"
                cm2.Parameters.AddWithValue("@GE_HDR_TRAN_ID", TRN_ID)
                'cm2.Parameters.AddWithValue("@val_GE_HDR_ID", TRANS_NAME & Trim(CStr(Trans_year)) & "\" & Trim(CStr(rec_no)))
                cm2.Parameters.AddWithValue("@val_GE_HDR_ID", Transaction_No_hdr_ID)
                If rbPurchase.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "PURCH")
                ElseIf rbSales.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "SALES")
                ElseIf RBSTOCKTRANSFEROUT.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "STKTROUT")
                ElseIf rbInterDept.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "INTRDEPT")
                ElseIf rbCONTRACTORITEM.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "CONTITEM")
                ElseIf rbSalesReturn.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "SALESRET")
                ElseIf rbPurchaseReturn.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "PURCHRET")

                ElseIf rbGatePass.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "GATEPASS")
                ElseIf rbFLYASH.Checked = True Then
                    cm2.Parameters.AddWithValue("@val_Type_Of_Vehicle", "FLYASH")
                End If


                cm2.Parameters.AddWithValue("@val_Unloading_No", "")

                If (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True) Then
                    cm2.Parameters.AddWithValue("@val_PO_No", "")
                Else
                    cm2.Parameters.AddWithValue("@val_PO_No", "")
                End If
                If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True) Then
                    cm2.Parameters.AddWithValue("@val_PO_Line_Item", "")
                Else
                    cm2.Parameters.AddWithValue("@val_PO_Line_Item", "")
                End If

                cm2.Parameters.AddWithValue("@val_DO_No", "")

                If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True) Then
                    cm2.Parameters.AddWithValue("@val_DO_Line_Item", "")
                Else
                    cm2.Parameters.AddWithValue("@val_DO_Line_Item", "")
                End If

                If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True) Then
                    cm2.Parameters.AddWithValue("@val_SO_No", "")
                Else
                    cm2.Parameters.AddWithValue("@val_SO_No", "")
                End If

                cm2.Parameters.AddWithValue("@val_SO_Line_Item", "")
                cm2.Parameters.AddWithValue("@val_Mat_Code", "")
                cm2.Parameters.AddWithValue("@val_Mat_Desc", "")
                cm2.Parameters.AddWithValue("@val_Challan_Date", "")
                cm2.Parameters.AddWithValue("@val_Challan_No", "")
                cm2.Parameters.AddWithValue("@val_Challan_Qty", 0)
                cm2.Parameters.AddWithValue("@val_UOM", "")
                cm2.Parameters.AddWithValue("@val_WayBill_No", "")
                cm2.Parameters.AddWithValue("@val_Vendor_Code", "")
                cm2.Parameters.AddWithValue("@val_Vendor_Name", "")
                cm2.Parameters.AddWithValue("@val_Customer_Code", "")
                cm2.Parameters.AddWithValue("@val_Customer_Name", "")
                cm2.Parameters.AddWithValue("@val_GatePass_No", "")
                cm2.Parameters.AddWithValue("@val_Unloading_Remarks", "")
                cm2.Parameters.AddWithValue("@val_CN_No", "")
                cm2.Parameters.AddWithValue("@val_CN_Date", "")
                cm2.Parameters.AddWithValue("@val_Grouping_Ref_Code", Trim(ddlRakeNoGroupingRefCode.Text))
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm2.ExecuteNonQuery()
                cm2.Dispose()
                SAP_CON_NOT_AVAIL = 0
            End If

            '''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
            dr = cc.GetDataReader("select * from tbl_Driver_mst where Driv_LIC_No = '" & Trim(txtDLNo.Text) & "'")
            Dim RdFlag As Boolean = False
            Try
                While dr.Read
                    RdFlag = True
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            If RdFlag = True Then
                Try
                    Dim cm3 As New SqlCommand
                    cm3.Connection = con
                    cm3.CommandType = CommandType.StoredProcedure
                    cm3.CommandText = "sp_ins_tbl_Driver_mst"
                    cm3.Parameters.AddWithValue("@val_Driv_Lic_no", Trim(txtDLNo.Text))
                    cm3.Parameters.AddWithValue("@val_Driv_Name", Trim(txtDriverName.Text))
                    cm3.Parameters.AddWithValue("@val_Blacklisted", "0")
                    cm3.Parameters.AddWithValue("@val_Warning", "0")
                    cm3.Parameters.AddWithValue("@val_Remarks", "")
                    cm3.Parameters.AddWithValue("@val_UpdateDate", Format(Today.Date, "dd-MM-yyyy"))
                    cm3.Parameters.AddWithValue("@val_UpdatedBy", User_ID)
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm3.ExecuteNonQuery()
                    cm3.Dispose()
                Catch ex As Exception

                End Try

            End If
            ''''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
            dt = TryCast(gvRouteCheckPost.DataSource, DataTable)

            For b As Integer = 0 To dt.Rows.Count - 1
                'If gvRouteCheckPost.ListItems(gl).Checked = True Then
                Dim cm4 As New SqlCommand
                cm4.Connection = con
                cm4.CommandType = CommandType.StoredProcedure
                cm4.CommandText = "sp_ins_Route_CheckPost_Allowed"
                ''cm4.Parameters.AddWithValue("@GE_HDR_TRAN_ID",TRN_ID
                cm4.Parameters.AddWithValue("@val_GE_HDR_ID", TRANS_NAME & Trim(CStr(Trans_year)) & "\" & Trim(CStr(rec_no)))
                'cm4.Parameters.AddWithValue("@val_Route_CheckPost_Code", ds.Tables(0).Rows(i).Item(1))
                cm4.Parameters.AddWithValue("@val_Route_CheckPost_Code", dt.Rows(b).Item(1))
                cm4.ExecuteNonQuery()
                cm4.Dispose()
                'End If
            Next

            Dim ansPrint = MsgBox("Vehicle IN Successfully ! Do you want to print GP Slip?", vbYesNo, "Electrosteel Castings Limited.")

            If ansPrint = vbYes Then
                'Me.Hide()
                PrintFront = True
                'GESlip_Print()
            End If

            Try
                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "update AutoChallanNo set AutoChallanNO = '" & Trim(ListView1.Items(0).SubItems(8).Text) & "' where TrukNo = '" & Trim(txtVehicleNo.Text) & "' and Active = 'Y'"
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.ExecuteNonQuery()
                cm.Dispose()
            Catch ex As Exception

            End Try
            'If Picture1.Picture <> 0 Then

            '    SavePicture(Picture1.Picture, VehicleImagePathServer & "\" & Trim(txtVehicleNo.Text) & TRN_ID & ".jpg")
            'End If

            'If picScan.Picture <> 0 Then

            '    SavePicture(picScan.Picture, BlueBookPathServer & "\" & Trim(txtVehicleNo.Text) & ".jpg")
            'End If

            'If DLScan.Picture <> 0 Then

            '    SavePicture(DLScan.Picture, DLICPathServer & "\" & Trim(txtDLNo.Text) & ".jpg")
            'End If

            'Picture1.Visible = True

            ''??????????????????????????????????????????????????????
            'Picture1.Picture = Nothing
            'picScan.Picture = Nothing
            'DLScan.Picture = Nothing

            'Image1.Picture = Nothing

            ListView1.Items.Clear()
            txtSAPGateEntryNo.Text = ""
            txtTransporterCode.Text = ""
            txtTransporterName.Text = ""
            txtDLNo.Text = ""
            txtDriverName.Text = ""
            txtRemarksGateIn.Text = ""
            txtRemarksGateOut.Text = ""
            txtVehicleNo.Text = ""
            txtChNo.Text = ""
            txtChQty.Text = ""
            txtRRNo.Text = ""
            txtRakeNoGroupingRefName.Text = ""

            ddlRakeNoGroupingRefCode.SelectedIndex = 0

            gbVehicleno.Enabled = True
            gbActivity.Enabled = True
            'If ans = vbYes Then
            'Else
            'End If

            Label25.Text = ""
            ''txtVehicleNo.Focus
        ElseIf lblOutDate.Visible = True Then

            Dim ans = MsgBox("Are you sure , you want to out this vehicle ?  Please confirm....", vbYesNo, "ElectroWay")

            If ans = vbYes Then

                ''kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk

                dr = cc.GetDataReader("select * from tbl_Allowed_Route_CheckPost_Det  where IN_Date_Time <> '' and OUT_Date_Time = '' and GE_HDR_ID  = '" & Trim(TextBox17.Text) & "'")
                Try
                    While dr.Read
                        MsgBox("Vehicle OUT not done from Check Post. Therefor you are not allowed to OUT this vehicle.", vbInformation, "ElectroWay")
                        dr.Close()
                        Exit Sub
                    End While
                Catch ex As Exception

                End Try
                dr.Close()
                ''KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK
                If rbFLYASH.Checked = True Then
                    Dim SecondWTF As Boolean = False
                    Dim strPurch As String = "select F_WT,S_WT,NET_WT from tbl_GE_Det where GE_HDR_ID  = '" & TextBox17.Text.Trim & "' and F_WT > 0"
                    dr = cc.GetDataReader(strPurch)
                    Try
                        While dr.Read
                            SecondWTF = True
                        End While
                    Catch ex As Exception

                    End Try
                    dr.Close()
                    If SecondWTF = True Then
                        Dim strPurch1 As String = "select F_WT,S_WT,NET_WT from tbl_GE_Det where GE_HDR_ID  = '" & TextBox17.Text.Trim & "' and S_WT > 0"
                        dr = cc.GetDataReader(strPurch1)
                        Try
                            While dr.Read
                                SecondWTF = False
                            End While
                        Catch ex As Exception

                        End Try
                        dr.Close()
                        If SecondWTF = True Then
                            MessageBox.Show("Second WT. not Found!")
                            Exit Sub
                        End If
                    End If
                End If
                ''^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

                If rbGatePass.Checked = True Then

                    Call SAP_Con1()

                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        SAP_CON_NOT_AVAIL = 1
                        Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                        Exit Sub
                    Else
                        Label25.Text = ""

                        ''*************************************************************************** start 111
                        'Dim objRfcFunc As Object
                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        objQueryTab.Value = "ZTM_HGATE_PASS"

                        objOptTab = objRfcFunc.Tables("OPTIONS")
                        objFldTab = objRfcFunc.Tables("FIELDS")
                        objDatTab = objRfcFunc.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        objOptTab.FreeTable()
                        'Then set values
                        objOptTab.Rows.Add()
                        ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                        ''objOptTab.Rows.Add
                        objOptTab(objOptTab.RowCount, "TEXT") = "VEHINO = '" & txtVehicleNo.Text & "'"

                        objFldTab.FreeTable()

                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VEHINO"

                        If objRfcFunc.Call = False Then
                            MsgBox(objRfcFunc.Exception)
                        End If

                        If objDatTab.Rows.Count = 0 Then
                            ''MsgBox "Invalid Vehicle !", vbInformation, "ElectroSteel Castings Limited"
                        Else

                            For Each objDatRec In objDatTab.Rows

                                For Each objFldRec In objFldTab.Rows

                                    GP_Veh_NO = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))

                                Next

                            Next
                        End If
                    End If



                    If GP_Veh_NO <> "" And GP_Veh_NO = Trim(txtVehicleNo.Text) Then

                        ''''
                    Else
                        MsgBox(" Gate Pass has not been created for this Vehicle....   Therefor the Vehicle is NOT PERMITTED to GO OUT .", vbCritical, "ElectroWay")
                        Exit Sub
                        '''''
                    End If
                End If
                ''^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                '--------------------------------------------
                If rbPurchase.Checked = True Or rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Then
                    Dim SecondWTF As Boolean = False
                    Dim strPurch As String = "select F_WT,S_WT,NET_WT from tbl_GE_Det where GE_HDR_ID  = '" & TextBox17.Text.Trim & "' and F_WT > 0"
                    dr = cc.GetDataReader(strPurch)
                    Try
                        While dr.Read
                            SecondWTF = True
                        End While
                    Catch ex As Exception

                    End Try
                    dr.Close()
                    If SecondWTF = True Then
                        Dim strPurch1 As String = "select F_WT,S_WT,NET_WT from tbl_GE_Det where GE_HDR_ID  = '" & TextBox17.Text.Trim & "' and S_WT > 0"
                        dr = cc.GetDataReader(strPurch1)
                        Try
                            While dr.Read
                                SecondWTF = False
                            End While
                        Catch ex As Exception

                        End Try
                        dr.Close()
                        If SecondWTF = True Then
                            MessageBox.Show("Second WT. not Found!")
                            Exit Sub
                        End If
                    End If
                End If
                '--------------------------------------------
                '--------------
                Dim HDR_ID As Boolean = searchSales_DO(TextBox17.Text)
                '--------------
                If HDR_ID = False Then

                    If (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True) Then
                        ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
                        Call SAP_Con1()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            Exit Sub

                        Else

                            For L_I = 0 To ListView1.Items.Count - 1
                                objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                                objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                'objQueryTab.Value = "J_1IEXCDTL"
                                objQueryTab.Value = "VBRP"
                                objOptTab = objRfcFunc.Tables("OPTIONS")
                                objFldTab = objRfcFunc.Tables("FIELDS")

                                objDatTab = objRfcFunc.Tables("DATA")
                                objOptTab.FreeTable()
                                objOptTab.Rows.Add()
                                objOptTab(objOptTab.RowCount, "TEXT") = "VGBEL = '" & Trim(ListView1.Items(L_I).SubItems(1).Text) & "'"
                                'objOptTab(objOptTab.RowCount, "TEXT") = "RDOC1 = '" & Trim(ListView1.Items(L_I).SubItems(1).Text) & "'" 'DO NO''' and TRUCK_NO ='" & txtVehicleNo.Text & "' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                objFldTab.FreeTable()

                                objFldTab.Rows.Add()
                                objFldTab(objFldTab.RowCount, "FIELDNAME") = "VBELN"
                                'objFldTab(objFldTab.RowCount, "FIELDNAME") = "EXNUM"

                                If objRfcFunc.Call = False Then
                                    MsgBox(objRfcFunc.Exception)
                                End If

                                If objDatTab.Rows.Count = 0 Then
                                    MsgBox("Commercial Invoice not made .... ! You are not allowed to OUT this vehicle", vbInformation, "ElectroSteel Castings Limited")
                                    'MsgBox("Excise Invoice not made .... ! You are not allowed to OUT this vehicle", vbInformation, "ElectroSteel Castings Limited")
                                    Exit Sub
                                Else

                                    For Each objDatRec In objDatTab.Rows
                                        i = i + 1
                                        For Each objFldRec In objFldTab.Rows
                                            j = j + 1

                                            Dim ExInvNo = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))

                                        Next
                                        j = 0
                                    Next
                                End If

                            Next L_I

                        End If
                    End If
                End If
                lblOutTime.Visible = False
                lblOutDate.Visible = False
                lblOut_EntryDate.Visible = True
                lblEntryTime.Visible = True
                Dim cm5 As New SqlCommand
                cm5.Connection = con
                cm5.CommandType = CommandType.StoredProcedure
                cm5.CommandText = "sp_upd_tbl_GE_HDR_GateOut"
                cm5.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(TextBox17.Text))
                cm5.Parameters.AddWithValue("@val_RemarksOut", Trim(txtRemarksGateOut.Text) & "")
                cm5.Parameters.AddWithValue("@val_DateTime", "")
                cm5.Parameters.AddWithValue("@val_OutDoneBy", User_ID)
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm5.ExecuteNonQuery()
                cm5.Dispose()

                MsgBox("Vehicle OUT successfully .....", vbInformation, "ElectroWay")
                'sendMail(TextBox17.Text.Trim)
                Dim cm6 As New SqlCommand
                cm6.Connection = con
                cm6.CommandType = CommandType.Text
                cm6.CommandText = "update AutoChallanNo set Active = 'N' where TrukNo = '" & Trim(txtVehicleNo.Text) & "' and Active = 'Y'"
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm6.ExecuteNonQuery()
                cm6.Dispose()

                Dim cm7 As New SqlCommand
                cm7.Connection = con
                cm7.CommandType = CommandType.Text
                cm7.CommandText = "update TRCK_ENT_TMP set TET_TRK_PR = 'N' where TET_TRK_NO = '" & Trim(txtVehicleNo.Text) & "' and TET_TRK_PR = 'Y'"
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm7.ExecuteNonQuery()
                cm7.Dispose()

                gvSearch.DataSource = Nothing
                ListView1.Items.Clear()
                txtSAPGateEntryNo.Text = ""
                txtTransporterCode.Text = ""
                txtTransporterName.Text = ""
                txtDLNo.Text = ""
                txtDriverName.Text = ""
                txtRemarksGateIn.Text = ""
                txtRemarksGateOut.Text = ""
                txtVehicleNo.Text = ""
                TextBox17.Text = ""
                TextBox18.Text = ""
                'txtDLNo0.Text = ""
                txtChNo.Text = ""

                gbVehicleno.Enabled = True
                gbActivity.Enabled = True
                gbSAPgateEntry.Enabled = True

                txtDLNo.Enabled = True
                txtDriverName.Enabled = True
                txtRemarksGateIn.Enabled = True

                dtValidity.Enabled = True


                lblChNo.Visible = False
                lblRemarksGateOut.Visible = False
                txtRemarksGateOut.Visible = False
                Label25.Text = ""
                'Picture1.Picture = Nothing
                'picScan.Picture = Nothing
                'DLScan.Picture = Nothing

                'Image1.Picture = Nothing
                '---------Mail for 12 HRS TAT-----------------
                'sendMail()
            End If
            '-----------OUT completed-------------------
        End If
        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        Try
            While dr.Read
                txtPlant.Text = dr("Plant_Name")
                txtGateNo.Text = dr("Node_No")
                txtCompany.Text = dr("Company_Code")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        txtVehicleNo.Focus()
        ''Call Command9_Click
err:
        If Err.Description <> "" Then
            MsgBox(Err.Number & Err.Description)
        End If

        ListView1.Items.Clear()
        txtSAPGateEntryNo.Text = ""
        txtTransporterCode.Text = ""
        txtTransporterName.Text = ""
        txtDLNo.Text = ""
        txtDriverName.Text = ""
        txtRemarksGateIn.Text = ""
        txtRemarksGateOut.Text = ""
        txtVehicleNo.Text = ""
        TextBox17.Text = ""
        TextBox18.Text = ""
        'txtDLNo0.Text = ""
        txtChNo.Text = ""
        ''txtDLNo4.Text = ""
        gbVehicleno.Enabled = True
        gbActivity.Enabled = True
        gbSAPgateEntry.Enabled = True

        txtDLNo.Enabled = True
        txtDriverName.Enabled = True
        txtRemarksGateIn.Enabled = True

        dtValidity.Enabled = True


        lblChNo.Visible = False
        lblRemarksGateOut.Visible = False
        txtRemarksGateOut.Visible = False
        Label25.Text = ""
        'Picture1.Picture = Nothing
        'picScan.Picture = Nothing
        'DLScan.Picture = Nothing

        'Image1.Picture = Nothing
        Err.Clear()
        '-----------------------
        If PrintFront = True Then
            GESlip_Print(Vehicle_NoF)
        End If
        txtVehicleNo.Text = ""
    End Sub
    Private Sub sendMail(ByVal GE_HDR_ID As String)
        Dim TATF As Boolean = False
        Dim Vehicle_No As String = ""
        Dim TATDiff As String = ""
        Dim str As String = "select  a.GE_HDR_ID as TransactionNo, a.Vehicle_No as VehicleNo, a.Type_Of_Vehicle as VehicleType , CASE WHEN (DATEDIFF(MINUTE, a.EntryDateTime ,a.OUT_DateTime) ) > 0 "
        str = str & " THEN concat((DATEDIFF(MINUTE, a.EntryDateTime, a.OUT_DateTime)/60),'.',(DATEDIFF(minute, a.EntryDateTime, a.OUT_DateTime)%60))  Else ''       END as   'TATDiff(HH:MM)' "
        str = str & " from tbl_GE_HDR a , tbl_GE_DET b"
        str = str & " where a.GE_HDR_ID = b.GE_HDR_ID and a.GE_HDR_ID = '" & GE_HDR_ID & "'"
        dr = cc.GetDataReader(str)
        Try
            While dr.Read
                If CDec(dr(3)) >= 12 Then
                    TATF = True
                    Vehicle_No = dr(1)
                    TATDiff = dr(3)
                End If
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        If TATF = False Then
            Exit Sub
        Else
            Try
                Dim Smtp_Server As New SmtpClient
                Dim e_mail As New MailMessage()
                'Dim attachment1 As System.Net.Mail.Attachment
                'Dim attachment2 As System.Net.Mail.Attachment
                'Dim attachment3 As System.Net.Mail.Attachment
                Smtp_Server.UseDefaultCredentials = False

                Smtp_Server.Port = 25
                Smtp_Server.EnableSsl = False
                Smtp_Server.Host = "************"
                e_mail = New MailMessage()
                e_mail.From = New MailAddress("<EMAIL>")

                'e_mail.To.Add("<EMAIL>")
                'e_mail.To.Add("<EMAIL>")
                e_mail.To.Add("<EMAIL>")
                e_mail.To.Add("<EMAIL>")
                e_mail.To.Add("<EMAIL>")
                e_mail.To.Add("<EMAIL>")
                '------------------------------------------
                'e_mail.CC.Add("<EMAIL>")
                e_mail.Bcc.Add("<EMAIL>")
                'e_mail.Bcc.Add("<EMAIL>")
                e_mail.Subject = "TAT Diff for more than 12 hrs"
                e_mail.IsBodyHtml = False

                Dim body As String = "To,\n "
                body = body & "\n "
                body = body & "Sir/Madam,\n "
                body = body & "\n "
                body = body & "Vehicle No - " & Vehicle_No & " , TransactionNo - " & GE_HDR_ID & " , TAT Diff is " & TATDiff & ""
                body = body & "\n "
                body = body & "\n "
                body = body & "Thanks & Regards\n "
                body = body & "\n "
                body = body & "For ELECTROSTEEL STEELS LIMITED\n "
                body = body & "\n "
                body = body & "\n "
                body = body & "This is system generated email, please do not reply.\n "
                body = body & "\n "
                body = body.Replace("\n ", Environment.NewLine)
                e_mail.Body = body
                'this line here excute correctly but if a user didd'nt attach  a file, sending fails..
                'i want to send even w/o an attach file..
                'attachment1 = New System.Net.Mail.Attachment(finalPath) 'file path

                'e_mail.Attachments.Add(attachment1) 'attachment
                'e_mail.Attachments.Add(attachment2)
                'e_mail.Attachments.Add(attachment3)
                Smtp_Server.Send(e_mail)
            Catch ex As Exception
                MsgBox(ex.Message)
            End Try
        End If
    End Sub
    Private Function searchSales_DO(ByVal TRAN_ID As String)
        Dim SALES_DOF As Boolean = False
        'Dim str As String = "select GE_HDR_ID from tbl_ge_det where GE_HDR_ID = '" & TRAN_ID & "' and Type_Of_Vehicle = 'SALES' and DO_No = ''"
        Dim str As String = "select GE_HDR_ID from tbl_ge_det where GE_HDR_ID = '" & TRAN_ID & "' and (Type_Of_Vehicle = 'SALES' OR Type_Of_Vehicle = 'STKTROUT') and DO_No = ''"
        dr = cc.GetDataReader(str)
        Try
            While dr.Read
                SALES_DOF = True
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        Return SALES_DOF
    End Function
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        If rbCONTRACTORITEM.Checked = True Then
            rbPurchase.Checked = True
        End If
        ListView1.Items.Clear()
        ddlRakeNoGroupingRefCode.SelectedIndex = 0
        txtSAPGateEntryNo.Text = ""
        txtVehicleNo.Text = ""
        'txtPlant.Text = ""
        txtTransporterCode.Text = ""
        txtTransporterName.Text = ""
        txtDLNo.Text = ""
        txtDriverName.Text = ""
        txtRemarksGateIn.Text = ""
        'txtPlant7.Text = ""
        'txtPlant8.Text = ""
        'txtDLNo0.Text = ""
        txtChNo.Text = ""
        'txtDLNo3.Text = ""
        txtRRNo.Text = ""
        txtRakeNoGroupingRefName.Text = ""


        txtDLNo.Enabled = True
        txtDriverName.Enabled = True
        txtRemarksGateIn.Enabled = True

        gbVehicleno.Enabled = True
        gbActivity.Enabled = True
        gbSAPgateEntry.Enabled = True
        SAP_CON_NOT_AVAIL = 0
        Label25.Text = ""

        lblOutTime.Visible = False
        lblOutDate.Visible = False


        lblOut_EntryDate.Visible = True
        lblEntryTime.Visible = True
        '------Added on 30April2016----------------
        lblOutDate.Visible = False
        Label8.Visible = True
        txtVehicleNo.Focus()
        txtDriverName.BackColor = Color.White
        '-----------------------
        lblChNo.Visible = False
        lblRemarksGateOut.Visible = False
        txtRemarksGateOut.Visible = False
        lblGateOut.Visible = False
        dtValidity.Enabled = True

        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        Try
            While dr.Read
                txtPlant.Text = dr("Plant_Name")
                txtGateNo.Text = dr("Node_No")
                txtCompany.Text = dr("Company_Code")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Timer1.Enabled = False
        Me.Close()
    End Sub

    Private Sub txtVehicleNo_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtVehicleNo.GotFocus
        Try
            txtVehicleNo.BackColor = Color.Gold  'RGB(255, 255, 0)
        Catch ex As Exception
            MsgBox(ex.Message, vbInformation, "ElectroWay")
        End Try
    End Sub

    Private Sub txtVehicleNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVehicleNo.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            txtSAPGateEntryNo.Focus()
        End If
    End Sub

    Private Sub txtVehicleNo_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtVehicleNo.LostFocus
        txtVehicleNo.BackColor = Color.White   ' RGB(255, 255, 255)
        '---------Check Doc Validity------------
        If txtVehicleNo.Text.Trim <> "" Then
            Dim DocF As Boolean = False
            If txtVehicleNo.Text.Trim.ToUpper.Contains("HAND") Then
                DocF = True
            Else
                Dim str As String = "SELECT * FROM tbl_DocumentsValidity WHERE Vehicle_No = '" & txtVehicleNo.Text.Trim & "' and RC_Validity >= convert(date, getdate()) and Tax_Validity >= convert(date, getdate()) and Insurance_Validity >= convert(date, getdate()) and Fitness_Validity >= convert(date, getdate()) and Pollution_Validity >= convert(date, getdate()) and DL_Validity >= convert(date, getdate())"
                dr = cc.GetDataReader(str)
                Try
                    While dr.Read
                        DocF = True
                    End While
                Catch ex As Exception

                End Try
                dr.Close()
            End If
            If DocF = False Then
                MessageBox.Show("Please check the Documents Validity!!")
                txtVehicleNo.Focus()
                txtVehicleNo.BackColor = Color.Gold
                Exit Sub
            End If
        End If
            '---------------------
            dr = cc.GetDataReader("select * from tbl_vehicle_tare_wt_mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'")
            Try
                While dr.Read
                    If dr("Warning") = 1 Then
                        MsgBox("Vehicle is Under Warning Condition !", vbInformation, "Electrosteel Castings Limited.")

                        'txtVehicleNo.SetFocus
                    End If
                    If dr("BlackListed") = 1 Then
                        MsgBox("Black Listed Vehicle, No further process will be allowed !", vbCritical, "Electrosteel Castings Limited.")
                        txtVehicleNo.Focus()
                        Exit Sub
                    End If

                End While
            Catch ex As Exception

            End Try
            dr.Close()
            ''''to be check
            dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN'  and company_code = '" & Trim(txtCompany.Text) & "'") ''''and Plant_Code ='" & Trim(txtPlant.Text) & "'
            Try
                If dr.Read = True Then
                    MsgBox("Vehicle already  IN !", vbInformation, "ElectroWay")
                    txtSAPGateEntryNo.Text = ""
                    txtVehicleNo.Focus()
                    Exit Sub
                Else
                    Call SAP_Con1()
                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        ''txtSAPGateEntryNo.SetFocus

                        'SAP_CON_NOT_AVAIL = 1
                        Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                        'Exit Sub
                    Else
                        Label25.Text = ""
                    End If
                End If
            Catch ex As Exception

            End Try
            dr.Close()
    End Sub

    Private Sub txtVehicleNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVehicleNo.TextChanged
        txtVehicleNo.Text = UCase(Trim(txtVehicleNo.Text))
        txtVehicleNo.SelectionStart = Len(txtVehicleNo.Text)
    End Sub
    Private Sub SAP_Con1()
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub
    Private Sub SAP_Close1()
        Try
            functionCtrl.Connection.Logoff()
            'functionCtr2.Connection.Logoff()
            sapConnection = Nothing
            functionCtrl = Nothing
            functionCtr2 = Nothing
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SAP_Con2()
        functionCtr2 = CreateObject("SAP.Functions")
        sapConnection = functionCtr2.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
    End Sub
    Private Sub GESlip_Print(ByVal Vehicle_No As String)
        If rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbGatePass.Checked = True Or rbPurchaseReturn.Checked = True Or rbCONTRACTORITEM.Checked = True Then
            '-------------------------------
            Dim ReportForm As New GateEntry_Slip
            Dim TableName(0) As String
            Dim QueryString(0) As String
            TableName(0) = "tbl_VIEW_GE_HDR_Details"
            'TableName(1) = "tbl_GE_DET"
            QueryString(0) = "Select * from tbl_VIEW_GE_HDR_Details where  Vehicle_No = '" & Vehicle_No & "' and Vehicle_Status = 'IN'"   '''''a.GE_HDR_ID ='" & Trim(Transaction_No_hdr_ID) & "'", con, adOpenKeyset '' where convert(varchar, EntryDateTime, 105) between '" & Format(dtValidity.Value, "dd-MM-yyyy") & "' and '" & Format(DTPicker2.Value, "dd-MM-yyyy") & "'"
            ReportForm.ViewReport("RptGateEntrySlip3.rpt", TableName, QueryString, )
            ReportForm.Show()
            'ReportForm.BringToFront()
            'ReportForm.WindowState = FormWindowState.Minimized
            'ReportForm.WindowState = FormWindowState.Maximized
            '-----------------------------------
        Else
            Dim ReportForm As New GateEntry_Slip
            Dim TableName(0) As String
            Dim QueryString(0) As String
            TableName(0) = "tbl_VIEW_GE_HDR_Details"
            'TableName(1) = "tbl_GE_DET"
            QueryString(0) = "Select * from tbl_VIEW_GE_HDR_Details where Vehicle_No = '" & Vehicle_No & "' and Vehicle_Status = 'IN'"
            ReportForm.ViewReport("RptGateEntrySlip1.rpt", TableName, QueryString, )
            ReportForm.Show()
            'ReportForm.WindowState = FormWindowState.Minimized
            'ReportForm.WindowState = FormWindowState.Maximized
        End If
        txtVehicleNo.Clear()
    End Sub

    Private Sub btnViewGP_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnViewGP.Click

    End Sub

    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        lblOut_EntryDate.Text = Format(Today.Date, "dd-MM-yyyy")
        lblOut_EntryTime.Text = TimeOfDay.ToString("hh:mm:ss tt")
    End Sub

    Private Sub rbPurchase_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbPurchase.CheckedChanged
        If rbPurchase.Checked = True Then
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "SAP Gate Entry No."
            btnViewGP.Visible = False
            txtSAPGateEntryNo.Enabled = True
            rbPurchase.Font = New System.Drawing.Font(rbPurchase.Font, FontStyle.Bold)
        Else
            rbPurchase.Font = New System.Drawing.Font(rbPurchase.Font, FontStyle.Regular)
        End If
    End Sub

    Private Sub rbSales_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbSales.CheckedChanged
        If rbSales.Checked = True Then
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "D.O. No."
            btnViewGP.Visible = False
            txtSAPGateEntryNo.Enabled = True
            rbSales.Font = New System.Drawing.Font(rbSales.Font, FontStyle.Bold)
        Else
            rbSales.Font = New System.Drawing.Font(rbSales.Font, FontStyle.Regular)
        End If
    End Sub

    Private Sub RBSTOCKTRANSFEROUT_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RBSTOCKTRANSFEROUT.CheckedChanged
        If RBSTOCKTRANSFEROUT.Checked = True Then
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "D.O. No."
            btnViewGP.Visible = False
            txtSAPGateEntryNo.Enabled = True
            RBSTOCKTRANSFEROUT.Font = New System.Drawing.Font(RBSTOCKTRANSFEROUT.Font, FontStyle.Bold)
        Else
            RBSTOCKTRANSFEROUT.Font = New System.Drawing.Font(RBSTOCKTRANSFEROUT.Font, FontStyle.Regular)
        End If
    End Sub

    Private Sub rbCONTRACTORITEM_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbCONTRACTORITEM.CheckedChanged
        If rbCONTRACTORITEM.Checked = True Then
            rbCONTRACTORITEM.Font = New System.Drawing.Font(rbCONTRACTORITEM.Font, FontStyle.Bold)
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "SAP Gate Entry No."
            btnViewGP.Visible = False

            txtSAPGateEntryNo.Enabled = False

            If ListView1.Items.Count = 0 Then
                Dim lvwItem As New ListViewItem()
                lvwItem.Text = ""
                ListView1.Items.Add(lvwItem)
            End If
        Else
            rbCONTRACTORITEM.Font = New System.Drawing.Font(rbCONTRACTORITEM.Font, FontStyle.Regular)
            ListView1.Items.Clear()
        End If

    End Sub

    Private Sub rbGatePass_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbGatePass.CheckedChanged
        If rbGatePass.Checked = True Then
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "RGP/NRGP No."
            ''Command8.Visible = True

            txtSAPGateEntryNo.Enabled = True
            rbGatePass.Font = New System.Drawing.Font(rbGatePass.Font, FontStyle.Bold)
        Else
            rbGatePass.Font = New System.Drawing.Font(rbGatePass.Font, FontStyle.Regular)
        End If
    End Sub

    Private Sub rbInterDept_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbInterDept.CheckedChanged
        If rbInterDept.Checked = True Then
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "SAP Gate Entry No."
            btnViewGP.Visible = False

            txtSAPGateEntryNo.Enabled = False
            rbInterDept.Font = New System.Drawing.Font(rbInterDept.Font, FontStyle.Bold)
        Else
            rbInterDept.Font = New System.Drawing.Font(rbInterDept.Font, FontStyle.Regular)
        End If
    End Sub

    Private Sub rbSalesReturn_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbSalesReturn.CheckedChanged
        If rbSalesReturn.Checked = True Then
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "D.O. No."
            btnViewGP.Visible = False

            txtSAPGateEntryNo.Enabled = True
            rbSalesReturn.Font = New System.Drawing.Font(rbSalesReturn.Font, FontStyle.Bold)
        Else
            rbSalesReturn.Font = New System.Drawing.Font(rbSalesReturn.Font, FontStyle.Regular)
        End If
    End Sub

    Private Sub rbPurchaseReturn_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbPurchaseReturn.CheckedChanged
        If rbPurchaseReturn.Checked = True Then
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "SAP Gate Entry No."
            btnViewGP.Visible = False

            txtSAPGateEntryNo.Enabled = True
            rbPurchaseReturn.Font = New System.Drawing.Font(rbPurchaseReturn.Font, FontStyle.Bold)
        Else
            rbPurchaseReturn.Font = New System.Drawing.Font(rbPurchaseReturn.Font, FontStyle.Regular)
        End If
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Dim str As String = "select distinct GE_HDR_ID , Vehicle_NO , Type_Of_Vehicle , EntryDateTime from tbl_GE_HDR   where Vehicle_Status = 'IN' and Company_Code = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%' and Vehicle_no like'%" & Trim(txtVehiclenoSearch.Text) & "%'order by GE_HDR_ID"
        Try
            ds = cc.GetDataset(str)
            gvSearch.DataSource = ds.Tables(0)
        Catch ex As Exception

        End Try

    End Sub
    Private Sub dtValidity_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles dtValidity.GotFocus
        dtValidity.CalendarForeColor = Color.Gold
    End Sub

    Private Sub dtValidity_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles dtValidity.KeyDown
        If e.KeyCode = 112 Then
            txtDriverName.Focus()
        End If
    End Sub

    Private Sub dtValidity_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtValidity.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            txtDriverName.Focus()
        End If
    End Sub

    Private Sub dtValidity_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles dtValidity.LostFocus
        dtValidity.CalendarMonthBackground = Color.Black
    End Sub

    Private Sub txtSAPGateEntryNo_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtSAPGateEntryNo.GotFocus
        txtSAPGateEntryNo.BackColor = Color.Gold
    End Sub

    Private Sub txtSAPGateEntryNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtSAPGateEntryNo.KeyPress
        Dim SAP_GE_CH_NO, SAP_GE_CH_DT, RR_No, RR_DT, LR_NO, LR_DT, Rake_No As String
        Dim PO_NO_11, Vendor_Coddee, Vendor_Namee, GatePass_n, matnamme, SoldToParty, ShipToParty, CustomerNameee, poText As String
        Dim iii As Integer

        Date_v = Format(Today.Date, "yyyy") & Format(Today.Date, "MM") & Format(Today.Date, "dd")
        Time_v = Format(TimeOfDay.ToString("HH")) & Format(Minute(TimeOfDay.ToString), "00") & Format(TimeOfDay.ToString("ss"))
        '---------------------------
        '''check Duplicate

        If Trim(txtSAPGateEntryNo.Text) <> "" Then
            Try
                For i = 0 To ListView1.Items.Count - 1
                    If Trim(txtSAPGateEntryNo.Text) = ListView1.Items(i).SubItems(1).Text Then
                        MsgBox("Delivery No. already entered !", vbInformation, "ElectroWay")
                        txtSAPGateEntryNo.Text = ""
                        txtSAPGateEntryNo.Focus()
                        Exit Sub
                    ElseIf Trim(txtSAPGateEntryNo.Text) = Trim(ListView1.Items(i).SubItems(7).Text) Then
                        MsgBox("Unloading No. already entered !", vbInformation, "ElectroWay")
                        txtSAPGateEntryNo.Text = ""
                        txtSAPGateEntryNo.Focus()
                        Exit Sub
                    End If
                Next
            Catch ex As Exception
                Exit Sub
            End Try

        Else
            Exit Sub
        End If

        If AscW(e.KeyChar) = 13 Then
            If rbInterDept.Checked = True Then
                MsgBox("SAP gate Entry No. not allowed for INTERDEPT Vehicle", vbInformation, "ElectroWay")
                txtSAPGateEntryNo.Text = ""
                txtSAPGateEntryNo.Focus()
                Exit Sub
            End If

            dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN' and Plant_Code ='" & Trim(txtPlant.Text) & "' and company_code = '" & Trim(txtCompany.Text) & "'")
            Try
                While dr.Read
                    MsgBox("Vehicle already  IN !", vbInformation, "Electrosteel Castings Limited.")
                    txtSAPGateEntryNo.Text = ""
                    txtVehicleNo.Focus()
                    dr.Close()
                    Exit Sub
                End While
            Catch ex As Exception

            End Try
            dr.Close()

            gbVehicleno.Enabled = False
            gbActivity.Enabled = False
            If rbFLYASH.Checked = True Then
                Fetch_FLY_ASH_DETAILS()
                Exit Sub
            End If
            '-----------List------------------
            Dim lvi As New ListViewItem
            '---------------------------
            If rbGatePass.Checked = True Then
                If Len(Trim(txtSAPGateEntryNo.Text)) > 10 Then
                    MsgBox("Invalid SAP gate Entry No.", vbInformation, "ElectroWay")
                    'rec4.Close
                    Exit Sub
                End If
                Call SAP_Con1()
                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    SAP_CON_NOT_AVAIL = 1
                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub

                Else

                    Label25.Text = ""

                    ''*************************************************************************** start 111
                    'Dim objRfcFunc As Object
                    ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "ZTM_HGATE_PASS"

                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    'Then set values
                    objOptTab.Rows.Add()
                    ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                    ''objOptTab.Rows.Add
                    objOptTab(objOptTab.RowCount, "TEXT") = "GATEPASS = '" & Trim(txtSAPGateEntryNo.Text) & "' and VEHINO ='" & Trim(txtVehicleNo.Text) & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "GATEPASS"
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CREATE_DATE"
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME" 'vehicle no'
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME"  '' date
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CREATED_BY" ''"LRBILL_NO"   '' time
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CREATED_BY" ''"LRBILL_DT" ''PO no  EBELP

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CREATED_BY" ''"DELI_NO" ''PO no  EBELP
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CREATED_BY" ''"DELI_DT" ''PO no  EBELP

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CREATED_BY" ''"LRBILL_NO" ''PO no  EBELP
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CREATED_BY" ''"LRBILL_DT" ''PO no  EBELP
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CREATED_BY" ''"MINESNAME" ''PO no  EBELP


                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If

                    ''i = 5

                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid Vehicle !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        For Each objDatRec In objDatTab.Rows
                            i = i + 1
                            For Each objFldRec In objFldTab.Rows
                                j = j + 1

                                If j = 1 Then
                                    SAP_GE_CH_NO = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 2 Then
                                    SAP_GE_CH_DT = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 3 Then
                                    txtTransporterCode.Text = ""  '''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 4 Then
                                    txtTransporterName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                ElseIf j = 5 Then
                                    RR_No = "" ''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                ElseIf j = 6 Then
                                    RR_DT = "" ''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 7 Then
                                    LR_NO = "" '''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 8 Then
                                    LR_DT = "" ''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 9 Then
                                    Rake_No = "" ''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                End If
                            Next
                            j = 0
                        Next

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            Exit Sub
                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")
                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "ZTM_IGATE_PASS"

                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        objOptTab.FreeTable()
                        objOptTab1.FreeTable()
                        'Then set values
                        objOptTab1.Rows.Add()
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "GATEPASS = '" & txtSAPGateEntryNo.Text & "'"  '''''' and LOEKZ = ''"

                        objFldTab1.FreeTable()
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELN"  '' PO number

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELP"  '' Line item
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MATNR"  '' Material code
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MAKTX"  '' Material Desc
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MENGE"  '' Del Ch Qty.

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MEINS"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATEPASS"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATEPASS"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATEPASS"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATEPASS"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATEPASS"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATEPASS"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATEPASS"  '' UOM

                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If

                        'Dim j As Integer
                        i = 5

                        For Each objDatRec1 In objDatTab1.Rows
                            'Dim k As Integer = ListView1.Items.Count + 1
                            Dim k As Integer = ListView1.Items.Count
                            For Each objFldRec1 In objFldTab1.Rows
                                'm = 1

                                If m = 0 Then
                                    '---------List----------
                                    lvi.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                    '------------------------
                                    'ListView1.Items.Add(k).Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                    'ListView1.Items(0).SubItems(1).Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""

                                    PO_NO_11 = Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")

                                    ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


                                    Call SAP_Con1()

                                    If sapConnection.Logon(0, True) <> True Then
                                        MsgBox("No connection to SAP System .......")
                                        SAP_CON_NOT_AVAIL = 1
                                        Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                        Exit Sub

                                    Else

                                        Label25.Text = ""

                                        ''*************************************************************************** start 111
                                        'Dim objRfcFunc As Object
                                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                        objQueryTab.Value = "EKKO"

                                        objOptTab = objRfcFunc.Tables("OPTIONS")
                                        objFldTab = objRfcFunc.Tables("FIELDS")
                                        objDatTab = objRfcFunc.Tables("DATA")
                                        'First we set the condition
                                        'Refresh table
                                        objOptTab.FreeTable()
                                        'Then set values
                                        objOptTab.Rows.Add()
                                        ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                        ''objOptTab.Rows.Add
                                        objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                        objFldTab.FreeTable()

                                        objFldTab.Rows.Add()
                                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "LIFNR"

                                        If objRfcFunc.Call = False Then
                                            MsgBox(objRfcFunc.Exception)
                                        End If

                                        i = 5

                                        If objDatTab.Rows.Count = 0 Then
                                            ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                        Else

                                            For Each objDatRec In objDatTab.Rows
                                                i = i + 1
                                                For Each objFldRec In objFldTab.Rows
                                                    Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))


                                                Next
                                            Next
                                        End If
                                    End If

                                    ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
                                    ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                    Call SAP_Con1()

                                    If sapConnection.Logon(0, True) <> True Then
                                        MsgBox("No connection to SAP System .......")
                                        SAP_CON_NOT_AVAIL = 1
                                        Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                        Exit Sub

                                    Else

                                        Label25.Text = ""

                                        ''*************************************************************************** start 111
                                        'Dim objRfcFunc As Object
                                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                        objQueryTab.Value = "LFA1"

                                        objOptTab = objRfcFunc.Tables("OPTIONS")
                                        objFldTab = objRfcFunc.Tables("FIELDS")
                                        objDatTab = objRfcFunc.Tables("DATA")
                                        'First we set the condition
                                        'Refresh table
                                        objOptTab.FreeTable()
                                        'Then set values
                                        objOptTab.Rows.Add()
                                        ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                        ''objOptTab.Rows.Add
                                        objOptTab(objOptTab.RowCount, "TEXT") = "LIFNR = '" & Vendor_Coddee & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                        objFldTab.FreeTable()

                                        objFldTab.Rows.Add()
                                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"

                                        If objRfcFunc.Call = False Then
                                            MsgBox(objRfcFunc.Exception)
                                        End If

                                        i = 5

                                        If objDatTab.Rows.Count = 0 Then
                                            ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                        Else

                                            For Each objDatRec In objDatTab.Rows
                                                i = i + 1
                                                For Each objFldRec In objFldTab.Rows
                                                    Vendor_Namee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                                Next
                                            Next
                                        End If
                                    End If

                                    ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                    ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
                                    If Trim(Vendor_Coddee) = "" Then
                                        If sapConnection.Logon(0, True) <> True Then
                                            MsgBox("No connection to SAP System .......")
                                            SAP_CON_NOT_AVAIL = 1
                                            Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                            Exit Sub
                                        Else
                                            Label25.Text = ""
                                            ''*************************************************************************** start 111
                                            'Dim objRfcFunc As Object
                                            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                                            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                            objQueryTab.Value = "EKKO"

                                            objOptTab = objRfcFunc.Tables("OPTIONS")
                                            objFldTab = objRfcFunc.Tables("FIELDS")
                                            objDatTab = objRfcFunc.Tables("DATA")
                                            'First we set the condition
                                            'Refresh table
                                            objOptTab.FreeTable()
                                            'Then set values
                                            objOptTab.Rows.Add()
                                            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                            ''objOptTab.Rows.Add
                                            objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                            objFldTab.FreeTable()

                                            objFldTab.Rows.Add()
                                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "RESWK"

                                            If objRfcFunc.Call = False Then
                                                MsgBox(objRfcFunc.Exception)
                                            End If

                                            i = 5

                                            If objDatTab.Rows.Count = 0 Then
                                                ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                            Else

                                                For Each objDatRec In objDatTab.Rows
                                                    i = i + 1
                                                    For Each objFldRec In objFldTab.Rows
                                                        Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))

                                                        Vendor_Namee = Vendor_Coddee
                                                    Next
                                                Next
                                            End If
                                        End If


                                    End If
                                    ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                Else

                                    If m = 1 And (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then
                                        '---------List----------
                                        lvi.SubItems.Add("")
                                        lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
                                        '------------------------
                                        'ListView1.Items(k).SubItems(m).Text = ""

                                        'ListView1.Items(k).SubItems(m + 1).Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                    ElseIf m = 12 Then
                                        '-------------------
                                        lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
                                        lvi.SubItems.Add(GatePass_n)
                                        lvi.SubItems.Add("")
                                        lvi.SubItems.Add(Vendor_Coddee)
                                        lvi.SubItems.Add(Vendor_Namee)
                                        '----------------------
                                        'ListView1.Items(k).SubItems.Add(m + 1).Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                        'ListView1.Items(k).SubItems.Add(14).Text = GatePass_n & ""
                                        'ListView1.Items(k).SubItems.Add(15).Text = ""
                                        'ListView1.Items(k).SubItems.Add(16).Text = Vendor_Coddee & ""  ''''  For VENDOR CODE
                                        'ListView1.Items(k).SubItems.Add(17).Text = Vendor_Namee & ""  ''''  For VENDOR NAME
                                    Else
                                        If m = 3 Then
                                            If Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))) = "" Then
                                                matnamme = InputBox("Plese Input Material Name.", "ElectroWay")
                                                lvi.SubItems.Add(matnamme)
                                                'ListView1.Items(k).SubItems.Add(m + 1).Text = matnamme
                                            Else
                                                lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")
                                                'ListView1.Items(k).SubItems.Add(m + 1).Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                            End If
                                        ElseIf m = 7 Then
                                            lvi.SubItems.Add(SAP_GE_CH_NO & "")
                                            'ListView1.Items(k).SubItems.Add(m + 1).Text = SAP_GE_CH_NO & "" '''SAP_GE_CH_NO
                                        ElseIf m = 8 Then
                                            lvi.SubItems.Add(SAP_GE_CH_DT & "")
                                            'ListView1.Items(k).SubItems.Add(m + 1).Text = SAP_GE_CH_DT & "" '''SAP_GE_CH_NO

                                        ElseIf m = 9 Then
                                            lvi.SubItems.Add(Trim(RR_No) & "")
                                            'ListView1.Items(k).SubItems.Add(m + 1).Text = Trim(RR_No) & "" '''SAP_GE_CH_NO

                                        ElseIf m = 10 Then
                                            lvi.SubItems.Add(RR_DT & "")
                                            'ListView1.Items(k).SubItems.Add(m + 1).Text = RR_DT & "" '''SAP_GE_CH_NO

                                        ElseIf m = 11 Then
                                            lvi.SubItems.Add(LR_NO & "")
                                            'ListView1.Items(k).SubItems.Add(m + 1).Text = LR_NO & "" '''SAP_GE_CH_NO
                                        ElseIf m = 12 Then
                                            lvi.SubItems.Add(LR_DT & "")
                                            'ListView1.Items(k).SubItems.Add(m + 1).Text = LR_DT & "" '''SAP_GE_CH_NO

                                        ElseIf m = 13 Then
                                            lvi.SubItems.Add(Rake_No & "")
                                            'ListView1.Items(k).SubItems.Add(m + 1).Text = Rake_No & "" '''SAP_GE_CH_NO

                                        Else
                                            lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")
                                            'ListView1.Items(k).SubItems.Add(m + 1).Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                        End If

                                    End If

                                End If
                                'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                m = m + 1
                            Next
                            m = 0
                        Next
                        ''*********************************************  end
                    End If
                End If
                ''----------------------------------------------
                ''''**********************************************************************************  02 jun 2014 end
                ''End If
            ElseIf rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Then
                '********************************************02 jun 2014 start
                If Len(Trim(txtSAPGateEntryNo.Text)) > 10 Then
                    MsgBox("Invalid SAP Gate Entry No.", vbInformation, "ElectroWay")
                    'rec4.Close
                    Exit Sub
                End If
                ''TTTTTTTTTTTTTTTTTTTTTTTTTT
                Call SAP_Con1()

                Dim VNumber As String

                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    SAP_CON_NOT_AVAIL = 1
                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub

                Else

                    Label25.Text = ""

                    ''*************************************************************************** start 111
                    'Dim objRfcFunc As Object
                    ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")

                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "ZTM_HGATE_ENTRY"

                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    'Then set values
                    objOptTab.Rows.Add()
                    ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                    ''objOptTab.Rows.Add
                    objOptTab(objOptTab.RowCount, "TEXT") = "GATENO = '" & Trim(txtSAPGateEntryNo.Text) & "'"    ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "VEHICLENO"

                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If

                    ''i = 5

                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid Vehicle no. !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        For Each objDatRec In objDatTab.Rows

                            For Each objFldRec In objFldTab.Rows

                                VNumber = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                            Next
                        Next
                    End If
                End If
                If VNumber <> Trim(txtVehicleNo.Text) Then
                    MsgBox("Invalid Vehicle No.", vbInformation, "ElectroWay")
                    Exit Sub
                End If

                ''TTTTTTTTTTTTTTTTTTTTTTTTTT

                Call SAP_Con1()

                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    SAP_CON_NOT_AVAIL = 1
                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub
                Else

                    Label25.Text = ""

                    ''*************************************************************************** start 111
                    'Dim objRfcFunc As Object
                    ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "ZTM_HGATE_ENTRY"

                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    'Then set values
                    objOptTab.Rows.Add()
                    ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                    ''objOptTab.Rows.Add
                    objOptTab(objOptTab.RowCount, "TEXT") = "VEHICLENO ='" & Trim(txtVehicleNo.Text) & "' and GATENO = '" & Trim(txtSAPGateEntryNo.Text) & "'"    ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CHALLAN_NO"
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "CHALLAN_DT"
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME" 'vehicle no'
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME"  '' date
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "LRBILL_NO"   '' time
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "LRBILL_DT" ''PO no  EBELP

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "DELI_NO" ''PO no  EBELP
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "DELI_DT" ''PO no  EBELP

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "LRBILL_NO" ''PO no  EBELP
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "LRBILL_DT" ''PO no  EBELP
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME" ''PO no  EBELP


                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If

                    ''i = 5
                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid Vehicle !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        For Each objDatRec In objDatTab.Rows
                            i = i + 1
                            For Each objFldRec In objFldTab.Rows
                                j = j + 1

                                If j = 1 Then
                                    SAP_GE_CH_NO = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 2 Then
                                    SAP_GE_CH_DT = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 3 Then
                                    txtTransporterCode.Text = "" ''''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 4 Then
                                    txtTransporterName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                ElseIf j = 5 Then
                                    RR_No = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                ElseIf j = 6 Then
                                    RR_DT = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 7 Then
                                    LR_NO = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 8 Then
                                    LR_DT = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                ElseIf j = 9 Then
                                    Rake_No = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                End If

                            Next
                            j = 0
                        Next

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            Exit Sub
                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "ZTM_IGATE_ENTRY"

                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        objOptTab.FreeTable()
                        objOptTab1.FreeTable()
                        'Then set values
                        objOptTab1.Rows.Add()
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "GATENO = '" & txtSAPGateEntryNo.Text & "'"  '''''' and LOEKZ = ''"

                        objFldTab1.FreeTable()
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELN"  '' PO number

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELP"  '' Line item
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MATNR"  '' Material code
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MAKTX"  '' Material Desc
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "CHAL_QTY"  '' Del Ch Qty.

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MEINS"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If

                        'Dim j As Integer
                        i = 5

                        For Each objDatRec1 In objDatTab1.Rows
                            lvi = New ListViewItem
                            'Dim k As Integer = ListView1.Items.Count + 1
                            Dim k As Integer = ListView1.Items.Count + 1
                            For Each objFldRec1 In objFldTab1.Rows
                                'm = 1

                                If m = 0 Then
                                    'ListView1.Items.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "", k)
                                    'ListView1.Items.Insert(0, (Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""))

                                    '---------List----------
                                    lvi.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                    'ListView1.Items.Add(lvi)
                                    '------------------------
                                    PO_NO_11 = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""

                                    ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

                                    Call SAP_Con1()

                                    If sapConnection.Logon(0, True) <> True Then
                                        MsgBox("No connection to SAP System .......")
                                        SAP_CON_NOT_AVAIL = 1
                                        Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                        Exit Sub
                                    Else
                                        Label25.Text = ""

                                        ''*************************************************************************** start 111
                                        'Dim objRfcFunc As Object
                                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                        objQueryTab.Value = "EKKO"

                                        objOptTab = objRfcFunc.Tables("OPTIONS")
                                        objFldTab = objRfcFunc.Tables("FIELDS")
                                        objDatTab = objRfcFunc.Tables("DATA")
                                        'First we set the condition
                                        'Refresh table
                                        objOptTab.FreeTable()
                                        'Then set values
                                        objOptTab.Rows.Add()
                                        ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                        ''objOptTab.Rows.Add
                                        objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                        objFldTab.FreeTable()

                                        objFldTab.Rows.Add()
                                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "LIFNR"

                                        If objRfcFunc.Call = False Then
                                            MsgBox(objRfcFunc.Exception)
                                        End If

                                        i = 5

                                        If objDatTab.Rows.Count = 0 Then
                                            ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                        Else

                                            For Each objDatRec In objDatTab.Rows
                                                i = i + 1
                                                For Each objFldRec In objFldTab.Rows
                                                    Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                                Next
                                            Next
                                        End If
                                    End If

                                    ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

                                    ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
                                    Call SAP_Con1()

                                    If sapConnection.Logon(0, True) <> True Then
                                        MsgBox("No connection to SAP System .......")
                                        SAP_CON_NOT_AVAIL = 1
                                        Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                        Exit Sub

                                    Else
                                        Label25.Text = ""
                                        ''*************************************************************************** start 111
                                        'Dim objRfcFunc As Object
                                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                        objQueryTab.Value = "LFA1"

                                        objOptTab = objRfcFunc.Tables("OPTIONS")
                                        objFldTab = objRfcFunc.Tables("FIELDS")
                                        objDatTab = objRfcFunc.Tables("DATA")
                                        'First we set the condition
                                        'Refresh table
                                        objOptTab.FreeTable()
                                        'Then set values
                                        objOptTab.Rows.Add()
                                        ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                        ''objOptTab.Rows.Add
                                        objOptTab(objOptTab.RowCount, "TEXT") = "LIFNR = '" & Vendor_Coddee & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                        objFldTab.FreeTable()

                                        objFldTab.Rows.Add()
                                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"

                                        If objRfcFunc.Call = False Then
                                            MsgBox(objRfcFunc.Exception)
                                        End If

                                        i = 5

                                        If objDatTab.Rows.Count = 0 Then
                                            ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                        Else
                                            For Each objDatRec In objDatTab.Rows
                                                i = i + 1
                                                For Each objFldRec In objFldTab.Rows
                                                    Vendor_Namee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                                Next
                                            Next
                                        End If
                                    End If

                                    ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                    ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
                                    If Trim(Vendor_Coddee) = "" Then
                                        If sapConnection.Logon(0, True) <> True Then
                                            MsgBox("No connection to SAP System .......")
                                            SAP_CON_NOT_AVAIL = 1
                                            Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                            Exit Sub
                                        Else
                                            Label25.Text = ""
                                            ''*************************************************************************** start 111
                                            'Dim objRfcFunc As Object
                                            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                                            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                            objQueryTab.Value = "EKKO"

                                            objOptTab = objRfcFunc.Tables("OPTIONS")
                                            objFldTab = objRfcFunc.Tables("FIELDS")
                                            objDatTab = objRfcFunc.Tables("DATA")
                                            'First we set the condition
                                            'Refresh table
                                            objOptTab.FreeTable()
                                            'Then set values
                                            objOptTab.Rows.Add()
                                            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                            ''objOptTab.Rows.Add
                                            objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                            objFldTab.FreeTable()

                                            objFldTab.Rows.Add()
                                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "RESWK"

                                            If objRfcFunc.Call = False Then
                                                MsgBox(objRfcFunc.Exception)
                                            End If

                                            i = 5

                                            If objDatTab.Rows.Count = 0 Then
                                                ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                            Else

                                                For Each objDatRec In objDatTab.Rows
                                                    i = i + 1
                                                    For Each objFldRec In objFldTab.Rows
                                                        Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))

                                                        Vendor_Namee = Vendor_Coddee
                                                    Next
                                                Next
                                            End If
                                        End If
                                    End If

                                    ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
                                Else

                                    If m = 1 And (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then

                                        '---------List----------
                                        lvi.SubItems.Add("")
                                        lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
                                        '------------------------
                                        'ListView1.Items(k).SubItems(m).Text = "a"
                                        'ListView1.Items(k).SubItems(m+1).Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""

                                    ElseIf m = 12 Then
                                        '-------------------
                                        lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
                                        lvi.SubItems.Add(GatePass_n)
                                        lvi.SubItems.Add("")
                                        lvi.SubItems.Add(Vendor_Coddee)
                                        lvi.SubItems.Add(Vendor_Namee)
                                        '----------------------
                                        'ListView1.ListItems(k).SubItems.Add (m + 1), , Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                        'ListView1.ListItems(k).SubItems.Add (14), , GatePass_n & ""
                                        'ListView1.ListItems(k).SubItems.Add (15), , ""
                                        'ListView1.ListItems(k).SubItems.Add (16), , Vendor_Coddee & ""  ''''  For VENDOR CODE
                                        'ListView1.ListItems(k).SubItems.Add (17), , Vendor_Namee & ""  ''''  For VENDOR NAME

                                    Else

                                        If m = 3 Then
                                            If Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))) = "" Then
                                                matnamme = InputBox("Plese Input Material Name.", "ElectroWay")
                                                '---------------------
                                                lvi.SubItems.Add(matnamme)
                                                '--------------------------
                                                'ListView1.ListItems(k).SubItems.Add (m + 1), , matnamme
                                            Else
                                                '---------------------
                                                lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
                                                '--------------------------
                                                'ListView1.ListItems(k).SubItems.Add (m + 1), , Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                            End If
                                        ElseIf m = 7 Then
                                            '---------------------
                                            lvi.SubItems.Add(SAP_GE_CH_NO)
                                            '--------------------------
                                            'ListView1.ListItems(k).SubItems.Add (m + 1), , SAP_GE_CH_NO & "" '''SAP_GE_CH_NO
                                        ElseIf m = 8 Then
                                            'ListView1.ListItems(k).SubItems.Add (m + 1), , SAP_GE_CH_DT & "" '''SAP_GE_CH_NO
                                            '---------------------
                                            lvi.SubItems.Add(SAP_GE_CH_DT)
                                            '--------------------------
                                        ElseIf m = 9 Then
                                            'ListView1.ListItems(k).SubItems.Add (m + 1), , Trim(RR_No) & "" '''SAP_GE_CH_NO
                                            '---------------------
                                            lvi.SubItems.Add(Trim(RR_No))
                                            '--------------------------
                                        ElseIf m = 10 Then
                                            'ListView1.ListItems(k).SubItems.Add (m + 1), , RR_DT & "" '''SAP_GE_CH_NO
                                            '---------------------
                                            lvi.SubItems.Add(RR_DT)
                                            '--------------------------
                                        ElseIf m = 11 Then
                                            '---------------------
                                            lvi.SubItems.Add(LR_NO)
                                            '--------------------------
                                            'ListView1.ListItems(k).SubItems.Add (m + 1), , LR_NO & "" '''SAP_GE_CH_NO
                                        ElseIf m = 12 Then
                                            'ListView1.ListItems(k).SubItems.Add (m + 1), , LR_DT & "" '''SAP_GE_CH_NO
                                            '---------------------
                                            lvi.SubItems.Add(LR_DT)
                                            '--------------------------
                                        ElseIf m = 13 Then
                                            'ListView1.ListItems(k).SubItems.Add (m + 1), , Rake_No & "" '''SAP_GE_CH_NO
                                            '---------------------
                                            lvi.SubItems.Add(Rake_No)
                                            '--------------------------
                                        Else
                                            '---------------------
                                            lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
                                            '--------------------------
                                            'ListView1.ListItems(k).SubItems.Add (m + 1), , Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                        End If
                                        'ListView1.Items.Add(lvi)
                                    End If

                                End If

                                'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))

                                m = m + 1

                            Next
                            m = 0
                            ListView1.Items.Add(lvi)
                        Next
                        ''*********************************************  end
                    End If
                End If
                ''----------------------------------------------
                ''''**********************************************************************************  02 jun 2014 end
            End If

            If rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True  Then

                If Len(Trim(txtSAPGateEntryNo.Text)) > 10 Then
                    MsgBox("Invalid Delivery No.", vbInformation, "ElectroWay")
                    'rec4.Close
                    Exit Sub
                End If

                '''''''''&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
                Call SAP_Con2()

                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    Exit Sub
                End If

                objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                objQueryTab1.Value = "LIKP"
                objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                objFldTab1 = objRfcFunc1.Tables("FIELDS")
                objDatTab1 = objRfcFunc1.Tables("DATA")
                objOptTab1.FreeTable()
                objOptTab1.Rows.Add()
                objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & txtSAPGateEntryNo.Text & "'"

                objFldTab1.FreeTable()

                objFldTab1.Rows.Add()
                objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
                objFldTab1.Rows.Add()
                objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNAG"   ''''
                objFldTab1.Rows.Add()
                objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNNR"   ''''KUNNR

                If objRfcFunc1.Call = False Then
                    MsgBox(objRfcFunc1.Exception)
                End If

                iii = 0

                If objDatTab1.Rows.Count = 0 Then
                    'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
                Else

                    For Each objDatRec1 In objDatTab1.Rows

                        For Each objFldRec1 In objFldTab1.Rows
                            iii = iii + 1

                            If iii = 2 Then
                                SoldToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                            End If

                            If iii = 3 Then

                                ShipToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))

                            End If

                            'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        Next
                    Next
                End If

                ''**********
                '''''''''&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&

                ''BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB

                Call SAP_Con2()

                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    Exit Sub

                End If

                objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                objQueryTab1.Value = "KNA1"
                objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                objFldTab1 = objRfcFunc1.Tables("FIELDS")
                objDatTab1 = objRfcFunc1.Tables("DATA")
                objOptTab1.FreeTable()
                objOptTab1.Rows.Add()
                If Trim(SoldToParty) <> "" Then
                    objOptTab1(objOptTab1.RowCount, "TEXT") = "KUNNR = '" & SoldToParty & "'"
                Else
                    objOptTab1(objOptTab1.RowCount, "TEXT") = "KUNNR = '" & ShipToParty & "'"
                End If

                objFldTab1.FreeTable()

                objFldTab1.Rows.Add()
                objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "NAME1"   ''''KUNNR

                If objRfcFunc1.Call = False Then
                    MsgBox(objRfcFunc1.Exception)
                End If

                If objDatTab1.Rows.Count = 0 Then
                    'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
                Else

                    For Each objDatRec1 In objDatTab1.Rows

                        For Each objFldRec1 In objFldTab1.Rows
                            CustomerNameee = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        Next
                    Next
                End If
                ''BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB
                Call SAP_Con1()

                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub
                Else
                    Label25.Text = ""

                    Dim RFC_READ_TEXT = functionCtrl.Add("RFC_READ_TEXT") '<------------
                    Dim tblText_Lines = RFC_READ_TEXT.Tables("TEXT_LINES")

                    tblText_Lines.AppendRow()
                    tblText_Lines(1, "TDOBJECT") = "VBBK"
                    tblText_Lines(1, "TDNAME") = txtSAPGateEntryNo.Text
                    tblText_Lines(1, "TDID") = "ZDL2"   '''"ZL01"
                    If RFC_READ_TEXT.Call = True Then

                        'MsgBox tblText_Lines.RowCount

                        If tblText_Lines.RowCount > 0 Then

                            For intRow = 1 To tblText_Lines.RowCount ' Change Next line to write a different header row

                                If intRow = 1 Then
                                    poText = tblText_Lines(intRow, "TDLINE")
                                    'Else
                                    'poText = poText & vbCrLf & tblText_Lines(intRow, "TDLINE")
                                End If
                            Next
                        Else

                        End If
                    Else
                        MsgBox("ERROR CALLING SAP REMOTE FUNCTION CALL")
                    End If

                    ''*************************************************************************** start 111
                    If poText <> txtVehicleNo.Text Then
                        MsgBox("Invalid Vehicle No. !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        objQueryTab.Value = "LIPS"
                        objOptTab = objRfcFunc.Tables("OPTIONS")
                        objFldTab = objRfcFunc.Tables("FIELDS")
                        objDatTab = objRfcFunc.Tables("DATA")
                        objOptTab.FreeTable()
                        objOptTab.Rows.Add()
                        objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '" & txtSAPGateEntryNo.Text & "'"

                        objFldTab.FreeTable()

                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VGBEL"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VBELN"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "POSNR"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MATNR" ''
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "ARKTX" ''
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "LFIMG"  ''
                        ''''                                                    objFldTab.Rows.Add
                        ''''                                                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "MEINS"   ''VRKME
                        ''''
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VRKME"   ''VRKME

                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VGPOS"   ''
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "WERKS"   ''


                        If objRfcFunc.Call = False Then
                            MsgBox(objRfcFunc.Exception)
                        End If

                        i = 5

                        If objDatTab.Rows.Count = 0 Then
                            MsgBox("Invalid Vehicle !", vbInformation, "ElectroSteel Castings Limited")
                        Else

                            For Each objDatRec In objDatTab.Rows
                                lvi = New ListViewItem

                                Dim k As Integer = ListView1.Items.Count + 1

                                For Each objFldRec In objFldTab.Rows
                                    'm = 1

                                    If m = 0 Then
                                        'ListView1.Items(k).Text = (Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & "")
                                        '---------List----------
                                        lvi.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                        'lvi.SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                        '------------------------
                                    Else

                                        If m = 1 And (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbGatePass.Checked = True Or rbPurchaseReturn.Checked = True) Then
                                            'ListView1.Items(k).SubItems(m).Text = ""
                                            ''ListView1.Items(k).SubItems.Add (m + 1), , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""
                                            ''m = m + 1
                                            '---------List----------
                                            lvi.SubItems.Add("")
                                            '------------------------
                                        ElseIf m = 7 Then
                                            For Lv_i = 7 To 14
                                                'ListView1.Items(k).SubItems(Lv_i).Text = ""
                                                '---------List----------
                                                lvi.SubItems.Add("")
                                                '------------------------
                                            Next Lv_i

                                            'ListView1.Items(k).SubItems(15).Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""
                                            '---------List----------
                                            lvi.SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                            '------------------------
                                            If Trim(SoldToParty) = "" Then
                                                'ListView1.Items(k).SubItems(16).Text = ShipToParty & ""      '''''' ShipToParty
                                                '---------List----------
                                                lvi.SubItems.Add(ShipToParty)
                                                '------------------------
                                            Else
                                                'ListView1.Items(k).SubItems(16).Text = SoldToParty & ""
                                                '---------List----------
                                                lvi.SubItems.Add(SoldToParty)
                                                '------------------------
                                            End If
                                            'ListView1.Items(k).SubItems(17).Text = CustomerNameee & ""   '''''''''' CUSTOMER NAME
                                            '---------List----------
                                            lvi.SubItems.Add(CustomerNameee)
                                            '------------------------
                                        ElseIf m = 8 Then

                                            'txtPlant.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""

                                        Else
                                            'ListView1.Items(k).SubItems(m).Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""
                                            '---------List----------
                                            lvi.SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                            '------------------------
                                        End If

                                    End If

                                    m = m + 1

                                Next

                                If m < 8 Then
                                    For Lv_i = 7 To 14
                                        'ListView1.Items(k).SubItems(Lv_i).Text = ""
                                        '---------List----------
                                        lvi.SubItems.Add("")
                                        '------------------------
                                    Next Lv_i

                                End If

                                m = 0
                                ListView1.Items.Add(lvi)
                            Next

                            ''*********************************************  end
                        End If
                        '''*************************************** TTTTTTTTTTT
                        ''''''                                                               Set functionCtr2 = CreateObject("SAP.Functions")
                        ''''''                                                               Set sapConnection = functionCtr2.Connection
                        ''''''                                                               sapConnection.User = "goutamb"
                        ''''''                                                               sapConnection.Password = "pizzahut"
                        ''''''                                                               sapConnection.System = "00"
                        ''''''                                                               sapConnection.ApplicationServer = "195.1.117.150"
                        ''''''                                                               sapConnection.SystemNumber = "01"
                        ''''''                                                               sapConnection.Client = "500"
                        ''''''                                                               sapConnection.Language = "EN"
                        ''''''                                                               'sapConnection.CodePage = "8600"

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            Exit Sub

                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "VBPA"
                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        objOptTab1.FreeTable()
                        objOptTab1.Rows.Add()
                        '' T% is for KHARDAH
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & txtSAPGateEntryNo.Text & "' and ( PARVW LIKE 'T%'  or PARVW LIKE 'S%')"  ''''or PARVW LIKE 'F%'
                        ''objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & txtSAPGateEntryNo.Text & "' and PARVW LIKE 'F%'"

                        objFldTab1.FreeTable()

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "PARVW"
                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "LIFNR"
                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If

                        Dim ii As Integer = 0

                        If objDatTab1.Rows.Count = 0 Then
                            MsgBox("Transporter Not mentioned in DO. !", vbInformation, "ElectroSteel Castings Limited")
                        Else
                            For Each objDatRec1 In objDatTab1.Rows

                                For Each objFldRec1 In objFldTab1.Rows
                                    ii = ii + 1

                                    If ii = 3 Then
                                        txtTransporterCode.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                    End If

                                    'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                Next
                            Next
                        End If

                        ''****************************************TTTTTTTTTTTT

                        '''*************************************** KKKKKKKKKKKKKKKKKK
                        If txtTransporterCode.Text <> "" Then

                            Call SAP_Con2()

                            If sapConnection.Logon(0, True) <> True Then
                                MsgBox("No connection to SAP System .......")
                                Exit Sub
                            End If

                            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                            objQueryTab1.Value = "LFA1"
                            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                            objFldTab1 = objRfcFunc1.Tables("FIELDS")
                            objDatTab1 = objRfcFunc1.Tables("DATA")
                            objOptTab1.FreeTable()
                            objOptTab1.Rows.Add()
                            objOptTab1(objOptTab1.RowCount, "TEXT") = "LIFNR = '" & (txtTransporterCode.Text) & "'"

                            objFldTab1.FreeTable()

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "NAME1"
                            If objRfcFunc1.Call = False Then
                                MsgBox(objRfcFunc1.Exception)
                            End If

                            ii = 0

                            If objDatTab1.Rows.Count = 0 Then
                                MsgBox("Transporter Not mentioned in DO. !", vbInformation, "ElectroSteel Castings Limited")
                            Else

                                For Each objDatRec1 In objDatTab1.Rows
                                    For Each objFldRec1 In objFldTab1.Rows
                                        'iii = iii + 1

                                        'If iii = 3 Then
                                        txtTransporterName.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                        'End If

                                        'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                    Next
                                Next
                            End If
                        End If
                        ''****************************************KKKKKKKKKKKKKKK
                    End If
                End If
                ''----------------------------------------------
                ''''**********************************************************************************  02 jun 2014 end
            End If
            'ListView1.Items.Add(lvi)
        End If
        SAP_Close1()
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            txtTransporterCode.Focus()
        End If
        Label22.Text = "Total No. Of Items : " & ListView1.Items.Count
    End Sub

    Private Sub txtSAPGateEntryNo_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtSAPGateEntryNo.LostFocus
        txtSAPGateEntryNo.BackColor = Color.White
    End Sub

    Private Sub txtTransporterCode_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTransporterCode.GotFocus
        txtTransporterCode.BackColor = Color.Gold
    End Sub

    Private Sub txtTransporterCode_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransporterCode.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "GATEENTRYTRPT"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub txtTransporterCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransporterCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            ''Text6.SetFocus
            dr = cc.GetDataReader("select * from tbl_Transporter_mst where Transporter_Code = '" & Trim(txtTransporterCode.Text) & "'")
            Try
                While dr.Read
                    txtTransporterName.Text = dr("Transporter_Name")
                    txtDLNo.Focus()
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            txtTransporterName.Focus()
        End If
    End Sub

    Private Sub txtTransporterCode_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTransporterCode.LostFocus
        txtTransporterCode.BackColor = Color.White
    End Sub

    Private Sub txtDLNo_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtDLNo.GotFocus
        txtDLNo.BackColor = Color.Gold
    End Sub

    Private Sub txtDLNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtDLNo.KeyPress
        If AscW(e.KeyChar) = 13 Then
            ds = cc.GetDataset("select * from tbl_Driver_mst where Driv_LIC_No = '" & Trim(txtDLNo.Text) & "'")
            For i As Integer = 0 To ds.Tables(0).Rows.Count - 1
                If ds.Tables(0).Rows(i).Item("BlackListed") = 1 Then
                    MsgBox("Black Listed Driver, Pls donot allow this Driver !", vbCritical, "")
                    txtDLNo.Focus()
                ElseIf ds.Tables(0).Rows(i).Item("Warning") = 1 Then
                    MsgBox("Driver was warned  !!! ", vbInformation, "")
                Else
                    dr = cc.GetDataReader("select * from tbl_GE_Hdr where Driver_LIC_no = '" & Trim(txtDLNo.Text) & "'")
                    Try
                        While dr.Read
                            txtDriverName.Text = dr("Driver_name")
                            dtValidity.Value = Format(dr("Driver_LIC_ValidUpto"), "dd-MM-yyyy")
                        End While
                    Catch ex As Exception

                    End Try
                    dr.Close()
                End If
            Next
            ds.Dispose()
            ds.Clear()
            dtValidity.Focus()
        End If
    End Sub

    Private Sub txtDLNo_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtDLNo.LostFocus
        txtDLNo.BackColor = Color.White

        ds = cc.GetDataset("select * from tbl_Driver_mst where Driv_LIC_No = '" & Trim(txtDLNo.Text) & "'")
        For i As Integer = 0 To ds.Tables(0).Rows.Count - 1
            If ds.Tables(0).Rows(i).Item("BlackListed") = 1 Then
                MsgBox("Black Listed Driver, Pls donot allow this Driver !", vbCritical, "")
                txtDLNo.Focus()
            ElseIf ds.Tables(0).Rows(i).Item("Warning") = 1 Then
                MsgBox("Driver was warned  !!! ", vbInformation, "")
            Else
                dr = cc.GetDataReader("select * from tbl_GE_Hdr where Driver_LIC_no = '" & Trim(txtDLNo.Text) & "'")
                Try
                    While dr.Read
                        txtDriverName.Text = dr("Driver_name")
                        dtValidity.Value = Format(dr("Driver_LIC_ValidUpto"), "dd-MM-yyyy")
                    End While
                Catch ex As Exception

                End Try
                dr.Close()
            End If
        Next


        '---------Blocked by----JNAN----------------------
        'Dim retval As String
        'retval = Dir$(DLICPathServer & Trim(txtDLNo.Text) & ".jpg")
        'If retval = (Trim(txtDLNo.Text) & ".jpg") Then
        '    DLScan.Picture = LoadPicture(DLICPathServer & Trim(txtDLNo.Text) & ".jpg")
        'End If

        '---------------------------------------


        ''''            rec1.ActiveConnection = con
        ''''            rec1.Open "select * from tbl_GE_Hdr where Driver_LIC_no = '" & Trim(txtDLNo.Text) & "'"
        ''''
        ''''                If rec1.EOF = False Then
        ''''                        rec.ActiveConnection = con
        ''''                        rec.Open "select * from tbl_Driver_mst where Driv_LIC_No = '" & Trim(txtDLNo.Text) & "'"
        ''''                        If rec.EOF = False Then
        ''''                                If rec.Fields("BlackListed") = 1 Then
        ''''                                    MsgBox "Black Listed Driver, Pls donot allow this Driver !", vbCritical, ""
        ''''                                    txtDLNo.SetFocus
        ''''                                ElseIf rec.Fields("Warning") = 1 Then
        ''''                                    MsgBox "Driver was warned  !!! ", vbInformation, ""
        ''''                                Else
        ''''                                    txtDriverName.Text = rec1.Fields("Driver_name")
        ''''                                End If
        ''''
        ''''                        End If
        ''''                        rec.Close
        ''''                End If
        ''''            rec1.Close
        'Frame1.Enabled = True
    End Sub

    Private Sub txtDLNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtDLNo.TextChanged
        txtDLNo.Text = UCase(Trim(txtDLNo.Text))
        txtDLNo.SelectionStart = Len(txtDLNo.Text)
    End Sub

    Private Sub txtChNo_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtChNo.GotFocus
        txtChNo.BackColor = Color.Gold

        txtChNo.SelectionStart = 0
        txtChNo.SelectionLength = Len(txtChNo.Text)
    End Sub

    Private Sub txtChNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtChNo.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtChQty.Text = 60
            txtChQty.Focus()
        End If
    End Sub

    Private Sub txtChNo_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtChNo.LostFocus
        txtChNo.BackColor = Color.White
    End Sub

    Private Sub txtChQty_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtChQty.GotFocus
        txtChQty.BackColor = Color.Gold

        txtChQty.SelectionStart = 0
        txtChQty.SelectionLength = Len(txtChQty.Text)
    End Sub

    Private Sub txtChQty_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtChQty.KeyPress

        If AscW(e.KeyChar) = 13 And ListView1.Items.Count = 0 And Trim(ddlRakeNoGroupingRefCode.Text) <> "" Then
            Dim lvi As New ListViewItem
            If rbPurchase.Checked = False Then
                MsgBox("Rake Entry only for PURCH option. Pls check .", vbInformation)
                Exit Sub
            End If

            If Trim(txtChNo.Text) = "" Then
                MsgBox("Challan No cannot be blank !", vbInformation, "ElectroWay")
                txtChNo.Text = ""
                txtChNo.Focus()
                Exit Sub
            End If

            If IsNumeric(txtChQty.Text) = False Then
                MsgBox("Challan Qty. should be numeric only !", vbInformation, "ElectroWay")
                txtChQty.Text = ""
                txtChQty.Focus()
                Exit Sub
            End If

            i = ListView1.Items.Count + 1

            'ListView1.Items(i).Text = POO_NOO
            'ListView1.Items(i).SubItems(1).Text = ""
            'ListView1.Items(i).SubItems(2).Text = POO_LIN_ITEMM
            'ListView1.Items(i).SubItems(3).Text = MateCode
            'ListView1.Items(i).SubItems(4).Text = MateName
            'ListView1.Items(i).SubItems(5).Text = Val(Trim(txtChQty.Text)) + 0
            'ListView1.Items(i).SubItems(6).Text = "TON"
            'ListView1.Items(i).SubItems(7).Text = ""
            'ListView1.Items(i).SubItems(8).Text = Trim(txtChNo.Text) & ""
            'ListView1.Items(i).SubItems(9).Text = ""
            'ListView1.Items(i).SubItems(10).Text = Trim(txtRRNo.Text) & ""
            'ListView1.Items(i).SubItems(11).Text = ""
            'ListView1.Items(i).SubItems(12).Text = ""
            'ListView1.Items(i).SubItems(13).Text = ""
            'ListView1.Items(i).SubItems(14).Text = Trim(ddlRakeNoGroupingRefCode.Text) & ""
            'ListView1.Items(i).SubItems(15).Text = ""
            'ListView1.Items(i).SubItems(16).Text = Vend_cde
            'ListView1.Items(i).SubItems(17).Text = Vend_Name

            '------------------------------
            lvi.Text = POO_NOO
            lvi.SubItems.Add("")
            lvi.SubItems.Add(POO_LIN_ITEMM)
            lvi.SubItems.Add(MateCode)
            lvi.SubItems.Add(MateName)
            lvi.SubItems.Add(Val(Trim(txtChQty.Text)) + 0)
            lvi.SubItems.Add("TON")
            lvi.SubItems.Add("")
            lvi.SubItems.Add(Trim(txtChNo.Text))
            lvi.SubItems.Add("")
            lvi.SubItems.Add(Trim(txtRRNo.Text))
            lvi.SubItems.Add("")
            lvi.SubItems.Add("")
            lvi.SubItems.Add("")
            lvi.SubItems.Add(Trim(ddlRakeNoGroupingRefCode.Text))
            lvi.SubItems.Add("")
            lvi.SubItems.Add(Vend_cde)
            lvi.SubItems.Add(Vend_Name)
            ListView1.Items.Add(lvi)
            txtTransporterCode.Focus()

            txtChNo.Text = ""
            txtChQty.Text = ""
        End If

    End Sub

    Private Sub txtChQty_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtChQty.LostFocus
        txtChQty.BackColor = Color.White
    End Sub

    Private Sub txtVehiclenoSearch_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVehiclenoSearch.KeyPress
        If AscW(e.KeyChar) = 13 Then
            Call btnSearch_Click(sender, e)
        End If
    End Sub

    Private Sub txtDriverName_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtDriverName.GotFocus
        txtDriverName.BackColor = Color.Gold
    End Sub
    Private Sub txtVehiclenoSearch_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVehiclenoSearch.TextChanged

    End Sub

    Private Sub txtSAPGateEntryNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSAPGateEntryNo.TextChanged

    End Sub

    Private Sub ddlRakeNoGroupingRefCode_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlRakeNoGroupingRefCode.SelectedIndexChanged
        txtRakeNoGroupingRefName.Text = ""
        ListView1.Items.Clear()
        txtRRNo.Text = ""
        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlRakeNoGroupingRefCode.Text) & "'")
        Try
            While dr.Read
                txtRakeNoGroupingRefName.Text = dr("Reference_Name") & ""
                txtRRNo.Text = dr("RR_NO") & ""
                POO_NOO = dr("PO_NO")
                POO_LIN_ITEMM = dr("PO_Line_Item")
                MateCode = dr("Mat_Code")
                MateName = dr("Mat_Desc")
                Vend_cde = dr("Vendor_Code")
                Vend_Name = dr("Vendor_Name")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
    End Sub
    Private Sub ListView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView1.DoubleClick
        'ListView1.Items.RemoveAt(ListView1.SelectedIndices(0))
        If rbCONTRACTORITEM.Checked = True Then
            '---------------------------------------------------------------------
            dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN'  and company_code = '" & Trim(txtCompany.Text) & "'")
            'and Plant_Code ='" & Trim(txtPlant.Text) & "'
            Try
                While dr.Read
                    MsgBox("Vehicle already  IN !", vbInformation, "Electrosteel Steels Limited.")
                    txtSAPGateEntryNo.Text = ""
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            '-----------------------------------------------------------------------
            If ListView1.Items.Count > 0 Then
                'Dim frmRequiredDate1 As New frmRequiredDate
                'frmRequiredDate1.Show()
                Dim f2 As New frmRequiredDate
                f2.Owner = Me
                f2.ShowDialog()
            Else
                ''Dim frmRequiredDate1 As New frmRequiredDate
                ''frmRequiredDate1.Show()
                'Dim f2 As New frmRequiredDate
                'f2.Owner = Me
                'f2.ShowDialog()
                Dim control2 = New frmRequiredDate
                'control2.MdiParent = Me
                control2.Show()
            End If
        End If
    End Sub

    Private Sub ListView1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ListView1.SelectedIndexChanged

    End Sub

    Private Sub gvSearch_MouseClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gvSearch.MouseClick
        Try

            Dim selectedCell As DataGridViewCell = gvSearch.SelectedCells(0)
            Dim strItem As String = selectedCell.FormattedValue
            'If selrec1 = "" Then
            If strItem = "" Then
                Exit Sub
            End If
            '-----------------------------------------------------
            Try
                selrec1 = strItem
                If ListView1.Items.Count > 0 Then
                    MsgBox("Record already exists in the list.", vbInformation, "ElectroWay")
                Else
                    dr = cc.GetDataReader("select * from tbl_GE_Hdr where GE_HDR_ID = '" & selrec1 & "'")
                    If dr.Read = True Then

                        Dim Trn_nmbr_11 As String = dr("TRN_ID")
                        gbVehicleno.Enabled = False
                        gbActivity.Enabled = False
                        gbSAPgateEntry.Enabled = False
                        txtDLNo.Enabled = False
                        txtDriverName.Enabled = False
                        txtRemarksGateIn.Enabled = False
                        txtRemarksGateOut.Visible = True
                        lblGateOut.Visible = True
                        lblRemarksGateOut.Visible = True
                        lblOutTime.Visible = True
                        lblOutDate.Visible = True
                        Label8.Visible = False
                        lblEntryTime.Visible = False
                        dtValidity.Enabled = False

                        TextBox17.Text = dr("GE_HDR_ID")
                        TextBox18.Text = dr("TRN_ID")
                        txtVehicleNo.Text = dr("Vehicle_NO")
                        txtGateNo.Text = dr("Gate_NO")
                        txtPlant.Text = dr("Plant_Code")
                        'Label24.Caption = Format(dr("EntryDateTime"), "dd-MM-yyyy")
                        ''Label24.Caption = Format(dr("EntryDateTime"), "dd-MM-yyyy")
                        txtDLNo.Text = dr("Driver_LIC_NO")
                        txtDriverName.Text = dr("Driver_Name")
                        txtRemarksGateIn.Text = dr("Remarks_IN")
                        txtTransporterCode.Text = dr("Transpoter_Code")
                        txtTransporterName.Text = dr("TransporterName")
                        'Text20.Text = dr("Seal_No")
                        dtValidity.Text = Format(dr("Driver_LIC_ValidUpto"), "dd-MMM-yyyy")

                        If dr("Type_Of_Vehicle") = "PURCH" Then
                            rbPurchase.Checked = True
                        ElseIf dr("Type_Of_Vehicle") = "SALES" Then
                            rbSales.Checked = True
                        ElseIf dr("Type_Of_Vehicle") = "STKTROUT" Then
                            RBSTOCKTRANSFEROUT.Checked = True
                        ElseIf dr("Type_Of_Vehicle") = "INTRDEPT" Then
                            rbInterDept.Checked = True
                        ElseIf dr("Type_Of_Vehicle") = "CONTITEM" Then
                            rbCONTRACTORITEM.Checked = True
                        ElseIf dr("Type_Of_Vehicle") = "SALESRET" Then
                            rbSalesReturn.Checked = True
                        ElseIf dr("Type_Of_Vehicle") = "PURCHRET" Then
                            rbPurchaseReturn.Checked = True
                        ElseIf dr("Type_Of_Vehicle") = "GATEPASS" Then
                            rbGatePass.Checked = True
                        ElseIf dr("Type_Of_Vehicle") = "FLYASH" Then
                            rbFLYASH.Checked = True
                        End If

                    End If
                    dr.Close()


                    dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID = '" & selrec1 & "'")
                    'Dim lvi As New ListViewItem
                    While dr.Read
                        Dim lvi As New ListViewItem
                        Dim v As Integer = ListView1.Items.Count + 1
                        If (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then
                            lvi.Text = dr("PO_NO")
                            'ListView1.Items.Add(v, , dr("PO_NO"))
                        ElseIf (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True) Then
                            lvi.Text = dr("SO_NO")
                        End If
                        lvi.SubItems.Add(dr("DO_No"))
                        If (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then
                            lvi.SubItems.Add(dr("PO_Line_Item"))
                        ElseIf (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True) Then
                            lvi.SubItems.Add(dr("DO_Line_Item"))
                        End If

                        lvi.SubItems.Add(dr("Mat_Code"))
                        lvi.SubItems.Add(dr("Mat_Desc"))
                        lvi.SubItems.Add(dr("DO_Challan_QTY"))
                        lvi.SubItems.Add(dr("UOM"))
                        lvi.SubItems.Add(dr("Unloading_NO"))
                        lvi.SubItems.Add(dr("Challan_No"))
                        lvi.SubItems.Add(dr("Challan_Date"))
                        lvi.SubItems.Add(dr("CN_No"))
                        lvi.SubItems.Add(dr("CN_Date"))
                        lvi.SubItems.Add(dr("WayBill_No"))
                        lvi.SubItems.Add(dr("Unloading_Remarks"))
                        lvi.SubItems.Add(dr("GatePass_No"))
                        lvi.SubItems.Add(dr("SO_Line_Item"))
                        If (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then
                            lvi.SubItems.Add(dr("Vendor_Code"))
                        ElseIf (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True) Then
                            lvi.SubItems.Add(dr("Customer_Code"))
                        End If

                        If (rbPurchase.Checked = True Or rbInterDept.Checked = True Or rbCONTRACTORITEM.Checked = True Or rbPurchaseReturn.Checked = True Or rbGatePass.Checked = True) Then
                            lvi.SubItems.Add(dr("Vendor_Name"))
                        ElseIf (rbSales.Checked = True Or RBSTOCKTRANSFEROUT.Checked = True Or rbSalesReturn.Checked = True) Then
                            lvi.SubItems.Add(dr("Customer_Name"))
                        End If

                        'rec1.MoveNext()
                        ListView1.Items.Add(lvi)
                    End While
                    dr.Close()
                    'ListView1.Items.Add(lvi)


                    '''''LLLLLLLLLLLLLLLLLLLLLL
                    ''Dim retval As String
                    ''retval = Dir$(BlueBookPathServer & Trim(Text6.Text) & ".jpg")
                    ''If retval = (Trim(Text6.Text) & ".jpg") Then
                    ''    picScan.Picture = LoadPicture(BlueBookPathServer & Trim(Text6.Text) & ".jpg")
                    ''End If

                    '''''LLLLLLLLLLLLLLLLLLLLLL

                    '''LLLLLLLLLLLLLLLLLLLLLL
                    '' ''Dim retval As String
                    ''retval = Dir$(DLICPathServer & Trim(txtDLNo.Text) & ".jpg")
                    ''If retval = (Trim(txtDLNo.Text) & ".jpg") Then
                    ''    DLScan.Picture = LoadPicture(DLICPathServer & Trim(txtDLNo.Text) & ".jpg")
                    ''End If

                    '''''LLLLLLLLLLLLLLLLLLLLLL

                    ''LoadPicture(VehicleImagePathServer & "\" & Trim(v_nbmr_11) & Trim(Trn_nmbr_11) & ".jpg")


                    '''LLLLLLLLLLLLLLLLLLLLLL
                    '' ''Dim retval As String
                    ''retval = Dir$(VehicleImagePathServer & Trim(Text6.Text) & Trim(Trn_nmbr_11) & ".jpg")
                    ''If retval = (Trim(Text6.Text) & Trim(Trn_nmbr_11) & ".jpg") Then
                    ''    ''Image1.Picture = LoadPicture(VehicleImagePathServer & Trim(Text6.Text) & Trim(Trn_nmbr_11) & ".jpg")
                    ''End If

                    '''''LLLLLLLLLLLLLLLLLLLLLL



                End If

                'MsgBox selrec1
                ''Unload Me
            Catch ex As Exception

            End Try

        Catch ex As Exception

        End Try
    End Sub
    Private Sub txtTransporterName_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransporterName.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            txtDLNo.Focus()
        End If
    End Sub

    Private Sub txtDriverName_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtDriverName.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            txtRemarksGateIn.Focus()
        End If
    End Sub
    Private Sub txtRemarksGateIn_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtRemarksGateIn.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            btnUpdate.Focus()
        End If
    End Sub

    Private Sub gvSearch_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles gvSearch.CellContentClick

    End Sub

    Private Sub rbFLYASH_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rbFLYASH.CheckedChanged
        If rbFLYASH.Checked = True Then
            'Frame7.Visible = False
            lblSAPGateEntryNo.Text = "D.O. No."
            btnViewGP.Visible = False
            txtSAPGateEntryNo.Enabled = True
            rbFLYASH.Font = New System.Drawing.Font(rbSales.Font, FontStyle.Bold)
        Else
            rbFLYASH.Font = New System.Drawing.Font(rbSales.Font, FontStyle.Regular)
        End If
    End Sub
    Private Sub Fetch_FLY_ASH_DETAILS()
        'ListView1.Columns.Add("PO / SO No.", 250, HorizontalAlignment.Left)
        'ListView1.Columns.Add("DO No.")
        'ListView1.Columns.Add("DO/PO Line Item")
        'ListView1.Columns.Add("Material Code")
        'ListView1.Columns.Add("Material Description")
        'ListView1.Columns.Add("DO/Ch Qty.")
        'ListView1.Columns.Add("Unit")
        'ListView1.Columns.Add("SAP Gate Entry No.")
        'ListView1.Columns.Add("Ch. No.")
        'ListView1.Columns.Add("Challan Date")
        'ListView1.Columns.Add("RR No.")
        'ListView1.Columns.Add("RR Date")
        'ListView1.Columns.Add("LR No.")
        'ListView1.Columns.Add("LR Date")
        'ListView1.Columns.Add("Rake No.")
        'ListView1.Columns.Add("SO Line Item")
        'ListView1.Columns.Add("Customer/Vendor")
        'ListView1.Columns.Add("Customer/Vendor Name")
        Dim VEH_NO, TRNS_NAME, DRV_NAME, MATNR, MAKTX, KUNNR, NAME1, MENGE_ISS, MAINS_ISS
        Call SAP_Con1()
        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            SAP_CON_NOT_AVAIL = 1
            Label25.Text = "SAP CONNECTION NOT AVAILABLE."
            Exit Sub

        Else

            Label25.Text = ""

            ''*************************************************************************** start 111
            'Dim objRfcFunc As Object
            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
            objQueryTab.Value = "ZSECSL_ISSUE"

            objOptTab = objRfcFunc.Tables("OPTIONS")
            objFldTab = objRfcFunc.Tables("FIELDS")
            objDatTab = objRfcFunc.Tables("DATA")
            'First we set the condition
            'Refresh table
            objOptTab.FreeTable()
            'Then set values
            objOptTab.Rows.Add()
            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
            ''objOptTab.Rows.Add
            objOptTab(objOptTab.RowCount, "TEXT") = "ZORDER_NO = '" & Trim(txtSAPGateEntryNo.Text) & "' and VEH_NO ='" & Trim(txtVehicleNo.Text) & "' "

            objFldTab.FreeTable()

            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "MATNR"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "MAKTX"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "MENGE_ISS"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "MAINS_ISS"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRNS_NAME"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "DRV_NAME"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "KUNNR"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"

            If objRfcFunc.Call = False Then
                MsgBox(objRfcFunc.Exception)
            End If

            ''i = 5

            If objDatTab.Rows.Count = 0 Then
                MsgBox("Invalid Vehicle !", vbInformation, "ElectroSteel Castings Limited")
            Else
                '-----------List------------------
                Dim lvi As New ListViewItem
                '---------------------------
                For Each objDatRec In objDatTab.Rows
                    i = i + 1
                    For Each objFldRec In objFldTab.Rows
                        If j = 0 Then
                            lvi.Text = txtSAPGateEntryNo.Text
                            lvi.SubItems.Add(txtSAPGateEntryNo.Text)
                            lvi.SubItems.Add("")
                            MATNR = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                            lvi.SubItems.Add(MATNR)
                        End If
                        'lvi.SubItems.Add("")
                        If j = 1 Then
                            MAKTX = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                            lvi.SubItems.Add(MAKTX)
                            
                        ElseIf j = 2 Then
                            MENGE_ISS = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                            lvi.SubItems.Add(MENGE_ISS)
                        ElseIf j = 3 Then
                            MAINS_ISS = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                            lvi.SubItems.Add(MAINS_ISS)
                            lvi.SubItems.Add("")
                            lvi.SubItems.Add("")
                            lvi.SubItems.Add("")
                            lvi.SubItems.Add("")
                            lvi.SubItems.Add("")
                            lvi.SubItems.Add("")
                            lvi.SubItems.Add("")
                            lvi.SubItems.Add("")
                            lvi.SubItems.Add("")
                        ElseIf j = 4 Then
                            txtTransporterName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                        ElseIf j = 5 Then
                            txtDriverName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                        ElseIf j = 6 Then
                            KUNNR = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                            lvi.SubItems.Add(KUNNR)
                        ElseIf j = 7 Then
                            NAME1 = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                            lvi.SubItems.Add(NAME1)
                        End If
                        j = j + 1
                    Next
                    j = 0
                Next
                ListView1.Items.Add(lvi)
            End If
        End If
    End Sub
End Class