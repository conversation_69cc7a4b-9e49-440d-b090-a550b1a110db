﻿Public Class frmGetMasterFromSAP

    Dim hLib As Long

    Dim ProgramPath As String

    Dim ScanEnabled As Boolean
    ''*******************************
    Dim Date_v As String
    Dim Time_v As String


    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    Dim i As Double
    Dim j As Double


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object
    Dim m As Integer
    Dim cc As New Class1

    Private Sub SAP_Con1()
        theFunc = CreateObject("SAP.Functions")
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection


        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub
    Private Sub frmGetMasterFromSAP_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        dr = cc.GetDataReader("select * from tbl_Customer_Mst order by Last_update_Date desc")
        Try
            While dr.Read
                Dim date1 As Date = dr("Last_update_Date")
                txtImportCustomerMaster.Text = Format(date1, "dd-MM-yyyy")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        '-----------------------------------
        dr = cc.GetDataReader("select * from tbl_Vendor_Mst order by Last_update_Date desc")
        Try
            While dr.Read
                txtImportVendorMaster.Text = Format(dr("Last_update_Date"), "dd-MM-yyyy")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        '-----------------------------------
        dr = cc.GetDataReader("select * from tbl_Material_Mst order by Last_update_Date desc")
        Try
            While dr.Read
                txtImportMaterialMaster.Text = Format(dr("Last_update_Date"), "dd-MM-yyyy")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
    End Sub
    Private Sub btnImportVendorMaster_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImportVendorMaster.Click
        Dim Vendor_Code, Vendor_Name, Vendor_Address As String
        Call SAP_Con1()

        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            Exit Sub
        Else
            ''*************************************************************************** 
            'Dim objRfcFunc As Object
            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
            objQueryTab.Value = "LFA1"
            objOptTab = objRfcFunc.Tables("OPTIONS")
            objFldTab = objRfcFunc.Tables("FIELDS")
            objDatTab = objRfcFunc.Tables("DATA")
            'First we set the condition
            'Refresh table
            objOptTab.FreeTable()
            'Then set values
            objOptTab.Rows.Add()
            objOptTab(objOptTab.RowCount, "TEXT") = "" ''UN_NO = '" & Text13.Text & "' and TRUCK_NO ='" & Text6.Text & "' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

            objFldTab.FreeTable()

            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "LIFNR"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "ORT01"

            If objRfcFunc.Call = False Then
                MsgBox(objRfcFunc.Exception)
            End If

            i = 5

            If objDatTab.Rows.Count = 0 Then
                MsgBox("No data available in SAP !", vbInformation, "ElectroSteel Castings Limited")
            Else

                For Each objDatRec In objDatTab.Rows
                    i = i + 1
                    For Each objFldRec In objFldTab.Rows
                        j = j + 1

                        If j = 1 Then
                            Vendor_Code = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                            'MsgBox Mat_Code
                        ElseIf j = 2 Then
                            Vendor_Name = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                        ElseIf j = 3 Then
                            Vendor_Address = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                            'MsgBox Mat_Name
                        End If
                    Next

                    ds = cc.GetDataset("select * from tbl_Vendor_Mst where Vendor_Code = '" & Vendor_Code & "'")
                    If ds.Tables(0).Rows.Count = 0 Then
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        cm.Connection = con
                        cm.CommandType = CommandType.StoredProcedure
                        cm.CommandText = "sp_ins_tbl_Vendor_Mst"

                        cm.Parameters.Clear()
                        cm.Parameters.AddWithValue("@val_Vendor_Code", Vendor_Code)
                        cm.Parameters.AddWithValue("@val_Vendor_Name", Vendor_Name)
                        cm.Parameters.AddWithValue("@val_Vendor_Address", Vendor_Address)
                        cm.Parameters.AddWithValue("@val_NON_SAP_Item", "0")
                        cm.ExecuteNonQuery()

                    End If
                    ds.Dispose()
                    j = 0
                Next
            End If
        End If

    End Sub

    Private Sub btnImportCustomerMaster_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImportCustomerMaster.Click
        Dim Cust_Code, Cust_Name, Cust_Address As String
        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            Exit Sub

        Else
            ''***************************************************************************
            'Dim objRfcFunc As Object
            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
            objQueryTab.Value = "KNA1"

            objOptTab = objRfcFunc.Tables("OPTIONS")
            objFldTab = objRfcFunc.Tables("FIELDS")
            objDatTab = objRfcFunc.Tables("DATA")
            'First we set the condition
            'Refresh table
            objOptTab.FreeTable()
            'Then set values
            objOptTab.Rows.Add()
            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
            ''objOptTab.Rows.Add
            objOptTab(objOptTab.RowCount, "TEXT") = "" ''UN_NO = '" & Text13.Text & "' and TRUCK_NO ='" & Text6.Text & "' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

            objFldTab.FreeTable()

            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "KUNNR"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "ORT01"

            If objRfcFunc.Call = False Then
                MsgBox(objRfcFunc.Exception)
            End If
            i = 5
            If objDatTab.Rows.Count = 0 Then
                MsgBox("No data available in SAP !", vbInformation, "ElectroSteel Castings Limited")
            Else
                For Each objDatRec In objDatTab.Rows
                    i = i + 1
                    For Each objFldRec In objFldTab.Rows
                        j = j + 1
                        If j = 1 Then
                            Cust_Code = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                            'MsgBox Mat_Code
                        ElseIf j = 2 Then
                            Cust_Name = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                        ElseIf j = 3 Then
                            Cust_Address = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                            'MsgBox Mat_Name
                        End If
                    Next
                    ds = cc.GetDataset("select * from tbl_Customer_Mst where Customer_Code = '" & Cust_Code & "'")
                    If ds.Tables(0).Rows.Count = 0 Then
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        cm.Connection = con
                        cm.CommandType = CommandType.StoredProcedure
                        cm.CommandText = "sp_ins_tbl_Customer_Mst"

                        cm.Parameters.Clear()
                        cm.Parameters.AddWithValue("@val_Customer_Code", Cust_Code)
                        cm.Parameters.AddWithValue("@val_Customer_Name", Cust_Name)
                        cm.Parameters.AddWithValue("@val_Customer_Address", Cust_Address)
                        cm.Parameters.AddWithValue("@val_NON_SAP_Item", "1")
                        cm.ExecuteNonQuery()

                    End If
                    ds.Dispose()
                    j = 0
                Next
            End If
        End If

    End Sub

    Private Sub btnImportMaterialMaster_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImportMaterialMaster.Click
        Dim Mat_Code, Mat_Name As String
        Call SAP_Con1()

        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            Exit Sub
        Else
            ''*************************************************************************** start 111
            'Dim objRfcFunc As Object
            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
            objQueryTab.Value = "MAKT"
            objOptTab = objRfcFunc.Tables("OPTIONS")
            objFldTab = objRfcFunc.Tables("FIELDS")
            objDatTab = objRfcFunc.Tables("DATA")
            'First we set the condition
            'Refresh table
            objOptTab.FreeTable()
            'Then set values
            objOptTab.Rows.Add()
            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
            ''objOptTab.Rows.Add
            objOptTab(objOptTab.RowCount, "TEXT") = "" ''UN_NO = '" & Text13.Text & "' and TRUCK_NO ='" & Text6.Text & "' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

            objFldTab.FreeTable()

            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "MATNR"
            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "MAKTG"

            If objRfcFunc.Call = False Then
                MsgBox(objRfcFunc.Exception)
            End If
            i = 5
            If objDatTab.Rows.Count = 0 Then
                MsgBox("No data available in SAP !", vbInformation, "ElectroSteel Castings Limited")
            Else
                For Each objDatRec In objDatTab.Rows
                    i = i + 1
                    For Each objFldRec In objFldTab.Rows
                        j = j + 1

                        If j = 1 Then
                            Mat_Code = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                            'MsgBox Mat_Code
                        ElseIf j = 2 Then
                            Mat_Name = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                            'MsgBox Mat_Name
                        End If
                    Next
                    ds = cc.GetDataset("select * from tbl_Material_mst where Material_Code = '" & Mat_Code & "'")
                    If ds.Tables(0).Rows.Count = 0 Then
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        cm.Connection = con
                        cm.CommandType = CommandType.StoredProcedure
                        cm.CommandText = "sp_ins_tbl_Material_mst"

                        cm.Parameters.Clear()
                        cm.Parameters.AddWithValue("@val_Mat_Code", Mat_Code)
                        cm.Parameters.AddWithValue("@val_Mat_Name", Mat_Name)
                        cm.Parameters.AddWithValue("@val_Last_update_Date", Format(Today.Date.ToString, "dd-MM-yyyy"))
                        cm.Parameters.AddWithValue("@val_NON_SAP_Item", "0")
                        cm.ExecuteNonQuery()
                    End If
                    ds.Dispose()
                    j = 0
                Next
            End If
        End If
    End Sub
End Class