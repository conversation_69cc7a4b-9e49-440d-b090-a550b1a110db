﻿Public Class frmGouping

    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    Dim i As Double


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object
    Dim m As Integer
    Dim TRN_ID As Double
    Dim lic As Integer
    Dim cc As New Class1
    'Dim lvi As New ListViewItem
    Dim TypeOfVehicle As String
    Private Sub frmGouping_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        dr = cc.GetDataReader("select distinct Grouping_Ref_Code  from tbl_GE_DET where Grouping_ID  =  0 and Grouping_Vehicle_No = '' and Grouping_Ref_Code <> '' order by Grouping_Ref_Code")
        'dr = cc.GetDataReader("select distinct Grouping_Ref_Code  from tbl_GE_DET where Grouping_ID  =  0 and Grouping_Vehicle_No = '' and Grouping_Ref_Code <> '' and Grouping_Ref_Code in (select Reference_Code from tbl_Reference_mst where Disabled = '0') order by Grouping_Ref_Code")
        Try
            While dr.Read
                ddlRake_ReferenceCode.Items.Add(dr("Grouping_Ref_Code"))
            End While
        Catch ex As Exception

        End Try
        dr.Close()

        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        Try
            While dr.Read
                txtPlant.Text = dr("Plant_Name")
                ''Text7.Text = dr("Node_No")
                txtCompany.Text = dr("Company_Code")
            End While
        Catch ex As Exception

        End Try
        dr.Close()

        dr = cc.GetDataReader("select TRAN_name , TRAN_YEAR from tbl_Trans_Mst where Plant_Code  = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' order by TRAN_YEAR Desc ")
        Try
            If dr.Read Then
                Text3.Text = Trim(dr(0)) & "\" & Trim(dr(1)) & "\G\1"
            End If
        Catch ex As Exception

        End Try
        dr.Close()
        txtRakeDesc.Text = "V1"
        ''-----------ListView--------------
        Dim lvwItem As New ListViewItem()
        'lvwItem.Checked = True
        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False
        ListView1.CheckBoxes = True
        'Headings
        ListView1.Columns.Add("Transaction No.", 250, HorizontalAlignment.Left)
        ListView1.Columns.Add("GE_HDR_ID")
        ListView1.Columns.Add("Vehicle No")
        ListView1.Columns.Add("Type Of Vehicle")
        ListView1.Columns.Add("PO No")
        ListView1.Columns.Add("PO line Item")
        ListView1.Columns.Add("Mat Code")
        ListView1.Columns.Add("Mat Description")
        ListView1.Columns.Add("Challan Qty.")
        ListView1.Columns.Add("UOM")
        ListView1.Columns.Add("Net Wt. (KG)")
        ListView1.Columns.Add("Vendor Code")
        ListView1.Columns.Add("Vendor Name")
        ListView1.Columns.Add("Transporter Code")
        ListView1.Columns.Add("Transporter Name")
        ListView1.Columns.Add("EntryDateTime")
        'ListView1.Items.Clear()
        For i As Integer = 0 To ListView1.Columns.Count - 1
            ListView1.Columns(i).Width = -2
        Next
        '------------------------------------
    End Sub
    Private Sub chkAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkAll.CheckedChanged
        Dim lvi As New ListViewItem
        txtTotNo.Text = ""
        txtTotWt.Text = ""

        If chkAll.Checked = False Then
            For i As Integer = 0 To ListView1.Items.Count - 1
                'lvi.Checked = False
                ListView1.Items.Item(i).Checked = False
            Next
            lic = 1
        ElseIf chkAll.Checked = True Then
            For i As Integer = 0 To ListView1.Items.Count - 1
                lvi.Checked = True
                ListView1.Items.Item(i).Checked = True
            Next
            lic = 0
        End If
    End Sub

    Private Sub Combo2_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlRake_ReferenceCode.SelectedIndexChanged
        Dim total_veh As Integer = 0
        Dim sum_gr_wt As Double

        txtTransporterName.Text = ""
        ddlTransporter.Items.Clear()
        txtRakeDesc.Text = ""
        ListView1.Items.Clear()
        chkAll.Checked = 0

        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlRake_ReferenceCode.Text) & "'")
        Try
            If dr.Read Then
                txtRakeDesc.Text = dr("Reference_Name")
                txtRRNo.Text = dr("RR_No")
            End If
        Catch ex As Exception

        End Try
        dr.Close()

        dr = cc.GetDataReader("select distinct a.Transpoter_Code from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID  = b.GE_HDR_ID and  b.Grouping_Ref_Code = '" & Trim(ddlRake_ReferenceCode.Text) & "'")
        Try
            While dr.Read
                ddlTransporter.Items.Add(dr("Transpoter_Code"))
                ''Text6.Text = dr("RR_No")
                'rec1.MoveNext()
            End While
        Catch ex As Exception

        End Try
        dr.Close()

        dr = cc.GetDataReader("select a.*,b.GE_DET_TRAN_ID , b.Type_of_vehicle,b.PO_No , b.PO_Line_Item, b.Mat_code , b.mat_desc , b.DO_Challan_Qty , b.UOM, b.NET_WT , b.Vendor_Code , b.Vendor_Name from  tbl_GE_HDR a , tbl_GE_DET b where a.TRN_ID = b.GE_HDR_TRAN_ID and a.vehicle_status <> 'C' and b.Grouping_Ref_Code = '" & Trim(ddlRake_ReferenceCode.Text) & "' and b.Unloading_No ='' order by b.GE_DET_TRAN_ID")
        Try
            While dr.Read
                sum_gr_wt = sum_gr_wt + dr("Net_wt")
                Dim lvi As New ListViewItem
                'i = ListView1.Items.Count + 1
                'ListView1.Items.Add(i, , dr("GE_DET_TRAN_ID"))
                'ListView1.Items(i).Checked = True
                lvi.Checked = True
                lvi.Text = dr("GE_DET_TRAN_ID")
                lvi.SubItems.Add(dr("GE_HDR_ID") & "")
                lvi.SubItems.Add(dr("Vehicle_NO") & "")
                lvi.SubItems.Add(dr("Type_Of_Vehicle") & "")
                lvi.SubItems.Add(dr("PO_No") & "")
                lvi.SubItems.Add(dr("PO_Line_Item") & "")
                lvi.SubItems.Add(dr("Mat_CODE") & "")
                lvi.SubItems.Add(dr("Mat_Desc") & "")
                If IsNumeric(dr("DO_Challan_Qty")) = True Then
                    lvi.SubItems.Add(CInt(dr("DO_Challan_Qty")) & "")
                Else
                    lvi.SubItems.Add(dr("DO_Challan_Qty") & "")
                End If

                lvi.SubItems.Add(dr("UOM") & "")
                If IsNumeric(dr("NET_WT")) = True Then
                    lvi.SubItems.Add(CInt(dr("NET_WT")))
                Else
                    lvi.SubItems.Add(dr("NET_WT") & "")
                End If

                lvi.SubItems.Add(dr("Vendor_code") & "")
                lvi.SubItems.Add(dr("Vendor_Name") & "")
                lvi.SubItems.Add(dr("Transpoter_Code") & "")
                lvi.SubItems.Add(dr("TransporterName") & "")
                lvi.SubItems.Add(dr("EntryDateTime") & "")
                total_veh = total_veh + 1
                ListView1.Items.Add(lvi)
            End While
        Catch ex As Exception

        End Try
        dr.Close()

        txtTotWt.Text = sum_gr_wt

        txtTotNo.Text = total_veh
        'ListView1.Items.Add(lvi)
    End Sub

    Private Sub Combo1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlTransporter.SelectedIndexChanged
        Dim total_veh As Integer = 0
        Dim sum_gr_wt As Double

        ListView1.Items.Clear()
        txtTotNo.Text = ""
        txtTotWt.Text = ""
        txtTransporterName.Text = ""

        ListView1.Items.Clear()
        chkAll.Checked = 0
        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlRake_ReferenceCode.Text) & "'")
        If dr.Read Then
            txtRakeDesc.Text = dr("Reference_Name")
            txtRRNo.Text = dr("RR_No")
        End If
        dr.Close()

        dr = cc.GetDataReader("select * from tbl_Transporter_mst where Transporter_Code = '" & Trim(ddlTransporter.Text) & "'")
        If dr.Read Then
            txtTransporterName.Text = dr("Transporter_Name")
        End If
        dr.Close()

        dr = cc.GetDataReader("select a.*,b.GE_DET_TRAN_ID , b.Type_of_vehicle,b.PO_No , b.PO_Line_Item, b.Mat_code , b.mat_desc , b.DO_Challan_Qty , b.UOM, b.NET_WT , b.Vendor_Code , b.Vendor_Name from  tbl_GE_HDR a , tbl_GE_DET b where a.TRN_ID = b.GE_HDR_TRAN_ID and a.vehicle_status <> 'C' and b.Grouping_Ref_Code = '" & Trim(ddlRake_ReferenceCode.Text) & "' and b.Unloading_No ='' and a.Transpoter_Code = '" & Trim(ddlTransporter.Text) & "' order by b.GE_DET_TRAN_ID")
        While dr.Read
            Dim lvi As New ListViewItem
            sum_gr_wt = sum_gr_wt + dr("Net_wt")
            i = ListView1.Items.Count + 1
            'ListView1.Items.Add(i, , dr("GE_DET_TRAN_ID"))
            'ListView1.Items(i).Checked = True
            lvi.Checked = True
            lvi.Text = dr("GE_DET_TRAN_ID")
            lvi.SubItems.Add(dr("GE_HDR_ID") & "")
            lvi.SubItems.Add(dr("Vehicle_NO") & "")
            lvi.SubItems.Add(dr("Type_Of_Vehicle") & "")
            lvi.SubItems.Add(dr("PO_No") & "")
            lvi.SubItems.Add(dr("PO_Line_Item") & "")
            lvi.SubItems.Add(dr("Mat_CODE") & "")
            lvi.SubItems.Add(dr("Mat_Desc") & "")
            lvi.SubItems.Add(dr("DO_Challan_Qty") & "")
            lvi.SubItems.Add(dr("UOM") & "")
            lvi.SubItems.Add(dr("NET_WT") & "")
            lvi.SubItems.Add(dr("Vendor_code") & "")
            lvi.SubItems.Add(dr("Vendor_Name") & "")
            lvi.SubItems.Add(dr("Transpoter_Code") & "")
            lvi.SubItems.Add(dr("TransporterName") & "")
            lvi.SubItems.Add(dr("EntryDateTime") & "")
            'ListView1.Items(i).ListSubItems.Add (1), , dr("GE_HDR_ID") & ""
            'ListView1.Items(i).ListSubItems.Add (2), , dr("Vehicle_NO") & ""
            'ListView1.Items(i).ListSubItems.Add (3), , dr("Type_Of_Vehicle") & ""
            'ListView1.Items(i).ListSubItems.Add (4), , dr("PO_No") & ""
            'ListView1.Items(i).ListSubItems.Add (5), , dr("PO_Line_Item") & ""
            'ListView1.Items(i).ListSubItems.Add (6), , dr("Mat_CODE") & ""
            'ListView1.Items(i).ListSubItems.Add (7), , dr("Mat_Desc") & ""
            'ListView1.Items(i).ListSubItems.Add (8), , dr("DO_Challan_Qty") + 0
            'ListView1.Items(i).ListSubItems.Add (9), , dr("UOM") & ""
            'ListView1.Items(i).ListSubItems.Add (10), , dr("NET_WT") + 0
            'ListView1.Items(i).ListSubItems.Add (11), , dr("Vendor_code") & ""
            'ListView1.Items(i).ListSubItems.Add (12), , dr("Vendor_Name") & ""
            'ListView1.Items(i).ListSubItems.Add (13), , dr("Transpoter_Code") & ""
            'ListView1.Items(i).ListSubItems.Add (14), , dr("TransporterName") & ""
            'rec1.MoveNext()
            total_veh = total_veh + 1
            ListView1.Items.Add(lvi)
        End While
        dr.Close()

        txtTotWt.Text = sum_gr_wt

        txtTotNo.Text = total_veh
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        ListView1.Items.Clear()
        txtTotWt.Text = ""
        '------------------------------
        lblMessage.Text = ""
        lblMessage.Refresh()
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        lblMessage.Text = "Please Wait...."
        lblMessage.Refresh()
        Dim Rake_ReferenceCode As String = ddlRake_ReferenceCode.Text.ToString.Trim
        '---------------------------
        Dim funcControl, oRFC, oTrnID, oStatus As Object
        Dim PostCoil, SAP_CON_NOT_AVAIL, CHALLAN_QTY, CHALLAN_QTY_UOM, mul_line, total_Grop_wt As String
        Dim Rake_No As String = ""

        Try

            Dim GroupVhcleNumbr As String
            dr = cc.GetDataReader("select * from tbl_GE_DET where Unloading_No = '" & Trim(txtSAPGateEntryNo.Text) & "'")
            If dr.Read Then
                MsgBox("This SAP Gate Entry No. already mapped... !", vbInformation, "ElectroWay")
                Exit Sub
            End If
            dr.Close()

            functionCtrl = CreateObject("SAP.Functions")
            sapConnection = functionCtrl.Connection
            sapConnection.User = SAPUsere_ID
            sapConnection.Password = SAPUsere_Pass
            sapConnection.System = SAPSys_name
            sapConnection.ApplicationServer = SAPApp_Server
            sapConnection.SystemNumber = SAPSys_No
            sapConnection.Client = SAP_Client
            sapConnection.Language = SAP_Lang
            sapConnection.CodePage = SAP_CodePage
            For i As Integer = 0 To ListView1.Items.Count - 1
                If ListView1.Items.Item(i).Checked = True Then

                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub
                    Else
                        ''MsgBox "Connected ........"

                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        'oTrnID.Value = "9\ES01111111111"    '''Trim((ListView1.Items(i).Text) & "\" & Trim(Right$(WB_TR_ID_FOR_DEL, 4)))
                        oTrnID.Value = Trim((ListView1.Items(i).Text) & "\" & Trim(txtPlant.Text))
                        ''oTrnID.Value = Trim((ListView1.Items(i).Text) & "\" & Trim(Text1.Text))

                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                            End If
                        End If
                    End If
                End If
            Next
            ''**************************************************

            ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO

            Call SAP_Con1()
            If sapConnection.Logon(0, True) <> True Then
                MsgBox("No connection to SAP System .......")
                SAP_CON_NOT_AVAIL = 1
                Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                Exit Sub

            Else

                Label25.Text = ""

                ''*************************************************************************** start 111
                'Dim objRfcFunc As Object
                ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                objQueryTab.Value = "ZTM_HGATE_ENTRY"

                objOptTab = objRfcFunc.Tables("OPTIONS")
                objFldTab = objRfcFunc.Tables("FIELDS")
                objDatTab = objRfcFunc.Tables("DATA")
                'First we set the condition
                'Refresh table
                objOptTab.FreeTable()
                'Then set values
                objOptTab.Rows.Add()
                ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                ''objOptTab.Rows.Add
                objOptTab(objOptTab.RowCount, "TEXT") = "GATENO = '" & txtSAPGateEntryNo.Text.Trim & "' and STATUS <> 'X'"
                '''' and MINESNAME ='" & Trim(Combo2.Text) & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                objFldTab.FreeTable()

                objFldTab.Rows.Add()
                objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME"

                If objRfcFunc.Call = False Then
                    MsgBox(objRfcFunc.Exception)
                End If

                ''i = 5
                If objDatTab.Rows.Count = 0 Then
                    MsgBox("Invalid SAP Gate Entry No. !", vbInformation, "ElectroSteel Steels Limited")
                    Exit Sub
                Else

                    For Each objDatRec In objDatTab.Rows

                        For Each objFldRec In objFldTab.Rows
                            Rake_No = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                        Next
                    Next
                End If
            End If

            If Rake_No.Trim <> Rake_ReferenceCode.Trim Then
                MsgBox("Selected Rake No not matched with SAP Gate Entry Rake No.")
                Exit Sub
            Else
                ''LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL
                Call SAP_Con2()

                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    Exit Sub

                Else
                    objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                    objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                    objQueryTab1.Value = "ZTM_IGATE_ENTRY"
                    objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                    objFldTab1 = objRfcFunc1.Tables("FIELDS")
                    objDatTab1 = objRfcFunc1.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    objOptTab1.FreeTable()
                    'Then set values
                    objOptTab1.Rows.Add()
                    objOptTab1(objOptTab1.RowCount, "TEXT") = "GATENO = '" & txtSAPGateEntryNo.Text.Trim & "'"  '''''' and LOEKZ = ''"

                    objFldTab1.FreeTable()
                    objFldTab1.Rows.Add()
                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "CHAL_QTY"  '' PO number
                    objFldTab1.Rows.Add()
                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MEINS"  '' PO number


                    If objRfcFunc1.Call = False Then
                        MsgBox(objRfcFunc1.Exception)
                    End If

                    For Each objDatRec1 In objDatTab1.Rows
                        Dim u As Integer = 1
                        For Each objFldRec1 In objFldTab1.Rows

                            If u = 1 Then
                                CHALLAN_QTY = Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")

                            ElseIf u = 2 Then
                                CHALLAN_QTY_UOM = Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")
                            End If
                            u = u + 1
                        Next
                        mul_line = mul_line + 1
                    Next
                End If

                If mul_line > 1 Then
                    MsgBox("SAP gate Entry have multiple line Items, hence grouping not allowed.", vbInformation, "ElectroWay")
                    Exit Sub
                End If

                total_Grop_wt = Val(txtTotWt.Text)

                If CHALLAN_QTY_UOM = "TON" Or CHALLAN_QTY_UOM = "TO" Then

                    total_Grop_wt = total_Grop_wt / 1000
                End If

                If Val(CHALLAN_QTY) <> total_Grop_wt Then
                    MsgBox("Total Grouping WT is not equal to SAP Gate Entry Challan Qty.", vbInformation, "ElectroWay")
                    Exit Sub
                End If

                For i As Integer = 0 To ListView1.Items.Count - 1
                    'If ListView1.Items(i).Checked = True Then
                    If ListView1.Items.Item(i).Checked = True Then
                        cm.Connection = con
                        cm.CommandType = CommandType.Text
                        'cm.CommandText = "update tbl_GE_DET set Unloading_No = '" & Trim(Text5.Text) & "'  where GE_DET_TRAN_ID ='" & Trim(ListView1.Items(i).Text) & "'"
                        Dim TranId As String = ListView1.Items.Item(0).Text
                        cm.CommandText = "update tbl_GE_DET set Unloading_No = '" & Trim(txtSAPGateEntryNo.Text.Trim) & "'  where GE_DET_TRAN_ID ='" & Trim(ListView1.Items.Item(i).Text) & "'"
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        cm.ExecuteNonQuery()
                    End If
                Next
                ''LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL
            End If

            ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
            SAP_Close1()
            Call Push_Data()

            MsgBox("Grouping data has been updated successfully !", vbInformation, "ElectroWay")
            'MsgBox GroupVhcleNumbr & "  Grouping vehicle No."
            ListView1.Items.Clear()
            txtTotWt.Text = ""
            txtSAPGateEntryNo.Text = ""
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
        '-------------
        lblMessage.Text = ""
        lblMessage.Refresh()
    End Sub
    Private Sub ListView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView1.DoubleClick
        Try
            Dim f2 As New frmUpdate
            f2.Owner = Me

            f2.txtTransactionNo.Text = ListView1.SelectedItems(0).SubItems(1).Text
            f2.txtChallanQty.Text = ListView1.SelectedItems(0).SubItems(8).Text
            f2.txtTransporterCode.Text = ListView1.SelectedItems(0).SubItems(13).Text
            If ListView1.SelectedItems(0).SubItems(14).Text.Trim <> "" Then
                f2.txtTransporterName.Text = ListView1.SelectedItems(0).SubItems(14).Text
            End If
            f2.ShowDialog()
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SAP_Con1()
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub
    Private Sub SAP_Con2()
        functionCtr2 = CreateObject("SAP.Functions")
        sapConnection = functionCtr2.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
    End Sub
    Private Sub SAP_Close1()
        Try
            functionCtrl.Connection.Logoff()
            'functionCtr2.Connection.Logoff()
            sapConnection = Nothing
            functionCtrl = Nothing
            functionCtr2 = Nothing
        Catch ex As Exception

        End Try
    End Sub
    Private Sub Push_Data()
        Try
            Dim Unld_Nmbr As Integer
            Dim WBWeightDet, pono_wb, ponoLinItm_wb, GE_HDRID1, PostCoil, Result As String
            Dim funcControl, oRFC, oTrnID, oItmID, oVhclNo, oVendor, oPONo, oPOItem, oUnloadingNo, oTrType, oWerks, oTrnDate, oTrnTime, oZInOut, oWTDet, oChWT, oUOM, oGrossWT, oTareWT, oNetWT, oWBTrID, oStatus

            Dim Date_v As String
            Dim Time_v As String
            Dim WT_UOM As String
            Dim ch_wt As Double
            Dim tareeWt As Double
            Dim GrosWT As Double
            Dim NettWT As Double

            Date_v = Format(Today.Date, "yyyy") & Format(Today.Date, "MM") & Format(Today.Date, "dd")
            Time_v = TimeOfDay.ToString("HHmmss")
            'Date_v = Format(Date, "yyyy") & Format(Date, "MM") & Format(Date, "DD")
            'Time_v = Format(Time, "HH") & Format(Minute(Time), "00") & Format(Time, "SS")
            For i As Integer = 0 To ListView1.Items.Count - 1

                If ListView1.Items.Item(i).Checked = True Then

                    Unld_Nmbr = Trim(txtSAPGateEntryNo.Text)

                    ''If TypeOfVehicle = "PURCH" Or TypeOfVehicle = "PURCHRET" Or TypeOfVehicle = "INTRDEPT" Or TypeOfVehicle = "GATEPASS" Then

                    WBWeightDet = ""

                    pono_wb = ListView1.Items(i).SubItems(4).Text & ""
                    ponoLinItm_wb = ListView1.Items(i).SubItems(5).Text & ""
                    ''Call WB_Determin
                    functionCtrl = CreateObject("SAP.Functions")
                    sapConnection = functionCtrl.Connection

                    sapConnection.User = SAPUsere_ID
                    sapConnection.Password = SAPUsere_Pass
                    sapConnection.System = SAPSys_name
                    sapConnection.ApplicationServer = SAPApp_Server
                    sapConnection.SystemNumber = SAPSys_No
                    sapConnection.Client = SAP_Client
                    sapConnection.Language = SAP_Lang
                    sapConnection.CodePage = SAP_CodePage



                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub
                    Else
                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_data_update")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        ''Set oIDItem = oRFC.Exports("ZTR_ID_ITEM")
                        oItmID = oRFC.Exports("ZMATNR")
                        oVhclNo = oRFC.Exports("ZVHCL_NMBR")

                        oVendor = oRFC.Exports("zlifnr")

                        oPONo = oRFC.Exports("ZPO_SO_NO")
                        oPOItem = oRFC.Exports("ZPO_ITEM")
                        oUnloadingNo = oRFC.Exports("ZGATENO")
                        oTrType = oRFC.Exports("ZTR_TYPE")

                        oWerks = oRFC.Exports("zwerks")

                        oTrnDate = oRFC.Exports("ZTRN_DATE")
                        oTrnTime = oRFC.Exports("ZTRN_TIME")

                        oZInOut = oRFC.Exports("zinout")

                        oWTDet = oRFC.Exports("ztweight")

                        oChWT = oRFC.Exports("ZCHL_GTY")
                        oUOM = oRFC.Exports("ZWT_UNIT")
                        oGrossWT = oRFC.Exports("ZGROSS_WT")
                        oTareWT = oRFC.Exports("ZTARE_WT")
                        oNetWT = oRFC.Exports("ZNET_WT")
                        oWBTrID = oRFC.Exports("ZWB_TR_ID")

                        oTrnID.Value = Trim((ListView1.Items(i).Text) & "\" & Trim(txtPlant.Text))               ''    "TEST0001136"
                        ''oIDItem.Value = "00"
                        oItmID.Value = ListView1.Items(i).SubItems(6).Text
                        oVhclNo.Value = ListView1.Items(i).SubItems(2).Text

                        oVendor.Value = Trim(ListView1.Items(i).SubItems(11).Text) & ""

                        oPONo.Value = Trim(ListView1.Items(i).SubItems(5).Text) & ""
                        oPOItem.Value = Trim(ListView1.Items(i).SubItems(4).Text) & ""
                        oUnloadingNo.Value = Trim(Unld_Nmbr) & ""
                        oTrType.Value = Trim(ListView1.Items(i).SubItems(3).Text) & ""
                        'oItmID.Value = ListView1.Items(i).SubItems(5).Text
                        'oVhclNo.Value = ListView1.Items(i).SubItems(1).Text

                        'oVendor.Value = Trim(ListView1.Items(i).SubItems(10).Text) & ""

                        'oPONo.Value = Trim(ListView1.Items(i).SubItems(4).Text) & ""
                        'oPOItem.Value = Trim(ListView1.Items(i).SubItems(3).Text) & ""
                        'oUnloadingNo.Value = Trim(Unld_Nmbr) & ""
                        'oTrType.Value = Trim(ListView1.Items(i).SubItems(2).Text) & ""

                        oWerks.Value = Trim(txtPlant.Text)

                        '''
                        'oTrnDate.Value = Date_v    ''' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        'oTrnTime.Value = Time_v    '''   "103442"      ''format used in ingress is Hour Minute Second without having any space    ''format(Time, "hh:mm:ss")
                        Dim Date1 As Date
                        Date1 = Trim(ListView1.Items(i).SubItems(15).Text) & ""
                        Date_v = Format(Date1, "yyyy") & Format(Date1, "MM") & Format(Date1, "dd")
                        Time_v = Date1.ToString("HHmmss")
                        oTrnDate.Value = Date_v
                        oTrnTime.Value = Time_v
                        '------------
                        If TypeOfVehicle = "PURCH" Then
                            oZInOut.Value = "I"
                        ElseIf TypeOfVehicle = "PURCHRET" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "INTRDEPT" Then
                            oZInOut.Value = "D"
                        ElseIf TypeOfVehicle = "GATEPASS" Then
                            oZInOut.Value = "G"
                        End If

                        oWTDet.Value = WBWeightDet

                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_DET_TRAN_ID = " & ListView1.Items(i).Text)
                        If dr.Read Then
                            GE_HDRID1 = dr("GE_HDR_ID")
                            ch_wt = dr("DO_Challan_QTY")
                            WT_UOM = Trim(dr("UOM"))
                            If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "DMT") Then
                                GrosWT = (dr("F_WT")) / 1000
                                tareeWt = (dr("S_WT")) / 1000
                                NettWT = (dr("Net_WT")) / 1000

                            Else
                                GrosWT = dr("F_WT")
                                tareeWt = dr("S_WT")
                                NettWT = dr("Net_WT")
                            End If

                        End If
                        dr.Close()
                        oChWT.Value = ch_wt
                        'oUOM.Value = WT_UOM
                        If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "KG" Or WT_UOM = "DMT") Then
                            oUOM.Value = "TO"
                        Else
                            oUOM.Value = "KG"
                        End If
                        oGrossWT.Value = GrosWT
                        oTareWT.Value = tareeWt    ''' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oNetWT.Value = NettWT
                        oWBTrID.Value = GE_HDRID1

                        ''   "TEST-VEHICLE052014"

                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                                '''''''''''''''''''''''''''
                                cm.Connection = con
                                cm.CommandType = CommandType.Text
                                cm.CommandText = "update tbl_GE_DET set DataUploadedIN_SAP = 'U' where GE_HDR_ID  ='" & GE_HDRID1 & "'"
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()
                                '''''''''''''''''''''''''''

                            End If
                            If oStatus = 2 Then ' Data Exists in SAP
                                PostCoil = 5
                            End If
                        End If

                        Result = oRFC.Call
                        'MsgBox Result

                        ''End If
                    End If
                End If
                SAP_Close1()
            Next

        Catch ex As Exception
            MsgBox(ex.Message, vbInformation, "ElectroWay")
        End Try
    End Sub

    Private Sub ListView1_ItemChecked(ByVal sender As Object, ByVal e As System.Windows.Forms.ItemCheckedEventArgs) Handles ListView1.ItemChecked
        Try
            Dim total_veh, tot_netwtt As Integer
            For i As Integer = 0 To ListView1.Items.Count - 1
                If ListView1.Items.Item(i).Checked = True Then
                    tot_netwtt = tot_netwtt + Val(Trim(ListView1.Items.Item(i).SubItems(10).Text))
                    total_veh = total_veh + 1
                End If
            Next

            txtTotWt.Text = Val(tot_netwtt)

            txtTotNo.Text = total_veh
        Catch ex As Exception

        End Try

    End Sub

    Private Sub ListView1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ListView1.SelectedIndexChanged

    End Sub
End Class