﻿Public Class frmGrosstareNetWtUpdation

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        On Error GoTo err

        For iu As Integer = 0 To ListView1.Items.Count - 1
            cm.Connection = con
            cm.CommandType = CommandType.Text
            cm.CommandText = "update tbl_GE_DET set S_WT =" & Trim(ListView1.Items(iu).SubItems(2).Text) & " , F_WT =" & Trim(ListView1.Items(iu).SubItems(3).Text) & " where GE_HDR_ID  ='" & Trim(txtTransactionNo.Text) & "' and GE_DET_TRAN_ID =" & Trim(ListView1.Items(iu).Text) & ""
            cm.ExecuteNonQuery()
        Next
        MsgBox("Gross & tare WT updated sucessfully", vbInformation, "ElectroWay")
        Me.Close()

err:
        Err.Clear()
    End Sub

    Private Sub Text2_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtGrossWt.KeyPress
        On Error GoTo err
        If AscW(e.KeyChar) = 13 Then

            If Trim(txtNetWt.Text) <> "" And Trim(txtTareWt.Text) <> "" And Trim(txtGrossWt.Text) <> "" Then
                Dim i As Integer = ListView1.Items.Count + 1
                ListView1.Items.Clear()
                Dim item1 As New ListViewItem("", 0)
                Dim item2 As New ListViewItem("Net WT", 0)
                Dim item3 As New ListViewItem("Gross WT", 1)
                Dim item4 As New ListViewItem("Tare WT", 1)
                item1.SubItems.Add(txtNetWt.Text)
                item1.SubItems.Add(txtTareWt.Text)
                item1.SubItems.Add(txtGrossWt.Text)
                ListView1.Items.AddRange(New ListViewItem() {item1, item2, item3})
                Text6.Text = ""
                txtNetWt.Text = ""
                txtTareWt.Text = ""
                txtGrossWt.Text = ""
            End If


        End If
err:
        Err.Clear()
    End Sub

    Private Sub frmGrosstareNetWtUpdation_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
       
    End Sub
End Class