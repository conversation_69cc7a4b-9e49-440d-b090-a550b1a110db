﻿
Public Class frmGroupingCancel
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object
    Dim functionCtrl As Object
    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    Dim SAP_CON_NOT_AVAIL As Integer
    '-----------------------------
    Dim cc As New Class1
    Private Sub frmGroupingCancel_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtRake_GroupingRefCode.Text = ""
        txtTotNoOfVehicles.Text = ""
        txtTotNetWt.Text = ""
        txtSAPGateEntryNo.Text = ""
        txtSAPGateEntryNo.Enabled = True
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        Call SAP_Con1()

        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            SAP_CON_NOT_AVAIL = 1
            'Label25.Caption = "SAP CONNECTION NOT AVAILABLE."
            Exit Sub
        Else
            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
            objQueryTab.Value = "ZWT_BG"

            objOptTab = objRfcFunc.Tables("OPTIONS")
            objFldTab = objRfcFunc.Tables("FIELDS")
            objDatTab = objRfcFunc.Tables("DATA")
            'First we set the condition
            'Refresh table
            objOptTab.FreeTable()
            'Then set values
            objOptTab.Rows.Add()
            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
            ''objOptTab.Rows.Add
            objOptTab(objOptTab.RowCount, "TEXT") = "GATENO = '" & Trim(txtSAPGateEntryNo.Text) & "'"
            objFldTab.FreeTable()

            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "SHKZG"


            If objRfcFunc.Call = False Then
                MsgBox(objRfcFunc.Exception)
            End If

            If objDatTab.Rows.Count = 0 Then
                'MsgBox "Invalid Transaction No for Cancellation !", vbInformation, "ElectroWay"
            Else

                For Each objDatRec In objDatTab.Rows
                    For Each objFldRec In objFldTab.Rows
                        Dim RefDocNo_11 = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                        If RefDocNo_11 = "S" Then
                            MsgBox("You are not allowed to cancel Grouping data as GRN already done.", vbInformation, "ElectroWay")
                            Exit Sub
                        End If
                    Next
                Next
            End If
        End If

        ''UUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUU
        Dim ans = MsgBox("Are you sure , you want to cancel the Grouping data ?", vbYesNo, "ElectroWay")
        If ans = vbYes Then
            Dim oStatus, PostCoil As String
            '' To delete data from ZWT_BG >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>


            dr = cc.GetDataReader("select * from tbl_GE_DET where Unloading_No = '" & Trim(txtSAPGateEntryNo.Text) & "'")
            While dr.Read
                functionCtrl = CreateObject("SAP.Functions")
                sapConnection = functionCtrl.Connection
                sapConnection.User = SAPUsere_ID
                sapConnection.Password = SAPUsere_Pass
                sapConnection.System = SAPSys_name
                sapConnection.ApplicationServer = SAPApp_Server
                sapConnection.SystemNumber = SAPSys_No
                sapConnection.Client = SAP_Client
                sapConnection.Language = SAP_Lang
                sapConnection.CodePage = SAP_CodePage
                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    SAP_CON_NOT_AVAIL = 1
                    'Label25.Caption = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub
                Else
                    Dim funcControl = CreateObject("SAP.Functions")
                    funcControl.Connection = sapConnection
                    Dim oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                    Dim oTrnID = oRFC.Exports("ZTR_ID")
                    oTrnID.Value = dr("GE_DET_TRAN_ID") & "\ES01" ''Trim(Text3.Text)
                    If oRFC.Call = True Then
                        'oStatus = oRFC.Imports("matnr")
                        ''MsgBox oStatus
                        If oStatus = 1 Then ' fail
                            PostCoil = 1
                        End If
                        If oStatus = 0 Then ' successfully deleted from Zwt_bg table
                            PostCoil = 2
                        End If
                    End If
                End If
                'rec1.MoveNext()
            End While
            dr.Close()

            cm.Connection = con
            cm.CommandType = CommandType.Text
            cm.CommandText = "update tbl_GE_DET set Unloading_No = '' where Unloading_No ='" & Trim(txtSAPGateEntryNo.Text) & "'"
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.ExecuteNonQuery()

            '' To delete data from ZWT_BG >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

            MsgBox("Grouping data has been cancelled sucessfully !", vbInformation, "ElectroWay")
            txtRake_GroupingRefCode.Text = ""
            'Text2.Text = ""
            'Text3.Text = ""
            txtTotNoOfVehicles.Text = ""
            txtTotNetWt.Text = ""
            txtSAPGateEntryNo.Text = ""
        End If
    End Sub

    Private Sub SAP_Con1()
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub

    Private Sub Text6_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtSAPGateEntryNo.KeyPress
        If AscW(e.KeyChar) = 13 Then
            If Trim(txtSAPGateEntryNo.Text) <> "" Then

                dr = cc.GetDataReader("select sum(NET_WT) , count(*) from tbl_GE_DET where Unloading_No = '" & Trim(txtSAPGateEntryNo.Text) & "' group by Unloading_No")
                If dr.Read Then
                    txtTotNoOfVehicles.Text = dr(1)
                    txtTotNetWt.Text = dr(0)
                    txtSAPGateEntryNo.Enabled = False

                End If

                dr.Close()

                dr = cc.GetDataReader("select Grouping_Ref_Code from tbl_GE_DET where Unloading_No = '" & Trim(txtSAPGateEntryNo.Text) & "'")
                If dr.Read Then
                    txtRake_GroupingRefCode.Text = dr("Grouping_Ref_Code")
                End If
                dr.Close()
            End If
        End If
    End Sub
End Class