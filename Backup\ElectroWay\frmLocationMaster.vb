﻿Public Class frmLocationMaster
    Dim cc As New Class1
    Dim Load1 As Boolean = False
    Private Sub frmLocationMaster_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        SelectCompany()
        SelectPlant()
        Load1 = True
        SelectCompanyName()
        SelectPlantName()
    End Sub
    Private Sub SelectPlant()
        Try
            Dim str As String = "select * from tbl_plant_mst"
            dt = cc.GetDataTable(str)
            ddlPlantCode.DataSource = dt

            ddlPlantCode.DisplayMember = "Plant_Code"
            ddlPlantCode.ValueMember = "Plant_Code"
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SelectPlantName()
        If Load1 = True Then
            Dim str As String = "select Plant_Name from tbl_plant_mst where Plant_Code ='" & ddlPlantCode.SelectedValue & "'"
            dr = cc.GetDataReader(str)
            Try
                While dr.Read
                    txtPlantName.Text = dr("Plant_Name").ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub
    Private Sub SelectCompany()
        Try
            Dim str As String = "select * from tbl_Company_mst"
            dt = cc.GetDataTable(str)
            ddlCompnayCode.DataSource = dt

            ddlCompnayCode.DisplayMember = "Company_Code"
            ddlCompnayCode.ValueMember = "Company_Code"
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SelectCompanyName()
        If Load1 = True Then
            Dim str As String = "select * from tbl_Company_mst where company_code = '" & Trim(ddlCompnayCode.SelectedValue) & "'"
            dr = cc.GetDataReader(str)
            Try
                While dr.Read
                    txtCompanyName.Text = dr("Company_Name").ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub
    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtLocationCode.Clear()
        txtLocationAddress.Clear()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If txtLocationCode.Text.Trim <> "" Then
            ''''        rec1.ActiveConnection = con
            ''''        rec1.Open "select * from tbl_plant_mst where Plant_code = '" & Trim(Text1.Text) & "' and Company_Code  = '" & Trim(Combo2.Text) & "'"
            ''''        If rec1.EOF = True Then
            ''''            cmd.ActiveConnection = con
            ''''            cmd.CommandType = adCmdStoredProc
            ''''            cmd.CommandText = "sp_ins_tbl_plant_mst"
            ''''            cmd.Parameters("@val_Plant_Code") = Trim(Text1.Text) & ""
            ''''            cmd.Parameters("@val_Plant_Name") = Trim(Text2.Text) & ""
            ''''            cmd.Parameters("@val_Plant_Address") = Trim(Text3.Text) & ""
            ''''            cmd.Parameters("@val_Company_Code") = Trim(Combo2.Text) & ""
            ''''            cmd.Execute
            ' '''
            ''''            MsgBox "Plant created successfully !", vbInformation, "ElectroWay"
            ' '''
            ''''            Text1.Text = ""
            ''''            Text2.Text = ""
            ''''            Text3.Text = ""
            ''''        Else
            ''''            ans = MsgBox("Plant Code already exists with the seleted company ! Do you want to update?", vbYesNo, "ElectroWay")
            ''''            If ans = vbYes Then
            ''''                cmd.ActiveConnection = con
            ''''                cmd.CommandType = adCmdStoredProc
            ''''                cmd.CommandText = "sp_ins_tbl_plant_mst"
            ''''                cmd.Parameters("@val_Plant_Code") = Trim(Text1.Text) & ""
            ''''                cmd.Parameters("@val_Plant_Name") = Trim(Text2.Text) & ""
            ''''                cmd.Parameters("@val_Plant_Address") = Trim(Text3.Text) & ""
            ''''                cmd.Parameters("@val_Company_Code") = Trim(Combo2.Text) & ""
            ''''                cmd.Execute
            ' '''
            ''''                MsgBox "Plant Updated successfully !", vbInformation, "ElectroWay"
            ''''                Text1.Text = ""
            ''''                Text2.Text = ""
            ''''                Text3.Text = ""
            ''''            End If
            ''''        End If
            ''''        rec1.Close
            ' '''

        Else
            MsgBox("Blank Company Code / Plant Code/ Location Not allowed .", vbInformation, "ElectroWay")

        End If

    End Sub
End Class