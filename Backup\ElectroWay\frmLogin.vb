﻿Imports System.Data
Imports System.Data.SqlClient
Imports System.Management
Imports System.IO
Imports System.Text
Imports System.Configuration
Imports System.Security.Cryptography
Imports Microsoft.Win32
Imports System.Security.Principal
Public Class frmLogin
    Dim cc As New Class1
    Dim bool As Boolean = False
    Dim user1 As String = Nothing
    '-----------------------
    Private enc As System.Text.UTF8Encoding
    Private encryptor As ICryptoTransform
    Private decryptor As ICryptoTransform

#Region "WindowsAuthentication"
    '-----------Windiws validation-----------------
    Private Declare Auto Function LogonUser Lib "advapi32.dll" (ByVal lpszUsername As String, _
   ByVal lpszDomain As String, ByVal lpszPassword As String, ByVal dwLogonType As Integer, _
   ByVal dwLogonProvider As Integer, ByRef phToken As IntPtr) As Integer
    Private Declare Auto Function CloseHandle Lib "kernel32.dll" (ByVal handle As IntPtr) As Boolean

    Private Const LOGON32_LOGON_INTERACTIVE = 2
    Private Const LOGON32_PROVIDER_DEFAULT = 0
    Private Function Login(ByVal userName As String, _
                           ByVal password As String, _
                           Optional ByVal domain As String = "ESL01") As System.Security.Principal.WindowsIdentity
        'If String.IsNullOrEmpty(domain) Then domain = Environment.UserDomainName
        Dim hToken As IntPtr
        If LogonUser(userName, domain, password, LOGON32_LOGON_INTERACTIVE, _
                     LOGON32_PROVIDER_DEFAULT, hToken) Then
            If Not hToken.Equals(IntPtr.Zero) Then
                Dim newId As New WindowsIdentity(hToken)
                CloseHandle(hToken)
                Return newId
            End If
        End If
        Return Nothing
    End Function
    Private Function GetUserGroups(ByVal id As WindowsIdentity) As String
        Dim sb As New System.Text.StringBuilder
        Dim p As New WindowsPrincipal(id)
        For Each knownRole In [Enum].GetValues(GetType(WindowsBuiltInRole))
            If p.IsInRole(knownRole) Then
                sb.AppendLine(knownRole.ToString)
            End If
        Next
        Return vbCrLf & sb.ToString
    End Function
    '----------------------------------------
#End Region
    Private Sub frmLogin_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Dim domainAndUserName As String = Environment.UserDomainName & "\\" & Environment.UserName
        txtUsername.Focus()
        '---------Cryptology-------------------
        Dim KEY_128 As Byte() = {42, 1, 52, 67, 231, 13, 94, 101, 123, 6, 0, 12, 32, 91, 4, 111, 31, 70, 21, 141, 123, 142, 234, 82, 95, 129, 187, 162, 12, 55, 98, 23}
        Dim IV_128 As Byte() = {234, 12, 52, 44, 214, 222, 200, 109, 2, 98, 45, 76, 88, 53, 23, 78}
        Dim symmetricKey As RijndaelManaged = New RijndaelManaged()
        symmetricKey.Mode = CipherMode.CBC

        Me.enc = New System.Text.UTF8Encoding
        Me.encryptor = symmetricKey.CreateEncryptor(KEY_128, IV_128)
        Me.decryptor = symmetricKey.CreateDecryptor(KEY_128, IV_128)
    End Sub
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Application.Exit()
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        'txtUsername.Text = Encrypt(txtUsername.Text)
        'Exit Sub
        '-----------------------------
        If txtUsername.Text.Trim = "" Then
            Exit Sub
        End If
        If txtPassword.Text.Trim = "" Then
            Exit Sub
        End If

        '---------------------------------------
        Dim userID As WindowsIdentity = Login(txtUsername.Text.Trim, txtPassword.Text.Trim)
        If userID Is Nothing Then
            MsgBox("Invalid domain user name or password", MsgBoxStyle.Critical)
            Exit Sub
        Else
            'MsgBox(String.Format("Hello {0}" & vbCrLf & "Your are the member of {1}.", userID.Name, GetUserGroups(userID)))
        End If
        '-----------------------------

        Dim msg As String = Validation()
        lblerror.Text = ""
        If msg <> "" Then
            lblerror.Text = msg
            Exit Sub
        End If
        If lblerror.Text <> "" Then
            Exit Sub
        End If
        txtPassword.Text = Encrypt(txtPassword.Text)
        Try
            'Dim str As String = "select User_ID,User_Name,User_Password from tbl_User_Mst where user_id = '" & Trim(txtUsername.Text) & "' and User_password ='" & Trim(txtPassword.Text) & "'"
            Dim str As String = "select User_ID,User_Name,User_Password from tbl_User_Mst where user_id = '" & Trim(txtUsername.Text) & "' "
            dr = cc.GetDataReader(str)

            While dr.Read
                'If txtUsername.Text = dr("User_ID") And txtPassword.Text = dr("User_Password") Then
                If txtUsername.Text = dr("User_ID") Then
                    bool = True
                    user1 = dr("User_ID").ToString
                End If
            End While
            dr.Close()
        Catch ex As Exception

        End Try
        '----------------------------------
        Dim str1 As String = "Update tbl_User_Mst set Last_Used = getdate() where user_id = '" & Trim(txtUsername.Text) & "' and User_password ='" & Trim(txtPassword.Text) & "'"
        Try
            cc.Execute(str1)
        Catch ex As Exception

        End Try
        '-----------------------
        If bool = True Then
            '---------------------------
            'Dim ValidPass As Boolean = ValidatePassword(txtPassword.Text.Trim)
            'If ValidPass = False Then
            '    'MsgBox(NewPasswordTextbox.Text & " is complex: " & ValidatePassword(Password))
            '    MsgBox("Please change your password !!")
            'End If

            ''Dim chngPassReq As String = chngPassReqired()
            ''If chngPassReq <> "" Then
            ''    MessageBox.Show(chngPassReq, "Paymate", MessageBoxButtons.OK)
            ''End If
            '---------------------
            Me.Hide()
            User_ID = user1
            Dim V As New MDIForm1
            V.MdiParent = MdiParent
            V.Show()
            Exit Sub
        Else
            MsgBox("Wrong User Name or Password !", vbInformation, "ElectroWay")
        End If
    End Sub
    Private Function Validation() As String
        Dim msg As String = ""

        Dim todayDate As Date = Today.Date
        'Dim str As String = "select pwd_expiry_date,active,lock_user from t_user_master where user_name = '" & UsernameTextBox.Text.Trim & "' and Password ='" & PasswordTextBox.Text.Trim & "'"
        Dim str As String = "select pwd_expiry_date,active from tbl_user_mst where user_id = '" & txtUsername.Text.Trim & "' "
        dr = cc.GetDataReader(str)
        Try
            While dr.Read()
                If todayDate > dr("pwd_expiry_date") Then
                    msg = "Yours Account have been Expired !!"
                ElseIf dr("active") = False Then
                    msg = "Yours Account is InAcive Now !!"
                End If
            End While
        Catch ex As Exception
            'lblerror.Text = ex.Message
        End Try
        dr.Close()
        Return msg
    End Function
    Private Function chngPassReqired() As String
        Dim msg As String = ""
        Dim todayDate As Date = Today.Date
        Dim ExpireDate As Date
        Try
            'Dim str As String = "select pwd_expiry_date from t_user_master where user_name = '" & UsernameTextBox.Text.Trim & "' and Password ='" & PasswordTextBox.Text.Trim & "'"
            Dim str As String = "select pwd_expiry_date from tbl_user_mst where User_ID = '" & txtUsername.Text.Trim & "' "
            dr = cc.GetDataReader(str)
            While dr.Read()
                ExpireDate = dr("pwd_expiry_date")
            End While
            dr.Close()
        Catch ex As Exception
            'lblerror.Text = ex.Message
        End Try
        Dim NoOfDays As Integer = DateDiff(DateInterval.Day, todayDate, ExpireDate)
        If NoOfDays <= 15 Then
            msg = "Yours password will be expired within " & NoOfDays & " days! please change your password !!"
        End If
        Return msg
    End Function
    Private Function Encrypt(ByVal clearText As String) As String
        Dim sPlainText As String = clearText
        If Not String.IsNullOrEmpty(sPlainText) Then
            Dim memoryStream As MemoryStream = New MemoryStream()
            Dim cryptoStream As CryptoStream = New CryptoStream(memoryStream, Me.encryptor, CryptoStreamMode.Write)
            cryptoStream.Write(Me.enc.GetBytes(sPlainText), 0, sPlainText.Length)
            cryptoStream.FlushFinalBlock()
            sPlainText = Convert.ToBase64String(memoryStream.ToArray())
            memoryStream.Close()
            cryptoStream.Close()
        End If
        Return sPlainText
    End Function
    Function ValidatePassword(ByVal pwd As String, Optional ByVal minLength As Integer = 8, Optional ByVal numUpper As Integer = 1, Optional ByVal numLower As Integer = 1, Optional ByVal numNumbers As Integer = 1, Optional ByVal numSpecial As Integer = 1) As Boolean
        ' Replace [A-Z] with \p{Lu}, to allow for Unicode uppercase letters.
        Dim upper As New System.Text.RegularExpressions.Regex("[A-Z]")
        Dim lower As New System.Text.RegularExpressions.Regex("[a-z]")
        Dim number As New System.Text.RegularExpressions.Regex("[0-9]")
        ' Special is "none of the above".
        Dim special As New System.Text.RegularExpressions.Regex("[^a-zA-Z0-9]")

        ' Check the length.
        If Len(pwd) < minLength Then Return False
        ' Check for minimum number of occurrences.
        If upper.Matches(pwd).Count < numUpper Then Return False
        If lower.Matches(pwd).Count < numLower Then Return False
        If number.Matches(pwd).Count < numNumbers Then Return False
        If special.Matches(pwd).Count < numSpecial Then Return False

        ' Passed all checks.
        Return True
    End Function
    Private Sub txtUsername_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtUsername.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            txtPassword.Focus()
        End If
    End Sub

    Private Sub txtPassword_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtPassword.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            btnOK.Focus()
            btnOK_Click(sender, e)
        End If
    End Sub
End Class
