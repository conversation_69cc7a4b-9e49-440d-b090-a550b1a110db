﻿Public Class frmMaterial
    Dim cc As New Class1
    Private Sub frmMaterial_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_Material_mst"
            ds = cc.GetDataset(str)
            gvMaterial.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtMaterialCode.Text) <> "" And Trim(txtMaterialName.Text) <> "" Then
            ds = cc.GetDataset("select * from tbl_Material_mst where Material_Code = '" & Trim(txtMaterialCode.Text) & "'")
            If ds.Tables(0).Rows.Count = 0 Then
                'WB_ID = rec1.Fields(0)
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_Material_mst"

                cm.Parameters.AddWithValue("@val_Mat_Code", Trim(txtMaterialCode.Text))
                cm.Parameters.AddWithValue("@val_Mat_Name", Trim(txtMaterialName.Text))
                cm.Parameters.AddWithValue("@val_Last_update_Date", Format(Today.Date, "dd-MM-yyyy"))
                cm.Parameters.AddWithValue("@val_NON_SAP_Item", "1")
                cm.ExecuteNonQuery()
                loadGrid()
                MsgBox("Material Master record has been updated successfully !", vbInformation, "ElectroWay")
                txtMaterialCode.Text = ""
                txtMaterialName.Text = ""
                txtMaterialCode.Focus()
            Else
                Dim ans = MsgBox("Material Master data already exists ! Do you want to update ?", vbYesNo, "ElectroWay")

                If ans = vbYes Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_Material_mst"
                    cm.Parameters.AddWithValue("@val_Mat_Code", Trim(txtMaterialCode.Text))
                    cm.Parameters.AddWithValue("@val_Mat_Name", Trim(txtMaterialName.Text))
                    cm.Parameters.AddWithValue("@val_Last_update_Date", Format(Today.Date, "dd-MM-yyyy"))
                    cm.Parameters.AddWithValue("@val_NON_SAP_Item", "1")
                    cm.ExecuteNonQuery()
                    loadGrid()
                    MsgBox("Material Master record has been updated successfully !", vbInformation, "ElectroWay")
                    txtMaterialCode.Text = ""
                    txtMaterialName.Text = ""
                    txtMaterialCode.Focus()
                End If
            End If
            ds.Dispose()
        Else

            MsgBox("Blank Material Master Record cannot be updated !", vbInformation, "ElectroWay")
        End If
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click

    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
       Clear
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub
    Private Sub Clear()
        txtMaterialCode.Clear()
        txtMaterialName.Clear()
    End Sub

    Private Sub txtMaterialCode_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtMaterialCode.KeyDown
        Help_callfrom = "MATERIAL"
        If e.KeyCode = 112 Or e.KeyCode = 40 Then
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub txtMaterialCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtMaterialCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtMaterialName.Focus()
            dr = cc.GetDataReader("select * from tbl_Material_mst where Material_Code = '" & Trim(txtMaterialCode.Text) & "'")
            Try
                txtMaterialName.Text = dr("Material_Name").ToString
                btnDelete.Enabled = True
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub txtMaterialCode_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtMaterialCode.TextChanged

    End Sub

    Private Sub txtMaterialName_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtMaterialName.KeyPress
        If AscW(e.KeyChar) = 13 Then
            btnUpdate.Focus()
        End If
    End Sub

    Private Sub gvMaterial_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvMaterial.CellMouseClick
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvMaterial.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvMaterial.SelectedRows(i).Index)

                    txtMaterialCode.Text = Convert.ToString(gvMaterial.Rows(index).Cells(1).Value)
                    txtMaterialName.Text = Convert.ToString(gvMaterial.Rows(index).Cells(2).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Try
            Dim str As String = "SELECT * FROM tbl_Material_mst where Material_Code like '%" & txtSearch.Text & "%' or Material_Name like '%" & txtSearch.Text & "%'"
            ds = cc.GetDataset(str)
            gvMaterial.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
End Class