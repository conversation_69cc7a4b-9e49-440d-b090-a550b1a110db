﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmNode
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox
        Me.btnExit = New System.Windows.Forms.Button
        Me.btnCancel = New System.Windows.Forms.Button
        Me.btnUpdate = New System.Windows.Forms.Button
        Me.GroupBox1 = New System.Windows.Forms.GroupBox
        Me.gbWB = New System.Windows.Forms.GroupBox
        Me.txtInputLen = New System.Windows.Forms.TextBox
        Me.Label11 = New System.Windows.Forms.Label
        Me.txtRThreshold = New System.Windows.Forms.TextBox
        Me.txtSettings = New System.Windows.Forms.TextBox
        Me.Label10 = New System.Windows.Forms.Label
        Me.Label9 = New System.Windows.Forms.Label
        Me.txtPortNo = New System.Windows.Forms.TextBox
        Me.Label8 = New System.Windows.Forms.Label
        Me.txtNodeIP = New System.Windows.Forms.TextBox
        Me.Label7 = New System.Windows.Forms.Label
        Me.txtNodeNo = New System.Windows.Forms.TextBox
        Me.ddlNode = New System.Windows.Forms.ComboBox
        Me.ddlPlantCode = New System.Windows.Forms.ComboBox
        Me.Label6 = New System.Windows.Forms.Label
        Me.Label5 = New System.Windows.Forms.Label
        Me.Label4 = New System.Windows.Forms.Label
        Me.txtPlantName = New System.Windows.Forms.TextBox
        Me.txtCompanyName = New System.Windows.Forms.TextBox
        Me.ddlCompnayCode = New System.Windows.Forms.ComboBox
        Me.Label1 = New System.Windows.Forms.Label
        Me.Label3 = New System.Windows.Forms.Label
        Me.Label2 = New System.Windows.Forms.Label
        Me.GroupBox4 = New System.Windows.Forms.GroupBox
        Me.gvNode = New System.Windows.Forms.DataGridView
        Me.GroupBox2.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.gbWB.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        CType(Me.gvNode, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.btnExit)
        Me.GroupBox2.Controls.Add(Me.btnCancel)
        Me.GroupBox2.Controls.Add(Me.btnUpdate)
        Me.GroupBox2.Location = New System.Drawing.Point(14, 209)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(788, 77)
        Me.GroupBox2.TabIndex = 13
        Me.GroupBox2.TabStop = False
        '
        'btnExit
        '
        Me.btnExit.Location = New System.Drawing.Point(669, 28)
        Me.btnExit.Name = "btnExit"
        Me.btnExit.Size = New System.Drawing.Size(75, 30)
        Me.btnExit.TabIndex = 16
        Me.btnExit.Text = "Exit"
        Me.btnExit.UseVisualStyleBackColor = True
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(574, 28)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 30)
        Me.btnCancel.TabIndex = 15
        Me.btnCancel.Text = "Cancel"
        Me.btnCancel.UseVisualStyleBackColor = True
        '
        'btnUpdate
        '
        Me.btnUpdate.Location = New System.Drawing.Point(481, 28)
        Me.btnUpdate.Name = "btnUpdate"
        Me.btnUpdate.Size = New System.Drawing.Size(75, 30)
        Me.btnUpdate.TabIndex = 14
        Me.btnUpdate.Text = "Update"
        Me.btnUpdate.UseVisualStyleBackColor = True
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.gbWB)
        Me.GroupBox1.Controls.Add(Me.txtNodeIP)
        Me.GroupBox1.Controls.Add(Me.Label7)
        Me.GroupBox1.Controls.Add(Me.txtNodeNo)
        Me.GroupBox1.Controls.Add(Me.ddlNode)
        Me.GroupBox1.Controls.Add(Me.ddlPlantCode)
        Me.GroupBox1.Controls.Add(Me.Label6)
        Me.GroupBox1.Controls.Add(Me.Label5)
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.txtPlantName)
        Me.GroupBox1.Controls.Add(Me.txtCompanyName)
        Me.GroupBox1.Controls.Add(Me.ddlCompnayCode)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(790, 190)
        Me.GroupBox1.TabIndex = 12
        Me.GroupBox1.TabStop = False
        '
        'gbWB
        '
        Me.gbWB.Controls.Add(Me.txtInputLen)
        Me.gbWB.Controls.Add(Me.Label11)
        Me.gbWB.Controls.Add(Me.txtRThreshold)
        Me.gbWB.Controls.Add(Me.txtSettings)
        Me.gbWB.Controls.Add(Me.Label10)
        Me.gbWB.Controls.Add(Me.Label9)
        Me.gbWB.Controls.Add(Me.txtPortNo)
        Me.gbWB.Controls.Add(Me.Label8)
        Me.gbWB.ForeColor = System.Drawing.Color.Blue
        Me.gbWB.Location = New System.Drawing.Point(360, 79)
        Me.gbWB.Name = "gbWB"
        Me.gbWB.Size = New System.Drawing.Size(424, 100)
        Me.gbWB.TabIndex = 21
        Me.gbWB.TabStop = False
        Me.gbWB.Text = "Weigh Bridge Settings"
        '
        'txtInputLen
        '
        Me.txtInputLen.Location = New System.Drawing.Point(308, 66)
        Me.txtInputLen.Name = "txtInputLen"
        Me.txtInputLen.Size = New System.Drawing.Size(79, 20)
        Me.txtInputLen.TabIndex = 23
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label11.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label11.Location = New System.Drawing.Point(312, 45)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(67, 17)
        Me.Label11.TabIndex = 22
        Me.Label11.Text = "Input Len"
        '
        'txtRThreshold
        '
        Me.txtRThreshold.Location = New System.Drawing.Point(224, 66)
        Me.txtRThreshold.Name = "txtRThreshold"
        Me.txtRThreshold.Size = New System.Drawing.Size(79, 20)
        Me.txtRThreshold.TabIndex = 22
        '
        'txtSettings
        '
        Me.txtSettings.Location = New System.Drawing.Point(74, 66)
        Me.txtSettings.Name = "txtSettings"
        Me.txtSettings.Size = New System.Drawing.Size(145, 20)
        Me.txtSettings.TabIndex = 22
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label10.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label10.Location = New System.Drawing.Point(227, 45)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(82, 17)
        Me.Label10.TabIndex = 21
        Me.Label10.Text = "RThreshold"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label9.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label9.Location = New System.Drawing.Point(78, 45)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(59, 17)
        Me.Label9.TabIndex = 21
        Me.Label9.Text = "Settings"
        '
        'txtPortNo
        '
        Me.txtPortNo.Location = New System.Drawing.Point(6, 66)
        Me.txtPortNo.Name = "txtPortNo"
        Me.txtPortNo.Size = New System.Drawing.Size(63, 20)
        Me.txtPortNo.TabIndex = 20
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label8.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label8.Location = New System.Drawing.Point(9, 45)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(60, 17)
        Me.Label8.TabIndex = 19
        Me.Label8.Text = "Port No."
        '
        'txtNodeIP
        '
        Me.txtNodeIP.Location = New System.Drawing.Point(253, 146)
        Me.txtNodeIP.Name = "txtNodeIP"
        Me.txtNodeIP.Size = New System.Drawing.Size(91, 20)
        Me.txtNodeIP.TabIndex = 20
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label7.Location = New System.Drawing.Point(256, 125)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(58, 17)
        Me.Label7.TabIndex = 19
        Me.Label7.Text = "Node IP"
        '
        'txtNodeNo
        '
        Me.txtNodeNo.Location = New System.Drawing.Point(169, 146)
        Me.txtNodeNo.Name = "txtNodeNo"
        Me.txtNodeNo.Size = New System.Drawing.Size(79, 20)
        Me.txtNodeNo.TabIndex = 18
        '
        'ddlNode
        '
        Me.ddlNode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.ddlNode.FormattingEnabled = True
        Me.ddlNode.Items.AddRange(New Object() {"GATE", "WEIGH BRIDGE", "ROUTE/CHECK POST"})
        Me.ddlNode.Location = New System.Drawing.Point(12, 145)
        Me.ddlNode.Name = "ddlNode"
        Me.ddlNode.Size = New System.Drawing.Size(151, 21)
        Me.ddlNode.TabIndex = 17
        '
        'ddlPlantCode
        '
        Me.ddlPlantCode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.ddlPlantCode.FormattingEnabled = True
        Me.ddlPlantCode.Location = New System.Drawing.Point(12, 85)
        Me.ddlPlantCode.Name = "ddlPlantCode"
        Me.ddlPlantCode.Size = New System.Drawing.Size(151, 21)
        Me.ddlPlantCode.TabIndex = 10
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label6.Location = New System.Drawing.Point(12, 125)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(83, 17)
        Me.Label6.TabIndex = 16
        Me.Label6.Text = "Node Name"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label5.Location = New System.Drawing.Point(179, 64)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(81, 17)
        Me.Label5.TabIndex = 14
        Me.Label5.Text = "Plant Name"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label4.Location = New System.Drawing.Point(11, 64)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(77, 17)
        Me.Label4.TabIndex = 13
        Me.Label4.Text = "Plant Code"
        '
        'txtPlantName
        '
        Me.txtPlantName.Enabled = False
        Me.txtPlantName.Location = New System.Drawing.Point(180, 85)
        Me.txtPlantName.Name = "txtPlantName"
        Me.txtPlantName.Size = New System.Drawing.Size(164, 20)
        Me.txtPlantName.TabIndex = 11
        '
        'txtCompanyName
        '
        Me.txtCompanyName.Enabled = False
        Me.txtCompanyName.Location = New System.Drawing.Point(180, 37)
        Me.txtCompanyName.Name = "txtCompanyName"
        Me.txtCompanyName.Size = New System.Drawing.Size(164, 20)
        Me.txtCompanyName.TabIndex = 9
        '
        'ddlCompnayCode
        '
        Me.ddlCompnayCode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.ddlCompnayCode.FormattingEnabled = True
        Me.ddlCompnayCode.Location = New System.Drawing.Point(12, 37)
        Me.ddlCompnayCode.Name = "ddlCompnayCode"
        Me.ddlCompnayCode.Size = New System.Drawing.Size(151, 21)
        Me.ddlCompnayCode.TabIndex = 8
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label1.Location = New System.Drawing.Point(11, 16)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(104, 17)
        Me.Label1.TabIndex = 5
        Me.Label1.Text = "Company Code"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label3.Location = New System.Drawing.Point(172, 125)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(68, 17)
        Me.Label3.TabIndex = 7
        Me.Label3.Text = "Node No."
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label2.Location = New System.Drawing.Point(177, 16)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(108, 17)
        Me.Label2.TabIndex = 6
        Me.Label2.Text = "Company Name"
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.gvNode)
        Me.GroupBox4.Location = New System.Drawing.Point(14, 293)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(788, 227)
        Me.GroupBox4.TabIndex = 14
        Me.GroupBox4.TabStop = False
        '
        'gvNode
        '
        Me.gvNode.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.DisplayedCellsExceptHeader
        Me.gvNode.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.gvNode.Location = New System.Drawing.Point(10, 19)
        Me.gvNode.Name = "gvNode"
        Me.gvNode.Size = New System.Drawing.Size(772, 202)
        Me.gvNode.TabIndex = 0
        '
        'frmNode
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(814, 532)
        Me.Controls.Add(Me.GroupBox4)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.GroupBox1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmNode"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "NODE/CLIENT CONFIGURATION"
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.gbWB.ResumeLayout(False)
        Me.gbWB.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        CType(Me.gvNode, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents btnExit As System.Windows.Forms.Button
    Friend WithEvents btnCancel As System.Windows.Forms.Button
    Friend WithEvents btnUpdate As System.Windows.Forms.Button
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents ddlPlantCode As System.Windows.Forms.ComboBox
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents txtPlantName As System.Windows.Forms.TextBox
    Friend WithEvents txtCompanyName As System.Windows.Forms.TextBox
    Friend WithEvents ddlCompnayCode As System.Windows.Forms.ComboBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents ddlNode As System.Windows.Forms.ComboBox
    Friend WithEvents txtNodeIP As System.Windows.Forms.TextBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents txtNodeNo As System.Windows.Forms.TextBox
    Friend WithEvents gbWB As System.Windows.Forms.GroupBox
    Friend WithEvents txtPortNo As System.Windows.Forms.TextBox
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents txtSettings As System.Windows.Forms.TextBox
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents txtInputLen As System.Windows.Forms.TextBox
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents txtRThreshold As System.Windows.Forms.TextBox
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents gvNode As System.Windows.Forms.DataGridView
End Class
