﻿Imports System.IO
Imports System.Text
Imports System.Data
Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Security.Cryptography
Public Class frmPassword
    Dim cc As New Class1
    '-----------------------
    Private enc As System.Text.UTF8Encoding
    Private encryptor As ICryptoTransform
    Private decryptor As ICryptoTransform
    Private Sub frmPassword_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        dr = cc.GetDataReader("select * from tbl_User_Mst where User_ID = '" & User_ID & "'")
        Try
            While dr.Read
                txtUserId.Text = Trim(dr("User_ID"))
                txtUserName.Text = Trim(dr("User_Name"))
            End While
        Catch ex As Exception

        End Try
        dr.Close()

        '---------Cryptology-------------------
        Di<PERSON>Y_128 As Byte() = {42, 1, 52, 67, 231, 13, 94, 101, 123, 6, 0, 12, 32, 91, 4, 111, 31, 70, 21, 141, 123, 142, 234, 82, 95, 129, 187, 162, 12, 55, 98, 23}
        Dim IV_128 As Byte() = {234, 12, 52, 44, 214, 222, 200, 109, 2, 98, 45, 76, 88, 53, 23, 78}
        Dim symmetricKey As RijndaelManaged = New RijndaelManaged()
        symmetricKey.Mode = CipherMode.CBC

        Me.enc = New System.Text.UTF8Encoding
        Me.encryptor = symmetricKey.CreateEncryptor(KEY_128, IV_128)
        Me.decryptor = symmetricKey.CreateDecryptor(KEY_128, IV_128)
    End Sub
    Private Function Encrypt(ByVal clearText As String) As String
        Dim sPlainText As String = clearText
        If Not String.IsNullOrEmpty(sPlainText) Then
            Dim memoryStream As MemoryStream = New MemoryStream()
            Dim cryptoStream As CryptoStream = New CryptoStream(memoryStream, Me.encryptor, CryptoStreamMode.Write)
            cryptoStream.Write(Me.enc.GetBytes(sPlainText), 0, sPlainText.Length)
            cryptoStream.FlushFinalBlock()
            sPlainText = Convert.ToBase64String(memoryStream.ToArray())
            memoryStream.Close()
            cryptoStream.Close()
        End If
        Return sPlainText
    End Function
    Function ValidatePassword(ByVal pwd As String, Optional ByVal minLength As Integer = 8, Optional ByVal numUpper As Integer = 1, Optional ByVal numLower As Integer = 1, Optional ByVal numNumbers As Integer = 1, Optional ByVal numSpecial As Integer = 1) As Boolean
        ' Replace [A-Z] with \p{Lu}, to allow for Unicode uppercase letters.
        Dim upper As New System.Text.RegularExpressions.Regex("[A-Z]")
        Dim lower As New System.Text.RegularExpressions.Regex("[a-z]")
        Dim number As New System.Text.RegularExpressions.Regex("[0-9]")
        ' Special is "none of the above".
        Dim special As New System.Text.RegularExpressions.Regex("[^a-zA-Z0-9]")

        ' Check the length.
        If Len(pwd) < minLength Then Return False
        ' Check for minimum number of occurrences.
        If upper.Matches(pwd).Count < numUpper Then Return False
        If lower.Matches(pwd).Count < numLower Then Return False
        If number.Matches(pwd).Count < numNumbers Then Return False
        If special.Matches(pwd).Count < numSpecial Then Return False

        ' Passed all checks.
        Return True
    End Function
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        '------------------
        Dim ValidPass As Boolean = ValidatePassword(txtConfirmNewPassword.Text.Trim)
        If ValidPass = False Then
            'MsgBox(NewPasswordTextbox.Text & " is complex: " & ValidatePassword(Password))
            MsgBox("Password must be at least 8 Characters long,one alphanumeric,one Uppercase and one Special Character.")
            Exit Sub
        End If
        '--------------------
        Dim PassWord_11 As String
        dr = cc.GetDataReader("select * from tbl_User_Mst where User_ID = '" & User_ID & "'")
        Try
            While dr.Read
                PassWord_11 = dr("User_password")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        '-------------------------------------------------
        If PassWord_11 <> Encrypt(txtOldPassword.Text.Trim) Then
            MsgBox("Old Password not matched !", vbCritical, "ElectroWay")
            txtOldPassword.Text = ""
            txtOldPassword.Focus()
            Exit Sub
        ElseIf Trim(txtNewPassword.Text) <> Trim(txtConfirmNewPassword.Text) Then
            MsgBox("New Password and Confirmed New Password Not matched !", vbCritical, "ElectroWay")
            txtNewPassword.Text = ""
            txtConfirmNewPassword.Text = ""
            txtNewPassword.Focus()
            Exit Sub
        Else
            txtNewPassword.Text = Encrypt(txtNewPassword.Text)
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_upd_tbl_User_Mst_Password"
            cm.Parameters.AddWithValue("@val_User_ID", Trim(txtUserId.Text))
            cm.Parameters.AddWithValue("@val_Password", Trim(txtNewPassword.Text))
            cm.ExecuteNonQuery()
            MsgBox("Password has been updated successfully !", vbInformation, "ElectroWay")
            txtOldPassword.Text = ""
            txtNewPassword.Text = ""
            txtConfirmNewPassword.Text = ""
            Try
                '----------------------------------
                Dim str1 As String = "Update tbl_User_Mst set pwd_expiry_date = DATEADD(DAY, 30, GETDATE()) where user_id = '" & Trim(txtUserId.Text) & "' "
                Try
                    cc.Execute(str1)
                Catch ex As Exception

                End Try
                '-----------------------
            Catch ex As Exception

            End Try
        End If

    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtOldPassword.Text = ""
        txtNewPassword.Text = ""
        txtConfirmNewPassword.Text = ""
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub
End Class