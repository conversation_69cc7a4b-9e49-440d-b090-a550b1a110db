﻿Public Class frmRe_Grouping
    Dim GE_D_T_ID_1 As Double
    Dim GE_H_T_ID_1 As Double
    Dim T_O_vhc As String
    '-----------------------------
    Dim cc As New Class1
    Private Sub frmRe_Grouping_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        dr = cc.GetDataReader("select distinct Reference_Code  from tbl_Reference_Mst where Disabled <> '1'  order by Reference_Code")
        While dr.Read
            'Combo2.AddItem dr("Reference_Code")
            ddlToGroupRefCode.Items.Add(dr("Reference_Code"))
            'Combo3.AddItem(dr("Reference_Code"))
        End While

        dr.Close()

       dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        If dr.Read Then
            Text2.Text = dr("Plant_Name")
            ''Text7.Text = dr("Node_No")
            Text19.Text = dr("Company_Code")
        End If
        dr.Close()

        dr = cc.GetDataReader("select TRAN_name , TRAN_YEAR from tbl_Trans_Mst where Plant_Code  = '" & Trim(Text2.Text) & "' and Company_Code  = '" & Trim(Text19.Text) & "' order by TRAN_YEAR Desc ")
        If dr.Read Then
            'Text3.Text = Trim(dr(0)) & "\" & Trim(dr(1)) & "\G\1"
            'Trans_Name = dr(0)
            'Trans_year = dr(1)
        End If
        dr.Close()
    End Sub

    Private Sub ddlToGroupRefCode_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlToGroupRefCode.SelectedIndexChanged
        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlToGroupRefCode.Text) & "'")
        If dr.Read Then
            Text5.Text = dr("Reference_Name")
            ''Text28.Text = dr("Company_Code")
        End If
        dr.Close()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Text1.Text = ""
        Text3.Text = ""
        Text4.Text = ""
        Text9.Text = ""
        Text7.Text = ""
        Text4.Enabled = True
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If IsNumeric(Trim(Text7.Text)) = False Then
            MsgBox("Pls input numeric value in split Qty.", vbInformation, "ElectroWay")
            Exit Sub
        End If

        If Trim(Text4.Text) <> "" And Trim(Text1.Text) <> "" And Trim(Text9.Text) <> "" And Trim(ddlToGroupRefCode.Text) <> "" Then
            If Val(Text7.Text) > Val(Text9.Text) Then
                MsgBox("You cannot split the weighment Qty. more than Net WT. or Equal Net Qty. ", vbInformation, "ElectroWay")
            Else
                Dim ans1 = MsgBox("Are you sure , you want to split the net qty. for selected Grouping reference ?", vbYesNo, "ElectroWay")
                If ans1 = vbYes Then
                    Dim N_WT11 As Integer = (Val(Text9.Text) - Val(Text7.Text))
                    cm.Connection = con
                    cm.CommandType = CommandType.Text
                    cm.CommandText = "update tbl_GE_DET set NET_WT  = " & N_WT11 & "  where GE_DET_TRAN_ID =  " & GE_D_T_ID_1
                    cm.ExecuteNonQuery()

                    cm.Connection = con
                    cm.CommandType = CommandType.Text
                    cm.CommandText = "insert into tbl_GE_DET select " & GE_H_T_ID_1 & " , '" & Trim(Text4.Text) & "' , '" & T_O_vhc & "' , '' , '' , '' ," & _
                                        "'' , '' , '' , '' , '' , '' , '' , '' , 0, ''  , '' , '' , '' , '' , '' , " & _
                                        "'' , '' , '' , '' , 0 , '' , 0 , '' , '' , '' , 0 , '' , '' ," & Val(Trim(Text7.Text)) & ", '' , '' , '' , 0 , '" & Trim(ddlToGroupRefCode.Text) & "' , '' , '' , 0"

                    cm.ExecuteNonQuery()

                    MsgBox("Weighment Qty. Splitted successfully .", vbInformation, "ElectroWay")
                    Text1.Text = ""
                    Text3.Text = ""
                    Text4.Text = ""
                    Text9.Text = ""
                    Text7.Text = ""
                    Text4.Enabled = True
                End If

            End If
        Else
            MsgBox("Blank Transaction No/Vehicle No/Group Ref.Code /Net Qty. cannot be updated for splitting.")
        End If
    End Sub

    Private Sub Text4_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles Text4.KeyPress
        On Error GoTo err
        If AscW(e.KeyChar) = 13 Then
            'Text4.Text = ""

            dr = cc.GetDataReader("select count(*) from tbl_GE_DET where  GE_HDR_ID = '" & Trim(Text4.Text) & "'")
            If dr.Read And dr(0) = 1 Then
                'Text1.Text = dr("Vehicle_No")
                'Text4.Enabled = False
            Else
                MsgBox("Multiple line items exists for this Transaction no., you cannot split net wt having multiple line items.", vbInformation, "ElectroWay")
                dr.Close()
                Exit Sub
            End If
            dr.Close()

            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(Text4.Text) & "'  and Company_Code = '" & Trim(Text19.Text) & "' and Plant_Code  = '" & Trim(Text2.Text) & "' ")
            If dr.Read Then
                Text1.Text = dr("Vehicle_No")
                'Text1.Text = dr("Vehicle_No")
                Text4.Enabled = False
            Else
                MsgBox("Invalid Transaction No....", vbInformation, "ElectroWay")
                Text4.Text = ""
                Text4.Focus()
            End If
            dr.Close()

            dr = cc.GetDataReader("select sum(NET_WT) from tbl_GE_DET where GE_HDR_ID = '" & Trim(Text4.Text) & "'")
            If dr.Read Then
                Text9.Text = dr(0)
            Else
                'MsgBox "Invalid Transaction No....", vbInformation, "ElectroWay"
                'Text4.Text = ""
                'Text4.SetFocus
            End If
            dr.Close()

            dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID = '" & Trim(Text4.Text) & "'")
            If dr.Read Then
                Text3.Text = dr("Grouping_Ref_Code")
                GE_H_T_ID_1 = dr("GE_HDR_TRAN_ID")
                T_O_vhc = dr("Type_Of_Vehicle")
                GE_D_T_ID_1 = dr("GE_DET_TRAN_ID")
            Else
                'MsgBox "Invalid Transaction No....", vbInformation, "ElectroWay"
                'Text4.Text = ""
                'Text4.SetFocus
            End If
            dr.Close()
        End If
err:
        ''MsgBox err.Number
        If Err.Description <> "" Then
            MsgBox(Err.Number & "  " & Err.Description)
        End If
    End Sub
End Class