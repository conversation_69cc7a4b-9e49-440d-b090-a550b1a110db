﻿Public Class frmReferenceMaster
    Dim cc As New Class1
    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    Dim i As Double


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object

    Private Sub frmReferenceMaster_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_Reference_mst"
            ds = cc.GetDataset(str)
            gvReference.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtRakeRefCode.Text) <> "" And Trim(txtRakeRefName.Text) <> "" Then
            ds = cc.GetDataset("select * from tbl_Reference_mst where Reference_code = '" & Trim(txtRakeRefCode.Text) & "'")
            If ds.Tables(0).Rows.Count = 0 Then
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_Reference_mst"
                cm.Parameters.AddWithValue("@val_Reference_Code", UCase(Trim(txtRakeRefCode.Text)))
                cm.Parameters.AddWithValue("@val_Reference_Name", UCase(Trim(txtRakeRefName.Text)))
                cm.Parameters.AddWithValue("@val_Remarks", Trim(txtRemarks.Text))
                cm.Parameters.AddWithValue("@val_Disabled", cbDisable.Checked)

                cm.Parameters.AddWithValue("@val_AutoUpdate", cbAutoUpdate.Checked)
                cm.Parameters.AddWithValue("@val_Mat_Code", Trim(txtMaterialCode.Text))
                cm.Parameters.AddWithValue("@val_Mat_Desc", Trim(txtMaterialName.Text))
                cm.Parameters.AddWithValue("@val_Vendor_Code", Trim(txtVendorCode.Text))
                cm.Parameters.AddWithValue("@val_Vendor_Name", Trim(txtVendorName.Text))
                cm.Parameters.AddWithValue("@val_Transporter_Code", Trim(txtTransporterCode.Text))
                cm.Parameters.AddWithValue("@val_Transporter_Name", Trim(txtTransporterName.Text))
                cm.Parameters.AddWithValue("@val_PO_No", Trim(txtPONo.Text))
                cm.Parameters.AddWithValue("@val_PO_Line_Item", Trim(txtPOLineItem.Text))
                cm.Parameters.AddWithValue("@val_RR_No", Trim(txtRRNo.Text))

                cm.ExecuteNonQuery()
                loadGrid()
                MsgBox("Grouping Reference created successfully !", vbInformation, "ElectroWay")

                txtRakeRefCode.Text = ""
                txtRakeRefName.Text = ""
                txtRemarks.Text = ""
                cbDisable.Checked = 0

                cbAutoUpdate.Checked = 0
                txtMaterialCode.Text = ""
                txtVendorCode.Text = ""
                txtVendorName.Text = ""
                txtTransporterCode.Text = ""
                txtTransporterName.Text = ""
                txtMaterialName.Text = ""

                txtPONo.Text = ""
                txtPOLineItem.Text = ""
                txtRRNo.Text = ""


            Else
                Dim upans = MsgBox("Grouping Reference already exists ! Are you want to update  ?", vbYesNo, "ElectroWay")

                If upans = vbYes Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_Reference_mst"
                    cm.Parameters.AddWithValue("@val_Reference_Code", Trim(txtRakeRefCode.Text))
                    cm.Parameters.AddWithValue("@val_Reference_Name", Trim(txtRakeRefName.Text))
                    cm.Parameters.AddWithValue("@val_Remarks", Trim(txtRemarks.Text))
                    cm.Parameters.AddWithValue("@val_Disabled", cbDisable.Checked)

                    cm.Parameters.AddWithValue("@val_AutoUpdate", cbAutoUpdate.Checked)
                    cm.Parameters.AddWithValue("@val_Mat_Code", Trim(txtMaterialCode.Text))
                    cm.Parameters.AddWithValue("@val_Mat_Desc", Trim(txtMaterialName.Text))
                    cm.Parameters.AddWithValue("@val_Vendor_Code", Trim(txtVendorCode.Text))
                    cm.Parameters.AddWithValue("@val_Vendor_Name", Trim(txtVendorName.Text))
                    cm.Parameters.AddWithValue("@val_Transporter_Code", Trim(txtTransporterCode.Text))
                    cm.Parameters.AddWithValue("@val_Transporter_Name", Trim(txtTransporterName.Text))
                    cm.Parameters.AddWithValue("@val_PO_No", Trim(txtPONo.Text))
                    cm.Parameters.AddWithValue("@val_PO_Line_Item", Trim(txtPOLineItem.Text))
                    cm.Parameters.AddWithValue("@val_RR_No", Trim(txtRRNo.Text))

                    cm.ExecuteNonQuery()
                    loadGrid()
                    MsgBox("Grouping Reference Updated successfully !", vbInformation, "ElectroWay")


                End If

                txtRakeRefCode.Text = ""
                txtRakeRefName.Text = ""
                txtRemarks.Text = ""
                txtRakeRefCode.Focus()
                cbDisable.Checked = 0

                cbAutoUpdate.Checked = 0
                txtMaterialCode.Text = ""
                txtVendorCode.Text = ""
                txtVendorName.Text = ""
                txtTransporterCode.Text = ""
                txtTransporterName.Text = ""
                txtMaterialName.Text = ""


                txtPONo.Text = ""
                txtPOLineItem.Text = ""
                txtRRNo.Text = ""

            End If
            ds.Dispose()


        Else
            MsgBox("Blank Reference Code & Name Not allowed .", vbInformation, "ElectroWay")

        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtRakeRefCode.Text = ""
        txtRakeRefName.Text = ""
        txtRemarks.Text = ""

        cbAutoUpdate.Checked = 0
        txtMaterialCode.Text = ""
        txtVendorCode.Text = ""
        txtVendorName.Text = ""
        txtTransporterCode.Text = ""
        txtTransporterName.Text = ""
        txtMaterialName.Text = ""


        txtPONo.Text = ""
        txtPOLineItem.Text = ""
        txtRRNo.Text = ""

        cbDisable.Checked = 0
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub txtRakeRefCode_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtRakeRefCode.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "REFERENCE"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()

            ''''        If rec1.State = 1 Then rec1.Close
            ''''        rec1.ActiveConnection = con
            ''''        rec1.Open "select * from tbl_GE_Hdr where Vehicle_Status = 'IN' and Plant_Code = '" & Trim(txtRakeRefName7.Text) & "' and Company_Code = '" & Trim(txtRakeRefName8.Text) & "' order by Vehicle_No"
            ' '''
            ' '''
            ''''            While rec1.EOF = False
            ''''                    i = frmVehicle.ListView1.ListItems.Count + 1
            ''''                        frmVehicle.ListView1.ListItems.Add i, , rec1.Fields("Vehicle_no")
            ''''                        frmVehicle.ListView1.ListItems(i).ListSubItems.Add (1), , rec1.Fields("Type_Of_Vehicle")
            ' '''
            ''''                        '' ListView1.ListItems(k).ListSubItems.Add (m), , ""
            ''''                        ''ListView1.ListItems(i).ListSubItems.Add (1), , rec1.Fields("PO_NO") & ""
            ''''                    rec1.MoveNext
            ''''            Wend
            ''''            rec1.Close
            ''''If frmVehicle.ListView1.ListItems.Count > 0 Then
            ''''    frmVehicle.Show vbModal
            ''''Else
            ''''    MsgBox "No Vehicle exists !", vbInformation
            ''''End If


        End If
    End Sub

    Private Sub txtRakeRefCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtRakeRefCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            dr = cc.GetDataReader("select * from tbl_Reference_mst where Reference_code = '" & Trim(txtRakeRefCode.Text) & "'")
            Try
                While dr.Read
                    txtRakeRefName.Text = dr("Reference_Name")
                    txtRemarks.Text = dr("Remarks")
                    cbDisable.Checked = dr("Disabled")


                    cbAutoUpdate.Checked = dr("Auto_Update")
                    txtMaterialCode.Text = dr("Mat_Code")
                    txtMaterialName.Text = dr("Mat_Desc")
                    txtVendorCode.Text = dr("Vendor_Code")
                    txtVendorName.Text = dr("Vendor_Name")
                    txtTransporterCode.Text = dr("Transporter_Code")
                    txtTransporterName.Text = dr("Transporter_Name")

                    txtPONo.Text = dr("PO_No")
                    txtPOLineItem.Text = dr("PO_Line_Item")
                    txtRRNo.Text = dr("RR_NO")
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub txtPOLineItem_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtPOLineItem.KeyPress
        Dim Vendor_Coddee, Vendor_Namee As String
        Try


            If AscW(e.KeyChar) = 13 Then
                Call SAP_Con1()



                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    'SAP_CON_NOT_AVAIL = 1
                    'Label25.Caption = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub

                Else

                    ''Label25.Caption = ""

                    ''*************************************************************************** start 111
                    'Dim objRfcFunc As Object
                    ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "EKKO"

                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    'Then set values
                    objOptTab.Rows.Add()
                    objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & Trim(txtPONo.Text) & "'"  ''' and "
                    ''objOptTab.Rows.Add
                    'objOptTab(objOptTab.RowCount, "TEXT") = "UN_NO = '" & txtRakeRefCode3.Text & "' and TRUCK_NO ='" & txtVendorName.Text & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "LIFNR"

                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If




                    i = 5

                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid PO/Line no. !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        For Each objDatRec In objDatTab.Rows

                            For Each objFldRec In objFldTab.Rows

                                txtVendorCode.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                            Next
                        Next
                    End If

                End If

                ''\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\


                Call SAP_Con1()



                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    'SAP_CON_NOT_AVAIL = 1
                    'Label25.Caption = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub

                Else

                    ''Label25.Caption = ""

                    ''*************************************************************************** start 111
                    'Dim objRfcFunc As Object
                    ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "EKPO"

                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    'Then set values
                    objOptTab.Rows.Add()
                    objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & Trim(txtPONo.Text) & "' and EBELP ='" & Trim(txtPOLineItem.Text) & "'"  ''' and "
                    ''objOptTab.Rows.Add
                    'objOptTab(objOptTab.RowCount, "TEXT") = "UN_NO = '" & txtRakeRefCode3.Text & "' and TRUCK_NO ='" & txtVendorName.Text & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "MATNR"


                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "TXZ01"

                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If




                    ''i = 5

                    Dim kj As Integer = 1
                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid PO/Line no. !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        For Each objDatRec In objDatTab.Rows

                            For Each objFldRec In objFldTab.Rows

                                If kj = 1 Then

                                    txtMaterialCode.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                Else
                                    txtMaterialName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                End If

                                kj = kj + 1

                            Next
                        Next
                    End If

                End If


                '''PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP



                If Trim(txtVendorCode.Text) <> "" Then
                    Call SAP_Con1()



                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        'SAP_CON_NOT_AVAIL = 1
                        'Label25.Caption = "SAP CONNECTION NOT AVAILABLE."
                        Exit Sub

                    Else

                        ''Label25.Caption = ""

                        ''*************************************************************************** start 111
                        'Dim objRfcFunc As Object
                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        objQueryTab.Value = "LFA1"

                        objOptTab = objRfcFunc.Tables("OPTIONS")
                        objFldTab = objRfcFunc.Tables("FIELDS")
                        objDatTab = objRfcFunc.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        objOptTab.FreeTable()
                        'Then set values
                        objOptTab.Rows.Add()
                        objOptTab(objOptTab.RowCount, "TEXT") = "LIFNR = '" & Trim(txtVendorCode.Text) & "'"  ''''' and EBELP ='" & Trim(txtPONo.Text) & "'"  ''' and "
                        ''objOptTab.Rows.Add
                        'objOptTab(objOptTab.RowCount, "TEXT") = "UN_NO = '" & txtRakeRefCode3.Text & "' and TRUCK_NO ='" & txtVendorName.Text & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                        objFldTab.FreeTable()

                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"


                        If objRfcFunc.Call = False Then
                            MsgBox(objRfcFunc.Exception)
                        End If




                        If objDatTab.Rows.Count = 0 Then
                            MsgBox("Invalid PO/Line no. !", vbInformation, "ElectroSteel Castings Limited")
                        Else

                            For Each objDatRec In objDatTab.Rows

                                For Each objFldRec In objFldTab.Rows

                                    txtVendorName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                Next
                            Next
                        End If

                    End If
                End If

                If Trim(txtVendorCode.Text) = "" Then


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        ''SAP_CON_NOT_AVAIL = 1
                        ''Label25.Caption = "SAP CONNECTION NOT AVAILABLE."
                        Exit Sub

                    Else

                        ''Label25.Caption = ""

                        ''*************************************************************************** start 111
                        'Dim objRfcFunc As Object
                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        objQueryTab.Value = "EKKO"

                        objOptTab = objRfcFunc.Tables("OPTIONS")
                        objFldTab = objRfcFunc.Tables("FIELDS")
                        objDatTab = objRfcFunc.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        objOptTab.FreeTable()
                        'Then set values
                        objOptTab.Rows.Add()
                        ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                        ''objOptTab.Rows.Add
                        objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & Trim(txtPONo.Text) & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                        objFldTab.FreeTable()

                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "RESWK"

                        If objRfcFunc.Call = False Then
                            MsgBox(objRfcFunc.Exception)
                        End If

                        i = 5

                        If objDatTab.Rows.Count = 0 Then
                            ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                        Else

                            For Each objDatRec In objDatTab.Rows
                                i = i + 1
                                For Each objFldRec In objFldTab.Rows
                                    Vendor_Coddee = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                    txtVendorCode.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                    Vendor_Namee = Vendor_Coddee
                                Next
                            Next
                        End If
                    End If


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        'SAP_CON_NOT_AVAIL = 1
                        'Label25.Caption = "SAP CONNECTION NOT AVAILABLE."
                        Exit Sub

                    Else

                        ''Label25.Caption = ""

                        ''*************************************************************************** start 111
                        'Dim objRfcFunc As Object
                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        objQueryTab.Value = "LFA1"

                        objOptTab = objRfcFunc.Tables("OPTIONS")
                        objFldTab = objRfcFunc.Tables("FIELDS")
                        objDatTab = objRfcFunc.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        objOptTab.FreeTable()
                        'Then set values
                        objOptTab.Rows.Add()
                        objOptTab(objOptTab.RowCount, "TEXT") = "WERKS = '" & Trim(Vendor_Coddee) & "'"  ''''' and EBELP ='" & Trim(txtPONo.Text) & "'"  ''' and "
                        ''objOptTab.Rows.Add
                        'objOptTab(objOptTab.RowCount, "TEXT") = "UN_NO = '" & txtRakeRefCode3.Text & "' and TRUCK_NO ='" & txtVendorName.Text & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                        objFldTab.FreeTable()

                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"


                        If objRfcFunc.Call = False Then
                            MsgBox(objRfcFunc.Exception)
                        End If




                        If objDatTab.Rows.Count = 0 Then
                            MsgBox("Invalid PO/Line no. !", vbInformation, "ElectroSteel Castings Limited")
                        Else

                            For Each objDatRec In objDatTab.Rows

                                For Each objFldRec In objFldTab.Rows

                                    txtVendorName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                                Next
                            Next
                        End If

                    End If


                End If


            End If
        Catch ex As Exception
            MsgBox(Err.Description)
        End Try
    End Sub

    Private Sub txtMaterialCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtMaterialCode.KeyPress
        Try
            If AscW(e.KeyChar) = 13 Then
                dr = cc.GetDataReader("select * from tbl_Material_mst where Material_Code = '" & Trim(txtMaterialCode.Text) & "'")
                Try
                    While dr.Read
                        txtMaterialName.Text = dr("Material_Name")
                        txtMaterialName.Focus()
                    End While
                    While dr.Read = False
                        MsgBox("Material Code not exixts in Master !", vbInformation, "ElectroWay")
                        txtMaterialCode.Focus()
                        Exit While
                    End While
                Catch ex As Exception

                End Try
                dr.Close()

            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub txtVendorCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVendorCode.KeyPress
        Try
            If AscW(e.KeyChar) = 13 Then
                dr = cc.GetDataReader("select * from tbl_Vendor_Mst where Vendor_code  = '" & Trim(txtVendorCode.Text) & "'")
                Try
                    While dr.Read
                        txtVendorName.Text = dr("Vendor_Name")
                        txtVendorName.Focus()
                    End While
                    While dr.Read = False
                        MsgBox("Vendor Code Not exists in master.", vbInformation, "ElectroWay")
                        txtVendorCode.Focus()
                        Exit While
                    End While
                Catch ex As Exception

                End Try
                dr.Close()

            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub txtTransporterCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransporterCode.KeyPress
        Try
            If AscW(e.KeyChar) = 13 Then
                dr = cc.GetDataReader("select * from tbl_Transporter_Mst where Transporter_code  = '" & Trim(txtTransporterCode.Text) & "'")
                Try
                    While dr.Read = True
                        txtTransporterName.Text = dr("Transporter_Name")
                        txtTransporterName.Focus()
                    End While
                    While dr.Read = False
                        MsgBox("Transporter Code Not exists in master.", vbInformation, "ElectroWay")
                        txtTransporterName.Focus()
                        Exit Try
                    End While
                Catch ex As Exception

                End Try
                dr.Close()

            End If
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SAP_Con1()
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub
    Private Sub SAP_Con2()
        functionCtr2 = CreateObject("SAP.Functions")
        sapConnection = functionCtr2.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Try
            Dim str As String = "SELECT * FROM tbl_Reference_mst where Reference_Code like '%" & txtSearch.Text & "%' or Reference_Name like '%" & txtSearch.Text & "%' or Mat_Code like '%" & txtSearch.Text & "%' or Mat_Desc like '%" & txtSearch.Text & "%' or Vendor_Code like '%" & txtSearch.Text & "%' or Vendor_Name like '%" & txtSearch.Text & "%' or PO_No like '%" & txtSearch.Text & "%' or PO_Line_Item like '%" & txtSearch.Text & "%'"
            ds = cc.GetDataset(str)
            gvReference.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub

    Private Sub gvReference_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvReference.CellMouseClick
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvReference.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvReference.SelectedRows(i).Index)

                    txtRakeRefCode.Text = Convert.ToString(gvReference.Rows(index).Cells(1).Value)
                    txtRakeRefName.Text = Convert.ToString(gvReference.Rows(index).Cells(2).Value)
                    txtRemarks.Text = Convert.ToString(gvReference.Rows(index).Cells(3).Value)
                    cbDisable.Checked = Convert.ToString(gvReference.Rows(index).Cells(4).Value)
                    cbAutoUpdate.Checked = Convert.ToString(gvReference.Rows(index).Cells(5).Value)
                    txtMaterialCode.Text = Convert.ToString(gvReference.Rows(index).Cells(6).Value)
                    txtMaterialName.Text = Convert.ToString(gvReference.Rows(index).Cells(7).Value)
                    txtVendorCode.Text = Convert.ToString(gvReference.Rows(index).Cells(8).Value)
                    txtVendorName.Text = Convert.ToString(gvReference.Rows(index).Cells(9).Value)
                    txtTransporterCode.Text = Convert.ToString(gvReference.Rows(index).Cells(10).Value)
                    txtTransporterName.Text = Convert.ToString(gvReference.Rows(index).Cells(11).Value)
                    txtPONo.Text = Convert.ToString(gvReference.Rows(index).Cells(12).Value)
                    txtPOLineItem.Text = Convert.ToString(gvReference.Rows(index).Cells(13).Value)
                    txtRRNo.Text = Convert.ToString(gvReference.Rows(index).Cells(14).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
End Class