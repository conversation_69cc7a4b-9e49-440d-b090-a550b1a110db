﻿Public Class frmRequiredDate
    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    Dim i As Double


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object
    Dim m As Integer
    Dim TRN_ID As Double
    Dim SAP_CON_NOT_AVAIL As Integer
    Dim cc As New Class1
    Dim f2 As frmGateEntry = CType(Application.OpenForms("frmGateEntry"), frmGateEntry)
    Private Sub frmRequiredDate_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'f2.txtVehicleNo.Text = txtPO_SONo.Text
        Do While f2.ListView1.Items.Count = 1
            'If Val(f2.ListView1.Items(0).SubItems(5).Text) = 0 Then
            f2.ListView1.Items.Remove(f2.ListView1.Items(0))
            f2.ListView1.Items.Clear()
            'End If
        Loop
        ' ''-----------ListView--------------
        'ListView1.Clear()
        'ListView1.View = View.Details
        'ListView1.GridLines = True
        'ListView1.FullRowSelect = True
        'ListView1.HideSelection = False
        'ListView1.MultiSelect = False

        ''Headings
        'ListView1.Columns.Add("PO / SO No.", 250, HorizontalAlignment.Left)
        'ListView1.Columns.Add("DO No.")
        'ListView1.Columns.Add("DO/PO Line Item")
        'ListView1.Columns.Add("Material Code")
        'ListView1.Columns.Add("Material Description")
        'ListView1.Columns.Add("DO/Ch Qty.")
        'ListView1.Columns.Add("Unit")
        'ListView1.Columns.Add("SAP Gate Entry No.")
        'ListView1.Columns.Add("Ch. No.")
        'ListView1.Columns.Add("Challan Date")
        'ListView1.Columns.Add("RR No.")
        'ListView1.Columns.Add("RR Date")
        'ListView1.Columns.Add("LR No.")
        'ListView1.Columns.Add("LR Date")
        'ListView1.Columns.Add("Rake No.")
        'ListView1.Columns.Add("SO Line Item")
        'ListView1.Columns.Add("Customer/Vendor")
        'ListView1.Columns.Add("Customer/Vendor Name")
        ''ListView1.Items.Clear()
        'For i As Integer = 0 To ListView1.Columns.Count - 1
        '    ListView1.Columns(i).Width = -2
        'Next
        ''----------------------
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        'Try
        '    Dim dtSTR As String = Format(CDate(txtChallanDate.Text), "dd.MMM.yyyy")
        'Catch ex As Exception
        '    MessageBox.Show("Please check the challan date!")
        '    Exit Sub
        'End Try
        Try
            If Val(Trim(txtChallanQuantity.Text)) = 0 Then
                MsgBox("Blank/Zero Challan Qty. not allowed", vbInformation, "ElectroWay")
                Exit Sub
            End If
            'frmGateEntry.ListView1.Items.Clear()
            i = f2.ListView1.Items.Count + 1
            '--------------------
            Dim lvi As New ListViewItem
            lvi.Text = txtPO_SONo.Text
            lvi.SubItems.Add("")
            lvi.SubItems.Add(txtPO_SOLineNo.Text)
            lvi.SubItems.Add(txtMaterialCode.Text)
            lvi.SubItems.Add(txtMaterialName.Text)
            lvi.SubItems.Add(txtChallanQuantity.Text)
            lvi.SubItems.Add(txtUOM.Text)
            lvi.SubItems.Add("")
            lvi.SubItems.Add(txtChallanNo.Text)
            lvi.SubItems.Add(txtChallanDate.Text)
            lvi.SubItems.Add("")
            lvi.SubItems.Add("")
            lvi.SubItems.Add("")
            lvi.SubItems.Add("")
            lvi.SubItems.Add("")
            lvi.SubItems.Add("")
            lvi.SubItems.Add(txtCustVendCode.Text)
            lvi.SubItems.Add(txtCustVendName.Text)
            'ListView1.Items.Add(lvi)
            f2.ListView1.Items.Add(lvi)
            'frmGateEntry.ListView1.Refresh()

            'frmGateEntry.ListView1.Items.Add (i), , Trim(txtPO_SONo.Text) & ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (1), , ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (2), , Trim(txtPO_SOLineNo.Text) & ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (3), , Trim(txtMaterialCode.Text) & ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (4), , Trim(txtMaterialName.Text) & ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (5), , Val(Trim(txtChallanQuantity.Text)) + 0
            'frmGateEntry.ListView1.Items(i).SubItems.Add (6), , Trim(txtUOM.Text) & ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (7), , ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (8), , Trim(txtChallanNo.Text) & ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (9), , Trim(txtChallanDate.Text) & ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (10), , ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (11), , ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (12), , ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (13), , ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (14), , ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (15), , ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (16), , Trim(txtCustVendCode.Text) & ""
            'frmGateEntry.ListView1.Items(i).SubItems.Add (17), , Trim(txtCustVendName.Text) & ""

            txtMaterialName.Text = ""
            Text3.Text = ""

            'txtCustVendName.Text = ""

            Text1.Text = ""

            txtMaterialCode.Text = ""
            'txtCustVendCode.Text = ""

            'txtChallanNo.Text = ""
            'txtChallanDate.Text = ""
            txtChallanQuantity.Text = ""
            txtUOM.Text = ""
            ''Unload Me
        Catch ex As Exception

        End Try
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtChallanDate.Clear()
        txtChallanNo.Clear()
        txtChallanQuantity.Clear()
        txtCustVendCode.Clear()
        txtCustVendName.Clear()
        txtMaterialCode.Clear()
        txtMaterialName.Clear()
        txtPO_SOLineNo.Clear()
        txtPO_SONo.Clear()
        txtUOM.Clear()
    End Sub

    Private Sub txtChallanQuantity_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtChallanQuantity.LostFocus
        Try
            If IsNumeric(Trim(txtChallanQuantity.Text)) = False Then
                MsgBox("Only numeric value can be allowed .", vbInformation, "ElectroWay")
                txtChallanQuantity.Text = ""
                txtChallanQuantity.Focus()
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub txtChallanDate_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtChallanDate.LostFocus
        Try
            If Format(txtChallanDate.Text) = "dd.MM.yyyy" Then
            Else
                'MsgBox "Pls input in specified format", vbInformation, "ElectroWay"
            End If
        Catch ex As Exception

        End Try
    End Sub
    Private Sub txtPO_SOLineNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtPO_SOLineNo.KeyPress
        Try
            Dim kj As Integer
            If AscW(e.KeyChar) = 13 Then
                Call SAP_Con1()

                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    SAP_CON_NOT_AVAIL = 1
                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub

                Else

                    ''Label25.Text = ""

                    ''*************************************************************************** start 111
                    'Dim objRfcFunc As Object
                    ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "EKKO"

                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    'Then set values
                    objOptTab.Rows.Add()
                    objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & Trim(txtPO_SONo.Text) & "'"  ' and "
                    ''objOptTab.Rows.Add
                    'objOptTab(objOptTab.RowCount, "TEXT") = "UN_NO = '" & Text13.Text & "' and TRUCK_NO ='" & txtChallanDate.Text & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "LIFNR"

                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If




                    i = 5

                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid PO/Line no. !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        For Each objDatRec In objDatTab.Rows

                            For Each objFldRec In objFldTab.Rows

                                txtCustVendCode.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                            Next
                        Next
                    End If

                End If

                ''\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\


                Call SAP_Con1()



                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    SAP_CON_NOT_AVAIL = 1
                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub

                Else

                    ''Label25.Text = ""

                    ''*************************************************************************** start 111
                    'Dim objRfcFunc As Object
                    ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "EKPO"

                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    'Then set values
                    objOptTab.Rows.Add()
                    objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & Trim(txtPO_SONo.Text) & "' and EBELP ='" & Trim(txtPO_SOLineNo.Text) & "'"  ''' and "
                    ''objOptTab.Rows.Add
                    'objOptTab(objOptTab.RowCount, "TEXT") = "UN_NO = '" & Text13.Text & "' and TRUCK_NO ='" & txtChallanDate.Text & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "MATNR"


                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "TXZ01"

                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If




                    ''i = 5

                    kj = 1
                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid PO/Line no. !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        For Each objDatRec In objDatTab.Rows

                            For Each objFldRec In objFldTab.Rows

                                If kj = 1 Then

                                    txtMaterialCode.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                Else
                                    txtMaterialName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                End If

                                kj = kj + 1

                            Next
                        Next
                    End If

                End If


                '''PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP




                Call SAP_Con1()



                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    SAP_CON_NOT_AVAIL = 1
                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                    Exit Sub

                Else

                    ''Label25.Text = ""

                    ''*************************************************************************** start 111
                    'Dim objRfcFunc As Object
                    ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "LFA1"

                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    'First we set the condition
                    'Refresh table
                    objOptTab.FreeTable()
                    'Then set values
                    objOptTab.Rows.Add()
                    objOptTab(objOptTab.RowCount, "TEXT") = "LIFNR = '" & Trim(txtCustVendCode.Text) & "'"  ''''' and EBELP ='" & Trim(txtPO_SOLineNo.Text) & "'"  ''' and "
                    ''objOptTab.Rows.Add
                    'objOptTab(objOptTab.RowCount, "TEXT") = "UN_NO = '" & Text13.Text & "' and TRUCK_NO ='" & txtChallanDate.Text & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"


                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If




                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid PO/Line no. !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        For Each objDatRec In objDatTab.Rows

                            For Each objFldRec In objFldTab.Rows

                                txtCustVendName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))

                            Next
                        Next
                    End If

                End If
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub SAP_Con1()
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub

    Private Sub txtMaterialCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtMaterialCode.KeyPress
        If AscW(e.KeyChar) = 13 And Trim(txtMaterialCode.Text) <> "" Then
            dr = cc.GetDataReader("select * from tbl_Material_Mst where Material_code  = '" & Trim(txtMaterialCode.Text) & "'")
            Try
                If dr.Read Then
                    txtMaterialName.Text = dr("Material_Name")

                Else
                    MsgBox("Material Code Not exists in master.", vbInformation, "ElectroWay")
                    txtMaterialCode.Text = ""
                    txtMaterialCode.Focus()
                End If
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub txtCustVendCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtCustVendCode.KeyPress
        If AscW(e.KeyChar) = 13 And Trim(txtCustVendCode.Text) <> "" Then
            If (frmGateEntry.rbPurchase.Checked = True Or frmGateEntry.rbInterDept.Checked = True Or frmGateEntry.rbCONTRACTORITEM.Checked = True Or frmGateEntry.rbPurchaseReturn.Checked = True Or frmGateEntry.rbGatePass.Checked = True) Then

                dr = cc.GetDataReader("select * from tbl_Vendor_Mst where Vendor_code  = '" & Trim(txtCustVendCode.Text) & "'")
                If dr.Read Then
                    txtCustVendName.Text = dr("Vendor_Name")

                Else
                    MsgBox("Vendor Code Not exists in master.", vbInformation, "ElectroWay")
                    txtCustVendCode.Text = ""
                    txtCustVendCode.Focus()

                End If
                dr.Close()

            ElseIf (frmGateEntry.rbSales.Checked = True Or frmGateEntry.RBSTOCKTRANSFEROUT.Checked = True Or frmGateEntry.rbSalesReturn.Checked = True) Then
                dr = cc.GetDataReader("select * from tbl_Customer_Mst where Customer_code  = '" & Trim(txtCustVendCode.Text) & "'")
                If dr.Read Then
                    txtCustVendName.Text = dr("Customer_Name")
                Else
                    MsgBox("Customer Code Not exists in master.", vbInformation, "ElectroWay")
                    txtCustVendCode.Text = ""
                    txtCustVendCode.Focus()

                End If
                dr.Close()
            End If
        End If
    End Sub
End Class