﻿Public Class frmServerConfig
    Dim cc As New Class1
    '------------------------------------
    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object
    Private Sub frmServerConfig_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        LoadList()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtSAPApplicationServer.Text) = "" Then

            MsgBox("Blank Entry cannot be updated !!", vbAbortRetryIgnore, "ElectroWay")
        Else
            Call SapCallFunc()

            If sapConnection.Logon(0, True) <> True Then
                MsgBox("Cannot Logon to SAP, Pls check with SAP System Administrator !", vbCritical, "ElectroWay")
                'SAP_CON_NOT_AVAIL = 1
                Exit Sub
            End If
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_ins_tbl_SAP_Server_Config_mst"
            cm.Parameters.AddWithValue("@val_SAP_Server_Name", "")
            cm.Parameters.AddWithValue("@val_SAP_user", Trim(txtSAPUser.Text))
            cm.Parameters.AddWithValue("@val_SAP_Password", Trim(txtSapPassword.Text))
            cm.Parameters.AddWithValue("@val_SAP_System_ID", Trim(txtSystemId.Text))
            cm.Parameters.AddWithValue("@val_SAP_System_No", Trim(txtSystemNo.Text))
            cm.Parameters.AddWithValue("@val_SAP_Client", Trim(txtSApClient.Text))
            cm.Parameters.AddWithValue("@val_SAP_Lang", Trim(txtSAPLanguage.Text))
            cm.Parameters.AddWithValue("@val_SAP_code_Page", Trim(txtSAPCodePage.Text))
            cm.Parameters.AddWithValue("@val_SAP_Application_Server", Trim(txtSAPApplicationServer.Text))
            cm.Parameters.AddWithValue("@val_SAP_Active_Status", cbActive.Checked)
            cm.ExecuteNonQuery()
            lvSapServerConfig.Clear()
            Call LoadList()
            Call ClearAll()
        End If
    End Sub
    Private Sub SapCallFunc()
        theFunc = CreateObject("SAP.Functions")
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection


        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        ClearAll()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub
    Private Sub ClearAll()
        txtSAPApplicationServer.Text = ""
        txtSAPUser.Text = ""
        txtSapPassword.Text = ""
        txtSystemId.Text = ""
        txtSystemNo.Text = ""
        txtSApClient.Text = ""
        txtSAPLanguage.Text = ""
        txtSAPCodePage.Text = ""
        cbActive.Checked = False
    End Sub
    Private Sub LoadList()
        lvSapServerConfig.Clear()
        lvSapServerConfig.Items.Clear()
        lvSapServerConfig.View = View.Details
        lvSapServerConfig.GridLines = True
        lvSapServerConfig.FullRowSelect = True
        lvSapServerConfig.HideSelection = False
        lvSapServerConfig.MultiSelect = False
        'Headings
        lvSapServerConfig.Columns.Add("SAP_Application_server")
        lvSapServerConfig.Columns.Add("SAP_User")
        lvSapServerConfig.Columns.Add("Password")
        lvSapServerConfig.Columns.Add("SAP_System_ID")
        lvSapServerConfig.Columns.Add("SAP_System_No")
        lvSapServerConfig.Columns.Add("SAP_Client")
        lvSapServerConfig.Columns.Add("SAP_Language")
        lvSapServerConfig.Columns.Add("SAP_Code_Page")
        lvSapServerConfig.Columns.Add("Active_Status")

        'LV.Items.Clear()
        '---------------------------------
        lvSapServerConfig.Items.Clear()
        dr = cc.GetDataReader("select * from tbl_SAP_Server_Config_mst order by SAP_Application_Server ")
        Try
            While dr.Read
                Dim i As Integer = lvSapServerConfig.Items.Count + 1
                Dim lvi As New ListViewItem

                lvi.Text = CStr(dr("SAP_Application_server"))
                'lvi.SubItems.Add(dr("Vehicle_no"))
                lvi.SubItems.Add(dr("SAP_User"))
                lvi.SubItems.Add("")
                lvi.SubItems.Add(dr("SAP_System_ID"))
                lvi.SubItems.Add(dr("SAP_System_No"))
                lvi.SubItems.Add(dr("SAP_Client"))
                lvi.SubItems.Add(dr("SAP_Language"))
                lvi.SubItems.Add(dr("SAP_Code_Page"))
                lvi.SubItems.Add(dr("Active_Status"))
                lvSapServerConfig.Items.Add(lvi)
              
            End While
        Catch ex As Exception

        End Try
        For j As Integer = 0 To 8
            lvSapServerConfig.Columns(j).Width = -2
        Next
        dr.Close()
    End Sub
End Class