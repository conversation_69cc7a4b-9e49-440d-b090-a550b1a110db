﻿Imports System.Data
Imports System.Data.SqlClient
Public Class frmSplitting

    Dim TypeOfVehicle As String
    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    Dim i As Integer


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object
    Dim m As Integer

    Dim WBWeightDet As String
    Dim pono_wb As String
    Dim ponoLinItm_wb As String
    '---------------------
    Dim lvi As ListViewItem
    Dim SAP_CON_NOT_AVAIL As Integer
    Dim cc As New Class1
    Private Sub frmSplitting_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        Try
            If dr.Read Then
                Text1.Text = dr("Plant_Name")
                ''Text7.Text = dr("Node_No")
            End If
        Catch ex As Exception

        End Try
        dr.Close()

        ''-----------ListView--------------
        Dim lvwItem As New ListViewItem()
        lvwItem.Checked = True
        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False

        'Headings
        ListView1.Columns.Add("TRAN_ID")
        ListView1.Columns.Add("PO / SO No.", 250, HorizontalAlignment.Left)
        ListView1.Columns.Add("DO No.")
        ListView1.Columns.Add("DO/PO Line Item")
        ListView1.Columns.Add("Material Code")
        ListView1.Columns.Add("Material Description")
        ListView1.Columns.Add("DO/Ch Qty.")
        ListView1.Columns.Add("Unit")
        ListView1.Columns.Add("NetWT")
        ListView1.Columns.Add("Ch. No.")
        ListView1.Columns.Add("Challan Date")
        ListView1.Columns.Add("SAP Gate Entry No.")
        ListView1.Columns.Add("SO Line Item")
        ListView1.Columns.Add("Customer/Vendor")
        ListView1.Columns.Add("Customer/Vendor Name")
        'ListView1.Items.Clear()
        For i As Integer = 0 To ListView1.Columns.Count - 1
            ListView1.Columns(i).Width = -2
        Next
        '------------------------------------
        ListView2.Clear()
        ListView2.View = View.Details
        ListView2.GridLines = True
        ListView2.FullRowSelect = True
        ListView2.HideSelection = False
        ListView2.MultiSelect = False

        'Headings
        ListView2.Columns.Add("TRAN_ID")
        ListView2.Columns.Add("PO/SONo.", 250, HorizontalAlignment.Left)
        ListView2.Columns.Add("DO No.")
        ListView2.Columns.Add("DO/PO Line Item")
        ListView2.Columns.Add("Material Code")
        ListView2.Columns.Add("Material Description")
        ListView2.Columns.Add("DO/Ch Qty.")
        ListView2.Columns.Add("Unit")
        ListView2.Columns.Add("UnloadingNo")
        ListView2.Columns.Add("Ch. No.")
        ListView2.Columns.Add("Challan Date")
        ListView2.Columns.Add("ConsNo")
        ListView2.Columns.Add("ConsDate")
        ListView2.Columns.Add("WayBillNo")
        ListView2.Columns.Add("UnloadingRemarks")
        ListView2.Columns.Add("GatePassNo")
        ListView2.Columns.Add("SO Line Item")
        ListView2.Columns.Add("Customer/Vendor")
        ListView2.Columns.Add("Customer/VendorName")
        ' ListView2.Items.Clear()
        For i As Integer = 0 To ListView2.Columns.Count - 1
            ListView2.Columns(i).Width = -2
        Next
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        SAP_Close1()
        Try
            Dim funcControl, oRFC, oTrnID, oStatus As Object
            Dim sum_n_wt As Double
            Dim Vehilce_Type_Check, Gross_WT_Sales, Tare_WT_Sales, uploadInSAP, Tre_WTT, WB_TR_ID_FOR_DEL, DET_TRAN_ID_NO, PostCoil As String
            ''''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> for Auto Grooss Tare wt updation
            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(Text5.Text) & "'")
            If dr.Read Then
                Vehilce_Type_Check = dr("Type_Of_Vehicle")
                ''Gross_WT_Sales = dr("S_WT")
            End If
            dr.Close()
            If ListView1.Items.Count > 1 And (Vehilce_Type_Check = "SALES" Or Vehilce_Type_Check = "STKTROUT") Then  ''"SALES" Or TypeOfVeh = "STKTROUT"
                Dim frmGrosstareNetWtUpdation As New frmGrosstareNetWtUpdation
                frmGrosstareNetWtUpdation.Owner = Me
                frmGrosstareNetWtUpdation.ShowDialog()

                frmGrosstareNetWtUpdation.txtTransactionNo.Text = Text5.Text

                dr = cc.GetDataReader("select S_WT from tbl_SPLIT_DET where GE_HDR_ID = '" & Trim(Text5.Text) & "' order by WB_COUNT_ID DESC")
                If dr.Read Then
                    ''Text1.Text = dr("Plant_Name")
                    Gross_WT_Sales = dr("S_WT")
                End If
                dr.Close()

                dr = cc.GetDataReader("select F_WT from tbl_SPLIT_DET where GE_HDR_ID = '" & Trim(Text5.Text) & "' order by WB_COUNT_ID")
                If dr.Read Then
                    ''Text1.Text = dr("Plant_Name")
                    Tare_WT_Sales = dr("F_WT")
                End If
                dr.Close()

                frmGrosstareNetWtUpdation.txtFinalTare.Text = Gross_WT_Sales
                frmGrosstareNetWtUpdation.txtFinalGross.Text = Tare_WT_Sales

                For i_s = 0 To ListView1.Items.Count - 1

                    Tre_WTT = Gross_WT_Sales - Trim(ListView1.Items(i_s).SubItems(8).Text)

                    frmGrosstareNetWtUpdation.ListView1.Items.Add(Trim(ListView1.Items(i_s).Text), i_s)
                    frmGrosstareNetWtUpdation.ListView1.Items(i_s).SubItems.Add(Trim(ListView1.Items(i_s).SubItems(8).Text))
                    frmGrosstareNetWtUpdation.ListView1.Items(i_s).SubItems.Add(Gross_WT_Sales)
                    frmGrosstareNetWtUpdation.ListView1.Items(i_s).SubItems.Add(Tre_WTT)

                    Gross_WT_Sales = Tre_WTT

                Next

                'frmGrosstareNetWtUpdation.Show(vbModal)
            End If
            ''''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

            If ListView1.Items.Count > 0 Then
                For i As Integer = 0 To ListView1.Items.Count - 1
                    sum_n_wt = sum_n_wt + Val(ListView1.Items(i).SubItems(8).Text) + 0
                Next i

                If sum_n_wt = Val(Text2.Text) Then

                    For i As Integer = 0 To ListView1.Items.Count - 1
                        'cm.Connection = con
                        'cm.CommandType = CommandType.StoredProcedure
                        'cm.CommandText = "sp_upd_tbl_GE_DET_For_Splitting"
                        'cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                        'cm.Parameters.AddWithValue("@val_NET_WT", Val(ListView1.Items(i).SubItems(8).Text) + 0)
                        'If con.State = ConnectionState.Closed Then
                        '    con.Open()
                        'End If
                        'cm.ExecuteNonQuery()
                        Try
                            Dim strU As String = "update tbl_GE_DET set NET_WT = '" & ListView1.Items(i).SubItems(8).Text.Trim & "' where GE_DET_Tran_ID = '" & ListView1.Items(i).Text.Trim & "'"
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            cc.Execute(strU)
                        Catch ex As Exception

                        End Try
                        ''''''''''''''''''''''''
                        ''\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            dr.Close()
                            Exit Sub

                        Else

                            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                            objQueryTab1.Value = "ZWT_BG"


                            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                            objFldTab1 = objRfcFunc1.Tables("FIELDS")
                            objDatTab1 = objRfcFunc1.Tables("DATA")
                            objOptTab1.FreeTable()
                            objOptTab1.Rows.Add()
                            objOptTab1(objOptTab1.RowCount, "TEXT") = "WB_TR_ID = '" & Trim(Text5.Text) & "'"   ''''''and GATEOUT = '' and LOEKZ = ''"

                            objFldTab1.FreeTable()
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "TR_ID"  '' PO number


                            If objRfcFunc1.Call = False Then
                                MsgBox(objRfcFunc1.Exception)
                            End If

                            For Each objDatRec1 In objDatTab1.Rows

                                For Each objFldRec1 In objFldTab1.Rows

                                    WB_TR_ID_FOR_DEL = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""

                                    functionCtrl = CreateObject("SAP.Functions")
                                    sapConnection = functionCtrl.Connection
                                    sapConnection.User = SAPUsere_ID
                                    sapConnection.Password = SAPUsere_Pass
                                    sapConnection.System = SAPSys_name
                                    sapConnection.ApplicationServer = SAPApp_Server
                                    sapConnection.SystemNumber = SAPSys_No
                                    sapConnection.Client = SAP_Client
                                    sapConnection.Language = SAP_Lang
                                    sapConnection.CodePage = SAP_CodePage


                                    If sapConnection.Logon(0, True) <> True Then
                                        MsgBox("No connection to SAP System .......")
                                        Exit Sub

                                    Else
                                        ''MsgBox "Connected ........"
                                        funcControl = CreateObject("SAP.Functions")
                                        funcControl.Connection = sapConnection
                                        oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                                        oTrnID = oRFC.Exports("ZTR_ID")

                                        oTrnID.Value = Trim((ListView1.Items(i).Text) & "\" & Trim(Microsoft.VisualBasic.Right(WB_TR_ID_FOR_DEL, 4)))

                                        ''oTrnID.Value = Trim((ListView1.Items(i).Text) & "\" & Trim(Text1.Text))

                                        If oRFC.Call = True Then
                                            'oStatus = oRFC.Imports("matnr")
                                            ''MsgBox oStatus
                                            If oStatus = 1 Then ' fail
                                                PostCoil = 1
                                            End If
                                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                                PostCoil = 2
                                            End If
                                        End If
                                    End If
                                    ''**************************************************

                                    ''''''''''''''''''''''''''''
                                Next
                            Next
                        End If
                        ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

                        ''\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
                        SAP_Close1()
                    Next i
                    MsgBox("Splitting updated sucessfully !", vbInformation, "Electrosteel Castings Limited.")

                    For i As Integer = 0 To ListView1.Items.Count - 1
                        If ((Trim(ListView1.Items(i).SubItems(7).Text) Like "KG" Or Trim(ListView1.Items(i).SubItems(7).Text) Like "TO*") And Val(Trim(ListView1.Items(i).SubItems(8).Text)) > 0) Then
                            uploadInSAP = 1
                        ElseIf (((Trim(ListView1.Items(i).SubItems(7).Text) <> "KG") And ((Trim(ListView1.Items(i).SubItems(7).Text) <> "TO") Or (Trim(ListView1.Items(i).SubItems(7).Text) <> "TON")))) And (Val(Trim(ListView1.Items(i).SubItems(8).Text)) > 0) Then

                            uploadInSAP = 1
                        ElseIf (Trim(ListView1.Items(i).SubItems(7).Text) Like "M*") And (Trim(ListView1.Items(i).SubItems(7).Text) Like "EA*") Then
                            uploadInSAP = 1
                        Else
                            uploadInSAP = 1
                            'uploadInSAP = 0
                            'Exit For
                        End If
                    Next
                    If uploadInSAP = 1 Then
                        Call Push_Data()
                    End If


                    ''YYYYYYYYYYYYYYYYYYYYYYYYYYYYYY  FOR NODE IP & WEIGHTMENT DATE UPDATION

                    cm.Connection = con
                    cm.CommandType = CommandType.Text
                    If ListView1.Items.Count > 1 And (Vehilce_Type_Check = "SALES" Or Vehilce_Type_Check = "STKTROUT") Then
                        cm.CommandText = "update tbl_GE_DET set F_WT_NODE_IP = ''  , F_WT_DoneBy = '' , F_WT_DateTime  = '' , F_WT_Note = '' , S_WT_NODE_IP = ''  , S_WT_DoneBy = '' , S_WT_DateTime  = '' , S_WT_Note = '' where GE_HDR_ID  = '" & Trim(Text5.Text) & "'"
                    Else
                        cm.CommandText = "update tbl_GE_DET set F_WT_NODE_IP = ''  , F_WT_DoneBy = '' , F_WT_DateTime  = '' , F_WT_Note = '' , F_WT = 0 , S_WT_NODE_IP = ''  , S_WT_DoneBy = '' , S_WT_DateTime  = '' , S_WT_Note = '' , S_WT = 0 where GE_HDR_ID  = '" & Trim(Text5.Text) & "'"
                    End If
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.ExecuteNonQuery()

                    Dim mn As Integer = 0

                    ds = cc.GetDataset("select * from tbl_SPLIT_DET where NET_WT > 0 and GE_HDR_ID = '" & Trim(Text5.Text) & "'")
                    For i As Integer = 0 To ds.Tables(0).Rows.Count - 1
                        mn = mn + 1

                        If mn <= ListView1.Items.Count Then

                            'DET_TRAN_ID_NO = Trim(ListView1.Items(mn).Text)
                            DET_TRAN_ID_NO = Trim(ListView1.Items(0).Text)
                            cm.Connection = con
                            cm.CommandType = CommandType.Text
                            If ListView1.Items.Count > 1 And (Vehilce_Type_Check = "SALES" Or Vehilce_Type_Check = "STKTROUT") Then
                                cm.CommandText = "update tbl_GE_DET set F_WT_NODE_IP = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT_NODE_IP")) & "'  , F_WT_DoneBy = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT_DoneBy")) & "' , F_WT_DateTime  = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT_DateTime")) & "' , F_WT_Note = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT_Note")) & "' , S_WT_NODE_IP = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT_NODE_IP")) & "'  , S_WT_DoneBy = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT_DoneBy")) & "' , S_WT_DateTime  = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT_DateTime")) & "' , S_WT_Note = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT_Note")) & "' where GE_HDR_ID  = '" & Trim(Text5.Text) & "' and GE_DET_TRAN_ID  = '" & DET_TRAN_ID_NO & "'"
                            Else
                                cm.CommandText = "update tbl_GE_DET set F_WT_NODE_IP = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT_NODE_IP")) & "'  , F_WT_DoneBy = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT_DoneBy")) & "' , F_WT_DateTime  = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT_DateTime")) & "' , F_WT_Note = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT_Note")) & "' , F_WT = '" & Trim(ds.Tables(0).Rows(i).Item("F_WT")) & "' , S_WT_NODE_IP = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT_NODE_IP")) & "'  , S_WT_DoneBy = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT_DoneBy")) & "' , S_WT_DateTime  = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT_DateTime")) & "' , S_WT_Note = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT_Note")) & "' , S_WT = '" & Trim(ds.Tables(0).Rows(i).Item("S_WT")) & "' where GE_HDR_ID  = '" & Trim(Text5.Text) & "' and GE_DET_TRAN_ID  = '" & DET_TRAN_ID_NO & "'"
                            End If
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            cm.ExecuteNonQuery()
                        End If
                    Next
                    ''YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY


                    ListView1.Items.Clear()
                    ListView2.Items.Clear()


                    Text13.Text = ""
                    Text3.Text = ""
                    Text4.Text = ""
                    Text8.Text = ""
                    Text12.Text = ""
                    Text10.Text = ""
                    Text11.Text = ""
                    Text9.Text = ""

                    Text14.Text = ""
                    Text15.Text = ""
                    Text16.Text = ""

                    Text5.Text = ""
                    Text6.Text = ""
                    Text2.Text = ""
                    'Text1.Text = ""
                    Text18.Text = ""
                    Text19.Text = ""
                    Text23.Text = ""
                    Text5.Enabled = True
                    btnAddNewLineItems.Visible = False

                    Command5.Visible = False


                Else
                    MsgBox("Splitted Net Wt Qty. is not eual to Total Net Wt. !", vbInformation, "Electrosteel Castings Limited.")
                End If
            Else
                MsgBox("There is no data for Net wt. splitting !", vbInformation, "Electrosteel Castings Limited.")
            End If

            btnUpdateGrossTareWt.Visible = False


        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
        'err:
        '        If Err.Description <> "" Then
        '            MsgBox(Err.Description)
        '        End If
        '        Err.Clear()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        ListView1.Items.Clear()
        ListView2.Items.Clear()


        Text13.Text = ""
        Text3.Text = ""
        Text4.Text = ""
        Text8.Text = ""
        Text12.Text = ""
        Text10.Text = ""
        Text11.Text = ""
        Text9.Text = ""

        Text14.Text = ""
        Text15.Text = ""
        Text16.Text = ""

        Text5.Text = ""
        Text6.Text = ""
        Text2.Text = ""

        'Text1.Text = ""
        Text18.Text = ""
        Text19.Text = ""
        Text23.Text = ""

        Text5.Enabled = True
        btnAddNewLineItems.Visible = False
        btnUpdateGrossTareWt.Visible = False
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        'Dim str0 As String = ListView1.Items(0).Text
        'Dim str1 As String = ListView1.Items(0).SubItems(1).Text
        'Dim str2 As String = ListView1.Items(0).SubItems(2).Text
        'Dim str3 As String = ListView1.Items(0).SubItems(3).Text
        Me.Close()
    End Sub

    Private Sub btnAddNewLineItems_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddNewLineItems.Click
        'If txtTransactionNo.Text.Trim = "" Then
        '    MessageBox.Show("Please Enter the Transaction No.")
        '    Exit Sub
        'End If
        'Try
        '    Dim TypeOfVeh, HDR_TRANS_ID_2, unload_No_1, poText, SoldToParty, ShipToParty As String
        '    ''AddLineSplit:
        '    ListView2.Items.Clear()
        '    FirstLevelAuth = 1
        '    If FirstLevelAuth = 1 Then
        '        ''MsgBox "Auhtorised for adding New Line Items .", vbInformation, "ElectroWay"
        '        cm.Connection = con
        '        cm.CommandType = CommandType.StoredProcedure
        '        cm.CommandText = "sp_send_mail_Electroway"
        '        cm.Parameters.AddWithValue("@val_User_Name", User_ID)
        '        cm.Parameters.AddWithValue("@val_Body", "New Line Items addition during splitting .")
        '        cm.Parameters.AddWithValue("@val_Recipient", "<EMAIL>")
        '        If con.State = ConnectionState.Closed Then
        '            con.Open()
        '        End If
        '        cm.ExecuteNonQuery()
        '        FirstLevelAuth = 0
        '    Else
        '        MsgBox("You are not authorised for adding new line items during splitting . ", vbInformation, "ElectroWay")
        '        Exit Sub
        '    End If
        '    Dim ds1 As New DataSet
        '    ds1 = cc.GetDataset("select * from tbl_GE_DET where GE_HDR_ID ='" & (txtTransactionNo.Text) & "'")
        '    'If dr.Read Then
        '    For m1 As Integer = 0 To ds.Tables(0).Rows.Count - 1
        '        Dim m As Integer = 0
        '        TypeOfVeh = ds.Tables(0).Rows(m1).Item("Type_Of_Vehicle")
        '        HDR_TRANS_ID_2 = ds.Tables(0).Rows(m1).Item("GE_HDR_TRAN_ID")
        '        If con.State = ConnectionState.Closed Then
        '            con.Open()
        '        End If
        '        Dim str1 As String = "select * from tbl_GE_HDR where GE_HDR_ID ='" & (txtTransactionNo.Text) & "'"
        '        Dim cm1 As New SqlCommand(str1, con)
        '        Dim dr1 As SqlDataReader = cm1.ExecuteReader
        '        If dr1.Read Then
        '            txtVehicleNo.Text = dr1("Vehicle_No")
        '            txtTransporterCode.Text = dr1("Transpoter_Code")
        '            txtTransporterName.Text = dr1("TransporterName")
        '        End If
        '        dr1.Close()
        '        Call SAP_Conn()
        '        ''***********************************************************
        '        If sapConnection.Logon(0, True) <> True Then
        '            MsgBox("No connection to SAP System .......")
        '            dr.Close()
        '            Exit Sub
        '        Else
        '            If TypeOfVeh = "PURCH" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "GATEPASS" Then
        '                ''*************************************************************************** start 111
        '               objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
        '                objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
        '                objQueryTab.Value = "ZTM_HGATE_ENTRY"
        '                objOptTab = objRfcFunc.Tables("OPTIONS")
        '                objFldTab = objRfcFunc.Tables("FIELDS")
        '                objDatTab = objRfcFunc.Tables("DATA")
        '                objOptTab.FreeTable()
        '                objFldTab.FreeTable()
        '                Dim unl_NO = InputBox("Please Enter the SAP Gate Entry No.", "ElectroWay", "")
        '                If Len(Trim(unl_NO)) > 10 Then
        '                    MsgBox("Invalid SAP Gate Entry No.", vbInformation, "ElectroWay")
        '                    dr.Close()
        '                    Exit Sub
        '                End If

        '                Dim funcContro1, oRFC, oTrnID, oStatus As Object
        '                Dim PostCoil As String
        '                For i As Integer = 0 To ListView1.Items.Count - 1
        '                    If unl_NO = Trim(ListView1.Items(i).SubItems(11).Text) Then
        '                        Dim Msg = MsgBox("SAP Gate Entry No. already mapped, Are you want to re-assign ?", vbYesNo, "ElectroWay")
        '                        If Msg = vbYes Then
        '                            '''' Delete Transaction from ZWT_BG  8888888888888888
        '                            For kt As Integer = 0 To ListView1.Items.Count - 1
        '                                funcContro1 = CreateObject("SAP.Functions")
        '                                funcContro1.Connection = sapConnection
        '                                oRFC = funcContro1.Add("ZRFC_WB_UPLDWD")
        '                                oTrnID = oRFC.Exports("ZTR_ID")
        '                                oTrnID.Value = ListView1.Items(kt).Text & "\" & Trim(txtPlant.Text)     ''Trim(txtTransactionNo.Text)
        '                                If oRFC.Call = True Then
        '                                    If oStatus = 1 Then ' fail
        '                                        PostCoil = 1
        '                                    End If
        '                                    If oStatus = 0 Then ' successfully inserted in Zwt_bg table
        '                                        PostCoil = 2
        '                                    End If
        '                                End If
        '                            Next
        '                            '''' 888888888888888888888888888888888888888888888888
        '                            ListView1.Items.Clear()
        '                            cm.Connection = con
        '                            cm.CommandType = CommandType.Text
        '                            cm.CommandText = "update tbl_GE_DET set DO_No = '' , Unloading_No = '' where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "'"
        '                            If con.State = ConnectionState.Closed Then
        '                                con.Open()
        '                            End If
        '                            cm.ExecuteNonQuery()
        '                            Exit For
        '                        Else
        '                            dr.Close()
        '                            Exit Sub
        '                        End If
        '                    End If
        '                Next
        '                objOptTab.Rows.Add()
        '                objOptTab(objOptTab.RowCount, "TEXT") = "VEHICLENO ='" & txtVehicleNo.Text & "' and GATENO = '" & unl_NO & "'"   '''' and LOEKZ = ''"
        '                objFldTab.Rows.Add()
        '                objFldTab(objFldTab.RowCount, "FIELDNAME") = "CHALLAN_NO"
        '                objFldTab.Rows.Add()
        '                objFldTab(objFldTab.RowCount, "FIELDNAME") = "CHALLAN_DT"
        '                objFldTab.Rows.Add()
        '                objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME"
        '                objFldTab.Rows.Add()
        '                objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME" 'vehicle no'
        '                objFldTab.Rows.Add()
        '                objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME"  '' date
        '                objFldTab.Rows.Add()
        '                objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME"   '' time
        '                objFldTab.Rows.Add()
        '                objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME" ''PO no  EBELP
        '                If objRfcFunc.Call = False Then
        '                    MsgBox(objRfcFunc.Exception)
        '                End If
        '                Dim Challan_noo As String
        '                Dim Challan_Datee As String
        '                Dim p As Integer = 1
        '                i = 5
        '                If objDatTab.Rows.Count = 0 Then
        '                    MsgBox("Invalid Vehicle ! Data Not available in SAP !", vbInformation, "ElectroSteel Castings Limited")
        '                Else
        '                    For Each objDatRec In objDatTab.Rows
        '                        i = i + 1
        '                        Dim j As Integer = 0
        '                        For Each objFldRec In objFldTab.Rows
        '                            j = j + 1
        '                       If p = 1 Then

        '                            End If
        '                            p = p + 1
        '                            unload_No_1 = unl_NO
        '                            Text17.Text = unload_No_1
        '                            If j = 1 Then
        '                                Challan_noo = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
        '                            ElseIf j = 2 Then
        '                                Challan_Datee = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
        '                            ElseIf j = 3 Then
        '                                txtTransporterCode.Text = ""  ''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
        '                            ElseIf j = 4 Then
        '                                txtTransporterName.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
        '                            End If
        '                        Next
        '                        dr = cc.GetDataReader("select * from tbl_GE_DET where Unloading_No ='" & unload_No_1 & "'")
        '                        If dr.Read Then
        '                            dr.Close()
        '                            Exit For
        '                        End If
        '                        dr.Close()
        '                        j = 0
        '                    Next
        '                    Call SAP_Con2()
        '                    If sapConnection.Logon(0, True) <> True Then
        '                        MsgBox("No connection to SAP System .......")
        '                        dr.Close()
        '                        Exit Sub
        '                    End If
        '                    objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")
        '                    objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
        '                    objQueryTab1.Value = "ZTM_IGATE_ENTRY"
        '                    objOptTab1 = objRfcFunc1.Tables("OPTIONS")
        '                    objFldTab1 = objRfcFunc1.Tables("FIELDS")
        '                    objDatTab1 = objRfcFunc1.Tables("DATA")
        '                    objOptTab.FreeTable()
        '                    objOptTab1.FreeTable()
        '                    objOptTab1.Rows.Add()
        '                    objOptTab1(objOptTab1.RowCount, "TEXT") = "GATENO = '" & unload_No_1 & "'"  '''''' and LOEKZ = ''"
        '                    objFldTab1.FreeTable()
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELN"  '' PO number
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELP"  '' Line item
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MATNR"  '' Material code
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MAKTX"  '' Material Desc
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "CHAL_QTY"  '' Del Ch Qty.
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MEINS"  '' UOM
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
        '                    objFldTab1.Rows.Add()
        '                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
        '                    If objRfcFunc1.Call = False Then
        '                        MsgBox(objRfcFunc1.Exception)
        '                    End If
        '                    Dim PO_NO_11, Vendor_Coddee, Vendor_Namee As String
        '                    Dim k As Integer
        '                    i = 5
        '                    ListView2.Items.Clear()
        '                    For Each objDatRec1 In objDatTab1.Rows
        '                        k = ListView2.Items.Count + 1
        '                        For Each objFldRec1 In objFldTab1.Rows
        '                            'm = 1
        '                            lvi = New ListViewItem
        '                            If m = 0 Then
        '                                lvi.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
        '                                PO_NO_11 = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
        '                                ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        '                                Call SAP_Con1()
        '                                If sapConnection.Logon(0, True) <> True Then
        '                                    MsgBox("No connection to SAP System .......")
        '                                    SAP_CON_NOT_AVAIL = 1
        '                                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
        '                                    Exit Sub
        '                                Else
        '                                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
        '                                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
        '                                    objQueryTab.Value = "EKKO"
        '                                    objOptTab = objRfcFunc.Tables("OPTIONS")
        '                                    objFldTab = objRfcFunc.Tables("FIELDS")
        '                                    objDatTab = objRfcFunc.Tables("DATA")
        '                                    objOptTab.FreeTable()
        '                                    objOptTab.Rows.Add()
        '                                    objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  '' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"
        '                                    objFldTab.FreeTable()
        '                                    objFldTab.Rows.Add()
        '                                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "LIFNR"
        '                                    If objRfcFunc.Call = False Then
        '                                        MsgBox(objRfcFunc.Exception)
        '                                    End If
        '                                    i = 5
        '                                    If objDatTab.Rows.Count = 0 Then
        '                                        MsgBox("Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited")
        '                                    Else
        '                                        For Each objDatRec In objDatTab.Rows
        '                                            i = i + 1
        '                                            For Each objFldRec In objFldTab.Rows
        '                                                Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
        '                                            Next
        '                                        Next
        '                                    End If
        '                                End If
        '                                ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        '                                ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
        '                                Call SAP_Con1()
        '                                If sapConnection.Logon(0, True) <> True Then
        '                                    MsgBox("No connection to SAP System .......")
        '                                    SAP_CON_NOT_AVAIL = 1
        '                                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
        '                                    Exit Sub
        '                                Else
        '                                    Label25.Text = ""
        '                                    ''*************************************************************************** start 111
        '                                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
        '                                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
        '                                    objQueryTab.Value = "LFA1"
        '                                    objOptTab = objRfcFunc.Tables("OPTIONS")
        '                                    objFldTab = objRfcFunc.Tables("FIELDS")
        '                                    objDatTab = objRfcFunc.Tables("DATA")
        '                                    objOptTab.FreeTable()
        '                                    objOptTab.Rows.Add()
        '                                    objOptTab(objOptTab.RowCount, "TEXT") = "LIFNR = '" & Vendor_Coddee & "'"  '' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"
        '                                    objFldTab.FreeTable()
        '                                    objFldTab.Rows.Add()
        '                                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"
        '                                    If objRfcFunc.Call = False Then
        '                                        MsgBox(objRfcFunc.Exception)
        '                                    End If
        '                                    i = 5
        '                                    If objDatTab.Rows.Count = 0 Then
        '                                        ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
        '                                    Else
        '                                        For Each objDatRec In objDatTab.Rows
        '                                            i = i + 1
        '                                            For Each objFldRec In objFldTab.Rows
        '                                                Vendor_Namee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
        '                                            Next
        '                                        Next
        '                                    End If
        '                                End If

        '                                ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
        '                                ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
        '                                If Trim(Vendor_Coddee) = "" Then
        '                                    If sapConnection.Logon(0, True) <> True Then
        '                                        MsgBox("No connection to SAP System .......")
        '                                        SAP_CON_NOT_AVAIL = 1
        '                                        Label25.Text = "SAP CONNECTION NOT AVAILABLE."
        '                                        Exit Sub
        '                                    Else
        '                                        Label25.Text = ""
        '                                        ''*************************************************************************** start 111
        '                                        'Dim objRfcFunc As Object
        '                                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
        '                                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
        '                                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
        '                                        objQueryTab.Value = "EKKO"
        '                                        objOptTab = objRfcFunc.Tables("OPTIONS")
        '                                        objFldTab = objRfcFunc.Tables("FIELDS")
        '                                        objDatTab = objRfcFunc.Tables("DATA")
        '                                        objOptTab.FreeTable()
        '                                        objOptTab.Rows.Add()
        '                                        objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  '' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"
        '                                        objFldTab.FreeTable()
        '                                        objFldTab.Rows.Add()
        '                                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "RESWK"
        '                                        If objRfcFunc.Call = False Then
        '                                            MsgBox(objRfcFunc.Exception)
        '                                        End If
        '                                        i = 5
        '                                        If objDatTab.Rows.Count = 0 Then
        '                                            ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
        '                                        Else
        '                                            For Each objDatRec In objDatTab.Rows
        '                                                i = i + 1
        '                                                For Each objFldRec In objFldTab.Rows
        '                                                    Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
        '                                                    Vendor_Namee = Vendor_Coddee
        '                                                Next
        '                                            Next
        '                                        End If
        '                                    End If
        '                                End If
        '                                ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
        '                            Else
        '                                If m = 1 And (TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS") Then
        '                                    lvi.SubItems.Add(m)
        '                                    lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
        '                                ElseIf m = 7 Then
        '                                    lvi.SubItems.Add(Challan_noo)
        '                                ElseIf m = 8 Then
        '                                    lvi.SubItems.Add(Challan_Datee)
        '                                ElseIf m = 12 Then
        '                                    lvi.SubItems.Add("")
        '                                    lvi.SubItems.Add("")
        '                                    lvi.SubItems.Add("")
        '                                    lvi.SubItems.Add(Vendor_Coddee)
        '                                    lvi.SubItems.Add(Vendor_Namee)
        '                                Else
        '                                    lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
        '                                End If
        '                            End If
        '                            m = m + 1
        '                            ListView2.Items.Add(lvi)
        '                        Next
        '                        m = 0
        '                    Next
        '                    ''*********************************************  end
        '                End If
        '            End If
        '        End If
        '        Try
        '            For i = 0 To ListView2.Items.Count - 1
        '                cm.Connection = con
        '                cm.CommandType = CommandType.StoredProcedure
        '                cm.CommandText = "sp_ins_tbl_GE_Det"
        '                cm.Parameters.AddWithValue("@GE_HDR_TRAN_ID", HDR_TRANS_ID_2)
        '                cm.Parameters.AddWithValue("@val_GE_HDR_ID", (txtTransactionNo.Text))

        '                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", TypeOfVeh)


        '                cm.Parameters.AddWithValue("@val_Unloading_No", ListView2.Items(i).SubItems(7).Text & "")

        '                If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
        '                    cm.Parameters.AddWithValue("@val_PO_No", ListView2.Items(i).Text)
        '                Else
        '                    cm.Parameters.AddWithValue("@val_PO_No", "")
        '                End If
        '                If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
        '                    cm.Parameters.AddWithValue("@val_PO_Line_Item", "")
        '                Else
        '                    cm.Parameters.AddWithValue("@val_PO_Line_Item", ListView2.Items(i).SubItems(2).Text & "")
        '                End If

        '                cm.Parameters.AddWithValue("@val_DO_No", ListView2.Items(i).SubItems(1).Text & "")

        '                If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
        '                    cm.Parameters.AddWithValue("@val_DO_Line_Item", ListView2.Items(i).SubItems(2).Text & "")
        '                Else
        '                    cm.Parameters.AddWithValue("@val_DO_Line_Item", "")
        '                End If

        '                If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
        '                    cm.Parameters.AddWithValue("@val_SO_No", ListView2.Items(i).Text)
        '                Else
        '                    cm.Parameters.AddWithValue("@val_SO_No", "")
        '                End If
        '                If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
        '                    cm.Parameters.AddWithValue("@val_SO_Line_Item", ListView2.Items(i).SubItems(15).Text & "")
        '                Else
        '                    cm.Parameters.AddWithValue("@val_SO_Line_Item", "")
        '                End If
        '                cm.Parameters.AddWithValue("@val_Mat_Code", ListView2.Items(i).SubItems(3).Text & "")
        '                cm.Parameters.AddWithValue("@val_Mat_Desc", ListView2.Items(i).SubItems(4).Text & "")
        '                cm.Parameters.AddWithValue("@val_Challan_Date", ListView2.Items(i).SubItems(9).Text & "")
        '                cm.Parameters.AddWithValue("@val_Challan_No", ListView2.Items(i).SubItems(8).Text & "")
        '                cm.Parameters.AddWithValue("@val_Challan_Qty", Val(ListView2.Items(i).SubItems(5).Text) + 0)
        '                cm.Parameters.AddWithValue("@val_UOM", ListView2.Items(i).SubItems(6).Text & "")
        '                cm.Parameters.AddWithValue("@val_WayBill_No", ListView2.Items(i).SubItems(12).Text & "")
        '                If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
        '                    cm.Parameters.AddWithValue("@val_Vendor_Code", ListView2.Items(i).SubItems(16).Text & "")
        '                Else
        '                    cm.Parameters.AddWithValue("@val_Vendor_Code", "")
        '                End If

        '                If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
        '                    cm.Parameters.AddWithValue("@val_Vendor_Name", ListView2.Items(i).SubItems(17).Text & "")
        '                Else
        '                    cm.Parameters.AddWithValue("@val_Vendor_Name", "")
        '                End If


        '                ''cm.Parameters.AddWithValue("@val_Vendor_Name") = ""
        '                If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
        '                    cm.Parameters.AddWithValue("@val_Customer_Code", ListView2.Items(i).SubItems(16).Text & "")
        '                Else
        '                    cm.Parameters.AddWithValue("@val_Customer_Code", "")
        '                End If

        '                If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
        '                    cm.Parameters.AddWithValue("@val_Customer_Name", ListView2.Items(i).SubItems(17).Text & "")
        '                Else
        '                    cm.Parameters.AddWithValue("@val_Customer_Name", "")
        '                End If

        '                ''cm.Parameters.AddWithValue("@val_Customer_Name") = ""
        '                cm.Parameters.AddWithValue("@val_GatePass_No", ListView2.Items(i).SubItems(14).Text & "")
        '                cm.Parameters.AddWithValue("@val_Unloading_Remarks", ListView2.Items(i).SubItems(13).Text & "")
        '                cm.Parameters.AddWithValue("@val_CN_No", ListView2.Items(i).SubItems(10).Text & "")
        '                cm.Parameters.AddWithValue("@val_CN_Date", ListView2.Items(i).SubItems(11).Text & "")
        '                ''@val_Grouping_Ref_Code
        '                cm.Parameters.AddWithValue("@val_Grouping_Ref_Code", "")
        '                If con.State = ConnectionState.Closed Then
        '                    con.Open()
        '                End If
        '                cm.ExecuteNonQuery()
        '            Next i
        '        Catch ex As Exception

        '        End Try
        '        Try
        '            If ListView2.Items.Count > 0 Then
        '                cm.Connection = con
        '                cm.CommandType = CommandType.StoredProcedure
        '                cm.CommandText = "sp_del_tbl_GE_DET_Without_SAP_CON"
        '                cm.Parameters.AddWithValue("@val_GE_HDR_TRAN_ID", HDR_TRANS_ID_2)
        '                cm.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(txtTransactionNo.Text))
        '                If con.State = ConnectionState.Closed Then
        '                    con.Open()
        '                End If
        '                cm.ExecuteNonQuery()
        '            End If
        '        Catch ex As Exception

        '        End Try
        '        cm.Connection = con
        '        cm.CommandType = CommandType.Text
        '        cm.CommandText = "update tbl_GE_HDR set Transpoter_Code  = '" & Trim(txtTransporterCode.Text) & "' , TransporterName  = '" & Trim(txtTransporterName.Text) & "' where GE_HDR_ID  ='" & Trim(txtTransactionNo.Text) & "'"
        '        If con.State = ConnectionState.Closed Then
        '            con.Open()
        '        End If
        '        cm.ExecuteNonQuery()

        '        ListView2.Items.Clear()
        '        ListView1.Items.Clear()

        '        Call Fetch_SQL_Data()

        '        'End If
        '    Next
        '    '--------------------------------------------------------
        '    dr.Close()

        '    dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (txtTransactionNo.Text) & "'")
        '    If dr.Read Then

        '        If (dr("Type_Of_Vehicle") = "SALES" Or dr("Type_Of_Vehicle") = "STKTROUT") And ListView1.Items.Count > 1 Then
        '            btnUpdateGrossTareWt.Visible = True
        '        End If
        '    End If
        '    dr.Close()

        '    dr = cc.GetDataReader("select sum(NET_WT) from tbl_SPLIT_Det where GE_HDR_ID ='" & (txtTransactionNo.Text) & "'")
        '    If dr.Read Then
        '        txtTotNetWt.Text = dr(0)
        '        'txtVehicleNo.Text = dr("Vehicle_No")
        '    End If
        '    dr.Close()
        'Catch ex As Exception

        'End Try
        Try
            Dim TypeOfVeh, HDR_TRANS_ID_2, unload_No_1, poText, SoldToParty, ShipToParty As String
            ListView2.Items.Clear()
            FirstLevelAuth = 1
            If FirstLevelAuth = 1 Then
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_send_mail_Electroway"
                cm.Parameters.AddWithValue("@val_User_Name", User_ID)
                cm.Parameters.AddWithValue("@val_Body", "New Line Items addition during splitting .")
                cm.Parameters.AddWithValue("@val_Recipient", "<EMAIL>")
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.ExecuteNonQuery()
                FirstLevelAuth = 0
            Else
                MsgBox("You are not authorised for adding new line items during splitting . ", vbInformation, "ElectroWay")
                Exit Sub
            End If

            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            ds = cc.GetDataset("select * from tbl_GE_DET where GE_HDR_ID ='" & (Text5.Text) & "'")

            'For MMM As Integer = 0 To ds.Tables(0).Rows.Count - 1
            For MMM As Integer = 0 To 0
                TypeOfVeh = ds.Tables(0).Rows(MMM).Item("Type_Of_Vehicle")
                HDR_TRANS_ID_2 = ds.Tables(0).Rows(MMM).Item("GE_HDR_TRAN_ID")

                dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (Text5.Text) & "'")
                Try
                    If dr.Read Then
                        Text6.Text = dr("Vehicle_No")
                        Text18.Text = dr("Transpoter_Code")
                        Text19.Text = dr("TransporterName")
                    End If
                Catch ex As Exception

                End Try
                dr.Close()
                Call SAP_Conn()
                ''***********************************************************
                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    Exit Sub
                Else
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "GATEPASS" Then
                        ''*************************************************************************** start 111
                        'Dim objRfcFunc As Object
                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        objQueryTab.Value = "ZTM_HGATE_ENTRY"

                        objOptTab = objRfcFunc.Tables("OPTIONS")
                        objFldTab = objRfcFunc.Tables("FIELDS")
                        objDatTab = objRfcFunc.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        objOptTab.FreeTable()
                        'Then set values
                        objFldTab.FreeTable()

                        Dim unl_NO = InputBox("Please Enter the SAP Gate Entry No.", "ElectroWay", "")

                        If Len(Trim(unl_NO)) > 10 Then
                            MsgBox("Invalid SAP Gate Entry No.", vbInformation, "ElectroWay")
                            Exit Sub
                        End If
                        Dim funcContro1, oRFC, oTrnID, oStatus As Object
                        Dim PostCoil As String
                        For i = 0 To ListView1.Items.Count - 1
                            If unl_NO = Trim(ListView1.Items(i).SubItems(11).Text) Then

                                Dim Msg = MsgBox("SAP Gate Entry No. already mapped, Are you want to re-assign ?", vbYesNo, "ElectroWay")

                                If Msg = vbYes Then

                                    '''' Delete Transaction from ZWT_BG  8888888888888888
                                    For kt = 0 To ListView1.Items.Count - 1
                                        funcContro1 = CreateObject("SAP.Functions")
                                        funcContro1.Connection = sapConnection
                                        oRFC = funcContro1.Add("ZRFC_WB_UPLDWD")
                                        oTrnID = oRFC.Exports("ZTR_ID")
                                        oTrnID.Value = ListView1.Items(kt).Text & "\" & Trim(Text1.Text)     '''Trim(txtTransactionNo.Text)
                                        If oRFC.Call = True Then
                                            'oStatus = oRFC.Imports("matnr")
                                            ''MsgBox oStatus
                                            If oStatus = 1 Then ' fail
                                                PostCoil = 1
                                            End If
                                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                                PostCoil = 2
                                            End If
                                        End If
                                    Next

                                    '''' 888888888888888888888888888888888888888888888888
                                    ListView1.Items.Clear()
                                    cm.Connection = con
                                    cm.CommandType = CommandType.Text
                                    cm.CommandText = "update tbl_GE_DET set DO_No = '' , Unloading_No = '' where GE_HDR_ID = '" & Trim(Text5.Text) & "'"
                                    If con.State = ConnectionState.Closed Then
                                        con.Open()
                                    End If
                                    cm.ExecuteNonQuery()
                                    Exit For
                                Else

                                    Exit Sub
                                End If
                            End If
                        Next

                        objOptTab.Rows.Add()
                        ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                        ''objOptTab.Rows.Add
                        objOptTab(objOptTab.RowCount, "TEXT") = "VEHICLENO ='" & Text6.Text & "' and GATENO = '" & unl_NO & "'"   '''' and LOEKZ = ''"
                        ''objOptTab(objOptTab.RowCount, "TEXT") = "TRUCK_NO ='CC'" '' and LOEKZ NE 'L'"

                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "CHALLAN_NO"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "CHALLAN_DT"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME" 'vehicle no'
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME"  '' date
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME"   '' time
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME" ''PO no  EBELP

                        If objRfcFunc.Call = False Then
                            MsgBox(objRfcFunc.Exception)
                        End If
                        Dim Challan_noo As String
                        Dim Challan_Datee As String
                        Dim p As Integer = 1
                        i = 5
                        p = 1

                        If objDatTab.Rows.Count = 0 Then
                            MsgBox("Invalid Vehicle ! Data Not available in SAP !", vbInformation, "ElectroSteel Castings Limited")
                        Else

                            For Each objDatRec In objDatTab.Rows
                                i = i + 1
                                'MsgBox (Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                Dim j As Integer = 0
                                For Each objFldRec In objFldTab.Rows
                                    j = j + 1
                                    If p = 1 Then
                                        ''unload_No_1 = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    End If
                                    p = p + 1
                                    unload_No_1 = unl_NO
                                    Text17.Text = unload_No_1
                                    If j = 1 Then
                                        Challan_noo = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                        ''Text17.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                        'txtPlant.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    ElseIf j = 2 Then
                                        Challan_Datee = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                        'PLANT   txtPlant.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    ElseIf j = 3 Then
                                        Text18.Text = ""  ''Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                    ElseIf j = 4 Then
                                        Text19.Text = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                    End If
                                Next

                                dr = cc.GetDataReader("select * from tbl_GE_DET where Unloading_No ='" & unload_No_1 & "'")
                                Try
                                    If dr.Read Then
                                        dr.Close()
                                        Exit For
                                        'rec4.Close
                                        'Exit Sub
                                    End If
                                Catch ex As Exception

                                End Try
                                dr.Close()
                                j = 0
                            Next

                            Call SAP_Con2()
                            If sapConnection.Logon(0, True) <> True Then
                                MsgBox("No connection to SAP System .......")
                                Exit Sub
                            End If
                            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")
                            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                            objQueryTab1.Value = "ZTM_IGATE_ENTRY"
                            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                            objFldTab1 = objRfcFunc1.Tables("FIELDS")
                            objDatTab1 = objRfcFunc1.Tables("DATA")
                            'First we set the condition
                            'Refresh table
                            objOptTab.FreeTable()
                            objOptTab1.FreeTable()
                            'Then set values
                            objOptTab1.Rows.Add()
                            objOptTab1(objOptTab1.RowCount, "TEXT") = "GATENO = '" & unload_No_1 & "'"  '''''' and LOEKZ = ''"
                            objFldTab1.FreeTable()
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELN"  '' PO number
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELP"  '' Line item
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MATNR"  '' Material code
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MAKTX"  '' Material Desc
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "CHAL_QTY"  '' Del Ch Qty.
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MEINS"  '' UOM
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM
                            If objRfcFunc1.Call = False Then
                                MsgBox(objRfcFunc1.Exception)
                            End If
                            Dim PO_NO_11, Vendor_Coddee, Vendor_Namee As String
                            Dim k As Integer
                            i = 5
                            ListView2.Items.Clear()
                            For Each objDatRec1 In objDatTab1.Rows
                                k = ListView2.Items.Count + 1
                                lvi = New ListViewItem
                                For Each objFldRec1 In objFldTab1.Rows
                                    'm = 1

                                    If m = 0 Then
                                        lvi.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""
                                        PO_NO_11 = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""

                                        ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
                                        Call SAP_Con1()

                                        If sapConnection.Logon(0, True) <> True Then
                                            MsgBox("No connection to SAP System .......")
                                            SAP_CON_NOT_AVAIL = 1
                                            Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                            Exit Sub
                                        Else
                                            'Label25.Text = ""

                                            ''*************************************************************************** start 111
                                            'Dim objRfcFunc As Object
                                            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
                                            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                            objQueryTab.Value = "EKKO"

                                            objOptTab = objRfcFunc.Tables("OPTIONS")
                                            objFldTab = objRfcFunc.Tables("FIELDS")
                                            objDatTab = objRfcFunc.Tables("DATA")
                                            'First we set the condition
                                            'Refresh table
                                            objOptTab.FreeTable()
                                            'Then set values
                                            objOptTab.Rows.Add()
                                            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                            ''objOptTab.Rows.Add
                                            objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                            objFldTab.FreeTable()

                                            objFldTab.Rows.Add()
                                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "LIFNR"

                                            If objRfcFunc.Call = False Then
                                                MsgBox(objRfcFunc.Exception)
                                            End If

                                            i = 5

                                            If objDatTab.Rows.Count = 0 Then
                                                MsgBox("Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited")
                                            Else

                                                For Each objDatRec In objDatTab.Rows
                                                    i = i + 1
                                                    For Each objFldRec In objFldTab.Rows
                                                        Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                                    Next
                                                Next
                                            End If
                                        End If
                                        ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
                                        ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
                                        Call SAP_Con1()

                                        If sapConnection.Logon(0, True) <> True Then
                                            MsgBox("No connection to SAP System .......")
                                            SAP_CON_NOT_AVAIL = 1
                                            Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                            Exit Sub

                                        Else
                                            Label25.Text = ""
                                            ''*************************************************************************** start 111
                                            'Dim objRfcFunc As Object
                                            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
                                            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                            objQueryTab.Value = "LFA1"
                                            objOptTab = objRfcFunc.Tables("OPTIONS")
                                            objFldTab = objRfcFunc.Tables("FIELDS")
                                            objDatTab = objRfcFunc.Tables("DATA")
                                            'First we set the condition
                                            'Refresh table
                                            objOptTab.FreeTable()
                                            'Then set values
                                            objOptTab.Rows.Add()
                                            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                            ''objOptTab.Rows.Add
                                            objOptTab(objOptTab.RowCount, "TEXT") = "LIFNR = '" & Vendor_Coddee & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                            objFldTab.FreeTable()

                                            objFldTab.Rows.Add()
                                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"

                                            If objRfcFunc.Call = False Then
                                                MsgBox(objRfcFunc.Exception)
                                            End If

                                            i = 5

                                            If objDatTab.Rows.Count = 0 Then
                                                ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                            Else

                                                For Each objDatRec In objDatTab.Rows
                                                    i = i + 1
                                                    For Each objFldRec In objFldTab.Rows
                                                        Vendor_Namee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                                    Next
                                                Next
                                            End If
                                        End If
                                        ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH
                                        ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                        If Trim(Vendor_Coddee) = "" Then

                                            If sapConnection.Logon(0, True) <> True Then
                                                MsgBox("No connection to SAP System .......")
                                                SAP_CON_NOT_AVAIL = 1
                                                Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                                Exit Sub

                                            Else

                                                Label25.Text = ""

                                                ''*************************************************************************** start 111
                                                'Dim objRfcFunc As Object
                                                ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                                objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                                                objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                                objQueryTab.Value = "EKKO"

                                                objOptTab = objRfcFunc.Tables("OPTIONS")
                                                objFldTab = objRfcFunc.Tables("FIELDS")
                                                objDatTab = objRfcFunc.Tables("DATA")
                                                'First we set the condition
                                                'Refresh table
                                                objOptTab.FreeTable()
                                                'Then set values
                                                objOptTab.Rows.Add()
                                                ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                                ''objOptTab.Rows.Add
                                                objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                                objFldTab.FreeTable()

                                                objFldTab.Rows.Add()
                                                objFldTab(objFldTab.RowCount, "FIELDNAME") = "RESWK"

                                                If objRfcFunc.Call = False Then
                                                    MsgBox(objRfcFunc.Exception)
                                                End If

                                                i = 5

                                                If objDatTab.Rows.Count = 0 Then
                                                    ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                                Else

                                                    For Each objDatRec In objDatTab.Rows
                                                        i = i + 1
                                                        For Each objFldRec In objFldTab.Rows
                                                            Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))

                                                            Vendor_Namee = Vendor_Coddee
                                                        Next
                                                    Next
                                                End If
                                            End If


                                        End If

                                        ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                    Else

                                        If m = 1 And (TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS") Then
                                            lvi.SubItems.Add(m)
                                            lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))

                                        ElseIf m = 7 Then
                                            lvi.SubItems.Add(Challan_noo)
                                        ElseIf m = 8 Then
                                            lvi.SubItems.Add(Challan_Datee) ''''Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))) & ""
                                        ElseIf m = 12 Then
                                            lvi.SubItems.Add("")
                                            lvi.SubItems.Add("")
                                            lvi.SubItems.Add("")
                                            lvi.SubItems.Add(Vendor_Coddee)
                                            lvi.SubItems.Add(Vendor_Namee)
                                        Else
                                            lvi.SubItems.Add(Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))))
                                        End If
                                    End If
                                    'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                    m = m + 1

                                Next
                                ListView2.Items.Add(lvi)
                                m = 0
                            Next
                            ''*********************************************  end
                        End If

                        ''ElseIf TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then

                        ''    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                        ''    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        ''    objQueryTab.Value = "VBUK"

                        ''    objOptTab = objRfcFunc.Tables("OPTIONS")
                        ''    objFldTab = objRfcFunc.Tables("FIELDS")
                        ''    objDatTab = objRfcFunc.Tables("DATA")
                        ''    objOptTab.FreeTable()

                        ''    Del_OrdeNo = InputBox("Please input the Delivery Order No.", "ElectroWay", "")


                        ''    If Len(Trim(Del_OrdeNo)) > 10 Then
                        ''        MsgBox("Invalid Delivery Order No.", vbInformation, "ElectroWay")
                        ''        rec4.Close()
                        ''        Exit Sub
                        ''    End If

                        ''    For i = 1 To ListView1.Items.Count
                        ''        If Del_OrdeNo = Trim(ListView1.Items(i).ListSubItems(2).Text) Then

                        ''            Dim Msg = MsgBox("Delivery No. already mapped, Are you want to re-assign ?", vbYesNo, "ElectroWay")

                        ''            If Msg = vbYes Then

                        ''                '''' Delete Transaction from ZWT_BG  8888888888888888
                        ''                For kt = 1 To ListView1.Items.Count
                        ''                    funcContro1 = CreateObject("SAP.Functions")
                        ''                    funcContro1.Connection = sapConnection
                        ''                    oRFC = funcContro1.Add("ZRFC_WB_UPLDWD")
                        ''                    oTrnID = oRFC.Exports("ZTR_ID")
                        ''                    oTrnID.Value = Trim(ListView1.Items(kt).Text) & "\" & Trim(txtPlant.Text)    '''Trim(txtTransactionNo.Text)
                        ''                    If oRFC.Call = True Then
                        ''                        'oStatus = oRFC.Imports("matnr")
                        ''                        ''MsgBox oStatus
                        ''                        If oStatus = 1 Then ' fail
                        ''                            PostCoil = 1
                        ''                        End If
                        ''                        If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                        ''                            PostCoil = 2
                        ''                        End If
                        ''                    End If
                        ''                Next

                        ''                '''' 888888888888888888888888888888888888888888888888

                        ''                ListView1.Items.Clear()

                        ''                cmd.ActiveConnection = con
                        ''                cmd.CommandType = adCmdText
                        ''                cmd.CommandText = "update tbl_GE_DET set DO_No = '' , Unloading_No = '' where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "'"
                        ''                cmd.Execute()

                        ''                Exit For
                        ''            Else

                        ''                rec4.Close()
                        ''                Exit Sub

                        ''            End If

                        ''        End If
                        ''    Next

                        ''    objOptTab.Rows.Add()
                        ''    objOptTab(objOptTab.RowCount, "TEXT") = "GBSTK NE 'C' and VBELN = '" & Del_OrdeNo & "'" ''''''& Text6.Text & "' " '' and LOEKZ NE 'L'"
                        ''    objFldTab.FreeTable()

                        ''    objFldTab.Rows.Add()
                        ''    objFldTab(objFldTab.RowCount, "FIELDNAME") = "VBELN"

                        ''    If objRfcFunc.Call = False Then
                        ''        MsgBox(objRfcFunc.Exception)
                        ''    End If

                        ''    p = 1

                        ''    i = 5
                        ''    If objDatTab.Rows.Count = 0 Then
                        ''        MsgBox("Invalid Vehicle ! Data Not available in SAP !", vbInformation, "ElectroSteel Castings Limited")
                        ''        rec4.Close()
                        ''        Exit Sub
                        ''    Else

                        ''        For Each objDatRec In objDatTab.Rows
                        ''            For Each objFldRec In objFldTab.Rows
                        ''                'MsgBox Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                        ''                DO_NO = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                        ''                functionCtr2 = CreateObject("SAP.Functions")
                        ''                sapConnection = functionCtr2.Connection
                        ''                sapConnection.User = SAPUsere_ID
                        ''                sapConnection.Password = SAPUsere_Pass
                        ''                sapConnection.System = SAPSys_name
                        ''                sapConnection.ApplicationServer = SAPApp_Server
                        ''                sapConnection.SystemNumber = SAPSys_No
                        ''                sapConnection.Client = SAP_Client
                        ''                sapConnection.Language = SAP_Lang
                        ''                If sapConnection.Logon(0, True) <> True Then
                        ''                    MsgBox("No connection to SAP System .......")
                        ''                    Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                        ''                    Exit Sub

                        ''                End If
                        ''                RFC_READ_TEXT = functionCtr2.Add("RFC_READ_TEXT") '<------------
                        ''                tblText_Lines = RFC_READ_TEXT.Tables("TEXT_LINES")

                        ''                tblText_Lines.AppendRow()
                        ''                tblText_Lines(1, "TDOBJECT") = "VBBK"
                        ''                tblText_Lines(1, "TDNAME") = DO_NO
                        ''                tblText_Lines(1, "TDID") = "ZL01"
                        ''                If RFC_READ_TEXT.Call = True Then

                        ''                    'MsgBox tblText_Lines.RowCount

                        ''                    If tblText_Lines.RowCount > 0 Then

                        ''                        For intRow = 1 To tblText_Lines.RowCount ' Change Next line to write a different header row

                        ''                            If intRow = 1 Then
                        ''                                poText = tblText_Lines(intRow, "TDLINE")
                        ''                                'Else
                        ''                                'poText = poText & vbCrLf & tblText_Lines(intRow, "TDLINE")
                        ''                            End If
                        ''                        Next
                        ''                    Else
                        ''                    End If
                        ''                End If
                        ''            Next
                        ''        Next
                        ''    End If

                        ''    If poText <> Trim(txtVehicleNo.Text) Then
                        ''        MsgBox("Invalid Delivery No. .....", vbInformation, "ElectroWay")
                        ''        rec4.Close()
                        ''        Exit Sub
                        ''    Else

                        ''        '''''''''&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
                        ''        Call SAP_Con2()

                        ''        If sapConnection.Logon(0, True) <> True Then
                        ''            MsgBox("No connection to SAP System .......")
                        ''            Exit Sub
                        ''        End If

                        ''        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        ''        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        ''        objQueryTab1.Value = "LIKP"
                        ''        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        ''        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        ''        objDatTab1 = objRfcFunc1.Tables("DATA")
                        ''        objOptTab1.FreeTable()
                        ''        objOptTab1.Rows.Add()
                        ''        objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Del_OrdeNo & "'"

                        ''        objFldTab1.FreeTable()

                        ''        objFldTab1.Rows.Add()
                        ''        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
                        ''        objFldTab1.Rows.Add()
                        ''        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNAG"
                        ''        objFldTab1.Rows.Add()
                        ''        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNNR"   ''''KUNNR

                        ''        If objRfcFunc1.Call = False Then
                        ''            MsgBox(objRfcFunc1.Exception)
                        ''        End If


                        ''        iii = 0

                        ''        If objDatTab1.Rows.Count = 0 Then
                        ''            'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
                        ''        Else

                        ''            For Each objDatRec1 In objDatTab1.Rows


                        ''                For Each objFldRec1 In objFldTab1.Rows
                        ''                    iii = iii + 1

                        ''                    If iii = 2 Then
                        ''                        SoldToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        ''                    End If

                        ''                    If iii = 3 Then

                        ''                        ShipToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))

                        ''                    End If

                        ''                    'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        ''                Next
                        ''            Next
                        ''        End If

                        ''        ''**********
                        ''        '''''''''&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&

                        ''        ''BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB

                        ''        Call SAP_Con2()

                        ''        If sapConnection.Logon(0, True) <> True Then
                        ''            MsgBox("No connection to SAP System .......")
                        ''            Exit Sub

                        ''        End If

                        ''        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        ''        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        ''        objQueryTab1.Value = "KNA1"
                        ''        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        ''        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        ''        objDatTab1 = objRfcFunc1.Tables("DATA")
                        ''        objOptTab1.FreeTable()
                        ''        objOptTab1.Rows.Add()
                        ''        If Trim(SoldToParty) <> "" Then
                        ''            objOptTab1(objOptTab1.RowCount, "TEXT") = "KUNNR = '" & SoldToParty & "'"
                        ''        Else
                        ''            objOptTab1(objOptTab1.RowCount, "TEXT") = "KUNNR = '" & ShipToParty & "'"
                        ''        End If

                        ''        objFldTab1.FreeTable()

                        ''        objFldTab1.Rows.Add()
                        ''        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "NAME1"   ''''KUNNR

                        ''        If objRfcFunc1.Call = False Then
                        ''            MsgBox(objRfcFunc1.Exception)
                        ''        End If

                        ''        If objDatTab1.Rows.Count = 0 Then
                        ''            'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
                        ''        Else
                        ''            For Each objDatRec1 In objDatTab1.Rows

                        ''                For Each objFldRec1 In objFldTab1.Rows

                        ''                    CustomerNameee = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        ''                Next
                        ''            Next
                        ''        End If
                        ''        ''BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB
                        ''        ''PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP

                        ''        Call SAP_Con2()

                        ''        If sapConnection.Logon(0, True) <> True Then
                        ''            MsgBox("No connection to SAP System .......")
                        ''            Exit Sub

                        ''        End If

                        ''        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        ''        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        ''        objQueryTab1.Value = "VBPA"
                        ''        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        ''        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        ''        objDatTab1 = objRfcFunc1.Tables("DATA")
                        ''        objOptTab1.FreeTable()
                        ''        objOptTab1.Rows.Add()
                        ''        objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Del_OrdeNo & "' and ( PARVW LIKE 'T%' or PARVW LIKE 'F%' or PARVW LIKE 'S%')"
                        ''        ''objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Del_OrdeNo & "' and PARVW LIKE 'T%'"

                        ''        objFldTab1.FreeTable()

                        ''        objFldTab1.Rows.Add()
                        ''        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
                        ''        objFldTab1.Rows.Add()
                        ''        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "PARVW"
                        ''        objFldTab1.Rows.Add()
                        ''        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "LIFNR"
                        ''        If objRfcFunc1.Call = False Then
                        ''            MsgBox(objRfcFunc1.Exception)
                        ''        End If


                        ''        ii = 0

                        ''        If objDatTab1.Rows.Count = 0 Then
                        ''            MsgBox("Transporter Not mentioned in DO. !", vbInformation, "ElectroSteel Castings Limited")
                        ''        Else


                        ''            For Each objDatRec1 In objDatTab1.Rows


                        ''                For Each objFldRec1 In objFldTab1.Rows
                        ''                    ii = ii + 1

                        ''                    If ii = 3 Then
                        ''                        txtTransporterCode.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        ''                    End If

                        ''                    'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        ''                Next
                        ''            Next
                        ''        End If




                        ''        If txtTransporterCode.Text <> "" Then

                        ''            Call SAP_Con2()

                        ''            If sapConnection.Logon(0, True) <> True Then
                        ''                MsgBox("No connection to SAP System .......")
                        ''                Exit Sub

                        ''            End If

                        ''            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        ''            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        ''            objQueryTab1.Value = "LFA1"
                        ''            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        ''            objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        ''            objDatTab1 = objRfcFunc1.Tables("DATA")
                        ''            objOptTab1.FreeTable()
                        ''            objOptTab1.Rows.Add()
                        ''            objOptTab1(objOptTab1.RowCount, "TEXT") = "LIFNR = '" & (txtTransporterCode.Text) & "'"

                        ''            objFldTab1.FreeTable()

                        ''            objFldTab1.Rows.Add()
                        ''            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "NAME1"
                        ''            If objRfcFunc1.Call = False Then
                        ''                MsgBox(objRfcFunc1.Exception)
                        ''            End If


                        ''            ii = 0

                        ''            If objDatTab1.Rows.Count = 0 Then
                        ''                MsgBox("Transporter Not mentioned in DO. !", vbInformation, "ElectroSteel Castings Limited")
                        ''            Else


                        ''                For Each objDatRec1 In objDatTab1.Rows


                        ''                    For Each objFldRec1 In objFldTab1.Rows
                        ''                        'iii = iii + 1

                        ''                        'If iii = 3 Then
                        ''                        txtTransporterName.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        ''                        'End If

                        ''                        'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        ''                    Next
                        ''                Next
                        ''            End If
                        ''        End If


                        ''        ''PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP

                        ''        ''///////////////////////////////////////

                        ''        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                        ''        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        ''        objQueryTab.Value = "LIPS"
                        ''        objOptTab = objRfcFunc.Tables("OPTIONS")
                        ''        objFldTab = objRfcFunc.Tables("FIELDS")
                        ''        objDatTab = objRfcFunc.Tables("DATA")
                        ''        objOptTab.FreeTable()
                        ''        objOptTab.Rows.Add()
                        ''        objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '" & Del_OrdeNo & "'"

                        ''        objFldTab.FreeTable()

                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VGBEL"
                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VBELN"
                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "POSNR"
                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MATNR" ''
                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "ARKTX" ''
                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "LFIMG"  ''
                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MEINS"   ''
                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VGPOS"   ''
                        ''        objFldTab.Rows.Add()
                        ''        objFldTab(objFldTab.RowCount, "FIELDNAME") = "WERKS"   ''


                        ''        If objRfcFunc.Call = False Then
                        ''            MsgBox(objRfcFunc.Exception)
                        ''        End If


                        ''        i = 5

                        ''        If objDatTab.Rows.Count = 0 Then
                        ''            MsgBox("Invalid Vehicle !", vbInformation, "ElectroSteel Castings Limited")
                        ''        Else


                        ''            For Each objDatRec In objDatTab.Rows
                        ''                k = ListView2.Items.Count + 1

                        ''                For Each objFldRec In objFldTab.Rows
                        ''                    'm = 1

                        ''                    If m = 0 Then
                        ''                        ListView2.Items.Add(k, , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & "")
                        ''                    Else

                        ''                        If m = 1 And (TypeOfVeh = "PURCH" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "GATEPASS") Then
                        ''                                                                                                            ListView2.Items(k).ListSubItems.Add (m), , ""
                        ''                            'ListView1.Items(k).ListSubItems.Add (m + 1), , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""
                        ''                            'm = m + 1

                        ''                        ElseIf m = 7 Then
                        ''                            For Lv_i = 7 To 14
                        ''                                                                                                                ListView2.Items(k).ListSubItems.Add (Lv_i), , ""
                        ''                            Next Lv_i

                        ''                                                                                                            ListView2.Items(k).ListSubItems.Add (15), , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""
                        ''                            'ListView2.Items(k).ListSubItems.Add (16), , SoldToParty & ""

                        ''                            If Trim(SoldToParty) = "" Then
                        ''                                                                                                                ListView2.Items(k).ListSubItems.Add (16), , ShipToParty & ""      '''''' ShipToParty
                        ''                            Else

                        ''                                                                                                                ListView2.Items(k).ListSubItems.Add (16), , SoldToParty & ""
                        ''                            End If

                        ''                                                                                                            ListView2.Items(k).ListSubItems.Add (17), , CustomerNameee & ""

                        ''                        ElseIf m = 8 Then

                        ''                            'txtPlant.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""

                        ''                        Else
                        ''                              ListView2.Items(k).ListSubItems.Add (m), , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""
                        ''                        End If
                        ''                    End If
                        ''                    m = m + 1
                        ''                Next
                        ''                If m < 8 Then
                        ''                    For Lv_i = 7 To 14
                        ''                        ListView2.Items(k).ListSubItems.Add (Lv_i), , ""
                        ''                    Next Lv_i
                        ''                End If
                        ''                m = 0
                        ''            Next
                        ''            ''*********************************************  end
                        ''        End If
                        ''        ''///////////////////////////////////////
                        ''    End If
                    End If
                End If

                For i = 0 To ListView2.Items.Count - 1
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_GE_Det"
                    cm.Parameters.AddWithValue("@GE_HDR_TRAN_ID", HDR_TRANS_ID_2)
                    cm.Parameters.AddWithValue("@val_GE_HDR_ID", Text5.Text)
                    cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", TypeOfVeh)
                    cm.Parameters.AddWithValue("@val_Unloading_No", ListView2.Items(i).SubItems(7).Text & "")

                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_PO_No", ListView2.Items(i).Text)
                    Else
                        cm.Parameters.AddWithValue("@val_PO_No", "")
                    End If
                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_PO_Line_Item", "")
                    Else
                        cm.Parameters.AddWithValue("@val_PO_Line_Item", ListView2.Items(i).SubItems(2).Text & "")
                    End If

                    cm.Parameters.AddWithValue("@val_DO_No", ListView2.Items(i).SubItems(1).Text & "")

                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_DO_Line_Item", ListView2.Items(i).SubItems(2).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_DO_Line_Item", "")
                    End If

                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_SO_No", ListView2.Items(i).Text)
                    Else
                        cm.Parameters.AddWithValue("@val_SO_No", "")
                    End If
                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_SO_Line_Item", ListView2.Items(i).SubItems(15).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_SO_Line_Item", "")
                    End If
                    cm.Parameters.AddWithValue("@val_Mat_Code", ListView2.Items(i).SubItems(3).Text & "")
                    cm.Parameters.AddWithValue("@val_Mat_Desc", ListView2.Items(i).SubItems(4).Text & "")
                    cm.Parameters.AddWithValue("@val_Challan_Date", ListView2.Items(i).SubItems(9).Text & "")
                    cm.Parameters.AddWithValue("@val_Challan_No", ListView2.Items(i).SubItems(8).Text & "")
                    cm.Parameters.AddWithValue("@val_Challan_Qty", Val(ListView2.Items(i).SubItems(5).Text) + 0)
                    cm.Parameters.AddWithValue("@val_UOM", ListView2.Items(i).SubItems(6).Text & "")
                    cm.Parameters.AddWithValue("@val_WayBill_No", ListView2.Items(i).SubItems(12).Text & "")
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Vendor_Code", ListView2.Items(i).SubItems(16).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Vendor_Code", "")
                    End If

                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Vendor_Name", ListView2.Items(i).SubItems(17).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Vendor_Name", "")
                    End If

                    ''cmd.Parameters.AddWithValue("@val_Vendor_Name") = ""
                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_Customer_Code", ListView2.Items(i).SubItems(16).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Customer_Code", "")
                    End If

                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_Customer_Name", ListView2.Items(i).SubItems(17).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Customer_Name", "")
                    End If

                    ''cmd.Parameters.AddWithValue("@val_Customer_Name") = "")
                    cm.Parameters.AddWithValue("@val_GatePass_No", ListView2.Items(i).SubItems(14).Text & "")
                    cm.Parameters.AddWithValue("@val_Unloading_Remarks", ListView2.Items(i).SubItems(13).Text & "")
                    cm.Parameters.AddWithValue("@val_CN_No", ListView2.Items(i).SubItems(10).Text & "")
                    cm.Parameters.AddWithValue("@val_CN_Date", ListView2.Items(i).SubItems(11).Text & "")
                    ''@val_Grouping_Ref_Code
                    cm.Parameters.AddWithValue("@val_Grouping_Ref_Code", "")
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.ExecuteNonQuery()
                Next i
                Try
                    If ListView2.Items.Count > 0 Then
                        Dim cmd As New SqlCommand
                        cmd.Connection = con
                        cmd.CommandType = CommandType.StoredProcedure
                        cmd.CommandText = "sp_del_tbl_GE_DET_Without_SAP_CON"
                        cmd.Parameters.AddWithValue("@val_GE_HDR_TRAN_ID", HDR_TRANS_ID_2)
                        cmd.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(Text5.Text))
                        cmd.ExecuteNonQuery()
                        cmd.Dispose()
                        'cm.Connection = con
                        'cm.CommandType = CommandType.StoredProcedure
                        'cm.CommandText = "sp_del_tbl_GE_DET_Without_SAP_CON"
                        'cm.Parameters.AddWithValue("@val_GE_HDR_TRAN_ID", HDR_TRANS_ID_2)
                        'cm.Parameters.AddWithValue("@val_GE_HDR_ID", txtTransactionNo.Text)
                        'If con.State = ConnectionState.Closed Then
                        '    con.Open()
                        'End If
                        'cm.ExecuteNonQuery()

                        'cmd.ActiveConnection = con
                        'cmd.CommandType = adCmdStoredProc
                        'cmd.CommandText = "sp_del_tbl_GE_DET_Without_SAP_CON"
                        'cmd.Parameters("@val_GE_HDR_TRAN_ID") = HDR_TRANS_ID_2
                        'cmd.Parameters("@val_GE_HDR_ID") = Trim(Text5.Text)
                        'cmd.Execute()
                    End If
                Catch ex As Exception

                End Try

                Try
                    Dim cmd As New SqlCommand
                    cmd.Connection = con
                    cmd.CommandType = CommandType.Text
                    cmd.CommandText = "update tbl_GE_HDR set Transpoter_Code  = '" & Trim(Text18.Text) & "' , TransporterName  = '" & Trim(Text19.Text) & "' where GE_HDR_ID  ='" & Trim(Text5.Text) & "'"
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cmd.ExecuteNonQuery()
                    cmd.Dispose()
                Catch ex As Exception

                End Try


                ListView2.Items.Clear()
                ListView1.Items.Clear()

                Call Fetch_SQL_Data()

            Next

            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (Text5.Text) & "'")
            Try
                If dr.Read Then
                    If (dr("Type_Of_Vehicle") = "SALES" Or dr("Type_Of_Vehicle") = "STKTROUT") And ListView1.Items.Count > 1 Then
                        btnUpdateGrossTareWt.Visible = True
                    End If
                End If
            Catch ex As Exception

            End Try
            dr.Close()
            dr = cc.GetDataReader("select sum(NET_WT) from tbl_SPLIT_Det where GE_HDR_ID ='" & (Text5.Text) & "'")
            Try
                If dr.Read Then
                    Text2.Text = dr(0)
                End If
            Catch ex As Exception

            End Try
            dr.Close()
        Catch ex As Exception

        End Try
    End Sub

    Private Sub btnUpdateGrossTareWt_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdateGrossTareWt.Click
        On Error GoTo err
        Dim Vehilce_Type_Check As String
        Dim Gross_WT_Sales, Tare_WT_Sales, Tre_WTT As Double
        dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(Text5.Text) & "'")
        If dr.Read Then
            Vehilce_Type_Check = dr("Type_Of_Vehicle")
            ''Gross_WT_Sales = dr("S_WT")
        End If
        dr.Close()


        If ListView1.Items.Count > 0 And (Vehilce_Type_Check = "SALES" Or Vehilce_Type_Check = "STKTROUT") Then  ''"SALES" Or TypeOfVeh = "STKTROUT"
            '--------------
            Dim frmGrosstareNetWtUpdation As New frmGrosstareNetWtUpdation
            frmGrosstareNetWtUpdation.Owner = Me
            'frmGrosstareNetWtUpdation.ShowDialog()
            '------------------
            frmGrosstareNetWtUpdation.txtTransactionNo.Text = Text5.Text
            dr = cc.GetDataReader("select S_WT from tbl_SPLIT_DET where GE_HDR_ID = '" & Trim(Text5.Text) & "' order by WB_COUNT_ID DESC")
            If dr.Read Then
                ''Text1.Text = dr("Plant_Name")
                Gross_WT_Sales = dr("S_WT")
            End If
            dr.Close()

            dr = cc.GetDataReader("select F_WT from tbl_SPLIT_DET where GE_HDR_ID = '" & Trim(Text5.Text) & "' order by WB_COUNT_ID")
            If dr.Read Then
                ''Text1.Text = dr("Plant_Name")
                Tare_WT_Sales = dr("F_WT")
            End If
            dr.Close()

            frmGrosstareNetWtUpdation.txtFinalTare.Text = Gross_WT_Sales
            frmGrosstareNetWtUpdation.txtFinalGross.Text = Tare_WT_Sales

            For i_s As Integer = 0 To ListView1.Items.Count - 1

                Tre_WTT = Gross_WT_Sales - Trim(ListView1.Items(i_s).SubItems(8).Text)

                frmGrosstareNetWtUpdation.ListView1.Items.Add(Trim(ListView1.Items(i_s).Text))
                frmGrosstareNetWtUpdation.ListView1.Items(i_s).SubItems.Add(Trim(ListView1.Items(i_s).SubItems(8).Text))
                frmGrosstareNetWtUpdation.ListView1.Items(i_s).SubItems.Add(Gross_WT_Sales)
                frmGrosstareNetWtUpdation.ListView1.Items(i_s).SubItems.Add(Tre_WTT)

                Gross_WT_Sales = Tre_WTT

            Next

            frmGrosstareNetWtUpdation.ShowDialog()
        Else
            MsgBox("Gross WT & Tare WT updation is only for SALES/Stock Transfer having multiple DO/line items.", vbInformation, "ElectroWay")
        End If
err:
        Err.Clear()
    End Sub

    Private Sub ListView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView1.DoubleClick
        Dim sel_item As Boolean
        sel_item = True
        If sel_item = True And ListView1.Items.Count > 0 Then

            If Trim(Text13.Text) = "" Then
                Text13.Text = ListView1.SelectedItems(0).SubItems(0).Text

                Text3.Text = ListView1.SelectedItems(0).SubItems(1).Text
                Text4.Text = ListView1.SelectedItems(0).SubItems(2).Text
                Text8.Text = ListView1.SelectedItems(0).SubItems(3).Text
                Text12.Text = ListView1.SelectedItems(0).SubItems(4).Text
                Text10.Text = ListView1.SelectedItems(0).SubItems(5).Text
                Text11.Text = ListView1.SelectedItems(0).SubItems(6).Text
                Text9.Text = ListView1.SelectedItems(0).SubItems(8).Text

                Text14.Text = ListView1.SelectedItems(0).SubItems(9).Text
                Text15.Text = ListView1.SelectedItems(0).SubItems(10).Text
                Text16.Text = ListView1.SelectedItems(0).SubItems(11).Text
                Text21.Text = ListView1.SelectedItems(0).SubItems(12).Text
                Text22.Text = ListView1.SelectedItems(0).SubItems(13).Text

                For Each i As ListViewItem In ListView1.SelectedItems
                    ListView1.Items.Remove(i)
                Next

                sel_item = False
            Else
                MsgBox("You have already selected a record, pls insert first !", vbInformation, "Electrosteel Castings Limited.")
            End If

        End If
        Dim sum_qty As Integer
        For ld = 0 To ListView1.Items.Count - 1
            sum_qty = sum_qty + Val(ListView1.Items(ld).SubItems(8).Text)
        Next ld

        Text23.Text = sum_qty
    End Sub

    Private Sub Text23_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Text23.TextChanged
        Text24.Text = Val(Text2.Text) - Val(Text23.Text)
    End Sub

    Private Sub txtTransactionNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Text5.KeyDown
        If e.KeyCode = 112 Then
            Dim frmTransaction1 As New frmTransaction
            frmTransaction1.Owner = Me
            'rec1.Open "select a.Vehicle_No , b.GE_HDR_ID , sum(b.NET_WT) from tbl_GE_HDR a , tbl_GE_DET b where b.DATAUploadedIn_SAP = '' group by a.Vehicle_No , b.GE_HDR_ID order by b.GE_HDR_ID , a.Vehicle_No"
            ds = cc.GetDataset("select distinct a.GE_HDR_ID , a.Vehicle_NO from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and b.DATAUploadedIn_SAP = ''  and  a.vehicle_status <> 'C' and a.Plant_Code = '" & Trim(Text1.Text) & "' order by a.Vehicle_no")
            frmTransaction1.gvTransaction.DataSource = ds.Tables(0)
            frmTransaction1.ShowDialog()
        End If
    End Sub

    Private Sub txtTransactionNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles Text5.KeyPress
        ListView2.Items.Clear()

        'Dim HDR_TRANS_ID_2 As Double
        Dim HDR_TRANS_ID_2 As String
        Dim TypeOfVeh, unload_No_1, unl_NO, Challan_noo, Challan_Datee, Vendor_Coddee, Vendor_Namee, PO_NO_11, poText, SoldToParty, ShipToParty, CustomerNameee As String

        If AscW(e.KeyChar) = 13 And Trim(Text5.Text) <> "" Then
            Text5.Enabled = False

            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (Text5.Text) & "'")
            If dr.Read Then
                If dr("Type_Of_Vehicle") = "SALES" Or dr("Type_Of_Vehicle") = "STKTROUT" Or dr("Type_Of_Vehicle") = "SALESRET" Then
                    If AuthForOUTBOUNDSplitting <> "OUTBOUNDSPLITTING" Then
                        MsgBox("You are not authorised for Splitting Weighment data of OUTBOUND")
                        dr.Close()
                        Exit Sub
                    End If
                End If
            End If
            dr.Close()


            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (Text5.Text) & "'")
            If dr.Read Then
                If dr("Type_Of_Vehicle") = "PURCH" Or dr("Type_Of_Vehicle") = "PURCHRET" Then
                    If AuthForOUTBOUNDSplitting <> "INBOUNDSPLITTING" Then
                        MsgBox("You are not authorised for Splitting Weighment data of INBOUND")
                        dr.Close()
                        Exit Sub
                    End If
                End If
            End If
            dr.Close()

            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (Text5.Text) & "'")
            If dr.Read Then
                If dr("Type_Of_Vehicle") = "INTRDEPT" Or dr("Type_Of_Vehicle") = "CONTITEM" Or dr("Type_Of_Vehicle") = "GATEPASS" Then
                    MsgBox("Splitting is not allowed for INTER DEPARTMENT / CONTRACTOR ITEM / GATE PASS Vehicles.", vbInformation, "ElectroWay")
                    Exit Sub
                End If
            End If
            dr.Close()

            ''UUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUU
            dr = cc.GetDataReader("select * from tbl_SPLIT_DET where GE_HDR_ID ='" & Trim(Text5.Text) & "'")
            Dim InsertFlag As Boolean = False
            If dr.Read = True Then
                InsertFlag = True
            End If
            dr.Close()
            '------------------------
            If InsertFlag = False Then
                Dim strI As String = "insert into tbl_SPLIT_DET select * from tbl_GE_DET where GE_HDR_ID ='" & Trim(Text5.Text) & "'"
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cc.Execute(strI)
            End If
            '----------------------------
            ''UUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUU

            ''<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
            Call SAP_Conn()

            If sapConnection.Logon(0, True) <> True Then
                MsgBox("No connection to SAP System .......")
                ''rec4.Close
                Exit Sub

            Else

                ''            If TypeOfVeh = "PURCH" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "GATEPASS" Then

                objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                objQueryTab.Value = "ZWT_BG"

                objOptTab = objRfcFunc.Tables("OPTIONS")
                objFldTab = objRfcFunc.Tables("FIELDS")
                objDatTab = objRfcFunc.Tables("DATA")

                objOptTab.FreeTable()
                objFldTab.FreeTable()
                objOptTab.Rows.Add()
                objOptTab(objOptTab.RowCount, "TEXT") = "SHKZG ='S' and WB_TR_ID ='" & Trim(Text5.Text) & "'"  '''' and UN_NO = '" & unl_NO & "'"   '' and LOEKZ NE 'L'"

                objFldTab.Rows.Add()
                objFldTab(objFldTab.RowCount, "FIELDNAME") = "WB_TR_ID"

                If objRfcFunc.Call = False Then
                    MsgBox(objRfcFunc.Exception)
                End If


                If objDatTab.Rows.Count = 0 Then
                    ''MsgBox "Invalid Vehicle ! Data Not available in SAP !", vbInformation, "ElectroSteel Castings Limited"
                Else
                    MsgBox("Splitting not allowed as the Goods movement already done...", vbInformation, "ElectroWay")
                    Exit Sub

                End If
            End If

            ''<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
            Dim ReaderFlag As Boolean = False
            dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID ='" & (Text5.Text) & "' and Unloading_No ='' and DO_No = ''")
            If dr.Read Then
                TypeOfVeh = dr("Type_Of_Vehicle")
                HDR_TRANS_ID_2 = dr("GE_HDR_TRAN_ID")
                ReaderFlag = True
            End If
            dr.Close()

            If ReaderFlag = True Then
                dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (Text5.Text) & "'")
                If dr.Read Then
                    'TypeOfVehicle = dr("Type_Of_Vehicle")
                    Text6.Text = dr("Vehicle_No")
                    ''Text1.Text = dr("Plant_Code")
                    Text18.Text = dr("Transpoter_Code")
                    Text19.Text = dr("TransporterName")
                End If
                dr.Close()

                Call SAP_Conn()

                ''***********************************************************
                If sapConnection.Logon(0, True) <> True Then
                    MsgBox("No connection to SAP System .......")
                    dr.Close()
                    Exit Sub
                Else
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "GATEPASS" Then
                        ''*************************************************************************** start 111
                        'Dim objRfcFunc As Object
                        ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        objQueryTab.Value = "ZTM_HGATE_ENTRY"

                        objOptTab = objRfcFunc.Tables("OPTIONS")
                        objFldTab = objRfcFunc.Tables("FIELDS")
                        objDatTab = objRfcFunc.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        objOptTab.FreeTable()
                        'Then set values
                        objFldTab.FreeTable()

                        unl_NO = InputBox("Please Enter the SAP Gate Entry No.", "ElectroWay", "")

                        objOptTab.Rows.Add()
                        ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                        ''objOptTab.Rows.Add
                        objOptTab(objOptTab.RowCount, "TEXT") = "VEHICLENO ='" & Trim(Text6.Text) & "' and GATENO = '" & Trim(unl_NO) & "'"   '' and LOEKZ NE 'L'"
                        ''objOptTab(objOptTab.RowCount, "TEXT") = "TRUCK_NO ='CC'" '' and LOEKZ NE 'L'"


                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "CHALLAN_NO"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "CHALLAN_DT"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME"
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "TRANSNAME" 'vehicle no'
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME"  '' date
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME"   '' time
                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "MINESNAME" ''PO no  EBELP


                        If objRfcFunc.Call = False Then
                            MsgBox(objRfcFunc.Exception)
                        End If


                        Dim p As Integer = 1

                        i = 5
                        Dim j As Integer
                        If objDatTab.Rows.Count = 0 Then
                            'MsgBox("Invalid Vehicle ! Data Not available in SAP !", vbInformation, "ElectroSteel Castings Limited")
                        Else
                            For Each objDatRec In objDatTab.Rows
                                i = i + 1
                                'MsgBox (Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                For Each objFldRec In objFldTab.Rows
                                    j = j + 1
                                    If p = 1 Then
                                        unload_No_1 = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    End If
                                    p = p + 1

                                    unload_No_1 = unl_NO

                                    Text17.Text = unload_No_1

                                    If j = 1 Then
                                        Challan_noo = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                        ''Text17.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                        'Text1.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    ElseIf j = 2 Then
                                        Challan_Datee = Trim(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                        ''Text1.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    ElseIf j = 3 Then
                                        Text18.Text = "" ''''Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    ElseIf j = 4 Then
                                        Text19.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))

                                    End If
                                Next

                                dr = cc.GetDataReader("select * from tbl_GE_DET where Unloading_No ='" & unload_No_1 & "'")
                                If dr.Read Then
                                    dr.Close()
                                    Exit For
                                    'rec4.Close
                                    'Exit Sub
                                End If
                                dr.Close()
                                j = 0
                            Next


                            Call SAP_Con2()

                            If sapConnection.Logon(0, True) <> True Then
                                MsgBox("No connection to SAP System .......")
                                dr.Close()
                                Exit Sub

                            End If

                            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                            objQueryTab1.Value = "ZTM_IGATE_ENTRY"
                            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                            objFldTab1 = objRfcFunc1.Tables("FIELDS")
                            objDatTab1 = objRfcFunc1.Tables("DATA")
                            'First we set the condition
                            'Refresh table
                            objOptTab.FreeTable()
                            objOptTab1.FreeTable()
                            'Then set values

                            objOptTab1.Rows.Add()
                            objOptTab1(objOptTab1.RowCount, "TEXT") = "GATENO = '" & unload_No_1 & "'"  '''''' and LOEKZ = ''"

                            objFldTab1.FreeTable()
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELN"  '' PO number

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "EBELP"  '' Line item
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MATNR"  '' Material code
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MAKTX"  '' Material Desc
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "CHAL_QTY"  '' Del Ch Qty.

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "MEINS"  '' UOM

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  '' UOM

                            If objRfcFunc1.Call = False Then
                                MsgBox(objRfcFunc1.Exception)
                            End If

                            Dim k As Integer
                            i = 5
                            ListView2.Items.Clear()
                            For Each objDatRec1 In objDatTab1.Rows

                                k = ListView2.Items.Count + 1
                                lvi = New ListViewItem
                                For Each objFldRec1 In objFldTab1.Rows
                                    'm = 1

                                    If m = 0 Then
                                        lvi.Text = HDR_TRANS_ID_2
                                        lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")
                                        PO_NO_11 = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & ""

                                        ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

                                        Call SAP_Con1()

                                        If sapConnection.Logon(0, True) <> True Then
                                            MsgBox("No connection to SAP System .......")
                                            SAP_CON_NOT_AVAIL = 1
                                            Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                            Exit Sub

                                        Else

                                            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")
                                            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                            objQueryTab.Value = "EKKO"

                                            objOptTab = objRfcFunc.Tables("OPTIONS")
                                            objFldTab = objRfcFunc.Tables("FIELDS")
                                            objDatTab = objRfcFunc.Tables("DATA")
                                            'First we set the condition
                                            'Refresh table
                                            objOptTab.FreeTable()
                                            'Then set values
                                            objOptTab.Rows.Add()
                                            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                            ''objOptTab.Rows.Add
                                            objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                            objFldTab.FreeTable()

                                            objFldTab.Rows.Add()
                                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "LIFNR"

                                            If objRfcFunc.Call = False Then
                                                MsgBox(objRfcFunc.Exception)
                                            End If

                                            i = 5

                                            If objDatTab.Rows.Count = 0 Then
                                                MsgBox("Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited")
                                            Else

                                                For Each objDatRec In objDatTab.Rows
                                                    i = i + 1
                                                    For Each objFldRec In objFldTab.Rows
                                                        Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))


                                                    Next
                                                Next
                                            End If
                                        End If

                                        ''%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


                                        ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                        Call SAP_Con1()

                                        If sapConnection.Logon(0, True) <> True Then
                                            MsgBox("No connection to SAP System .......")
                                            SAP_CON_NOT_AVAIL = 1
                                            Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                            Exit Sub

                                        Else

                                            Label25.Text = ""

                                            ''*************************************************************************** start 111
                                            'Dim objRfcFunc As Object
                                            ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




                                            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                            objQueryTab.Value = "LFA1"

                                            objOptTab = objRfcFunc.Tables("OPTIONS")
                                            objFldTab = objRfcFunc.Tables("FIELDS")
                                            objDatTab = objRfcFunc.Tables("DATA")
                                            'First we set the condition
                                            'Refresh table
                                            objOptTab.FreeTable()
                                            'Then set values
                                            objOptTab.Rows.Add()
                                            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                            ''objOptTab.Rows.Add
                                            objOptTab(objOptTab.RowCount, "TEXT") = "LIFNR = '" & Vendor_Coddee & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                            objFldTab.FreeTable()

                                            objFldTab.Rows.Add()
                                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "NAME1"

                                            If objRfcFunc.Call = False Then
                                                MsgBox(objRfcFunc.Exception)
                                            End If

                                            i = 5

                                            If objDatTab.Rows.Count = 0 Then
                                                ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                            Else

                                                For Each objDatRec In objDatTab.Rows
                                                    i = i + 1
                                                    For Each objFldRec In objFldTab.Rows
                                                        Vendor_Namee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))

                                                    Next
                                                Next
                                            End If
                                        End If

                                        ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                        ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                        If Trim(Vendor_Coddee) = "" Then


                                            If sapConnection.Logon(0, True) <> True Then
                                                MsgBox("No connection to SAP System .......")
                                                SAP_CON_NOT_AVAIL = 1
                                                Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                                Exit Sub

                                            Else

                                                Label25.Text = ""

                                                ''*************************************************************************** start 111
                                                'Dim objRfcFunc As Object
                                                ''Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
                                                objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                                                objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                                                objQueryTab.Value = "EKKO"

                                                objOptTab = objRfcFunc.Tables("OPTIONS")
                                                objFldTab = objRfcFunc.Tables("FIELDS")
                                                objDatTab = objRfcFunc.Tables("DATA")
                                                'First we set the condition
                                                'Refresh table
                                                objOptTab.FreeTable()
                                                'Then set values
                                                objOptTab.Rows.Add()
                                                ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
                                                ''objOptTab.Rows.Add
                                                objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & PO_NO_11 & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

                                                objFldTab.FreeTable()

                                                objFldTab.Rows.Add()
                                                objFldTab(objFldTab.RowCount, "FIELDNAME") = "RESWK"

                                                If objRfcFunc.Call = False Then
                                                    MsgBox(objRfcFunc.Exception)
                                                End If

                                                i = 5

                                                If objDatTab.Rows.Count = 0 Then
                                                    ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
                                                Else

                                                    For Each objDatRec In objDatTab.Rows
                                                        i = i + 1
                                                        For Each objFldRec In objFldTab.Rows
                                                            Vendor_Coddee = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))

                                                            Vendor_Namee = Vendor_Coddee
                                                        Next
                                                    Next
                                                End If
                                            End If


                                        End If


                                        ''HHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHHH

                                    Else

                                        If m = 1 And (TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS") Then
                                            lvi.SubItems.Add("")
                                            lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")))
                                            'ListView2.Items(k).SubItems.Add("")
                                            'ListView2.Items(k).SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")
                                            ''m = m + 1
                                        ElseIf m = 7 Then
                                            lvi.SubItems.Add(Challan_noo)
                                            'ListView2.Items(k).SubItems.Add(Challan_noo) 'Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))) & ""
                                        ElseIf m = 8 Then
                                            lvi.SubItems.Add(Challan_Datee)
                                            'ListView2.Items(k).SubItems.Add(Challan_Datee) ''Trim(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))) & ""

                                        ElseIf m = 12 Then
                                            lvi.SubItems.Add("")
                                            lvi.SubItems.Add("")
                                            lvi.SubItems.Add("")
                                            lvi.SubItems.Add(Vendor_Coddee)
                                            lvi.SubItems.Add(Vendor_Namee)
                                            'ListView2.Items(k).SubItems.Add("") '''''
                                            'ListView2.Items(k).SubItems.Add("") ''''' GAte Pass No
                                            'ListView2.Items(k).SubItems.Add("")  ''  SO Line Item
                                            'ListView2.Items(k).SubItems.Add(Vendor_Coddee & "")
                                            'ListView2.Items(k).SubItems.Add(Vendor_Namee & "")
                                        Else
                                            lvi.SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")
                                            'ListView2.Items(k).SubItems.Add(Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH")) & "")
                                        End If

                                    End If

                                    'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))

                                    m = m + 1
                                Next
                                ListView2.Items.Add(lvi)
                                m = 0

                            Next

                            ''*********************************************  end

                        End If

                    ElseIf TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then

                        objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                        objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                        objQueryTab.Value = "VBUK"

                        objOptTab = objRfcFunc.Tables("OPTIONS")
                        objFldTab = objRfcFunc.Tables("FIELDS")
                        objDatTab = objRfcFunc.Tables("DATA")
                        objOptTab.FreeTable()

                        Dim Del_OrdeNo = InputBox("Please input the Delivery Order No.", "ElectroWay", "")

                        objOptTab.Rows.Add()
                        objOptTab(objOptTab.RowCount, "TEXT") = "GBSTK NE 'C' and VBELN = '" & Del_OrdeNo & "'" ''''''& Text6.Text & "' " '' and LOEKZ NE 'L'"
                        objFldTab.FreeTable()



                        objFldTab.Rows.Add()
                        objFldTab(objFldTab.RowCount, "FIELDNAME") = "VBELN"

                        If objRfcFunc.Call = False Then
                            MsgBox(objRfcFunc.Exception)
                        End If


                        Dim p As Integer = 1

                        i = 5

                        If objDatTab.Rows.Count = 0 Then
                            MsgBox("Invalid Vehicle ! Data Not available in SAP !", vbInformation, "ElectroSteel Castings Limited")
                            dr.Close()
                            Exit Sub
                        Else
                            Dim DO_NO As String
                            For Each objDatRec In objDatTab.Rows
                                For Each objFldRec In objFldTab.Rows
                                    'MsgBox Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    DO_NO = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                    functionCtr2 = CreateObject("SAP.Functions")
                                    sapConnection = functionCtr2.Connection




                                    sapConnection.User = SAPUsere_ID
                                    sapConnection.Password = SAPUsere_Pass
                                    sapConnection.System = SAPSys_name
                                    sapConnection.ApplicationServer = SAPApp_Server
                                    sapConnection.SystemNumber = SAPSys_No
                                    sapConnection.Client = SAP_Client
                                    sapConnection.Language = SAP_Lang
                                    If sapConnection.Logon(0, True) <> True Then
                                        MsgBox("No connection to SAP System .......")
                                        Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                                        Exit Sub

                                    End If
                                    Dim RFC_READ_TEXT = functionCtr2.Add("RFC_READ_TEXT") '<------------
                                    Dim tblText_Lines = RFC_READ_TEXT.Tables("TEXT_LINES")

                                    tblText_Lines.AppendRow()
                                    tblText_Lines(1, "TDOBJECT") = "VBBK"
                                    tblText_Lines(1, "TDNAME") = DO_NO
                                    tblText_Lines(1, "TDID") = "ZL01"
                                    If RFC_READ_TEXT.Call = True Then

                                        'MsgBox tblText_Lines.RowCount

                                        If tblText_Lines.RowCount > 0 Then

                                            For intRow = 1 To tblText_Lines.RowCount ' Change Next line to write a different header row

                                                If intRow = 1 Then
                                                    poText = tblText_Lines(intRow, "TDLINE")
                                                    'Else
                                                    'poText = poText & vbCrLf & tblText_Lines(intRow, "TDLINE")
                                                End If
                                            Next
                                        Else



                                        End If
                                    End If
                                Next
                            Next


                        End If

                        If poText <> Trim(Text6.Text) Then
                            MsgBox("Invalid Delivery No. .....", vbInformation, "ElectroWay")
                            dr.Close()
                            Exit Sub
                        Else

                            '''''''''&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&
                            Call SAP_Con2()

                            If sapConnection.Logon(0, True) <> True Then
                                MsgBox("No connection to SAP System .......")
                                Exit Sub

                            End If

                            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                            objQueryTab1.Value = "LIKP"
                            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                            objFldTab1 = objRfcFunc1.Tables("FIELDS")
                            objDatTab1 = objRfcFunc1.Tables("DATA")
                            objOptTab1.FreeTable()
                            objOptTab1.Rows.Add()
                            objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Del_OrdeNo & "'"

                            objFldTab1.FreeTable()

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNAG"
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNNR"   ''''KUNNR

                            If objRfcFunc1.Call = False Then
                                MsgBox(objRfcFunc1.Exception)
                            End If


                            Dim iii As Integer = 0

                            If objDatTab1.Rows.Count = 0 Then
                                'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
                            Else

                                For Each objDatRec1 In objDatTab1.Rows


                                    For Each objFldRec1 In objFldTab1.Rows
                                        iii = iii + 1

                                        If iii = 2 Then
                                            SoldToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                        End If

                                        If iii = 3 Then

                                            ShipToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))

                                        End If


                                        'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                    Next
                                Next
                            End If

                            ''**********
                            '''''''''&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&



                            ''BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB

                            Call SAP_Con2()

                            If sapConnection.Logon(0, True) <> True Then
                                MsgBox("No connection to SAP System .......")
                                Exit Sub

                            End If

                            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                            objQueryTab1.Value = "KNA1"
                            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                            objFldTab1 = objRfcFunc1.Tables("FIELDS")
                            objDatTab1 = objRfcFunc1.Tables("DATA")
                            objOptTab1.FreeTable()
                            objOptTab1.Rows.Add()
                            If Trim(SoldToParty) <> "" Then
                                objOptTab1(objOptTab1.RowCount, "TEXT") = "KUNNR = '" & SoldToParty & "'"
                            Else
                                objOptTab1(objOptTab1.RowCount, "TEXT") = "KUNNR = '" & ShipToParty & "'"
                            End If

                            objFldTab1.FreeTable()

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "NAME1"   ''''KUNNR

                            If objRfcFunc1.Call = False Then
                                MsgBox(objRfcFunc1.Exception)
                            End If




                            If objDatTab1.Rows.Count = 0 Then
                                'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
                            Else


                                For Each objDatRec1 In objDatTab1.Rows

                                    For Each objFldRec1 In objFldTab1.Rows

                                        CustomerNameee = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))

                                    Next
                                Next
                            End If
                            ''BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB
                            ''PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP

                            Call SAP_Con2()

                            If sapConnection.Logon(0, True) <> True Then
                                MsgBox("No connection to SAP System .......")
                                Exit Sub

                            End If

                            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                            objQueryTab1.Value = "VBPA"
                            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                            objFldTab1 = objRfcFunc1.Tables("FIELDS")
                            objDatTab1 = objRfcFunc1.Tables("DATA")
                            objOptTab1.FreeTable()
                            objOptTab1.Rows.Add()
                            objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Del_OrdeNo & "' and ( PARVW LIKE 'T%' or PARVW LIKE 'F%' or PARVW LIKE 'S%')"
                            ''objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Del_OrdeNo & "' and PARVW LIKE 'T%'"

                            objFldTab1.FreeTable()

                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "PARVW"
                            objFldTab1.Rows.Add()
                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "LIFNR"
                            If objRfcFunc1.Call = False Then
                                MsgBox(objRfcFunc1.Exception)
                            End If

                            Dim ii As Integer = 0

                            If objDatTab1.Rows.Count = 0 Then
                                MsgBox("Transporter Not mentioned in DO. !", vbInformation, "ElectroSteel Castings Limited")
                            Else
                                For Each objDatRec1 In objDatTab1.Rows
                                    For Each objFldRec1 In objFldTab1.Rows
                                        ii = ii + 1
                                        If ii = 3 Then
                                            Text18.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                        End If
                                        'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                    Next
                                Next
                            End If

                            If Text18.Text <> "" Then

                                Call SAP_Con2()

                                If sapConnection.Logon(0, True) <> True Then
                                    MsgBox("No connection to SAP System .......")
                                    Exit Sub

                                End If

                                objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                                objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                                objQueryTab1.Value = "LFA1"
                                objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                                objFldTab1 = objRfcFunc1.Tables("FIELDS")
                                objDatTab1 = objRfcFunc1.Tables("DATA")
                                objOptTab1.FreeTable()
                                objOptTab1.Rows.Add()
                                objOptTab1(objOptTab1.RowCount, "TEXT") = "LIFNR = '" & (Text18.Text) & "'"

                                objFldTab1.FreeTable()

                                objFldTab1.Rows.Add()
                                objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "NAME1"
                                If objRfcFunc1.Call = False Then
                                    MsgBox(objRfcFunc1.Exception)
                                End If

                                ii = 0

                                If objDatTab1.Rows.Count = 0 Then
                                    MsgBox("Transporter Not mentioned in DO. !", vbInformation, "ElectroSteel Castings Limited")
                                Else
                                    For Each objDatRec1 In objDatTab1.Rows


                                        For Each objFldRec1 In objFldTab1.Rows
                                            'iii = iii + 1

                                            'If iii = 3 Then
                                            Text19.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                            'End If

                                            'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                        Next
                                    Next
                                End If
                            End If

                            ''PPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP

                            ''///////////////////////////////////////

                            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                            objQueryTab.Value = "LIPS"
                            objOptTab = objRfcFunc.Tables("OPTIONS")
                            objFldTab = objRfcFunc.Tables("FIELDS")
                            objDatTab = objRfcFunc.Tables("DATA")
                            objOptTab.FreeTable()
                            objOptTab.Rows.Add()
                            objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '" & Del_OrdeNo & "'"

                            objFldTab.FreeTable()

                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "VGBEL"
                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "VBELN"
                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "POSNR"
                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "MATNR" ''
                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "ARKTX" ''
                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "LFIMG"  ''
                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "MEINS"   ''
                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "VGPOS"   ''
                            objFldTab.Rows.Add()
                            objFldTab(objFldTab.RowCount, "FIELDNAME") = "WERKS"   ''


                            If objRfcFunc.Call = False Then
                                MsgBox(objRfcFunc.Exception)
                            End If
                            i = 5
                            Dim K As Integer
                            If objDatTab.Rows.Count = 0 Then
                                MsgBox("Invalid Vehicle !", vbInformation, "ElectroSteel Castings Limited")
                            Else

                                For Each objDatRec In objDatTab.Rows
                                    K = ListView2.Items.Count + 1

                                    For Each objFldRec In objFldTab.Rows
                                        'ListView2.Items.Add(lvi)
                                        lvi = New ListViewItem

                                        'm = 1

                                        If m = 0 Then
                                            lvi.SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & "")
                                            'ListView2.Items.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & "")
                                        Else

                                            If m = 1 And (TypeOfVeh = "PURCH" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "GATEPASS") Then
                                                lvi.SubItems.Add("")
                                                'ListView2.Items(K).SubItems.Add("")
                                                ''ListView1.Items(k).SubItems.Add (m + 1), , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""
                                                ''m = m + 1

                                            ElseIf m = 7 Then
                                                For Lv_i = 7 To 14
                                                    lvi.SubItems.Add("")
                                                    'ListView2.Items(K).SubItems.Add("")
                                                Next Lv_i
                                                lvi.SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                                'ListView2.Items(K).SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & "")

                                                If Trim(SoldToParty) = "" Then
                                                    lvi.SubItems.Add(ShipToParty)
                                                    'ListView2.Items(K).SubItems.Add(ShipToParty & "")
                                                Else
                                                    lvi.SubItems.Add(ShipToParty)
                                                    'ListView2.Items(K).SubItems.Add(SoldToParty & "")
                                                End If
                                                lvi.SubItems.Add(CustomerNameee)
                                                'ListView2.Items(K).SubItems.Add(CustomerNameee & "")


                                            ElseIf m = 8 Then

                                                'Text1.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""

                                            Else
                                                lvi.SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & "")
                                                'ListView2.Items(K).SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & "")
                                            End If

                                        End If

                                        m = m + 1

                                    Next

                                    If m < 8 Then
                                        For Lv_i = 7 To 14
                                            lvi.SubItems.Add("")
                                            'ListView2.Items(K).SubItems.Add("")
                                        Next Lv_i


                                    End If

                                    m = 0
                                Next

                                ''*********************************************  end

                            End If

                            ''///////////////////////////////////////

                        End If
                    End If
                End If
                'Exit Sub
                For i = 0 To ListView2.Items.Count - 1
                    cm = New SqlCommand
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_GE_Det"
                    cm.Parameters.AddWithValue("@GE_HDR_TRAN_ID", HDR_TRANS_ID_2)
                    cm.Parameters.AddWithValue("@val_GE_HDR_ID", (Text5.Text))

                    cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", TypeOfVeh)

                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Unloading_No", ListView2.Items(i).SubItems(8).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Unloading_No", ListView2.Items(i).SubItems(7).Text & "")
                    End If


                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_PO_No", ListView2.Items(i).SubItems(1).Text)
                    Else
                        cm.Parameters.AddWithValue("@val_PO_No", "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_PO_Line_Item", ListView2.Items(i).SubItems(3).Text & "")
                    End If

                    cm.Parameters.AddWithValue("@val_DO_No", ListView2.Items(i).SubItems(1).Text & "")

                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_DO_Line_Item", ListView2.Items(i).SubItems(3).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_DO_Line_Item", "")
                    End If

                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_SO_No", ListView2.Items(i).Text)
                    Else
                        cm.Parameters.AddWithValue("@val_SO_No", "")
                    End If
                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_SO_Line_Item", ListView2.Items(i).SubItems(15).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_SO_Line_Item", "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Mat_Code", ListView2.Items(i).SubItems(4).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Mat_Code", ListView2.Items(i).SubItems(3).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Mat_Desc", ListView2.Items(i).SubItems(5).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Mat_Desc", ListView2.Items(i).SubItems(4).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Challan_Date", ListView2.Items(i).SubItems(10).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Challan_Date", ListView2.Items(i).SubItems(9).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Challan_No", ListView2.Items(i).SubItems(9).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Challan_No", ListView2.Items(i).SubItems(8).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Challan_Qty", Val(ListView2.Items(i).SubItems(6).Text) + 0)
                    Else
                        cm.Parameters.AddWithValue("@val_Challan_Qty", Val(ListView2.Items(i).SubItems(5).Text) + 0)
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_UOM", ListView2.Items(i).SubItems(7).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_UOM", ListView2.Items(i).SubItems(6).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_WayBill_No", ListView2.Items(i).SubItems(13).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_WayBill_No", ListView2.Items(i).SubItems(12).Text & "")
                    End If

                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Vendor_Code", ListView2.Items(i).SubItems(17).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Vendor_Code", "")
                    End If

                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Vendor_Name", ListView2.Items(i).SubItems(18).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Vendor_Name", "")
                    End If

                    ''cm.Parameters.AddWithValue("@val_Vendor_Name") = ""
                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_Customer_Code", ListView2.Items(i).SubItems(17).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Customer_Code", "")
                    End If

                    If TypeOfVeh = "SALES" Or TypeOfVeh = "STKTROUT" Or TypeOfVeh = "SALESRET" Then
                        cm.Parameters.AddWithValue("@val_Customer_Name", ListView2.Items(i).SubItems(18).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Customer_Name", "")
                    End If

                    ''cm.Parameters.AddWithValue("@val_Customer_Name") = ""
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_GatePass_No", ListView2.Items(i).SubItems(15).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_GatePass_No", ListView2.Items(i).SubItems(14).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Unloading_Remarks", ListView2.Items(i).SubItems(14).Text & "")
                    Else
                        cm.Parameters.AddWithValue("@val_Unloading_Remarks", ListView2.Items(i).SubItems(13).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        'cm.Parameters.AddWithValue("@val_CN_No", ListView2.Items(i).SubItems(11).Text & "")
                        cm.Parameters.AddWithValue("@val_CN_No", "")
                    Else
                        cm.Parameters.AddWithValue("@val_CN_No", ListView2.Items(i).SubItems(10).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_CN_Date", "")
                    Else
                        cm.Parameters.AddWithValue("@val_CN_Date", ListView2.Items(i).SubItems(11).Text & "")
                    End If
                    If TypeOfVeh = "PURCH" Or TypeOfVeh = "INTRDEPT" Or TypeOfVeh = "PURCHRET" Or TypeOfVeh = "GATEPASS" Then
                        cm.Parameters.AddWithValue("@val_Grouping_Ref_Code", "")
                    Else
                        cm.Parameters.AddWithValue("@val_Grouping_Ref_Code", "")
                    End If

                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.ExecuteNonQuery()
                Next i
                Try
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_upd_tbl_GE_HDR_Plant_Transporter"
                    cm.Parameters.AddWithValue("@val_GE_HDR_ID", (Text5.Text) & "")
                    cm.Parameters.AddWithValue("@val_TransporterCode", (Text18.Text) & "")
                    cm.Parameters.AddWithValue("@val_TransporterName", (Text19.Text) & "")
                    cm.Parameters.AddWithValue("@val_PlantCode", (Text1.Text) & "")
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.ExecuteNonQuery()
                Catch ex As Exception

                End Try


                If ListView2.Items.Count > 0 Then
                    Dim cmd As New SqlCommand
                    cmd.Connection = con
                    cmd.CommandType = CommandType.StoredProcedure
                    cmd.CommandText = "sp_del_tbl_GE_DET_Without_SAP_CON"
                    cmd.Parameters.AddWithValue("@val_GE_HDR_TRAN_ID", HDR_TRANS_ID_2)
                    cmd.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(Text5.Text))
                    cmd.ExecuteNonQuery()
                    cmd.Dispose()
                    'cm.Connection = con
                    'cm.CommandType = CommandType.StoredProcedure
                    'cm.CommandText = "sp_del_tbl_GE_DET_Without_SAP_CON"
                    'cm.Parameters.AddWithValue("@val_GE_HDR_TRAN_ID", "400371")
                    'cm.Parameters.AddWithValue("@val_GE_HDR_ID", "ESL\ES01\2016\400371")
                    'If con.State = ConnectionState.Closed Then
                    '    con.Open()
                    'End If
                    'Try
                    '    cm.ExecuteNonQuery()
                    'Catch ex As Exception

                    'End Try


                    Call Fetch_SQL_Data()

                End If
                ''***********************************************************
            Else

                Call Fetch_SQL_Data()

            End If
            dr.Close()

            If ListView1.Items.Count > 0 Then
                btnAddNewLineItems.Visible = True
                'Command5.Visible = True

            End If
            ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
            ''''
            Dim Tot_NN_WT As Integer
            dr = cc.GetDataReader("select sum(NET_WT) from tbl_SPLIT_DET where GE_HDR_ID = '" & Trim(Text5.Text) & "'")
            Try
                If dr.Read Then
                    Tot_NN_WT = dr(0)
                End If
            Catch ex As Exception

            End Try
            dr.Close()

            Text2.Text = ""
            If IsDBNull(Tot_NN_WT) = True Then
                Tot_NN_WT = 0
            End If
            Text2.Text = Tot_NN_WT
            ''''
            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (Text5.Text) & "'")
            If dr.Read Then

                If (dr("Type_Of_Vehicle") = "SALES" Or dr("Type_Of_Vehicle") = "STKTROUT") And ListView1.Items.Count > 1 Then
                    btnUpdateGrossTareWt.Visible = True
                End If
            End If
            dr.Close()

            ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO

        End If
        Try
            Dim sum_qty As Integer
            For ld = 0 To ListView1.Items.Count - 1
                sum_qty = sum_qty + Val(ListView1.Items(ld).SubItems(8).Text)
            Next ld

            Text23.Text = sum_qty

            If User_ID = "admin" Then
                Text2.Enabled = True
                Text2.ReadOnly = False
            End If
            If AuthForOUTBOUNDSplitting = "INBOUNDSPLITTING" Then
                btnAddNewLineItems.Enabled = True
            End If
        Catch ex As Exception

        End Try

    End Sub

    Private Sub txtTransactionNo_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Text5.TextChanged
        Text5.Text = UCase(Trim(Text5.Text))
        Text5.SelectionStart = Len(Text5.Text)
    End Sub
    Private Sub SAP_Con2()
        functionCtr2 = CreateObject("SAP.Functions")
        sapConnection = functionCtr2.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
    End Sub

    Private Sub txtNetWt_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles Text9.KeyPress
        If AscW(e.KeyChar) = 13 Then

            If IsNumeric(Text9.Text) = False Then
                MsgBox("Wrong Net Wt. Value !", vbInformation, "Electrosteel Castings Limited.")
                Text9.Focus()
                Exit Sub
            ElseIf Trim(Text13.Text) = "" Then

            Else
                dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID ='" & (Text5.Text) & "'")
                If dr.Read Then
                    TypeOfVehicle = dr("Type_Of_Vehicle")
                    'Text6.Text = dr("Vehicle_No")
                End If
                dr.Close()

                dr = cc.GetDataReader("select * from tbl_GE_Det where GE_DET_TRAN_ID ='" & (Text13.Text) & "' order by GE_DET_TRAN_ID")

                If dr.Read Then
                    lvi = New ListViewItem
                    'rec1.Close
                    i = ListView1.Items.Count + 1
                    lvi.Text = dr("GE_DET_TRAN_ID")
                    'ListView1.Items.Add(dr("GE_DET_TRAN_ID"))
                    'ListView1.Items(i).Checked = True
                    If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Then
                        lvi.SubItems.Add(dr("PO_NO"))
                        'ListView1.Items(i).SubItems.Add(dr("PO_NO") & "")
                    ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                        lvi.SubItems.Add(dr("SO_NO"))
                        'ListView1.Items(i).SubItems.Add(dr("SO_NO") & "")
                    End If
                    If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Then
                        lvi.SubItems.Add("")
                        'ListView1.Items(i).SubItems.Add("")
                    ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                        lvi.SubItems.Add(dr("DO_NO"))
                        'ListView1.Items(i).SubItems.Add(dr("DO_NO") & "")
                    End If

                    If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Then
                        'lvi.SubItems.Add(dr("PO_Line_Item"))
                        lvi.SubItems.Add(Text8.Text.Trim)

                        'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item") & "")
                    ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                        lvi.SubItems.Add(dr("DO_Line_Item"))
                        'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item") & "")
                    End If
                    lvi.SubItems.Add(dr("Mat_CODE"))
                    'ListView1.Items(i).SubItems.Add(dr("Mat_CODE") & "")
                    lvi.SubItems.Add(dr("Mat_Desc"))
                    'ListView1.Items(i).SubItems.Add(dr("Mat_Desc") & "")
                    lvi.SubItems.Add(dr("DO_Challan_Qty"))
                    'ListView1.Items(i).SubItems.Add(dr("DO_Challan_Qty") + 0)
                    lvi.SubItems.Add(dr("UOM"))
                    'ListView1.Items(i).SubItems.Add(dr("UOM") & "")
                    lvi.SubItems.Add(Val(Text9.Text))
                    'ListView1.Items(i).SubItems.Add(Val(txtNetWt.Text))
                    lvi.SubItems.Add(dr("Challan_No") & "")
                    'ListView1.Items(i).SubItems.Add(dr("Challan_No") & "")
                    lvi.SubItems.Add(dr("Challan_Date") & "")
                    'ListView1.Items(i).SubItems.Add(dr("Challan_Date") & "")
                    lvi.SubItems.Add(dr("Unloading_No") & "")
                    'ListView1.Items(i).SubItems.Add(dr("Unloading_No") & "")
                    lvi.SubItems.Add(Text21.Text.Trim)
                    'ListView1.Items(i).SubItems.Add(Trim(Text21.Text) & "")
                    lvi.SubItems.Add(Text22.Text.Trim)
                    'ListView1.Items(i).SubItems.Add(Trim(Text22.Text) & "")

                    Text13.Text = ""
                    Text3.Text = ""
                    Text4.Text = ""
                    Text8.Text = ""
                    Text12.Text = ""
                    Text10.Text = ""
                    Text11.Text = ""
                    Text9.Text = ""

                    Text14.Text = ""
                    Text15.Text = ""
                    Text16.Text = ""
                    Text21.Text = ""
                    Text22.Text = ""

                    'rec1.MoveNext
                    ListView1.Items.Add(lvi)
                End If
                dr.Close()

            End If
            Dim sum_qty As Integer
            For ld As Integer = 0 To ListView1.Items.Count - 1
                sum_qty = sum_qty + Val(ListView1.Items(ld).SubItems(8).Text)
            Next ld

            Text23.Text = sum_qty

        End If
    End Sub
    Private Sub SAP_Conn()
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub

    Private Sub Fetch_SQL_Data()
        '''YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY
        ''YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY
        dr = cc.GetDataReader("select Type_Of_Vehicle from tbl_GE_DET where GE_HDR_ID ='" & (Text5.Text) & "'")
        If dr.Read Then
            TypeOfVehicle = dr("Type_Of_Vehicle")
            'Text6.Text = dr("Vehicle_No")
        End If
        dr.Close()

        dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID ='" & (Text5.Text) & "'")
        If dr.Read Then
            'TypeOfVehicle = dr("Type_Of_Vehicle")
            Text6.Text = dr("Vehicle_No")
            'Text1.Text = dr("Plant_Code")
            Text18.Text = dr("Transpoter_Code")
            Text19.Text = dr("TransporterName")
        End If
        dr.Close()

        dr = cc.GetDataReader("select isnull(sum(NET_WT),0) from tbl_GE_Det where GE_HDR_ID ='" & (Text5.Text) & "'")
        If dr.Read Then
            Text2.Text = dr(0)
            'Text6.Text = dr("Vehicle_No")
        End If
        dr.Close()

        dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_ID ='" & (Text5.Text) & "' order by GE_DET_TRAN_ID")

        While dr.Read
            lvi = New ListViewItem
            'i = ListView1.Items.Count + 1
            i = 0
            lvi.Text = dr("GE_DET_TRAN_ID")
            'ListView1.Items.Add(dr("GE_DET_TRAN_ID"))
            'ListView1.Items(i).Checked = True
            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                lvi.SubItems.Add(dr("PO_NO"))
                'ListView1.Items(i).SubItems.Add(dr("PO_NO") & "")
            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                lvi.SubItems.Add(dr("SO_NO"))
                'ListView1.Items(i).SubItems.Add(dr("SO_NO") & "")
            End If
            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                lvi.SubItems.Add("")
                'ListView1.Items(i).SubItems.Add("")
            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                lvi.SubItems.Add(dr("DO_NO"))
                'ListView1.Items(i).SubItems.Add(dr("DO_NO") & "")
            End If

            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                lvi.SubItems.Add(dr("PO_Line_Item"))
                'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item") & "")
            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                lvi.SubItems.Add(dr("DO_Line_Item"))
                'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item") & "")
            End If
            lvi.SubItems.Add(dr("Mat_CODE"))
            'ListView1.Items(i).SubItems.Add(dr("Mat_CODE") & "")
            lvi.SubItems.Add(dr("Mat_Desc"))
            'ListView1.Items(i).SubItems.Add(dr("Mat_Desc") & "")
            lvi.SubItems.Add(dr("DO_Challan_Qty"))
            'ListView1.Items(i).SubItems.Add(dr("DO_Challan_Qty") + 0)
            lvi.SubItems.Add(dr("UOM"))
            'ListView1.Items(i).SubItems.Add(dr("UOM") & "")
            lvi.SubItems.Add(dr("NET_WT"))
            'ListView1.Items(i).SubItems.Add(dr("NET_WT") + 0)
            lvi.SubItems.Add(dr("Challan_No"))
            'ListView1.Items(i).SubItems.Add(dr("Challan_No") & "")
            lvi.SubItems.Add(dr("Challan_Date"))
            'ListView1.Items(i).SubItems.Add(dr("Challan_Date") & "")
            lvi.SubItems.Add(dr("Unloading_No"))
            'ListView1.Items(i).SubItems.Add(dr("Unloading_No") & "")
            lvi.SubItems.Add(dr("SO_Line_Item"))
            'ListView1.Items(i).SubItems.Add(dr("SO_Line_Item") & "")
            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                lvi.SubItems.Add(dr("Vendor_Code"))
                'ListView1.Items(i).SubItems.Add(dr("Vendor_Code") & "")
            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                lvi.SubItems.Add(dr("Customer_Code"))
                'ListView1.Items(i).SubItems.Add(dr("Customer_Code") & "")
            End If

            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                lvi.SubItems.Add(dr("Vendor_Name"))
                'ListView1.Items(i).SubItems.Add(dr("Vendor_Name") & "")
            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                lvi.SubItems.Add(dr("Customer_Name"))
                'ListView1.Items(i).SubItems.Add(dr("Customer_Name") & "")
            End If
            ListView1.Items.Add(lvi)
            'rec1.MoveNext()
        End While
        dr.Close()
    End Sub
    Private Sub Push_Data()

        On Error GoTo err

        Dim Date_v As String
        Dim Time_v As String
        Dim WT_UOM As String
        Dim ch_wt As Double
        Dim tareeWt As Double
        Dim GrosWT As Double
        Dim NettWT As Double
        Dim DO_Nmbr, Unld_Nmbr As String
        Dim PLANT_CODE111, GE_HDRID1, Result, oStatus, PostCoil As String

        Date_v = Format(Today.Date, "yyyy") & Format(Today.Date, "MM") & Format(Today.Date, "dd")
        Time_v = TimeOfDay.ToString("HH") & TimeOfDay.ToString("mm") & TimeOfDay.ToString("ss")


        'TimeOfDay.ToString()

        For i = 0 To ListView1.Items.Count - 1

            If Val(ListView1.Items(i).SubItems(8).Text) > 0 Then

                DO_Nmbr = Trim(ListView1.Items(i).SubItems(2).Text)
                Unld_Nmbr = Trim(ListView1.Items(i).SubItems(11).Text)

                If TypeOfVehicle = "PURCH" Or TypeOfVehicle = "PURCHRET" Or TypeOfVehicle = "INTRDEPT" Or TypeOfVehicle = "GATEPASS" Then

                    WBWeightDet = ""

                    pono_wb = ListView1.Items(i).SubItems(1).Text & ""
                    ponoLinItm_wb = ListView1.Items(i).SubItems(3).Text & ""


                    ''Call WB_Determin


                    functionCtrl = CreateObject("SAP.Functions")
                    sapConnection = functionCtrl.Connection

                    sapConnection.User = SAPUsere_ID
                    sapConnection.Password = SAPUsere_Pass
                    sapConnection.System = SAPSys_name
                    sapConnection.ApplicationServer = SAPApp_Server
                    sapConnection.SystemNumber = SAPSys_No
                    sapConnection.Client = SAP_Client
                    sapConnection.Language = SAP_Lang
                    sapConnection.CodePage = SAP_CodePage


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub

                    Else
                        ''MsgBox "Connected ........"

                        '''
                        '''                                                        Set funcControl = CreateObject("SAP.Functions")
                        '''                                                        funcControl.Connection = sapConnection
                        '''                                                        Set oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                        '''                                                        Set oTrnID = oRFC.Exports("ZTR_ID")
                        '''                                                        oTrnID.Value = ListView1.Items(i).Text
                        '''                                                        If oRFC.Call = True Then
                        '''                                                        'oStatus = oRFC.Imports("matnr")
                        '''                                                        ''MsgBox oStatus
                        '''                                                        If oStatus = 1 Then ' fail
                        '''                                                            PostCoil = 1
                        '''                                                        End If
                        '''                                                        If oStatus = 0 Then ' successfully deleted from Zwt_bg table
                        '''                                                            PostCoil = 2
                        '''                                                        End If
                        '''                                                        End If
                        ''**************************************************

                        Dim funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        Dim oRFC = funcControl.Add("ZRFC_WB_data_update")
                        Dim oTrnID = oRFC.Exports("ZTR_ID")
                        Dim oItmID = oRFC.Exports("ZMATNR")
                        Dim oVhclNo = oRFC.Exports("ZVHCL_NMBR")

                        Dim oVendor = oRFC.Exports("zlifnr")

                        Dim oPONo = oRFC.Exports("ZPO_SO_NO")
                        Dim oPOItem = oRFC.Exports("ZPO_ITEM")
                        Dim oUnloadingNo = oRFC.Exports("ZGATENO")
                        Dim oTrType = oRFC.Exports("ZTR_TYPE")

                        Dim oWerks = oRFC.Exports("zwerks")


                        Dim oTrnDate = oRFC.Exports("ZTRN_DATE")
                        Dim oTrnTime = oRFC.Exports("ZTRN_TIME")

                        Dim oZInOut = oRFC.Exports("zinout")

                        Dim oWTDet = oRFC.Exports("ztweight")

                        Dim oChWT = oRFC.Exports("ZCHL_GTY")
                        Dim oUOM = oRFC.Exports("ZWT_UNIT")
                        Dim oGrossWT = oRFC.Exports("ZGROSS_WT")
                        Dim oTareWT = oRFC.Exports("ZTARE_WT")
                        Dim oNetWT = oRFC.Exports("ZNET_WT")
                        Dim oWBTrID = oRFC.Exports("ZWB_TR_ID")

                        oTrnID.Value = Trim((ListView1.Items(i).Text) & "\" & Trim(Text1.Text))               ''    "TEST0001136"
                        oItmID.Value = Trim(ListView1.Items(i).SubItems(4).Text)
                        oVhclNo.Value = Trim(Text6.Text)

                        oVendor.Value = Trim(ListView1.Items(i).SubItems(13).Text)

                        oPONo.Value = ListView1.Items(i).SubItems(3).Text
                        oPOItem.Value = ListView1.Items(i).SubItems(1).Text
                        oUnloadingNo.Value = ListView1.Items(i).SubItems(11).Text
                        oTrType.Value = TypeOfVehicle



                        '''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        '''''''                                                            Call SAP_Con2
                        '''''''
                        '''''''                                                                If sapConnection.Logon(0, True) <> True Then
                        '''''''                                                                    MsgBox "No connection to SAP System ......."
                        '''''''                                                                    rec4.Close
                        '''''''                                                                    Exit Sub
                        '''''''
                        '''''''                                                                End If
                        '''''''
                        '''''''                                                            Set objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")
                        '''''''
                        '''''''                                                            Set objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        '''''''                                                            objQueryTab1.Value = "YMM_UNLOAD_H"
                        '''''''
                        '''''''
                        '''''''                                                            Set objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        '''''''                                                            Set objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        '''''''                                                            Set objDatTab1 = objRfcFunc1.Tables("DATA")
                        '''''''                                                            'First we set the condition
                        '''''''                                                            'Refresh table
                        '''''''                                                            ''objOptTab.FreeTable
                        '''''''                                                            objOptTab1.FreeTable
                        '''''''                                                            'Then set values
                        '''''''                                                            objOptTab1.Rows.Add
                        '''''''                                                            objOptTab1(objOptTab1.RowCount, "TEXT") = "UN_NO = '" & Unld_Nmbr & "'"
                        '''''''
                        '''''''                                                            objFldTab1.FreeTable
                        '''''''
                        '''''''                                                            objFldTab1.Rows.Add
                        '''''''                                                            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "WERKS"  ''
                        '''''''
                        '''''''
                        '''''''
                        '''''''                                                            If objRfcFunc1.call = False Then
                        '''''''                                                            MsgBox objRfcFunc1.Exception
                        '''''''                                                            End If
                        '''''''
                        '''''''                                                             For Each objDatRec1 In objDatTab1.Rows
                        '''''''
                        '''''''
                        '''''''                                                                    For Each objFldRec1 In objFldTab1.Rows
                        '''''''                                                                                PLANT_CODE111 = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        '''''''
                        '''''''
                        '''''''                                                                    Next
                        '''''''                                                            Next
                        '''''''
                        '''''''
                        '''''''                                        ''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        'oWerks.Value = "ES01"   ''PLANT_CODE111
                        oWerks.Value = "1000"   ''PLANT_CODE111


                        '''                                                        If PLANT_CODE_UPL_ZWT_BG = "" Then
                        '''                                                            oWerks.Value = Trim(Text1.Text)
                        '''                                                        Else
                        '''                                                            oWerks.Value = PLANT_CODE_UPL_ZWT_BG
                        '''                                                        End If
                        '''
                        oTrnDate.Value = Date_v     ''' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oTrnTime.Value = Time_v    '''   "103442"      ''format used in ingress is Hour Minute Second without having any space    ''format(Time, "hh:mm:ss")



                        If TypeOfVehicle = "PURCH" Then
                            oZInOut.Value = "I"
                        ElseIf TypeOfVehicle = "PURCHRET" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "INTRDEPT" Then
                            oZInOut.Value = "D"
                        ElseIf TypeOfVehicle = "GATEPASS" Then
                            oZInOut.Value = "G"
                        End If


                        oWTDet.Value = WBWeightDet

                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_DET_TRAN_ID = " & ListView1.Items(i).Text)
                        If dr.Read Then
                            GE_HDRID1 = dr("GE_HDR_ID")
                            ch_wt = dr("DO_Challan_QTY")
                            WT_UOM = Trim(dr("UOM"))
                            If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "DMT") Then
                                GrosWT = (dr("F_WT")) / 1000
                                tareeWt = (dr("S_WT")) / 1000
                                NettWT = (dr("Net_WT")) / 1000

                            Else
                                GrosWT = dr("F_WT")
                                tareeWt = dr("S_WT")
                                NettWT = dr("Net_WT")
                            End If

                        End If
                        dr.Close()
                        oChWT.Value = ch_wt
                        'oUOM.Value = WT_UOM
                        If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "KG" Or WT_UOM = "DMT") Then
                            oUOM.Value = WT_UOM
                        Else
                            oUOM.Value = "KG"
                        End If
                        oGrossWT.Value = GrosWT
                        oTareWT.Value = tareeWt    ''' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oNetWT.Value = NettWT
                        oWBTrID.Value = GE_HDRID1

                        ''   "TEST-VEHICLE052014"


                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                                '''''''''''''''''''''''''''
                                cm.Connection = con
                                cm.CommandType = CommandType.Text
                                cm.CommandText = "update tbl_GE_DET set DataUploadedIN_SAP = 'U' where GE_HDR_ID  ='" & GE_HDRID1 & "'"
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()

                                '''''''''''''''''''''''''''

                            End If
                            If oStatus = 2 Then ' Data Exists in SAP
                                PostCoil = 5
                            End If
                        End If

                        Result = oRFC.Call
                        'MsgBox Result

                    End If
                ElseIf TypeOfVehicle = "SALES" Or TypeOfVehicle = "STKTROUT" Or TypeOfVehicle = "SALESRET" Then


                    functionCtrl = CreateObject("SAP.Functions")
                    sapConnection = functionCtrl.Connection



                    sapConnection.User = SAPUsere_ID
                    sapConnection.Password = SAPUsere_Pass
                    sapConnection.System = SAPSys_name
                    sapConnection.ApplicationServer = SAPApp_Server
                    sapConnection.SystemNumber = SAPSys_No
                    sapConnection.Client = SAP_Client
                    sapConnection.Language = SAP_Lang
                    sapConnection.CodePage = SAP_CodePage


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub

                    Else
                        ''MsgBox "Connected ........"


                        Dim funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        Dim oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                        Dim oTrnID = oRFC.Exports("ZTR_ID")
                        oTrnID.Value = Trim(Text5.Text)
                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                            End If
                        End If
                        ''**************************************************

                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_data_update")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        Dim oItmID = oRFC.Exports("ZMATNR")
                        Dim oVhclNo = oRFC.Exports("ZVHCL_NMBR")

                        Dim oCustomer = oRFC.Exports("zkunnr")

                        Dim oSONo = oRFC.Exports("ZPO_SO_NO")
                        Dim oSOLItem = oRFC.Exports("ZPO_ITEM")

                        Dim oPONo = oRFC.Exports("zvbeln")  ''' DO no
                        Dim oPOItem = oRFC.Exports("zposnr")   ''   DO Line item
                        'Set oUnloadingNo = oRFC.Exports("ZUN_NO")
                        Dim oTrType = oRFC.Exports("ZTR_TYPE")

                        Dim oWerks = oRFC.Exports("zwerks")

                        Dim oTrnDate = oRFC.Exports("ZTRN_DATE")
                        Dim oTrnTime = oRFC.Exports("ZTRN_TIME")

                        Dim oZInOut = oRFC.Exports("zinout")

                        Dim oChWT = oRFC.Exports("ZCHL_GTY")
                        Dim oUOM = oRFC.Exports("ZWT_UNIT")
                        Dim oGrossWT = oRFC.Exports("ZTARE_WT")
                        Dim oTareWT = oRFC.Exports("ZGROSS_WT")
                        Dim oNetWT = oRFC.Exports("ZNET_WT")
                        Dim oWBTrID = oRFC.Exports("ZWB_TR_ID")

                        oTrnID.Value = Trim((ListView1.Items(i).Text) & "\" & Trim(Text1.Text))              ''    "TEST0001136"
                        oItmID.Value = ListView1.Items(i).SubItems(4).Text
                        oVhclNo.Value = Trim(Text6.Text)

                        oCustomer.Value = ListView1.Items(i).SubItems(13).Text & ""

                        oSONo.Value = ListView1.Items(i).SubItems(12).Text
                        oSOLItem.Value = ListView1.Items(i).SubItems(1).Text

                        oPONo.Value = ListView1.Items(i).SubItems(2).Text
                        oPOItem.Value = ListView1.Items(i).SubItems(3).Text
                        ''oUnloadingNo.Value = ListView1.Items(i).SubItems(11).Text
                        oTrType.Value = TypeOfVehicle


                        '''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            dr.Close()
                            Exit Sub

                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "LIPS"


                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        ''objOptTab.FreeTable
                        objOptTab1.FreeTable()
                        'Then set values
                        objOptTab1.Rows.Add()
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & DO_Nmbr & "'"

                        objFldTab1.FreeTable()

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "WERKS"  ''


                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If

                        For Each objDatRec1 In objDatTab1.Rows


                            For Each objFldRec1 In objFldTab1.Rows
                                PLANT_CODE111 = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))


                            Next
                        Next


                        ''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        oWerks.Value = PLANT_CODE111
                        '''''
                        '''''                                                        If PLANT_CODE_UPL_ZWT_BG = "" Then
                        '''''
                        '''''                                                            oWerks.Value = Trim(Text1.Text)
                        '''''                                                        Else
                        '''''                                                            oWerks.Value = PLANT_CODE_UPL_ZWT_BG
                        '''''                                                        End If

                        oTrnDate.Value = Date_v    ''' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oTrnTime.Value = Time_v    '''   "103442"      ''format used in ingress is Hour Minute Second without having any space    ''format(Time, "hh:mm:ss")





                        If TypeOfVehicle = "SALES" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "STKTROUT" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "SALESRET" Then
                            oZInOut.Value = "I"
                        End If




                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_DET_TRAN_ID = " & ListView1.Items(i).Text)
                        If dr.Read Then
                            GE_HDRID1 = dr("GE_HDR_ID")
                            ch_wt = dr("DO_Challan_QTY")
                            WT_UOM = Trim(dr("UOM"))
                            If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "DMT") Then
                                GrosWT = (dr("F_WT")) / 1000
                                tareeWt = (dr("S_WT")) / 1000
                                NettWT = (dr("Net_WT")) / 1000

                            Else
                                GrosWT = dr("F_WT")
                                tareeWt = dr("S_WT")
                                NettWT = dr("Net_WT")
                            End If
                        End If
                        dr.Close()
                        oChWT.Value = ch_wt
                        If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "KG") Then
                            oUOM.Value = WT_UOM
                        Else
                            oUOM.Value = "KG"
                        End If
                        oGrossWT.Value = GrosWT
                        oTareWT.Value = tareeWt    ''' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oNetWT.Value = NettWT
                        oWBTrID.Value = GE_HDRID1
                        ''   "TEST-VEHICLE052014"


                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                                '''''''''''''''''''''''''''
                                cm.Connection = con
                                cm.CommandType = CommandType.Text
                                cm.CommandText = "update tbl_GE_DET set DataUploadedIN_SAP = 'U' where GE_HDR_ID  ='" & GE_HDRID1 & "'"
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()

                                '''''''''''''''''''''''''''
                            End If
                            If oStatus = 2 Then ' Data Exists in SAP
                                PostCoil = 5
                            End If
                        End If

                        Result = oRFC.Call
                        'MsgBox Result

                    End If


                End If
            End If
            SAP_Close1()
        Next

err:
        If Err.Number <> 0 Then
            MsgBox(Err.Number & "   " & Err.Description)
        End If
        Err.Clear()
    End Sub

    Private Sub SAP_Con1()
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub
    Private Sub SAP_Close1()
        Try
            functionCtrl.Connection.Logoff()
            'functionCtr2.Connection.Logoff()
            sapConnection = Nothing
            functionCtrl = Nothing
            functionCtr2 = Nothing
        Catch ex As Exception

        End Try
    End Sub
    Private Sub WB_Determin()

        On Error GoTo err
        Dim TWeight_1 As String
        Call SAP_Con1()

        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            SAP_CON_NOT_AVAIL = 1
            'Label25.Text = "SAP CONNECTION NOT AVAILABLE."
            Exit Sub

        Else

            'Label25.Text = ""

            Dim objRfcFunc As Object
            'Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
            objQueryTab.Value = "EKPO"

            objOptTab = objRfcFunc.Tables("OPTIONS")
            objFldTab = objRfcFunc.Tables("FIELDS")
            objDatTab = objRfcFunc.Tables("DATA")
            'First we set the condition
            'Refresh Table
            objOptTab.FreeTable()
            'Then set values
            ''objOptTab.Rows.Add
            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
            objOptTab.Rows.Add()
            objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & pono_wb & "' and EBELP = '" & ponoLinItm_wb & "'"  ''' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

            objFldTab.FreeTable()

            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "TWEIGHT"

            If objRfcFunc.Call = False Then
                MsgBox(objRfcFunc.Exception)
            End If

            'i = 5

            If objDatTab.Rows.Count = 0 Then
                ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
            Else

                For Each objDatRec In objDatTab.Rows
                    'i = i + 1
                    For Each objFldRec In objFldTab.Rows
                        TWeight_1 = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))


                    Next
                Next
            End If

            WBWeightDet = TWeight_1

        End If

err:
        If Err.Number <> 0 Then
            MsgBox(Err.Number & "    " & Err.Description)
        End If
        Err.Clear()


    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        ListView1.Items.Clear()
    End Sub

    Private Sub txtNetWt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Text9.TextChanged

    End Sub
End Class