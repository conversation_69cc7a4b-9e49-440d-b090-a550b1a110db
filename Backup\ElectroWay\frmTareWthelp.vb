﻿Imports Microsoft.Office.Interop
Imports System.Windows.Forms

Public Class frmTareWthelp
    Dim cc As New Class1
    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        ListView1.Items.Clear()
        dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No like '%" & txtVehicleSearch.Text.Trim & "%' order by Vehicle_No")
        Try
            While dr.Read
                Dim i As Integer = ListView1.Items.Count + 1
                Dim lvi As New ListViewItem

                lvi.Text = CStr(dr("Vehicle_no"))
                'lvi.SubItems.Add(dr("Vehicle_no"))
                lvi.SubItems.Add(dr("Vehicle_Tare_WT"))
                lvi.SubItems.Add(dr("TareWtValidUpto"))
                ListView1.Items.Add(lvi)
            End While
        Catch ex As Exception

        End Try
        dr.Close()
    End Sub

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        'Try

        '    Dim ExcelObj As Object

        '    Dim ExcelBook As Object

        '    Dim ExcelSheet As Object

        '    Dim i As Integer

        '    ExcelObj = CreateObject("Excel.Application")
        '    ExcelBook = ExcelObj.Workbooks.Add

        '    ExcelSheet = ExcelBook.Worksheets(1)

        '    With ExcelSheet
        '        For i = 0 To ListView1.Columns.Count - 1
        '            .Cells(1, i + 1) = ListView1.Columns(i).Text
        '        Next i
        '        For i = 0 To ListView1.Items.Count - 1
        '            .Cells(i + 2, 1) = ListView1.Items(i).Text

        '            .Cells(i + 2, 2) = ListView1.Items(i).SubItems(1).Text

        '            .Cells(i + 2, 3) = ListView1.Items(i).SubItems(2).Text
        '        Next
        '    End With

        '    ExcelObj.Visible = True

        'Catch ex As Exception

        'End Try
        Try
            'create objects to interface to Excel

            Dim xls As New Excel.Application
            Dim book As Excel.Workbook
            Dim sheet As Excel.Worksheet

            'create a workbook and get reference to first worksheet

            xls.Workbooks.Add()
            book = xls.ActiveWorkbook
            sheet = book.ActiveSheet
            '-----------------------------
            sheet.Range("A:C").ColumnWidth = 16
            sheet.Cells(1, 1) = "Vehicle_no"
            sheet.Cells(1, 2) = "Vehicle_Tare_WT"
            sheet.Cells(1, 3) = "TareWtValidUpto"
            '-----------------------------
            'step through rows and columns and copy data to worksheet

            Dim row As Integer = 2
            Dim col As Integer = 1

            For Each item As ListViewItem In ListView1.Items
                For i As Integer = 0 To item.SubItems.Count - 1
                    sheet.Cells(row, col) = item.SubItems(i).Text
                    col = col + 1
                Next
                row += 1
                col = 1
            Next

            ''save the workbook and clean up

            'book.SaveAs(SaveFileDialog1.FileName)
            'xls.Workbooks.Close()
            'xls.Quit()

            'releaseObject(sheet)
            'releaseObject(book)
            'releaseObject(xls)
            xls.Visible = True

        Catch ex As Exception

        End Try
    End Sub
    Dim f2 As frmVehicleWt = CType(Application.OpenForms("frmVehicleWt"), frmVehicleWt)
    Private Sub ListView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView1.DoubleClick
        f2.txtVehicleNo.Text = Trim(ListView1.SelectedItems.Item(0).Text)
        f2.txtTareWt.Text = Trim(ListView1.SelectedItems.Item(0).SubItems(1).Text)
        f2.dtTareWtValidUpto.Value = Trim(ListView1.SelectedItems.Item(0).SubItems(2).Text)
        Me.Close()
    End Sub

    Private Sub ListView1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ListView1.SelectedIndexChanged

    End Sub
End Class