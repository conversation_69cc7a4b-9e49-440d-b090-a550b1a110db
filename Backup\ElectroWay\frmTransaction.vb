﻿Public Class frmTransaction
    Dim cc As New Class1
    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        gvTransaction.DataSource = Nothing
        ds = cc.GetDataset("select distinct GE_HDR_ID , Vehicle_NO from tbl_GE_HDR   where GE_HDR_ID like '%" & Trim(txtTransactionNo.Text) & "%' order by Vehicle_No , GE_HDR_ID ")
        gvTransaction.DataSource = ds.Tables(0)
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        gvTransaction.DataSource = Nothing
        If Help_callfrom = "DO_UPDATION" Then
            ds = cc.GetDataset("select distinct GE_HDR_ID ,Vehicle_NO , EntryDateTime from tbl_GE_HDR   where VEHICLE_NO like '%" & Trim(txtVehicleNo.Text) & "%' and Vehicle_Status = 'IN' group by Vehicle_NO , GE_HDR_ID , EntryDateTime order by EntryDateTime DESC")
        Else
            ds = cc.GetDataset("select distinct GE_HDR_ID ,Vehicle_NO , EntryDateTime from tbl_GE_HDR   where VEHICLE_NO like '%" & Trim(txtVehicleNo.Text) & "%' group by Vehicle_NO , GE_HDR_ID , EntryDateTime order by EntryDateTime DESC")
        End If
        gvTransaction.DataSource = ds.Tables(0)
    End Sub

    Private Sub gvTransaction_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles gvTransaction.MouseClick
        Dim selectedCell As DataGridViewCell = gvTransaction.SelectedCells(0)
        Dim strItem As String = selectedCell.FormattedValue
        Try
            Dim f2 As frmSplitting = CType(Application.OpenForms("frmSplitting"), frmSplitting)
            f2.Text5.Text = strItem
        Catch ex As Exception

        End Try
       

        If Help_callfrom = "DO_UPDATION" Then
            'frmUnloadingDOUpdation.txtTransactionNo.Text = strItem
            Dim f2 As frmUnloadingDOUpdation = CType(Application.OpenForms("frmUnloadingDOUpdation"), frmUnloadingDOUpdation)
            f2.txtTransactionNo.Text = strItem
        End If
        Me.Close()
    End Sub
End Class