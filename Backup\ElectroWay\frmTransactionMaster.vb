﻿Public Class frmTransactionMaster
    Dim cc As New Class1
    Dim Load1 As Boolean = False
    Private Sub frmTransactionMaster_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        SelectCompany()
        SelectPlant()
        Load1 = True
        SelectCompanyName()
        SelectPlantName()
        SelectYear()
        ddlYear.SelectedItem = Today.Year
    End Sub
    Private Sub SelectYear()
        Dim Y As Integer = Today.Year - 2
        For i As Integer = 0 To 5
            ddlYear.Items.Add(Y + i)
        Next
    End Sub
    Private Sub SelectPlant()
        Try
            Dim str As String = "select * from tbl_plant_mst"
            dt = cc.GetDataTable(str)
            ddlPlantCode.DataSource = dt

            ddlPlantCode.DisplayMember = "Plant_Code"
            ddlPlantCode.ValueMember = "Plant_Code"
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SelectPlantName()
        If Load1 = True Then
            Dim str As String = "select Plant_Name from tbl_plant_mst where Plant_Code ='" & ddlPlantCode.SelectedValue & "'"
            dr = cc.GetDataReader(str)
            Try
                While dr.Read
                    txtPlantName.Text = dr("Plant_Name").ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub
    Private Sub SelectCompany()
        Try
            Dim str As String = "select * from tbl_Company_mst"
            dt = cc.GetDataTable(str)
            ddlCompnayCode.DataSource = dt

            ddlCompnayCode.DisplayMember = "Company_Code"
            ddlCompnayCode.ValueMember = "Company_Code"
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SelectCompanyName()
        If Load1 = True Then
            Dim str As String = "select * from tbl_Company_mst where company_code = '" & Trim(ddlCompnayCode.SelectedValue) & "'"
            dr = cc.GetDataReader(str)
            Try
                While dr.Read
                    txtCompanyName.Text = dr("Company_Name").ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(ddlYear.Text) = "" Or Trim(txtTransactionParameter.Text) = "" Or Trim(txtSlNo.Text) = "" Then
            MsgBox("Blank Year / Transaction parameter / serial no. not allowed .", vbInformation, "ElectroWay")
            Exit Sub
        Else

            ds = cc.GetDataset("select * from tbl_Trans_Mst where TRAN_NAME = '" & Trim(txtTransactionParameter.Text) & "' and TRAN_YEAR = '" & Trim(ddlYear.Text) & "'")
            If ds.Tables(0).Rows.Count = 0 Then
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_Trans_Mst"
                cm.Parameters.Clear()
                cm.Parameters.AddWithValue("@val_Trans_Name", txtTransactionParameter.Text.Trim)
                cm.Parameters.AddWithValue("@val_Trans_Year", ddlYear.Text.Trim)
                cm.Parameters.AddWithValue("@val_SL_NO", txtSlNo.Text.Trim)
                cm.Parameters.AddWithValue("@val_Plant_Code", ddlPlantCode.Text.Trim)
                cm.Parameters.AddWithValue("@val_Company_Code", ddlCompnayCode.Text.Trim)
                cm.ExecuteNonQuery()
                MsgBox("Transaction master data has been updated sucessfully !", vbInformation, "ElectroWay")
                txtSlNo.Text = ""
            Else
                MsgBox("Transaction master already exixts.", vbInformation, "ElectroWay")
                'txtTransactionParameter.Text = ""
                'txtSlNo.Text = ""
                'txtTransactionParameter.SetFocus

            End If

        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtSlNo.Clear()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub ddlPlantCode_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlPlantCode.SelectedIndexChanged
        txtPlantName.Text = ""
        If Trim(ddlCompnayCode.Text) <> "" And Trim(ddlPlantCode.Text) <> "" Then
            txtTransactionParameter.Text = Trim(ddlCompnayCode.Text) & "\" & Trim(ddlPlantCode.Text) & "\"
        End If

        dr = cc.GetDataReader("select Plant_Name from tbl_plant_mst where Plant_Code  = '" & Trim(ddlPlantCode.Text) & "'")
        Try
            While dr.Read
                txtPlantName.Text = dr("Plant_Name")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
    End Sub
End Class