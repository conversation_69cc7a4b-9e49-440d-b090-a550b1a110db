﻿Public Class frmTransferToPlant
    Dim cc As New Class1
    Private Sub frmTransferToPlant_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        If dr.Read Then
            txtFromPlant.Text = dr("Plant_Name")
            ''Text7.Text = rec1.Fields("Node_No")

        Else
            MsgBox("IP number not mapped as ElectroWay Node ....or You are not connected with ElectroWayServer.")

        End If
        dr.Close()

        dr = cc.GetDataReader("select * from tbl_plant_mst  order by Plant_Code")
        While dr.Read
            ddlToPlant.Items.Add(dr("Plant_Code"))
        End While
        dr.Close()
    End Sub

    Private Sub Combo2_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlToPlant.SelectedIndexChanged
        txtPlantName.Text = ""
        dr = cc.GetDataReader("select * from tbl_plant_mst where Plant_Code = '" & Trim(ddlToPlant.Text) & "'")
        If dr.Read Then
            txtPlantName.Text = dr("Plant_Name")
        End If
        dr.Close()
    End Sub

    Private Sub btnTransfer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnTransfer.Click
        If Trim(txtTransactionNo.Text) <> "" And Trim(ddlToPlant.Text) <> "" Then
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_upd_tbl_GE_HDR_TransferToPlant"
            cm.Parameters.AddWithValue("@val_TransactionNo", Trim(txtTransactionNo.Text) & "")
            cm.Parameters.AddWithValue("@val_TransferToPlant", Trim(ddlToPlant.Text) & "")

            cm.ExecuteNonQuery()

            MsgBox("Vehicle Transfered successfully !", vbInformation, "ElectroWay")
            txtTransactionNo.Text = ""
            txtVehicleNo.Text = ""
            Me.Close()
        Else
            MsgBox("Transaction No and Transfer to Plant cannot be blank !", vbInformation, "ElectroWay")
            txtTransactionNo.Text = ""
            txtVehicleNo.Text = ""

        End If
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub txtTransactionNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransactionNo.KeyDown
        On Error GoTo err
        If e.KeyCode = 112 Then
            Help_callfrom = "TRANSFER_TO_OTHER_PLANT"
            Dim f2 As New frmHelp
            f2.Owner = Me

            f2.gvHelp.DataSource = Nothing
            f2.ShowDialog()
        End If
err:
        Err.Clear()
    End Sub

    Private Sub txtTransactionNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo.KeyPress
        On Error GoTo err

        If AscW(e.KeyChar) = 13 Then
            txtVehicleNo.Text = ""
            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "'") ''''' and Vehicle_Status  = 'OUT'"
            If dr.Read Then
                txtVehicleNo.Text = dr("Vehicle_No")
            Else
                MsgBox("Invalid Transaction No....or Vehicle is still inside the premises", vbInformation, "ElectroWay")
                txtTransactionNo.Text = ""
                txtTransactionNo.Focus()
            End If
            dr.Close()

        End If
err:
        ''MsgBox err.Number
        If Err.Description <> "" Then
            MsgBox(Err.Number & "  " & Err.Description)
        End If
    End Sub
End Class