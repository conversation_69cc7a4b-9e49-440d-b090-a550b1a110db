﻿Public Class frmUnloadingDOUpdation
    Dim Tpe_Of_veh As String

    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    Dim i As Double


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object
    Dim m As Integer
    Dim TRN_ID As Double
    Dim cc As New Class1
    Private Sub frmUnloadingDOUpdation_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ''-----------ListView--------------
        'Dim lvwItem As New ListViewItem()
        'lvwItem.Checked = True
        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False
        ListView1.CheckBoxes = True
        'Headings
        ListView1.Columns.Add("PO / SO No.", 250, HorizontalAlignment.Left)
        ListView1.Columns.Add("DO No.")
        ListView1.Columns.Add("DO/PO Line Item")
        ListView1.Columns.Add("Material Code")
        ListView1.Columns.Add("Material Description")
        ListView1.Columns.Add("DO / Ch. Qty")
        ListView1.Columns.Add("Unit")
        ListView1.Columns.Add("Unloading No.")
        ListView1.Columns.Add("Ch. No.")
        ListView1.Columns.Add("Challan Date")
        ListView1.Columns.Add("Cons. No.")
        ListView1.Columns.Add("Cons. Date")
        ListView1.Columns.Add("WayBill No.")
        ListView1.Columns.Add("Unloading Remarks")
        ListView1.Columns.Add("Gate Pass No.")
        ListView1.Columns.Add("SO Line Item")
        ListView1.Columns.Add("Customer/Vendor")
        ListView1.Columns.Add("Customer/Vendor Name")
        'ListView1.Items.Clear()
        For i As Integer = 0 To ListView1.Columns.Count - 1
            ListView1.Columns(i).Width = -2
        Next
        '-----------ListView2-------------------------
        'lvwItem.Checked = True
        ListView2.Clear()
        ListView2.View = View.Details
        ListView2.GridLines = True
        ListView2.FullRowSelect = True
        ListView2.HideSelection = False
        ListView2.MultiSelect = False
        ListView2.CheckBoxes = True
        'Headings
        ListView2.Columns.Add("Material Code", 250, HorizontalAlignment.Left)
        ListView2.Columns.Add("Material Description")
        ListView2.Columns.Add("F WT")
        ListView2.Columns.Add("S WT")
        ListView2.Columns.Add("Net Wt.")
        For i As Integer = 0 To ListView2.Columns.Count - 1
            ListView2.Columns(i).Width = -2
        Next
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtTransactionNo.Text = ""
        txtVehicleNo.Text = ""
        txtTransporterCode.Text = ""
        txtTransporterName.Text = ""
        ListView1.Items.Clear()
        ListView2.Items.Clear()
        txtTransactionNo.Enabled = True
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        On Error GoTo err

        If ListView1.Items.Count = 1 Then

            Dim ans = MsgBox("Are you sure to update Delivery Order No details ? ", vbYesNo, "ElectroWay")

            If ans = vbYes Then
                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "update tbl_GE_DET set DO_No = '" & Trim(ListView1.Items(1).SubItems(1).Text) & "' , DO_Line_Item = '" & Trim(ListView1.Items(1).SubItems(2).Text) & "' , SO_No ='" & Trim(ListView1.Items(1).Text) & "' , SO_Line_Item= '" & Trim(ListView1.Items(1).SubItems(15).Text) & "' , Mat_Code  = '" & Trim(ListView1.Items(1).SubItems(3).Text) & "' , Mat_Desc = '" & Trim(ListView1.Items(1).SubItems(4).Text) & "' , DO_Challan_QTy ='" & Trim(ListView1.Items(1).SubItems(5).Text) & "' , UOM = '" & Trim(ListView1.Items(1).SubItems(6).Text) & "' , Customer_Code ='" & Trim(ListView1.Items(1).SubItems(16).Text) & "' , Customer_Name ='" & Trim(ListView1.Items(1).SubItems(17).Text) & "' where GE_HDR_ID  ='" & Trim(txtTransactionNo.Text) & "' and GE_DET_TRAN_ID ='" & Trim(ListView2.Items(1).Text) & "'"
                cm.ExecuteNonQuery()

                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "update tbl_GE_HDR set Transpoter_Code = '" & Trim(txtTransporterCode.Text) & "' , TransporterName ='" & Trim(txtTransporterName.Text) & "' where GE_HDR_ID  ='" & Trim(txtTransactionNo.Text) & "'"
                cm.ExecuteNonQuery()

                MsgBox("Delivery Order No details has been updated sucessfully !", vbInformation, "ElectroWay")
                Me.Close()

            End If
        Else
            MsgBox("Blank or Multiple records cannot be updated.", vbInformation, "ElectroWay")
        End If


err:
        If Err.Description <> "" Then
            MsgBox(Err.Description)
        End If
        Err.Clear()
    End Sub

    Private Sub ListView2_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView2.DoubleClick
        On Error GoTo err
        Dim SoldToParty, ShipToParty, CustomerNameee, poText As String
        Dim RFC_READ_TEXT, tblText_Lines

        If ListView2.Items.Count = 1 And Trim(ListView2.Items(0).SubItems(4).Text) = 0 Then

            Dim Delivery_no = InputBox("Please input Delivery order No.", "ElectroWay")

            Call SAP_Con2()

            If sapConnection.Logon(0, True) <> True Then
                MsgBox("No connection to SAP System .......")
                Exit Sub

            End If

            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
            objQueryTab1.Value = "LIKP"
            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
            objFldTab1 = objRfcFunc1.Tables("FIELDS")
            objDatTab1 = objRfcFunc1.Tables("DATA")
            objOptTab1.FreeTable()
            objOptTab1.Rows.Add()
            objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Delivery_no & "'"

            objFldTab1.FreeTable()

            objFldTab1.Rows.Add()
            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
            objFldTab1.Rows.Add()
            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNAG"   ''''
            objFldTab1.Rows.Add()
            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "KUNNR"   ''''KUNNR

            If objRfcFunc1.Call = False Then
                MsgBox(objRfcFunc1.Exception)
            End If

            Dim iii As Integer = 0

            If objDatTab1.Rows.Count = 0 Then
                'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
            Else


                For Each objDatRec1 In objDatTab1.Rows


                    For Each objFldRec1 In objFldTab1.Rows
                        iii = iii + 1

                        If iii = 2 Then
                            SoldToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                        End If

                        If iii = 3 Then

                            ShipToParty = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))

                        End If

                        'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                    Next
                Next
            End If

            ''**********
            '''''''''&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&&

            ''BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB

            Call SAP_Con2()

            If sapConnection.Logon(0, True) <> True Then
                MsgBox("No connection to SAP System .......")
                Exit Sub

            End If

            objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

            objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
            objQueryTab1.Value = "KNA1"
            objOptTab1 = objRfcFunc1.Tables("OPTIONS")
            objFldTab1 = objRfcFunc1.Tables("FIELDS")
            objDatTab1 = objRfcFunc1.Tables("DATA")
            objOptTab1.FreeTable()
            objOptTab1.Rows.Add()
            If Trim(SoldToParty) <> "" Then
                objOptTab1(objOptTab1.RowCount, "TEXT") = "KUNNR = '" & SoldToParty & "'"
            Else
                objOptTab1(objOptTab1.RowCount, "TEXT") = "KUNNR = '" & ShipToParty & "'"
            End If

            objFldTab1.FreeTable()

            objFldTab1.Rows.Add()
            objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "NAME1"   ''''KUNNR

            If objRfcFunc1.Call = False Then
                MsgBox(objRfcFunc1.Exception)
            End If




            If objDatTab1.Rows.Count = 0 Then
                'MsgBox "Sold to Party Not mentioned. !", vbInformation, "ElectroSteel Castings Limited"
            Else


                For Each objDatRec1 In objDatTab1.Rows

                    For Each objFldRec1 In objFldTab1.Rows

                        CustomerNameee = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))

                    Next
                Next
            End If


            ''BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB


            Call SAP_Con1()

            If sapConnection.Logon(0, True) <> True Then
                MsgBox("No connection to SAP System .......")
                Label25.Text = "SAP CONNECTION NOT AVAILABLE."
                Exit Sub

            Else
                'Label25.Text = ""

                RFC_READ_TEXT = functionCtrl.Add("RFC_READ_TEXT") '<------------
                tblText_Lines = RFC_READ_TEXT.Tables("TEXT_LINES")

                tblText_Lines.AppendRow()
                tblText_Lines(1, "TDOBJECT") = "VBBK"
                tblText_Lines(1, "TDNAME") = Delivery_no
                tblText_Lines(1, "TDID") = "ZL01"
                If RFC_READ_TEXT.Call = True Then

                    'MsgBox tblText_Lines.RowCount

                    If tblText_Lines.RowCount > 0 Then

                        For intRow = 1 To tblText_Lines.RowCount ' Change Next line to write a different header row

                            If intRow = 1 Then
                                poText = tblText_Lines(intRow, "TDLINE")
                                'Else
                                'poText = poText & vbCrLf & tblText_Lines(intRow, "TDLINE")
                            End If
                        Next
                    Else
                    End If
                Else
                    MsgBox("ERROR CALLING SAP REMOTE FUNCTION CALL")
                End If

                ''*************************************************************************** start 111
                If poText <> txtVehicleNo.Text Then
                    MsgBox("Invalid Vehicle No. !", vbInformation, "ElectroSteel Castings Limited")
                Else

                    objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")

                    objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
                    objQueryTab.Value = "LIPS"
                    objOptTab = objRfcFunc.Tables("OPTIONS")
                    objFldTab = objRfcFunc.Tables("FIELDS")
                    objDatTab = objRfcFunc.Tables("DATA")
                    objOptTab.FreeTable()
                    objOptTab.Rows.Add()
                    objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '" & Delivery_no & "'"

                    objFldTab.FreeTable()

                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "VGBEL"
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "VBELN"
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "POSNR"
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "MATNR" ''
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "ARKTX" ''
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "LFIMG"  ''
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "MEINS"   ''
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "VGPOS"   ''
                    objFldTab.Rows.Add()
                    objFldTab(objFldTab.RowCount, "FIELDNAME") = "WERKS"   ''


                    If objRfcFunc.Call = False Then
                        MsgBox(objRfcFunc.Exception)
                    End If


                    i = 5

                    If objDatTab.Rows.Count = 0 Then
                        MsgBox("Invalid Vehicle !", vbInformation, "ElectroSteel Castings Limited")
                    Else

                        Dim lvi As New ListViewItem
                        For Each objDatRec In objDatTab.Rows
                            Dim k As Integer = ListView1.Items.Count + 1

                            For Each objFldRec In objFldTab.Rows
                                'm = 1

                                If m = 0 Then
                                    'ListView1.Items.Add(k, , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & "")
                                    lvi.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))
                                Else
                                    If m = 1 And (Tpe_Of_veh = "PURCH" Or Tpe_Of_veh = "PURCHRET" Or Tpe_Of_veh = "INTRDEPT" Or Tpe_Of_veh = "GATEPASS") Then
                                        'ListView1.Items(k).SubItems.Add (m), , ""
                                        lvi.SubItems.Add("")

                                    ElseIf m = 7 Then
                                        For Lv_i = 7 To 14
                                            lvi.SubItems.Add("")
                                        Next Lv_i
                                        lvi.SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                        'ListView1.Items(k).SubItems.Add (15), , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""

                                        If Trim(SoldToParty) = "" Then
                                            lvi.SubItems.Add(ShipToParty)
                                            'ListView1.Items(k).SubItems.Add (16), , ShipToParty & ""      '''''' ShipToParty
                                        Else
                                            lvi.SubItems.Add(SoldToParty)
                                            'ListView1.Items(k).SubItems.Add (16), , SoldToParty & ""
                                        End If
                                        lvi.SubItems.Add(CustomerNameee)
                                        'ListView1.Items(k).SubItems.Add (17), , CustomerNameee & ""   '''''''''' CUSTOMER NAME

                                    ElseIf m = 8 Then

                                        'Text1.Text = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""

                                    Else
                                        lvi.SubItems.Add(Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")))
                                        'ListView1.Items(k).SubItems.Add (m), , Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH")) & ""
                                    End If

                                End If

                                m = m + 1

                            Next

                            If m < 8 Then
                                For Lv_i = 7 To 14
                                    lvi.SubItems.Add("")
                                    'ListView1.Items(k).SubItems.Add (Lv_i), , ""
                                Next Lv_i

                            End If

                            m = 0
                        Next

                        ''*********************************************  end
                    End If
                    '''*************************************** TTTTTTTTTTT
                    ''''''                                                               Set functionCtr2 = CreateObject("SAP.Functions")
                    ''''''                                                               Set sapConnection = functionCtr2.Connection
                    ''''''                                                               sapConnection.User = "goutamb"
                    ''''''                                                               sapConnection.Password = "pizzahut"
                    ''''''                                                               sapConnection.System = "00"
                    ''''''                                                               sapConnection.ApplicationServer = "195.1.117.150"
                    ''''''                                                               sapConnection.SystemNumber = "01"
                    ''''''                                                               sapConnection.Client = "500"
                    ''''''                                                               sapConnection.Language = "EN"
                    ''''''                                                               'sapConnection.CodePage = "8600"

                    Call SAP_Con2()

                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub

                    End If

                    objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                    objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                    objQueryTab1.Value = "VBPA"
                    objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                    objFldTab1 = objRfcFunc1.Tables("FIELDS")
                    objDatTab1 = objRfcFunc1.Tables("DATA")
                    objOptTab1.FreeTable()
                    objOptTab1.Rows.Add()
                    '' T% is for KHARDAH
                    objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Delivery_no & "' and ( PARVW LIKE 'T%' or PARVW LIKE 'F%')"
                    ''objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & Text13.Text & "' and PARVW LIKE 'F%'"

                    objFldTab1.FreeTable()

                    objFldTab1.Rows.Add()
                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "VBELN"
                    objFldTab1.Rows.Add()
                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "PARVW"
                    objFldTab1.Rows.Add()
                    objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "LIFNR"
                    If objRfcFunc1.Call = False Then
                        MsgBox(objRfcFunc1.Exception)
                    End If


                    Dim ii As Integer = 0

                    If objDatTab1.Rows.Count = 0 Then
                        MsgBox("Transporter Not mentioned in DO. !", vbInformation, "ElectroSteel Castings Limited")
                    Else


                        For Each objDatRec1 In objDatTab1.Rows


                            For Each objFldRec1 In objFldTab1.Rows
                                ii = ii + 1

                                If ii = 3 Then
                                    txtTransporterCode.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                End If

                                'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                            Next
                        Next
                    End If

                    ''****************************************TTTTTTTTTTTT

                    '''*************************************** KKKKKKKKKKKKKKKKKK
                    If txtTransporterCode.Text <> "" Then

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            Exit Sub

                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "LFA1"
                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        objOptTab1.FreeTable()
                        objOptTab1.Rows.Add()
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "LIFNR = '" & (txtTransporterCode.Text) & "'"

                        objFldTab1.FreeTable()

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "NAME1"
                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If


                        ii = 0

                        If objDatTab1.Rows.Count = 0 Then
                            MsgBox("Transporter Not mentioned in DO. !", vbInformation, "ElectroSteel Castings Limited")
                        Else


                            For Each objDatRec1 In objDatTab1.Rows


                                For Each objFldRec1 In objFldTab1.Rows
                                    'iii = iii + 1

                                    'If iii = 3 Then
                                    txtTransporterName.Text = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                    'End If

                                    'MsgBox Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                                Next
                            Next
                        End If
                    End If

                    ''****************************************KKKKKKKKKKKKKKK
                End If
            End If
        Else
            MsgBox("After final Weighment or Multiple weighment DO Updation is not permitted.", vbInformation, "ElectroWay")
        End If

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description)
        End If
        Err.Clear()
    End Sub

    Private Sub txtTransactionNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransactionNo.KeyDown
        If e.KeyCode = 112 Then

            Help_callfrom = "DO_UPDATION"
            'Load(frmTransaction)
            Dim f2 As New frmTransaction
            f2.Owner = Me

            f2.gvTransaction.DataSource = Nothing
            ds = cc.GetDataset("select distinct a.GE_HDR_ID , a.Vehicle_NO from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and b.DATAUploadedIn_SAP = ''  and  a.vehicle_status <> 'C' and a.vehicle_status = 'IN'  order by a.Vehicle_no")
            f2.gvTransaction.DataSource = ds.Tables(0)
            f2.ShowDialog()
        End If
    End Sub
    Private Sub txtTransactionNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo.KeyPress
        On Error GoTo err
        If AscW(e.KeyChar) = 13 And Trim(txtTransactionNo.Text) <> "" Then
            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'")
            If dr.Read Then
                txtVehicleNo.Text = dr("Vehicle_no")
                Tpe_Of_veh = dr("Type_Of_Vehicle")
            End If
            dr.Close()

            dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "' and Unloading_No ='' and DO_No =''")
            While dr.Read
                Dim lvi As New ListViewItem
                lvi.Text = dr("Mat_Code")
                lvi.SubItems.Add(dr("Mat_Desc") & "")
                lvi.SubItems.Add(dr("F_WT") & "")
                lvi.SubItems.Add(dr("S_WT") & "")
                lvi.SubItems.Add(dr("NET_WT") & "")
                ListView2.Items.Add(lvi)
            End While
            dr.Close()
            txtTransactionNo.Enabled = False
        End If
err:
        Err.Clear()
    End Sub
    Private Sub SAP_Con1()
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
        sapConnection.CodePage = SAP_CodePage
    End Sub
    Private Sub SAP_Con2()
        functionCtr2 = CreateObject("SAP.Functions")
        sapConnection = functionCtr2.Connection
        sapConnection.User = SAPUsere_ID
        sapConnection.Password = SAPUsere_Pass
        sapConnection.System = SAPSys_name
        sapConnection.ApplicationServer = SAPApp_Server
        sapConnection.SystemNumber = SAPSys_No
        sapConnection.Client = SAP_Client
        sapConnection.Language = SAP_Lang
    End Sub

    Private Sub txtTransactionNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTransactionNo.TextChanged

    End Sub

    Private Sub ListView2_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ListView2.SelectedIndexChanged

    End Sub
End Class