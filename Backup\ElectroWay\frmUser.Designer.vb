﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmUser
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmUser))
        Me.GroupBox1 = New System.Windows.Forms.GroupBox
        Me.Label4 = New System.Windows.Forms.Label
        Me.ddlSAPUser = New System.Windows.Forms.ComboBox
        Me.txtPassword = New System.Windows.Forms.TextBox
        Me.Label3 = New System.Windows.Forms.Label
        Me.txtUserName = New System.Windows.Forms.TextBox
        Me.Label2 = New System.Windows.Forms.Label
        Me.txtUserId = New System.Windows.Forms.TextBox
        Me.Label1 = New System.Windows.Forms.Label
        Me.gbPermisssionMaster = New System.Windows.Forms.GroupBox
        Me.cbVehicleTareWtAuto = New System.Windows.Forms.CheckBox
        Me.CBSCANNEDDOCPATHSETTINGS = New System.Windows.Forms.CheckBox
        Me.CBLOCATIONMASTER = New System.Windows.Forms.CheckBox
        Me.CBGROUPINGREFERENCEMASTER = New System.Windows.Forms.CheckBox
        Me.CBTRANSPORTERMASTER = New System.Windows.Forms.CheckBox
        Me.CBCUSTOMERMASTER = New System.Windows.Forms.CheckBox
        Me.CBVENDORMASTER = New System.Windows.Forms.CheckBox
        Me.CBMATERIALMASTER = New System.Windows.Forms.CheckBox
        Me.CBTRANSACTIONPARAMETER = New System.Windows.Forms.CheckBox
        Me.CBPLANT = New System.Windows.Forms.CheckBox
        Me.CBCOMPANY = New System.Windows.Forms.CheckBox
        Me.CBNODECLIENTCONFIG = New System.Windows.Forms.CheckBox
        Me.cbSAPSERVERCONFIGURATION = New System.Windows.Forms.CheckBox
        Me.cbDriverDetails = New System.Windows.Forms.CheckBox
        Me.cbVehicleTareWtManual = New System.Windows.Forms.CheckBox
        Me.cbImportMasterDatafromSAP = New System.Windows.Forms.CheckBox
        Me.cbUserMaster = New System.Windows.Forms.CheckBox
        Me.GroupBox2 = New System.Windows.Forms.GroupBox
        Me.CBCONTRACTORITEMOUTPASS = New System.Windows.Forms.CheckBox
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND = New System.Windows.Forms.CheckBox
        Me.CBUPDATELINEITEMS = New System.Windows.Forms.CheckBox
        Me.CBVEHICLENOCHANGE = New System.Windows.Forms.CheckBox
        Me.CBVEHICLEACTIVITYCHANGE = New System.Windows.Forms.CheckBox
        Me.CBVEHICLEWTSPLITFORGROUPING = New System.Windows.Forms.CheckBox
        Me.CBCANCELVEHICLEENTRY = New System.Windows.Forms.CheckBox
        Me.******************************** = New System.Windows.Forms.CheckBox
        Me.CBCANCELGROUPINGDATA = New System.Windows.Forms.CheckBox
        Me.CBCHANGEGROUPINGREFERENCE = New System.Windows.Forms.CheckBox
        Me.CBGROUPINGBAISISONREFERENCE = New System.Windows.Forms.CheckBox
        Me.CBWEIGHMENTMANUALMODE = New System.Windows.Forms.CheckBox
        Me.CBWEIGHMENTDATASPLITTINGINBOUND = New System.Windows.Forms.CheckBox
        Me.CBWEIGHMENT = New System.Windows.Forms.CheckBox
        Me.cbgateentry = New System.Windows.Forms.CheckBox
        Me.GroupBox3 = New System.Windows.Forms.GroupBox
        Me.CBDETAILSREPORT = New System.Windows.Forms.CheckBox
        Me.CBMATERIALWISE = New System.Windows.Forms.CheckBox
        Me.CBSOWISE = New System.Windows.Forms.CheckBox
        Me.CBPOWISE = New System.Windows.Forms.CheckBox
        Me.CBCUSTOMERWISE = New System.Windows.Forms.CheckBox
        Me.CBVENDORWISE = New System.Windows.Forms.CheckBox
        Me.CBWEIGHMENTSLIPDIRECT = New System.Windows.Forms.CheckBox
        Me.CBDETAILSEXPORTINEXCEL = New System.Windows.Forms.CheckBox
        Me.CBWEIGHMENTSLIP = New System.Windows.Forms.CheckBox
        Me.cbgateEntrySlip = New System.Windows.Forms.CheckBox
        Me.GroupBox4 = New System.Windows.Forms.GroupBox
        Me.btnExit = New System.Windows.Forms.Button
        Me.btnUpdate = New System.Windows.Forms.Button
        Me.btnCancel = New System.Windows.Forms.Button
        Me.GroupBox5 = New System.Windows.Forms.GroupBox
        Me.gvUser = New System.Windows.Forms.DataGridView
        Me.GroupBox1.SuspendLayout()
        Me.gbPermisssionMaster.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.GroupBox4.SuspendLayout()
        Me.GroupBox5.SuspendLayout()
        CType(Me.gvUser, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.Label4)
        Me.GroupBox1.Controls.Add(Me.ddlSAPUser)
        Me.GroupBox1.Controls.Add(Me.txtPassword)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.txtUserName)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.txtUserId)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Location = New System.Drawing.Point(13, 13)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(861, 68)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(481, 16)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(53, 13)
        Me.Label4.TabIndex = 7
        Me.Label4.Text = "SAP User"
        '
        'ddlSAPUser
        '
        Me.ddlSAPUser.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.ddlSAPUser.FormattingEnabled = True
        Me.ddlSAPUser.Location = New System.Drawing.Point(472, 34)
        Me.ddlSAPUser.Name = "ddlSAPUser"
        Me.ddlSAPUser.Size = New System.Drawing.Size(153, 21)
        Me.ddlSAPUser.TabIndex = 6
        '
        'txtPassword
        '
        Me.txtPassword.Location = New System.Drawing.Point(357, 34)
        Me.txtPassword.Name = "txtPassword"
        Me.txtPassword.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.txtPassword.Size = New System.Drawing.Size(100, 20)
        Me.txtPassword.TabIndex = 5
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(355, 16)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(53, 13)
        Me.Label3.TabIndex = 4
        Me.Label3.Text = "Password"
        '
        'txtUserName
        '
        Me.txtUserName.Location = New System.Drawing.Point(130, 34)
        Me.txtUserName.Name = "txtUserName"
        Me.txtUserName.Size = New System.Drawing.Size(211, 20)
        Me.txtUserName.TabIndex = 3
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(129, 16)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(60, 13)
        Me.Label2.TabIndex = 2
        Me.Label2.Text = "User Name"
        '
        'txtUserId
        '
        Me.txtUserId.Location = New System.Drawing.Point(14, 34)
        Me.txtUserId.Name = "txtUserId"
        Me.txtUserId.Size = New System.Drawing.Size(100, 20)
        Me.txtUserId.TabIndex = 1
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(12, 16)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(41, 13)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "User Id"
        '
        'gbPermisssionMaster
        '
        Me.gbPermisssionMaster.Controls.Add(Me.cbVehicleTareWtAuto)
        Me.gbPermisssionMaster.Controls.Add(Me.CBSCANNEDDOCPATHSETTINGS)
        Me.gbPermisssionMaster.Controls.Add(Me.CBLOCATIONMASTER)
        Me.gbPermisssionMaster.Controls.Add(Me.CBGROUPINGREFERENCEMASTER)
        Me.gbPermisssionMaster.Controls.Add(Me.CBTRANSPORTERMASTER)
        Me.gbPermisssionMaster.Controls.Add(Me.CBCUSTOMERMASTER)
        Me.gbPermisssionMaster.Controls.Add(Me.CBVENDORMASTER)
        Me.gbPermisssionMaster.Controls.Add(Me.CBMATERIALMASTER)
        Me.gbPermisssionMaster.Controls.Add(Me.CBTRANSACTIONPARAMETER)
        Me.gbPermisssionMaster.Controls.Add(Me.CBPLANT)
        Me.gbPermisssionMaster.Controls.Add(Me.CBCOMPANY)
        Me.gbPermisssionMaster.Controls.Add(Me.CBNODECLIENTCONFIG)
        Me.gbPermisssionMaster.Controls.Add(Me.cbSAPSERVERCONFIGURATION)
        Me.gbPermisssionMaster.Controls.Add(Me.cbDriverDetails)
        Me.gbPermisssionMaster.Controls.Add(Me.cbVehicleTareWtManual)
        Me.gbPermisssionMaster.Controls.Add(Me.cbImportMasterDatafromSAP)
        Me.gbPermisssionMaster.Controls.Add(Me.cbUserMaster)
        Me.gbPermisssionMaster.Font = New System.Drawing.Font("Times New Roman", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.gbPermisssionMaster.ForeColor = System.Drawing.Color.Blue
        Me.gbPermisssionMaster.Location = New System.Drawing.Point(13, 88)
        Me.gbPermisssionMaster.Name = "gbPermisssionMaster"
        Me.gbPermisssionMaster.Size = New System.Drawing.Size(239, 417)
        Me.gbPermisssionMaster.TabIndex = 1
        Me.gbPermisssionMaster.TabStop = False
        Me.gbPermisssionMaster.Text = "User Permisssion Master"
        '
        'cbVehicleTareWtAuto
        '
        Me.cbVehicleTareWtAuto.AutoSize = True
        Me.cbVehicleTareWtAuto.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cbVehicleTareWtAuto.ForeColor = System.Drawing.SystemColors.ControlText
        Me.cbVehicleTareWtAuto.Location = New System.Drawing.Point(15, 389)
        Me.cbVehicleTareWtAuto.Name = "cbVehicleTareWtAuto"
        Me.cbVehicleTareWtAuto.Size = New System.Drawing.Size(189, 17)
        Me.cbVehicleTareWtAuto.TabIndex = 16
        Me.cbVehicleTareWtAuto.Text = "VEHICLE TARE WEIGHT (AUTO)"
        Me.cbVehicleTareWtAuto.UseVisualStyleBackColor = True
        '
        'CBSCANNEDDOCPATHSETTINGS
        '
        Me.CBSCANNEDDOCPATHSETTINGS.AutoSize = True
        Me.CBSCANNEDDOCPATHSETTINGS.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBSCANNEDDOCPATHSETTINGS.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBSCANNEDDOCPATHSETTINGS.Location = New System.Drawing.Point(15, 366)
        Me.CBSCANNEDDOCPATHSETTINGS.Name = "CBSCANNEDDOCPATHSETTINGS"
        Me.CBSCANNEDDOCPATHSETTINGS.Size = New System.Drawing.Size(193, 17)
        Me.CBSCANNEDDOCPATHSETTINGS.TabIndex = 15
        Me.CBSCANNEDDOCPATHSETTINGS.Text = "SCANNED DOC PATH SETTINGS"
        Me.CBSCANNEDDOCPATHSETTINGS.UseVisualStyleBackColor = True
        '
        'CBLOCATIONMASTER
        '
        Me.CBLOCATIONMASTER.AutoSize = True
        Me.CBLOCATIONMASTER.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBLOCATIONMASTER.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBLOCATIONMASTER.Location = New System.Drawing.Point(15, 343)
        Me.CBLOCATIONMASTER.Name = "CBLOCATIONMASTER"
        Me.CBLOCATIONMASTER.Size = New System.Drawing.Size(128, 17)
        Me.CBLOCATIONMASTER.TabIndex = 14
        Me.CBLOCATIONMASTER.Text = "LOCATION MASTER"
        Me.CBLOCATIONMASTER.UseVisualStyleBackColor = True
        '
        'CBGROUPINGREFERENCEMASTER
        '
        Me.CBGROUPINGREFERENCEMASTER.AutoSize = True
        Me.CBGROUPINGREFERENCEMASTER.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBGROUPINGREFERENCEMASTER.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBGROUPINGREFERENCEMASTER.Location = New System.Drawing.Point(15, 320)
        Me.CBGROUPINGREFERENCEMASTER.Name = "CBGROUPINGREFERENCEMASTER"
        Me.CBGROUPINGREFERENCEMASTER.Size = New System.Drawing.Size(200, 17)
        Me.CBGROUPINGREFERENCEMASTER.TabIndex = 13
        Me.CBGROUPINGREFERENCEMASTER.Text = "GROUPING REFERENCE MASTER"
        Me.CBGROUPINGREFERENCEMASTER.UseVisualStyleBackColor = True
        '
        'CBTRANSPORTERMASTER
        '
        Me.CBTRANSPORTERMASTER.AutoSize = True
        Me.CBTRANSPORTERMASTER.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBTRANSPORTERMASTER.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBTRANSPORTERMASTER.Location = New System.Drawing.Point(15, 297)
        Me.CBTRANSPORTERMASTER.Name = "CBTRANSPORTERMASTER"
        Me.CBTRANSPORTERMASTER.Size = New System.Drawing.Size(156, 17)
        Me.CBTRANSPORTERMASTER.TabIndex = 12
        Me.CBTRANSPORTERMASTER.Text = "TRANSPORTER MASTER"
        Me.CBTRANSPORTERMASTER.UseVisualStyleBackColor = True
        '
        'CBCUSTOMERMASTER
        '
        Me.CBCUSTOMERMASTER.AutoSize = True
        Me.CBCUSTOMERMASTER.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBCUSTOMERMASTER.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBCUSTOMERMASTER.Location = New System.Drawing.Point(15, 274)
        Me.CBCUSTOMERMASTER.Name = "CBCUSTOMERMASTER"
        Me.CBCUSTOMERMASTER.Size = New System.Drawing.Size(135, 17)
        Me.CBCUSTOMERMASTER.TabIndex = 11
        Me.CBCUSTOMERMASTER.Text = "CUSTOMER MASTER"
        Me.CBCUSTOMERMASTER.UseVisualStyleBackColor = True
        '
        'CBVENDORMASTER
        '
        Me.CBVENDORMASTER.AutoSize = True
        Me.CBVENDORMASTER.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBVENDORMASTER.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBVENDORMASTER.Location = New System.Drawing.Point(15, 251)
        Me.CBVENDORMASTER.Name = "CBVENDORMASTER"
        Me.CBVENDORMASTER.Size = New System.Drawing.Size(120, 17)
        Me.CBVENDORMASTER.TabIndex = 10
        Me.CBVENDORMASTER.Text = "VENDOR MASTER"
        Me.CBVENDORMASTER.UseVisualStyleBackColor = True
        '
        'CBMATERIALMASTER
        '
        Me.CBMATERIALMASTER.AutoSize = True
        Me.CBMATERIALMASTER.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBMATERIALMASTER.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBMATERIALMASTER.Location = New System.Drawing.Point(15, 228)
        Me.CBMATERIALMASTER.Name = "CBMATERIALMASTER"
        Me.CBMATERIALMASTER.Size = New System.Drawing.Size(128, 17)
        Me.CBMATERIALMASTER.TabIndex = 9
        Me.CBMATERIALMASTER.Text = "MATERIAL MASTER"
        Me.CBMATERIALMASTER.UseVisualStyleBackColor = True
        '
        'CBTRANSACTIONPARAMETER
        '
        Me.CBTRANSACTIONPARAMETER.AutoSize = True
        Me.CBTRANSACTIONPARAMETER.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBTRANSACTIONPARAMETER.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBTRANSACTIONPARAMETER.Location = New System.Drawing.Point(15, 205)
        Me.CBTRANSACTIONPARAMETER.Name = "CBTRANSACTIONPARAMETER"
        Me.CBTRANSACTIONPARAMETER.Size = New System.Drawing.Size(173, 17)
        Me.CBTRANSACTIONPARAMETER.TabIndex = 8
        Me.CBTRANSACTIONPARAMETER.Text = "TRANSACTION PARAMETER"
        Me.CBTRANSACTIONPARAMETER.UseVisualStyleBackColor = True
        '
        'CBPLANT
        '
        Me.CBPLANT.AutoSize = True
        Me.CBPLANT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBPLANT.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBPLANT.Location = New System.Drawing.Point(15, 182)
        Me.CBPLANT.Name = "CBPLANT"
        Me.CBPLANT.Size = New System.Drawing.Size(61, 17)
        Me.CBPLANT.TabIndex = 7
        Me.CBPLANT.Text = "PLANT"
        Me.CBPLANT.UseVisualStyleBackColor = True
        '
        'CBCOMPANY
        '
        Me.CBCOMPANY.AutoSize = True
        Me.CBCOMPANY.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBCOMPANY.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBCOMPANY.Location = New System.Drawing.Point(15, 159)
        Me.CBCOMPANY.Name = "CBCOMPANY"
        Me.CBCOMPANY.Size = New System.Drawing.Size(79, 17)
        Me.CBCOMPANY.TabIndex = 6
        Me.CBCOMPANY.Text = "COMPANY"
        Me.CBCOMPANY.UseVisualStyleBackColor = True
        '
        'CBNODECLIENTCONFIG
        '
        Me.CBNODECLIENTCONFIG.AutoSize = True
        Me.CBNODECLIENTCONFIG.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBNODECLIENTCONFIG.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBNODECLIENTCONFIG.Location = New System.Drawing.Point(15, 136)
        Me.CBNODECLIENTCONFIG.Name = "CBNODECLIENTCONFIG"
        Me.CBNODECLIENTCONFIG.Size = New System.Drawing.Size(190, 17)
        Me.CBNODECLIENTCONFIG.TabIndex = 5
        Me.CBNODECLIENTCONFIG.Text = "NODE CLIENT CONFIGURATION"
        Me.CBNODECLIENTCONFIG.UseVisualStyleBackColor = True
        '
        'cbSAPSERVERCONFIGURATION
        '
        Me.cbSAPSERVERCONFIGURATION.AutoSize = True
        Me.cbSAPSERVERCONFIGURATION.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cbSAPSERVERCONFIGURATION.ForeColor = System.Drawing.SystemColors.ControlText
        Me.cbSAPSERVERCONFIGURATION.Location = New System.Drawing.Point(15, 113)
        Me.cbSAPSERVERCONFIGURATION.Name = "cbSAPSERVERCONFIGURATION"
        Me.cbSAPSERVERCONFIGURATION.Size = New System.Drawing.Size(186, 17)
        Me.cbSAPSERVERCONFIGURATION.TabIndex = 4
        Me.cbSAPSERVERCONFIGURATION.Text = "SAP SERVER CONFIGURATION"
        Me.cbSAPSERVERCONFIGURATION.UseVisualStyleBackColor = True
        '
        'cbDriverDetails
        '
        Me.cbDriverDetails.AutoSize = True
        Me.cbDriverDetails.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cbDriverDetails.ForeColor = System.Drawing.SystemColors.ControlText
        Me.cbDriverDetails.Location = New System.Drawing.Point(15, 90)
        Me.cbDriverDetails.Name = "cbDriverDetails"
        Me.cbDriverDetails.Size = New System.Drawing.Size(115, 17)
        Me.cbDriverDetails.TabIndex = 3
        Me.cbDriverDetails.Text = "DRIVER DETAILS"
        Me.cbDriverDetails.UseVisualStyleBackColor = True
        '
        'cbVehicleTareWtManual
        '
        Me.cbVehicleTareWtManual.AutoSize = True
        Me.cbVehicleTareWtManual.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cbVehicleTareWtManual.ForeColor = System.Drawing.SystemColors.ControlText
        Me.cbVehicleTareWtManual.Location = New System.Drawing.Point(15, 67)
        Me.cbVehicleTareWtManual.Name = "cbVehicleTareWtManual"
        Me.cbVehicleTareWtManual.Size = New System.Drawing.Size(204, 17)
        Me.cbVehicleTareWtManual.TabIndex = 2
        Me.cbVehicleTareWtManual.Text = "VEHICLE TARE WEIGHT (MANUAL)"
        Me.cbVehicleTareWtManual.UseVisualStyleBackColor = True
        '
        'cbImportMasterDatafromSAP
        '
        Me.cbImportMasterDatafromSAP.AutoSize = True
        Me.cbImportMasterDatafromSAP.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cbImportMasterDatafromSAP.ForeColor = System.Drawing.SystemColors.ControlText
        Me.cbImportMasterDatafromSAP.Location = New System.Drawing.Point(15, 44)
        Me.cbImportMasterDatafromSAP.Name = "cbImportMasterDatafromSAP"
        Me.cbImportMasterDatafromSAP.Size = New System.Drawing.Size(206, 17)
        Me.cbImportMasterDatafromSAP.TabIndex = 1
        Me.cbImportMasterDatafromSAP.Text = "IMPORT MASTER DATA FROM SAP"
        Me.cbImportMasterDatafromSAP.UseVisualStyleBackColor = True
        '
        'cbUserMaster
        '
        Me.cbUserMaster.AutoSize = True
        Me.cbUserMaster.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cbUserMaster.ForeColor = System.Drawing.SystemColors.ControlText
        Me.cbUserMaster.Location = New System.Drawing.Point(15, 21)
        Me.cbUserMaster.Name = "cbUserMaster"
        Me.cbUserMaster.Size = New System.Drawing.Size(104, 17)
        Me.cbUserMaster.TabIndex = 0
        Me.cbUserMaster.Text = "USER MASTER"
        Me.cbUserMaster.UseVisualStyleBackColor = True
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.CBCONTRACTORITEMOUTPASS)
        Me.GroupBox2.Controls.Add(Me.CBWEIGHMENTDATASPLITTINGOUTBOUND)
        Me.GroupBox2.Controls.Add(Me.CBUPDATELINEITEMS)
        Me.GroupBox2.Controls.Add(Me.CBVEHICLENOCHANGE)
        Me.GroupBox2.Controls.Add(Me.CBVEHICLEACTIVITYCHANGE)
        Me.GroupBox2.Controls.Add(Me.CBVEHICLEWTSPLITFORGROUPING)
        Me.GroupBox2.Controls.Add(Me.CBCANCELVEHICLEENTRY)
        Me.GroupBox2.Controls.Add(Me.********************************)
        Me.GroupBox2.Controls.Add(Me.CBCANCELGROUPINGDATA)
        Me.GroupBox2.Controls.Add(Me.CBCHANGEGROUPINGREFERENCE)
        Me.GroupBox2.Controls.Add(Me.CBGROUPINGBAISISONREFERENCE)
        Me.GroupBox2.Controls.Add(Me.CBWEIGHMENTMANUALMODE)
        Me.GroupBox2.Controls.Add(Me.CBWEIGHMENTDATASPLITTINGINBOUND)
        Me.GroupBox2.Controls.Add(Me.CBWEIGHMENT)
        Me.GroupBox2.Controls.Add(Me.cbgateentry)
        Me.GroupBox2.Font = New System.Drawing.Font("Times New Roman", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox2.ForeColor = System.Drawing.Color.Blue
        Me.GroupBox2.Location = New System.Drawing.Point(252, 88)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(266, 417)
        Me.GroupBox2.TabIndex = 2
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "User Permisssion Transaction"
        '
        'CBCONTRACTORITEMOUTPASS
        '
        Me.CBCONTRACTORITEMOUTPASS.AutoSize = True
        Me.CBCONTRACTORITEMOUTPASS.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBCONTRACTORITEMOUTPASS.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBCONTRACTORITEMOUTPASS.Location = New System.Drawing.Point(15, 343)
        Me.CBCONTRACTORITEMOUTPASS.Name = "CBCONTRACTORITEMOUTPASS"
        Me.CBCONTRACTORITEMOUTPASS.Size = New System.Drawing.Size(184, 17)
        Me.CBCONTRACTORITEMOUTPASS.TabIndex = 14
        Me.CBCONTRACTORITEMOUTPASS.Text = "CONTRACTOR ITEM OUTPASS"
        Me.CBCONTRACTORITEMOUTPASS.UseVisualStyleBackColor = True
        '
        'CBWEIGHMENTDATASPLITTINGOUTBOUND
        '
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.AutoSize = True
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.Location = New System.Drawing.Point(15, 320)
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.Name = "CBWEIGHMENTDATASPLITTINGOUTBOUND"
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.Size = New System.Drawing.Size(250, 17)
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.TabIndex = 13
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.Text = "WEIGHMENT DATA SPLITTING OUTBOUND"
        Me.CBWEIGHMENTDATASPLITTINGOUTBOUND.UseVisualStyleBackColor = True
        '
        'CBUPDATELINEITEMS
        '
        Me.CBUPDATELINEITEMS.AutoSize = True
        Me.CBUPDATELINEITEMS.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBUPDATELINEITEMS.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBUPDATELINEITEMS.Location = New System.Drawing.Point(15, 297)
        Me.CBUPDATELINEITEMS.Name = "CBUPDATELINEITEMS"
        Me.CBUPDATELINEITEMS.Size = New System.Drawing.Size(133, 17)
        Me.CBUPDATELINEITEMS.TabIndex = 12
        Me.CBUPDATELINEITEMS.Text = "UPDATE LINE ITEMS"
        Me.CBUPDATELINEITEMS.UseVisualStyleBackColor = True
        '
        'CBVEHICLENOCHANGE
        '
        Me.CBVEHICLENOCHANGE.AutoSize = True
        Me.CBVEHICLENOCHANGE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBVEHICLENOCHANGE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBVEHICLENOCHANGE.Location = New System.Drawing.Point(15, 274)
        Me.CBVEHICLENOCHANGE.Name = "CBVEHICLENOCHANGE"
        Me.CBVEHICLENOCHANGE.Size = New System.Drawing.Size(169, 17)
        Me.CBVEHICLENOCHANGE.TabIndex = 11
        Me.CBVEHICLENOCHANGE.Text = "VEHICLE NUMBER CHANGE"
        Me.CBVEHICLENOCHANGE.UseVisualStyleBackColor = True
        '
        'CBVEHICLEACTIVITYCHANGE
        '
        Me.CBVEHICLEACTIVITYCHANGE.AutoSize = True
        Me.CBVEHICLEACTIVITYCHANGE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBVEHICLEACTIVITYCHANGE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBVEHICLEACTIVITYCHANGE.Location = New System.Drawing.Point(15, 251)
        Me.CBVEHICLEACTIVITYCHANGE.Name = "CBVEHICLEACTIVITYCHANGE"
        Me.CBVEHICLEACTIVITYCHANGE.Size = New System.Drawing.Size(170, 17)
        Me.CBVEHICLEACTIVITYCHANGE.TabIndex = 10
        Me.CBVEHICLEACTIVITYCHANGE.Text = "VEHICLE ACTIVITY CHANGE"
        Me.CBVEHICLEACTIVITYCHANGE.UseVisualStyleBackColor = True
        '
        'CBVEHICLEWTSPLITFORGROUPING
        '
        Me.CBVEHICLEWTSPLITFORGROUPING.AutoSize = True
        Me.CBVEHICLEWTSPLITFORGROUPING.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBVEHICLEWTSPLITFORGROUPING.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBVEHICLEWTSPLITFORGROUPING.Location = New System.Drawing.Point(15, 228)
        Me.CBVEHICLEWTSPLITFORGROUPING.Name = "CBVEHICLEWTSPLITFORGROUPING"
        Me.CBVEHICLEWTSPLITFORGROUPING.Size = New System.Drawing.Size(211, 17)
        Me.CBVEHICLEWTSPLITFORGROUPING.TabIndex = 9
        Me.CBVEHICLEWTSPLITFORGROUPING.Text = "VEHICLE WT SPLIT FOR GROUPING"
        Me.CBVEHICLEWTSPLITFORGROUPING.UseVisualStyleBackColor = True
        '
        'CBCANCELVEHICLEENTRY
        '
        Me.CBCANCELVEHICLEENTRY.AutoSize = True
        Me.CBCANCELVEHICLEENTRY.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBCANCELVEHICLEENTRY.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBCANCELVEHICLEENTRY.Location = New System.Drawing.Point(15, 205)
        Me.CBCANCELVEHICLEENTRY.Name = "CBCANCELVEHICLEENTRY"
        Me.CBCANCELVEHICLEENTRY.Size = New System.Drawing.Size(156, 17)
        Me.CBCANCELVEHICLEENTRY.TabIndex = 8
        Me.CBCANCELVEHICLEENTRY.Text = "CANCEL VEHICLE ENTRY"
        Me.CBCANCELVEHICLEENTRY.UseVisualStyleBackColor = True
        '
        '********************************
        '
        Me.********************************.AutoSize = True
        Me.********************************.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.********************************.ForeColor = System.Drawing.SystemColors.ControlText
        Me.********************************.Location = New System.Drawing.Point(15, 182)
        Me.********************************.Name = "********************************"
        Me.********************************.Size = New System.Drawing.Size(248, 17)
        Me.********************************.TabIndex = 7
        Me.********************************.Text = "VEHICLE TRANSFER TO OTHER LOCATION"
        Me.********************************.UseVisualStyleBackColor = True
        '
        'CBCANCELGROUPINGDATA
        '
        Me.CBCANCELGROUPINGDATA.AutoSize = True
        Me.CBCANCELGROUPINGDATA.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBCANCELGROUPINGDATA.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBCANCELGROUPINGDATA.Location = New System.Drawing.Point(15, 159)
        Me.CBCANCELGROUPINGDATA.Name = "CBCANCELGROUPINGDATA"
        Me.CBCANCELGROUPINGDATA.Size = New System.Drawing.Size(161, 17)
        Me.CBCANCELGROUPINGDATA.TabIndex = 6
        Me.CBCANCELGROUPINGDATA.Text = "CANCEL GROUPING DATA"
        Me.CBCANCELGROUPINGDATA.UseVisualStyleBackColor = True
        '
        'CBCHANGEGROUPINGREFERENCE
        '
        Me.CBCHANGEGROUPINGREFERENCE.AutoSize = True
        Me.CBCHANGEGROUPINGREFERENCE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBCHANGEGROUPINGREFERENCE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBCHANGEGROUPINGREFERENCE.Location = New System.Drawing.Point(15, 136)
        Me.CBCHANGEGROUPINGREFERENCE.Name = "CBCHANGEGROUPINGREFERENCE"
        Me.CBCHANGEGROUPINGREFERENCE.Size = New System.Drawing.Size(200, 17)
        Me.CBCHANGEGROUPINGREFERENCE.TabIndex = 5
        Me.CBCHANGEGROUPINGREFERENCE.Text = "CHANGE GROUPING REFERENCE"
        Me.CBCHANGEGROUPINGREFERENCE.UseVisualStyleBackColor = True
        '
        'CBGROUPINGBAISISONREFERENCE
        '
        Me.CBGROUPINGBAISISONREFERENCE.AutoSize = True
        Me.CBGROUPINGBAISISONREFERENCE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBGROUPINGBAISISONREFERENCE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBGROUPINGBAISISONREFERENCE.Location = New System.Drawing.Point(15, 113)
        Me.CBGROUPINGBAISISONREFERENCE.Name = "CBGROUPINGBAISISONREFERENCE"
        Me.CBGROUPINGBAISISONREFERENCE.Size = New System.Drawing.Size(205, 17)
        Me.CBGROUPINGBAISISONREFERENCE.TabIndex = 4
        Me.CBGROUPINGBAISISONREFERENCE.Text = "GROUPNIG BASIS ON REFERENCE"
        Me.CBGROUPINGBAISISONREFERENCE.UseVisualStyleBackColor = True
        '
        'CBWEIGHMENTMANUALMODE
        '
        Me.CBWEIGHMENTMANUALMODE.AutoSize = True
        Me.CBWEIGHMENTMANUALMODE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBWEIGHMENTMANUALMODE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBWEIGHMENTMANUALMODE.Location = New System.Drawing.Point(15, 90)
        Me.CBWEIGHMENTMANUALMODE.Name = "CBWEIGHMENTMANUALMODE"
        Me.CBWEIGHMENTMANUALMODE.Size = New System.Drawing.Size(177, 17)
        Me.CBWEIGHMENTMANUALMODE.TabIndex = 3
        Me.CBWEIGHMENTMANUALMODE.Text = "WEIGHMENT MANUAL MODE"
        Me.CBWEIGHMENTMANUALMODE.UseVisualStyleBackColor = True
        '
        'CBWEIGHMENTDATASPLITTINGINBOUND
        '
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.AutoSize = True
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.Location = New System.Drawing.Point(15, 67)
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.Name = "CBWEIGHMENTDATASPLITTINGINBOUND"
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.Size = New System.Drawing.Size(238, 17)
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.TabIndex = 2
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.Text = "WEIGHMENT DATA SPLITTING INBOUND"
        Me.CBWEIGHMENTDATASPLITTINGINBOUND.UseVisualStyleBackColor = True
        '
        'CBWEIGHMENT
        '
        Me.CBWEIGHMENT.AutoSize = True
        Me.CBWEIGHMENT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBWEIGHMENT.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBWEIGHMENT.Location = New System.Drawing.Point(15, 44)
        Me.CBWEIGHMENT.Name = "CBWEIGHMENT"
        Me.CBWEIGHMENT.Size = New System.Drawing.Size(94, 17)
        Me.CBWEIGHMENT.TabIndex = 1
        Me.CBWEIGHMENT.Text = "WEIGHMENT"
        Me.CBWEIGHMENT.UseVisualStyleBackColor = True
        '
        'cbgateentry
        '
        Me.cbgateentry.AutoSize = True
        Me.cbgateentry.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cbgateentry.ForeColor = System.Drawing.SystemColors.ControlText
        Me.cbgateentry.Location = New System.Drawing.Point(15, 21)
        Me.cbgateentry.Name = "cbgateentry"
        Me.cbgateentry.Size = New System.Drawing.Size(95, 17)
        Me.cbgateentry.TabIndex = 0
        Me.cbgateentry.Text = "GATE ENTRY"
        Me.cbgateentry.UseVisualStyleBackColor = True
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.CBDETAILSREPORT)
        Me.GroupBox3.Controls.Add(Me.CBMATERIALWISE)
        Me.GroupBox3.Controls.Add(Me.CBSOWISE)
        Me.GroupBox3.Controls.Add(Me.CBPOWISE)
        Me.GroupBox3.Controls.Add(Me.CBCUSTOMERWISE)
        Me.GroupBox3.Controls.Add(Me.CBVENDORWISE)
        Me.GroupBox3.Controls.Add(Me.CBWEIGHMENTSLIPDIRECT)
        Me.GroupBox3.Controls.Add(Me.CBDETAILSEXPORTINEXCEL)
        Me.GroupBox3.Controls.Add(Me.CBWEIGHMENTSLIP)
        Me.GroupBox3.Controls.Add(Me.cbgateEntrySlip)
        Me.GroupBox3.Font = New System.Drawing.Font("Times New Roman", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox3.ForeColor = System.Drawing.Color.Blue
        Me.GroupBox3.Location = New System.Drawing.Point(518, 88)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(239, 417)
        Me.GroupBox3.TabIndex = 3
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "User Permisssion Reports"
        '
        'CBDETAILSREPORT
        '
        Me.CBDETAILSREPORT.AutoSize = True
        Me.CBDETAILSREPORT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBDETAILSREPORT.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBDETAILSREPORT.Location = New System.Drawing.Point(15, 228)
        Me.CBDETAILSREPORT.Name = "CBDETAILSREPORT"
        Me.CBDETAILSREPORT.Size = New System.Drawing.Size(119, 17)
        Me.CBDETAILSREPORT.TabIndex = 9
        Me.CBDETAILSREPORT.Text = "DETAILS REPORT"
        Me.CBDETAILSREPORT.UseVisualStyleBackColor = True
        '
        'CBMATERIALWISE
        '
        Me.CBMATERIALWISE.AutoSize = True
        Me.CBMATERIALWISE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBMATERIALWISE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBMATERIALWISE.Location = New System.Drawing.Point(15, 205)
        Me.CBMATERIALWISE.Name = "CBMATERIALWISE"
        Me.CBMATERIALWISE.Size = New System.Drawing.Size(111, 17)
        Me.CBMATERIALWISE.TabIndex = 8
        Me.CBMATERIALWISE.Text = "MATERIAL WISE"
        Me.CBMATERIALWISE.UseVisualStyleBackColor = True
        '
        'CBSOWISE
        '
        Me.CBSOWISE.AutoSize = True
        Me.CBSOWISE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBSOWISE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBSOWISE.Location = New System.Drawing.Point(15, 182)
        Me.CBSOWISE.Name = "CBSOWISE"
        Me.CBSOWISE.Size = New System.Drawing.Size(78, 17)
        Me.CBSOWISE.TabIndex = 7
        Me.CBSOWISE.Text = "S.O. WISE"
        Me.CBSOWISE.UseVisualStyleBackColor = True
        '
        'CBPOWISE
        '
        Me.CBPOWISE.AutoSize = True
        Me.CBPOWISE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBPOWISE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBPOWISE.Location = New System.Drawing.Point(15, 159)
        Me.CBPOWISE.Name = "CBPOWISE"
        Me.CBPOWISE.Size = New System.Drawing.Size(78, 17)
        Me.CBPOWISE.TabIndex = 6
        Me.CBPOWISE.Text = "P.O. WISE"
        Me.CBPOWISE.UseVisualStyleBackColor = True
        '
        'CBCUSTOMERWISE
        '
        Me.CBCUSTOMERWISE.AutoSize = True
        Me.CBCUSTOMERWISE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBCUSTOMERWISE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBCUSTOMERWISE.Location = New System.Drawing.Point(15, 136)
        Me.CBCUSTOMERWISE.Name = "CBCUSTOMERWISE"
        Me.CBCUSTOMERWISE.Size = New System.Drawing.Size(118, 17)
        Me.CBCUSTOMERWISE.TabIndex = 5
        Me.CBCUSTOMERWISE.Text = "CUSTOMER WISE"
        Me.CBCUSTOMERWISE.UseVisualStyleBackColor = True
        '
        'CBVENDORWISE
        '
        Me.CBVENDORWISE.AutoSize = True
        Me.CBVENDORWISE.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBVENDORWISE.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBVENDORWISE.Location = New System.Drawing.Point(15, 113)
        Me.CBVENDORWISE.Name = "CBVENDORWISE"
        Me.CBVENDORWISE.Size = New System.Drawing.Size(103, 17)
        Me.CBVENDORWISE.TabIndex = 4
        Me.CBVENDORWISE.Text = "VENDOR WISE"
        Me.CBVENDORWISE.UseVisualStyleBackColor = True
        '
        'CBWEIGHMENTSLIPDIRECT
        '
        Me.CBWEIGHMENTSLIPDIRECT.AutoSize = True
        Me.CBWEIGHMENTSLIPDIRECT.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBWEIGHMENTSLIPDIRECT.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBWEIGHMENTSLIPDIRECT.Location = New System.Drawing.Point(15, 90)
        Me.CBWEIGHMENTSLIPDIRECT.Name = "CBWEIGHMENTSLIPDIRECT"
        Me.CBWEIGHMENTSLIPDIRECT.Size = New System.Drawing.Size(157, 17)
        Me.CBWEIGHMENTSLIPDIRECT.TabIndex = 3
        Me.CBWEIGHMENTSLIPDIRECT.Text = "WEIGHMENT SLIP (Direct)"
        Me.CBWEIGHMENTSLIPDIRECT.UseVisualStyleBackColor = True
        '
        'CBDETAILSEXPORTINEXCEL
        '
        Me.CBDETAILSEXPORTINEXCEL.AutoSize = True
        Me.CBDETAILSEXPORTINEXCEL.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBDETAILSEXPORTINEXCEL.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBDETAILSEXPORTINEXCEL.Location = New System.Drawing.Point(15, 67)
        Me.CBDETAILSEXPORTINEXCEL.Name = "CBDETAILSEXPORTINEXCEL"
        Me.CBDETAILSEXPORTINEXCEL.Size = New System.Drawing.Size(169, 17)
        Me.CBDETAILSEXPORTINEXCEL.TabIndex = 2
        Me.CBDETAILSEXPORTINEXCEL.Text = "DETAILS EXPORT IN EXCEL"
        Me.CBDETAILSEXPORTINEXCEL.UseVisualStyleBackColor = True
        '
        'CBWEIGHMENTSLIP
        '
        Me.CBWEIGHMENTSLIP.AutoSize = True
        Me.CBWEIGHMENTSLIP.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CBWEIGHMENTSLIP.ForeColor = System.Drawing.SystemColors.ControlText
        Me.CBWEIGHMENTSLIP.Location = New System.Drawing.Point(15, 44)
        Me.CBWEIGHMENTSLIP.Name = "CBWEIGHMENTSLIP"
        Me.CBWEIGHMENTSLIP.Size = New System.Drawing.Size(120, 17)
        Me.CBWEIGHMENTSLIP.TabIndex = 1
        Me.CBWEIGHMENTSLIP.Text = "WEIGHMENT SLIP"
        Me.CBWEIGHMENTSLIP.UseVisualStyleBackColor = True
        '
        'cbgateEntrySlip
        '
        Me.cbgateEntrySlip.AutoSize = True
        Me.cbgateEntrySlip.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.cbgateEntrySlip.ForeColor = System.Drawing.SystemColors.ControlText
        Me.cbgateEntrySlip.Location = New System.Drawing.Point(15, 21)
        Me.cbgateEntrySlip.Name = "cbgateEntrySlip"
        Me.cbgateEntrySlip.Size = New System.Drawing.Size(121, 17)
        Me.cbgateEntrySlip.TabIndex = 0
        Me.cbgateEntrySlip.Text = "GATE ENTRY SLIP"
        Me.cbgateEntrySlip.UseVisualStyleBackColor = True
        '
        'GroupBox4
        '
        Me.GroupBox4.Controls.Add(Me.btnExit)
        Me.GroupBox4.Controls.Add(Me.btnUpdate)
        Me.GroupBox4.Controls.Add(Me.btnCancel)
        Me.GroupBox4.Font = New System.Drawing.Font("Times New Roman", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox4.ForeColor = System.Drawing.Color.Blue
        Me.GroupBox4.Location = New System.Drawing.Point(757, 88)
        Me.GroupBox4.Name = "GroupBox4"
        Me.GroupBox4.Size = New System.Drawing.Size(117, 417)
        Me.GroupBox4.TabIndex = 4
        Me.GroupBox4.TabStop = False
        '
        'btnExit
        '
        Me.btnExit.ForeColor = System.Drawing.SystemColors.ControlText
        Me.btnExit.Location = New System.Drawing.Point(17, 181)
        Me.btnExit.Name = "btnExit"
        Me.btnExit.Size = New System.Drawing.Size(75, 30)
        Me.btnExit.TabIndex = 15
        Me.btnExit.Text = "Exit"
        Me.btnExit.UseVisualStyleBackColor = True
        '
        'btnUpdate
        '
        Me.btnUpdate.ForeColor = System.Drawing.SystemColors.ControlText
        Me.btnUpdate.Location = New System.Drawing.Point(17, 97)
        Me.btnUpdate.Name = "btnUpdate"
        Me.btnUpdate.Size = New System.Drawing.Size(75, 30)
        Me.btnUpdate.TabIndex = 13
        Me.btnUpdate.Text = "Update"
        Me.btnUpdate.UseVisualStyleBackColor = True
        '
        'btnCancel
        '
        Me.btnCancel.ForeColor = System.Drawing.SystemColors.ControlText
        Me.btnCancel.Location = New System.Drawing.Point(17, 139)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 30)
        Me.btnCancel.TabIndex = 14
        Me.btnCancel.Text = "Cancel"
        Me.btnCancel.UseVisualStyleBackColor = True
        '
        'GroupBox5
        '
        Me.GroupBox5.Controls.Add(Me.gvUser)
        Me.GroupBox5.Location = New System.Drawing.Point(13, 511)
        Me.GroupBox5.Name = "GroupBox5"
        Me.GroupBox5.Size = New System.Drawing.Size(861, 140)
        Me.GroupBox5.TabIndex = 5
        Me.GroupBox5.TabStop = False
        '
        'gvUser
        '
        Me.gvUser.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.DisplayedCells
        Me.gvUser.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.gvUser.Location = New System.Drawing.Point(6, 16)
        Me.gvUser.Name = "gvUser"
        Me.gvUser.ReadOnly = True
        Me.gvUser.Size = New System.Drawing.Size(849, 118)
        Me.gvUser.TabIndex = 0
        '
        'frmUser
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(885, 663)
        Me.Controls.Add(Me.GroupBox5)
        Me.Controls.Add(Me.GroupBox4)
        Me.Controls.Add(Me.GroupBox3)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.gbPermisssionMaster)
        Me.Controls.Add(Me.GroupBox1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmUser"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "USER MASTER SETTINGS"
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.gbPermisssionMaster.ResumeLayout(False)
        Me.gbPermisssionMaster.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.GroupBox4.ResumeLayout(False)
        Me.GroupBox5.ResumeLayout(False)
        CType(Me.gvUser, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents ddlSAPUser As System.Windows.Forms.ComboBox
    Friend WithEvents txtPassword As System.Windows.Forms.TextBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents txtUserName As System.Windows.Forms.TextBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents txtUserId As System.Windows.Forms.TextBox
    Friend WithEvents gbPermisssionMaster As System.Windows.Forms.GroupBox
    Friend WithEvents cbUserMaster As System.Windows.Forms.CheckBox
    Friend WithEvents cbImportMasterDatafromSAP As System.Windows.Forms.CheckBox
    Friend WithEvents CBGROUPINGREFERENCEMASTER As System.Windows.Forms.CheckBox
    Friend WithEvents CBTRANSPORTERMASTER As System.Windows.Forms.CheckBox
    Friend WithEvents CBCUSTOMERMASTER As System.Windows.Forms.CheckBox
    Friend WithEvents CBVENDORMASTER As System.Windows.Forms.CheckBox
    Friend WithEvents CBMATERIALMASTER As System.Windows.Forms.CheckBox
    Friend WithEvents CBTRANSACTIONPARAMETER As System.Windows.Forms.CheckBox
    Friend WithEvents CBPLANT As System.Windows.Forms.CheckBox
    Friend WithEvents CBCOMPANY As System.Windows.Forms.CheckBox
    Friend WithEvents CBNODECLIENTCONFIG As System.Windows.Forms.CheckBox
    Friend WithEvents cbSAPSERVERCONFIGURATION As System.Windows.Forms.CheckBox
    Friend WithEvents cbDriverDetails As System.Windows.Forms.CheckBox
    Friend WithEvents cbVehicleTareWtManual As System.Windows.Forms.CheckBox
    Friend WithEvents cbVehicleTareWtAuto As System.Windows.Forms.CheckBox
    Friend WithEvents CBSCANNEDDOCPATHSETTINGS As System.Windows.Forms.CheckBox
    Friend WithEvents CBLOCATIONMASTER As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents CBCONTRACTORITEMOUTPASS As System.Windows.Forms.CheckBox
    Friend WithEvents CBWEIGHMENTDATASPLITTINGOUTBOUND As System.Windows.Forms.CheckBox
    Friend WithEvents CBUPDATELINEITEMS As System.Windows.Forms.CheckBox
    Friend WithEvents CBVEHICLENOCHANGE As System.Windows.Forms.CheckBox
    Friend WithEvents CBVEHICLEACTIVITYCHANGE As System.Windows.Forms.CheckBox
    Friend WithEvents CBVEHICLEWTSPLITFORGROUPING As System.Windows.Forms.CheckBox
    Friend WithEvents CBCANCELVEHICLEENTRY As System.Windows.Forms.CheckBox
    Friend WithEvents ******************************** As System.Windows.Forms.CheckBox
    Friend WithEvents CBCANCELGROUPINGDATA As System.Windows.Forms.CheckBox
    Friend WithEvents CBCHANGEGROUPINGREFERENCE As System.Windows.Forms.CheckBox
    Friend WithEvents CBGROUPINGBAISISONREFERENCE As System.Windows.Forms.CheckBox
    Friend WithEvents CBWEIGHMENTMANUALMODE As System.Windows.Forms.CheckBox
    Friend WithEvents CBWEIGHMENTDATASPLITTINGINBOUND As System.Windows.Forms.CheckBox
    Friend WithEvents CBWEIGHMENT As System.Windows.Forms.CheckBox
    Friend WithEvents cbgateentry As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents CBDETAILSREPORT As System.Windows.Forms.CheckBox
    Friend WithEvents CBMATERIALWISE As System.Windows.Forms.CheckBox
    Friend WithEvents CBSOWISE As System.Windows.Forms.CheckBox
    Friend WithEvents CBPOWISE As System.Windows.Forms.CheckBox
    Friend WithEvents CBCUSTOMERWISE As System.Windows.Forms.CheckBox
    Friend WithEvents CBVENDORWISE As System.Windows.Forms.CheckBox
    Friend WithEvents CBWEIGHMENTSLIPDIRECT As System.Windows.Forms.CheckBox
    Friend WithEvents CBDETAILSEXPORTINEXCEL As System.Windows.Forms.CheckBox
    Friend WithEvents CBWEIGHMENTSLIP As System.Windows.Forms.CheckBox
    Friend WithEvents cbgateEntrySlip As System.Windows.Forms.CheckBox
    Friend WithEvents GroupBox4 As System.Windows.Forms.GroupBox
    Friend WithEvents btnExit As System.Windows.Forms.Button
    Friend WithEvents btnUpdate As System.Windows.Forms.Button
    Friend WithEvents btnCancel As System.Windows.Forms.Button
    Friend WithEvents GroupBox5 As System.Windows.Forms.GroupBox
    Friend WithEvents gvUser As System.Windows.Forms.DataGridView
End Class
