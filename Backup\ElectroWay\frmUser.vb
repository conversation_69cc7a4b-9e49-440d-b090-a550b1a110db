﻿Imports System.IO
Imports System.Text
Imports System.Data
Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Security.Cryptography
Public Class frmUser
    Dim cc As New Class1
    '-----------------------
    Private enc As System.Text.UTF8Encoding
    Private encryptor As ICryptoTransform
    Private decryptor As ICryptoTransform
    Private Sub frmUser_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        dr = cc.GetDataReader("select * from tbl_SAP_Server_Config_mst order by SAP_User")
        Try
            While dr.Read
                ddlSAPUser.Items.Add(dr("SAP_User"))
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        ddlSAPUser.SelectedIndex = 0
        LoadGrid()
        If User_ID <> "superadmin" Then
            CBVEHICLEACTIVITYCHANGE.Visible = False
            CBVEHICLENOCHANGE.Visible = False
        End If
        '---------Cryptology-------------------
        <PERSON><PERSON>_128 As Byte() = {42, 1, 52, 67, 231, 13, 94, 101, 123, 6, 0, 12, 32, 91, 4, 111, 31, 70, 21, 141, 123, 142, 234, 82, 95, 129, 187, 162, 12, 55, 98, 23}
        <PERSON>m IV_128 As Byte() = {234, 12, 52, 44, 214, 222, 200, 109, 2, 98, 45, 76, 88, 53, 23, 78}
        <PERSON>m symmetric<PERSON>ey <PERSON> <PERSON>ijndael<PERSON>anaged = <PERSON> <PERSON>ijndael<PERSON>anaged()
        symmetric<PERSON>ey.Mode = CipherMode.CBC

        Me.enc = New System.Text.UTF8Encoding
        Me.encryptor = symmetricKey.CreateEncryptor(KEY_128, IV_128)
        Me.decryptor = symmetricKey.CreateDecryptor(KEY_128, IV_128)
    End Sub
    Private Function Encrypt(ByVal clearText As String) As String
        Dim sPlainText As String = clearText
        If Not String.IsNullOrEmpty(sPlainText) Then
            Dim memoryStream As MemoryStream = New MemoryStream()
            Dim cryptoStream As CryptoStream = New CryptoStream(memoryStream, Me.encryptor, CryptoStreamMode.Write)
            cryptoStream.Write(Me.enc.GetBytes(sPlainText), 0, sPlainText.Length)
            cryptoStream.FlushFinalBlock()
            sPlainText = Convert.ToBase64String(memoryStream.ToArray())
            memoryStream.Close()
            cryptoStream.Close()
        End If
        Return sPlainText
    End Function
    Private Sub LoadGrid()
        Dim str As String = "select * from tbl_User_Mst order by SL_No"
        ds = cc.GetDataset(str)
        gvUser.DataSource = ds.Tables(0)
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        Dim cbUserMaster1, cbImportMasterDatafromSAP1, cbVehicleTareWtManual1, cbDriverDetails1, cbSAPSERVERCONFIGURATION1, CBNODECLIENTCONFIG1, cbgateentry1, CBWEIGHMENT1, CBWEIGHMENTDATASPLITTINGINBOUND1 As String
        Dim CBWEIGHMENTMANUALMODE1, CBCOMPANY1, CBTRANSACTIONPARAMETER1, CBPLANT1, CBGROUPINGREFERENCEMASTER1, CBGROUPINGBAISISONREFERENCE1, CBMATERIALMASTER1, CBVENDORMASTER1, CBCUSTOMERMASTER1, CBCONTRACTORITEMOUTPASS1, CBCHANGEGROUPINGREFERENCE1, CBCANCELGROUPINGDATA1, CBLOCATIONMASTER1, CBSCANNEDDOCPATHSETTINGS1, CBVEHICLETRANSFERTOOTHERLOCATION1, CBCANCELVEHICLEENTRY1 As String
        Dim cbgateEntrySlip1, CBWEIGHMENTSLIP1, CBDETAILSEXPORTINEXCEL1, CBVEHICLEWTSPLITFORGROUPING1, CBVEHICLEACTIVITYCHANGE1, CBVEHICLENOCHANGE1, CBUPDATELINEITEMS1, CBWEIGHMENTSLIPDIRECT1, CBVENDORWISE1 As String
        Dim CBPOWISE1, CBSOWISE1, CBMATERIALWISE1, cbVehicleTareWtAuto1, CBWEIGHMENTDATASPLITTINGOUTBOUND1, CBDETAILSREPORT1, CBTRANSPORTERMASTER1 As String
        If cbUserMaster.Checked = True Then
            cbUserMaster1 = "1"
        Else
            cbUserMaster1 = "0"
        End If
        If cbImportMasterDatafromSAP.Checked = True Then
            cbImportMasterDatafromSAP1 = "1"
        Else
            cbImportMasterDatafromSAP1 = "0"
        End If
        If cbVehicleTareWtManual.Checked = True Then
            cbVehicleTareWtManual1 = "1"
        Else
            cbVehicleTareWtManual1 = "0"
        End If
        If cbDriverDetails.Checked = True Then
            cbDriverDetails1 = "1"
        Else
            cbDriverDetails1 = "0"
        End If
        If cbSAPSERVERCONFIGURATION.Checked = True Then
            cbSAPSERVERCONFIGURATION1 = "1"
        Else
            cbSAPSERVERCONFIGURATION1 = "0"
        End If
        If CBNODECLIENTCONFIG.Checked = True Then
            CBNODECLIENTCONFIG1 = "1"
        Else
            CBNODECLIENTCONFIG1 = "0"
        End If
        If cbgateentry.Checked = True Then
            cbgateentry1 = "1"
        Else
            cbgateentry1 = "0"
        End If
        If CBWEIGHMENT.Checked = True Then
            CBWEIGHMENT1 = "1"
        Else
            CBWEIGHMENT1 = "0"
        End If
        If CBWEIGHMENTDATASPLITTINGINBOUND.Checked = True Then
            CBWEIGHMENTDATASPLITTINGINBOUND1 = "1"
        Else
            CBWEIGHMENTDATASPLITTINGINBOUND1 = "0"
        End If

        If CBWEIGHMENTMANUALMODE.Checked = True Then
            CBWEIGHMENTMANUALMODE1 = "1"
        Else
            CBWEIGHMENTMANUALMODE1 = "0"
        End If
        If CBCOMPANY.Checked = True Then
            CBCOMPANY1 = "1"
        Else
            CBCOMPANY1 = "0"
        End If
        If CBTRANSACTIONPARAMETER.Checked = True Then
            CBTRANSACTIONPARAMETER1 = "1"
        Else
            CBTRANSACTIONPARAMETER1 = "0"
        End If
        If CBMATERIALMASTER.Checked = True Then
            CBMATERIALMASTER1 = "1"
        Else
            CBMATERIALMASTER1 = "0"
        End If
        If CBVENDORMASTER.Checked = True Then
            CBVENDORMASTER1 = "1"
        Else
            CBVENDORMASTER1 = "0"
        End If
        If CBCUSTOMERMASTER.Checked = True Then
            CBCUSTOMERMASTER1 = "1"
        Else
            CBCUSTOMERMASTER1 = "0"
        End If

        If CBPLANT.Checked = True Then
            CBPLANT1 = "1"
        Else
            CBPLANT1 = "0"
        End If
        If CBGROUPINGREFERENCEMASTER.Checked = True Then
            CBGROUPINGREFERENCEMASTER1 = "1"
        Else
            CBGROUPINGREFERENCEMASTER1 = "0"
        End If
        If CBGROUPINGBAISISONREFERENCE.Checked = True Then
            CBGROUPINGBAISISONREFERENCE1 = "1"
        Else
            CBGROUPINGBAISISONREFERENCE1 = "0"
        End If

        If CBCHANGEGROUPINGREFERENCE.Checked = True Then
            CBCHANGEGROUPINGREFERENCE1 = "1"
        Else
            CBCHANGEGROUPINGREFERENCE1 = "0"
        End If
        If CBCANCELGROUPINGDATA.Checked = True Then
            CBCANCELGROUPINGDATA1 = "1"
        Else
            CBCANCELGROUPINGDATA1 = "0"
        End If
        If CBLOCATIONMASTER.Checked = True Then
            CBLOCATIONMASTER1 = "1"
        Else
            CBLOCATIONMASTER1 = "0"
        End If
        If CBSCANNEDDOCPATHSETTINGS.Checked = True Then
            CBSCANNEDDOCPATHSETTINGS1 = "1"
        Else
            CBSCANNEDDOCPATHSETTINGS1 = "0"
        End If

        If CBVEHICLETRANSFERTOOTHERLOCATION.Checked = True Then
            CBVEHICLETRANSFERTOOTHERLOCATION1 = "1"
        Else
            CBVEHICLETRANSFERTOOTHERLOCATION1 = "0"
        End If
        If CBCANCELVEHICLEENTRY.Checked = True Then
            CBCANCELVEHICLEENTRY1 = "1"
        Else
            CBCANCELVEHICLEENTRY1 = "0"
        End If
        If cbgateEntrySlip.Checked = True Then
            cbgateEntrySlip1 = "1"
        Else
            cbgateEntrySlip1 = "0"
        End If
        If CBWEIGHMENTSLIP.Checked = True Then
            CBWEIGHMENTSLIP1 = "1"
        Else
            CBWEIGHMENTSLIP1 = "0"
        End If
        If CBDETAILSEXPORTINEXCEL.Checked = True Then
            CBDETAILSEXPORTINEXCEL1 = "1"
        Else
            CBDETAILSEXPORTINEXCEL1 = "0"
        End If

        If CBVEHICLEWTSPLITFORGROUPING.Checked = True Then
            CBVEHICLEWTSPLITFORGROUPING1 = "1"
        Else
            CBVEHICLEWTSPLITFORGROUPING1 = "0"
        End If
        If CBVEHICLEACTIVITYCHANGE.Checked = True Then
            CBVEHICLEACTIVITYCHANGE1 = "1"
        Else
            CBVEHICLEACTIVITYCHANGE1 = "0"
        End If
        If CBVEHICLENOCHANGE.Checked = True Then
            CBVEHICLENOCHANGE1 = "1"
        Else
            CBVEHICLENOCHANGE1 = "0"
        End If
        If CBUPDATELINEITEMS.Checked = True Then
            CBUPDATELINEITEMS1 = "1"
        Else
            CBUPDATELINEITEMS1 = "0"
        End If
        If CBWEIGHMENTSLIPDIRECT.Checked = True Then
            CBWEIGHMENTSLIPDIRECT1 = "1"
        Else
            CBWEIGHMENTSLIPDIRECT1 = "0"
        End If
        If CBVENDORWISE.Checked = True Then
            CBVENDORWISE1 = "1"
        Else
            CBVENDORWISE1 = "0"
        End If

        If CBPOWISE.Checked = True Then
            CBPOWISE1 = "1"
        Else
            CBPOWISE1 = "0"
        End If
        If CBSOWISE.Checked = True Then
            CBSOWISE1 = "1"
        Else
            CBSOWISE1 = "0"
        End If
        If CBMATERIALWISE.Checked = True Then
            CBMATERIALWISE1 = "1"
        Else
            CBMATERIALWISE1 = "0"
        End If
        If cbVehicleTareWtAuto.Checked = True Then
            cbVehicleTareWtAuto1 = "1"
        Else
            cbVehicleTareWtAuto1 = "0"
        End If
        If CBTRANSPORTERMASTER.Checked = True Then
            CBTRANSPORTERMASTER1 = "1"
        Else
            CBTRANSPORTERMASTER1 = "0"
        End If
        If CBDETAILSREPORT.Checked = True Then
            CBDETAILSREPORT1 = "1"
        Else
            CBDETAILSREPORT1 = "0"
        End If

        If CBWEIGHMENTDATASPLITTINGOUTBOUND.Checked = True Then
            CBWEIGHMENTDATASPLITTINGOUTBOUND1 = "1"
        Else
            CBWEIGHMENTDATASPLITTINGOUTBOUND1 = "0"
        End If
        If CBCONTRACTORITEMOUTPASS.Checked = True Then
            CBCONTRACTORITEMOUTPASS1 = "1"
        Else
            CBCONTRACTORITEMOUTPASS1 = "0"
        End If
        '----------------------------------

        Try
            ds = cc.GetDataset("select * from tbl_User_mst where User_ID = '" & Trim(txtUserId.Text) & "'")
            If ds.Tables(0).Rows.Count <> 0 Then
                Dim ans = MsgBox("User already exists, would you like to update .", vbYesNo, "ElectroWay")
                If ans = vbYes Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_User_Mst"
                    cm.Parameters.AddWithValue("@val_User_ID", Trim(txtUserId.Text))
                    cm.Parameters.AddWithValue("@val_User_name", Trim(txtUserName.Text))
                    cm.Parameters.AddWithValue("@val_User_password", Encrypt(txtPassword.Text.Trim))
                    cm.Parameters.AddWithValue("@val_SAP_User_ID", Trim(ddlSAPUser.Text))
                    cm.Parameters.AddWithValue("@val_Per_User_mst", cbUserMaster1)
                    cm.Parameters.AddWithValue("@val_Per_Imp_Data_SAP", cbImportMasterDatafromSAP1)
                    cm.Parameters.AddWithValue("@val_Per_Veh_Tare_WT", cbVehicleTareWtManual1)
                    cm.Parameters.AddWithValue("@val_Per_Driver_Det", cbDriverDetails1)
                    cm.Parameters.AddWithValue("@val_Per_SAP_Server_Config", cbSAPSERVERCONFIGURATION1)
                    cm.Parameters.AddWithValue("@val_Per_Node_ClienT_config", CBNODECLIENTCONFIG1)
                    cm.Parameters.AddWithValue("@val_Per_Gate_Entry", cbgateentry1)
                    cm.Parameters.AddWithValue("@val_Per_Weighment", CBWEIGHMENT1)
                    cm.Parameters.AddWithValue("@val_Per_Weight_Data_Splitting", CBWEIGHMENTDATASPLITTINGINBOUND1)
                    cm.Parameters.AddWithValue("@val_Per_Manual_Weighment", CBWEIGHMENTMANUALMODE1)
                    cm.Parameters.AddWithValue("@val_Per_Company", CBCOMPANY1)
                    cm.Parameters.AddWithValue("@val_Per_TransactionPar", CBTRANSACTIONPARAMETER1)
                    cm.Parameters.AddWithValue("@val_Per_Material", CBMATERIALMASTER1)
                    cm.Parameters.AddWithValue("@val_Per_Vendor", CBVENDORMASTER1)
                    cm.Parameters.AddWithValue("@val_Per_Customer", CBCUSTOMERMASTER1)
                    cm.Parameters.AddWithValue("@val_Per_PlantMaster", CBPLANT1)
                    cm.Parameters.AddWithValue("@val_Per_Grouping_Ref_master", CBGROUPINGREFERENCEMASTER1)
                    cm.Parameters.AddWithValue("@val_Per_Grouping_Basis_on_Ref", CBGROUPINGBAISISONREFERENCE1)
                    cm.Parameters.AddWithValue("@val_Per_Change_Grouping_Ref", CBCHANGEGROUPINGREFERENCE1)
                    cm.Parameters.AddWithValue("@val_Per_Cancel_Grouping_Data", CBCANCELGROUPINGDATA1)
                    cm.Parameters.AddWithValue("@val_Per_Location_master", CBLOCATIONMASTER1)
                    cm.Parameters.AddWithValue("@val_Per_Scan_Doc_Path_setting", CBSCANNEDDOCPATHSETTINGS1)
                    cm.Parameters.AddWithValue("@val_Per_Vehicle_Transfer_To_Other_Location", CBVEHICLETRANSFERTOOTHERLOCATION1)
                    cm.Parameters.AddWithValue("@val_Per_Cancel_Vehicle_Entry", CBCANCELVEHICLEENTRY1)
                    cm.Parameters.AddWithValue("@val_Per_GateEntry_Slip", cbgateEntrySlip1)
                    cm.Parameters.AddWithValue("@val_Per_Weighment_Slip", CBWEIGHMENTSLIP1)
                    cm.Parameters.AddWithValue("@val_Per_Export_In_Excel", CBDETAILSEXPORTINEXCEL1)
                    cm.Parameters.AddWithValue("@val_Vehicle_WT_Split", CBVEHICLEWTSPLITFORGROUPING1)
                    cm.Parameters.AddWithValue("@val_Per_Vehicle_Activity_Change", CBVEHICLEACTIVITYCHANGE1)
                    cm.Parameters.AddWithValue("@val_Per_Vehicle_Number_Change", CBVEHICLENOCHANGE1)
                    cm.Parameters.AddWithValue("@val_Per_UpdateLite_Items", CBUPDATELINEITEMS1)
                    cm.Parameters.AddWithValue("@val_Per_Weighment_Slip_Direct", CBWEIGHMENTSLIPDIRECT1)
                    cm.Parameters.AddWithValue("@val_Per_Vendor_Wise", CBVENDORWISE1)
                    cm.Parameters.AddWithValue("@val_Per_Customer_Wise", CBCUSTOMERMASTER1)
                    cm.Parameters.AddWithValue("@val_Per_PO_Wise", CBPOWISE1)
                    cm.Parameters.AddWithValue("@val_Per_SO_Wise", CBSOWISE1)
                    cm.Parameters.AddWithValue("@val_Per_Material_Wise", CBMATERIALWISE1)
                    cm.Parameters.AddWithValue("@val_Per_Vehicle_Tare_Wt_Auto", cbVehicleTareWtAuto1)
                    cm.Parameters.AddWithValue("@val_Per_Transporter_Mst", CBTRANSPORTERMASTER1)
                    cm.Parameters.AddWithValue("@val_Per_Details_Report", CBDETAILSREPORT1)
                    cm.Parameters.AddWithValue("@val_Per_Weighment_Data_Splitting_OUTBOUND", CBWEIGHMENTDATASPLITTINGOUTBOUND1)
                    cm.Parameters.AddWithValue("@val_Cont_Item_OUTPASS", CBCONTRACTORITEMOUTPASS1)
                    cm.Parameters.AddWithValue("@val_INBOUND_Report", 0)
                    cm.Parameters.AddWithValue("@val_OUTBOUND_Report", 0)
                    cm.Parameters.AddWithValue("@val_WB_Report", 0)
                    cm.Parameters.AddWithValue("@val_CheckPostReport", 0)
                    cm.Parameters.AddWithValue("@val_Vehicle_Status_Blacklist", 0)
                    cm.Parameters.AddWithValue("@val_Cont_Mat_Approval", 0)
                    cm.Parameters.AddWithValue("@Last_Used", Today.Date)
                    cm.Parameters.AddWithValue("@Created_Date", Today.Date)
                    cm.Parameters.AddWithValue("@CreatedBy", "admin")
                    cm.Parameters.AddWithValue("@Updated_Date", Today.Date)
                    cm.Parameters.AddWithValue("@UpdatedBy", "admin")
                    cm.Parameters.AddWithValue("@Active", "1")
                    cm.Parameters.AddWithValue("@pwd_expiry_date", Today.Date.AddDays(30))
                    cm.ExecuteNonQuery()
                    'ListView1.ListItems.Clear()
                    LoadGrid()
                    'Call ClearAll
                    MsgBox("Updated Successfully !", vbInformation, "ElectroWay")

                End If
            Else
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_User_Mst"
                cm.Parameters.AddWithValue("@val_User_ID", Trim(txtUserId.Text))
                cm.Parameters.AddWithValue("@val_User_name", Trim(txtUserName.Text))
                cm.Parameters.AddWithValue("@val_User_password", Encrypt(txtPassword.Text.Trim))
                cm.Parameters.AddWithValue("@val_SAP_User_ID", Trim(ddlSAPUser.Text))
                cm.Parameters.AddWithValue("@val_Per_User_mst", cbUserMaster1)
                cm.Parameters.AddWithValue("@val_Per_Imp_Data_SAP", cbImportMasterDatafromSAP1)
                cm.Parameters.AddWithValue("@val_Per_Veh_Tare_WT", cbVehicleTareWtManual1)
                cm.Parameters.AddWithValue("@val_Per_Driver_Det", cbDriverDetails1)
                cm.Parameters.AddWithValue("@val_Per_SAP_Server_Config", cbSAPSERVERCONFIGURATION1)
                cm.Parameters.AddWithValue("@val_Per_Node_ClienT_config", CBNODECLIENTCONFIG1)
                cm.Parameters.AddWithValue("@val_Per_Gate_Entry", cbgateentry1)
                cm.Parameters.AddWithValue("@val_Per_Weighment", CBWEIGHMENT1)
                cm.Parameters.AddWithValue("@val_Per_Weight_Data_Splitting", CBWEIGHMENTDATASPLITTINGINBOUND1)
                cm.Parameters.AddWithValue("@val_Per_Manual_Weighment", CBWEIGHMENTMANUALMODE1)
                cm.Parameters.AddWithValue("@val_Per_Company", CBCOMPANY1)
                cm.Parameters.AddWithValue("@val_Per_TransactionPar", CBTRANSACTIONPARAMETER1)
                cm.Parameters.AddWithValue("@val_Per_Material", CBMATERIALMASTER1)
                cm.Parameters.AddWithValue("@val_Per_Vendor", CBVENDORMASTER1)
                cm.Parameters.AddWithValue("@val_Per_Customer", CBCUSTOMERMASTER1)
                cm.Parameters.AddWithValue("@val_Per_PlantMaster", CBPLANT1)
                cm.Parameters.AddWithValue("@val_Per_Grouping_Ref_master", CBGROUPINGREFERENCEMASTER1)
                cm.Parameters.AddWithValue("@val_Per_Grouping_Basis_on_Ref", CBGROUPINGBAISISONREFERENCE1)
                cm.Parameters.AddWithValue("@val_Per_Change_Grouping_Ref", CBCHANGEGROUPINGREFERENCE1)
                cm.Parameters.AddWithValue("@val_Per_Cancel_Grouping_Data", CBCANCELGROUPINGDATA1)
                cm.Parameters.AddWithValue("@val_Per_Location_master", CBLOCATIONMASTER1)
                cm.Parameters.AddWithValue("@val_Per_Scan_Doc_Path_setting", CBSCANNEDDOCPATHSETTINGS1)
                cm.Parameters.AddWithValue("@val_Per_Vehicle_Transfer_To_Other_Location", CBVEHICLETRANSFERTOOTHERLOCATION1)
                cm.Parameters.AddWithValue("@val_Per_Cancel_Vehicle_Entry", CBCANCELVEHICLEENTRY1)
                cm.Parameters.AddWithValue("@val_Per_GateEntry_Slip", cbgateEntrySlip1)
                cm.Parameters.AddWithValue("@val_Per_Weighment_Slip", CBWEIGHMENTSLIP1)
                cm.Parameters.AddWithValue("@val_Per_Export_In_Excel", CBDETAILSEXPORTINEXCEL1)
                cm.Parameters.AddWithValue("@val_Vehicle_WT_Split", CBVEHICLEWTSPLITFORGROUPING1)
                cm.Parameters.AddWithValue("@val_Per_Vehicle_Activity_Change", CBVEHICLEACTIVITYCHANGE1)
                cm.Parameters.AddWithValue("@val_Per_Vehicle_Number_Change", CBVEHICLENOCHANGE1)
                cm.Parameters.AddWithValue("@val_Per_UpdateLite_Items", CBUPDATELINEITEMS1)
                cm.Parameters.AddWithValue("@val_Per_Weighment_Slip_Direct", CBWEIGHMENTSLIPDIRECT1)
                cm.Parameters.AddWithValue("@val_Per_Vendor_Wise", CBVENDORWISE1)
                cm.Parameters.AddWithValue("@val_Per_Customer_Wise", CBCUSTOMERMASTER1)
                cm.Parameters.AddWithValue("@val_Per_PO_Wise", CBPOWISE1)
                cm.Parameters.AddWithValue("@val_Per_SO_Wise", CBSOWISE1)
                cm.Parameters.AddWithValue("@val_Per_Material_Wise", CBMATERIALWISE1)
                cm.Parameters.AddWithValue("@val_Per_Vehicle_Tare_Wt_Auto", cbVehicleTareWtAuto1)
                cm.Parameters.AddWithValue("@val_Per_Transporter_Mst", CBTRANSPORTERMASTER1)
                cm.Parameters.AddWithValue("@val_Per_Details_Report", CBDETAILSREPORT1)
                cm.Parameters.AddWithValue("@val_Per_Weighment_Data_Splitting_OUTBOUND", CBWEIGHMENTDATASPLITTINGOUTBOUND1)
                cm.Parameters.AddWithValue("@val_Cont_Item_OUTPASS", CBCONTRACTORITEMOUTPASS1)
                cm.Parameters.AddWithValue("@val_INBOUND_Report", 0)
                cm.Parameters.AddWithValue("@val_OUTBOUND_Report", 0)
                cm.Parameters.AddWithValue("@val_WB_Report", 0)
                cm.Parameters.AddWithValue("@val_CheckPostReport", 0)
                cm.Parameters.AddWithValue("@val_Vehicle_Status_Blacklist", 0)
                cm.Parameters.AddWithValue("@val_Cont_Mat_Approval", 0)
                cm.Parameters.AddWithValue("@Last_Used", Today.Date)
                cm.Parameters.AddWithValue("@Created_Date", Today.Date)
                cm.Parameters.AddWithValue("@CreatedBy", "admin")
                cm.Parameters.AddWithValue("@Updated_Date", Today.Date)
                cm.Parameters.AddWithValue("@UpdatedBy", "admin")
                cm.Parameters.AddWithValue("@Active", "1")
                cm.Parameters.AddWithValue("@pwd_expiry_date", Today.Date.AddDays(30))
                cm.ExecuteNonQuery()

                LoadGrid()
                'Call ClearAll
                MsgBox("Updated Successfully !", vbInformation, "ElectroWay")
            End If
        Catch ex As Exception

        End Try
    End Sub
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        clearAll()
    End Sub
    Private Sub clearAll()
        txtUserId.Clear()
        txtPassword.Clear()
        txtUserName.Clear()
        CBCANCELGROUPINGDATA.Checked = False
        CBCANCELVEHICLEENTRY.Checked = False
        CBCHANGEGROUPINGREFERENCE.Checked = False
        CBCOMPANY.Checked = False
        CBCONTRACTORITEMOUTPASS.Checked = False
        CBCUSTOMERMASTER.Checked = False
        CBCUSTOMERWISE.Checked = False
        CBDETAILSEXPORTINEXCEL.Checked = False
        CBDETAILSREPORT.Checked = False
        cbDriverDetails.Checked = False
        cbgateentry.Checked = False
        CBGROUPINGBAISISONREFERENCE.Checked = False
        CBGROUPINGREFERENCEMASTER.Checked = False
        cbImportMasterDatafromSAP.Checked = False
        CBLOCATIONMASTER.Checked = False
        CBMATERIALMASTER.Checked = False
        CBMATERIALWISE.Checked = False
        CBNODECLIENTCONFIG.Checked = False
        CBPLANT.Checked = False
        CBPOWISE.Checked = False
        cbSAPSERVERCONFIGURATION.Checked = False
        CBSCANNEDDOCPATHSETTINGS.Checked = False
        CBSOWISE.Checked = False
        CBTRANSACTIONPARAMETER.Checked = False
        CBTRANSPORTERMASTER.Checked = False
        CBUPDATELINEITEMS.Checked = False
        cbUserMaster.Checked = False
        CBVEHICLEACTIVITYCHANGE.Checked = False
        CBVEHICLENOCHANGE.Checked = False
        cbVehicleTareWtAuto.Checked = False
        cbVehicleTareWtManual.Checked = False
        CBVEHICLETRANSFERTOOTHERLOCATION.Checked = False
        CBVEHICLEWTSPLITFORGROUPING.Checked = False
        CBVENDORMASTER.Checked = False
        CBVENDORWISE.Checked = False
        CBWEIGHMENT.Checked = False
        CBWEIGHMENTDATASPLITTINGINBOUND.Checked = False
        CBWEIGHMENTDATASPLITTINGOUTBOUND.Checked = False
        CBWEIGHMENTMANUALMODE.Checked = False
        CBWEIGHMENTSLIP.Checked = False
        CBWEIGHMENTSLIPDIRECT.Checked = False
    End Sub
    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub gvUser_CellFormatting(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellFormattingEventArgs) Handles gvUser.CellFormatting
        If (e.ColumnIndex <> -1 AndAlso gvUser.Columns(e.ColumnIndex).Name = "User_Password") Then
            If (Not e.Value Is Nothing) Then
                e.Value = New String("*", e.Value.ToString().Length)
            End If
            'e.Value = New String("*", e.Value.ToString().Length)
        End If
    End Sub

    Private Sub gvUser_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvUser.CellMouseClick
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvUser.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvUser.SelectedRows(i).Index)
                    txtUserId.Text = Convert.ToString(gvUser.Rows(index).Cells(1).Value)
                    txtUserName.Text = Convert.ToString(gvUser.Rows(index).Cells(2).Value)
                    txtPassword.Text = Convert.ToString(gvUser.Rows(index).Cells(3).Value)
                    ddlSAPUser.Text = Convert.ToString(gvUser.Rows(index).Cells(4).Value)
                    cbUserMaster.Checked = Convert.ToString(gvUser.Rows(index).Cells(5).Value)
                    cbImportMasterDatafromSAP.Checked = Convert.ToString(gvUser.Rows(index).Cells(6).Value)
                    cbVehicleTareWtManual.Checked = Convert.ToString(gvUser.Rows(index).Cells(7).Value)
                    cbDriverDetails.Checked = Convert.ToString(gvUser.Rows(index).Cells(8).Value)
                    cbSAPSERVERCONFIGURATION.Checked = Convert.ToString(gvUser.Rows(index).Cells(9).Value)
                    CBNODECLIENTCONFIG.Checked = Convert.ToString(gvUser.Rows(index).Cells(10).Value)
                    cbgateentry.Checked = Convert.ToString(gvUser.Rows(index).Cells(11).Value)
                    CBWEIGHMENT.Checked = Convert.ToString(gvUser.Rows(index).Cells(12).Value)
                    CBWEIGHMENTDATASPLITTINGINBOUND.Checked = Convert.ToString(gvUser.Rows(index).Cells(13).Value)
                    CBWEIGHMENTMANUALMODE.Checked = Convert.ToString(gvUser.Rows(index).Cells(14).Value)
                    CBCOMPANY.Checked = Convert.ToString(gvUser.Rows(index).Cells(15).Value)
                    CBTRANSACTIONPARAMETER.Checked = Convert.ToString(gvUser.Rows(index).Cells(16).Value)
                    CBMATERIALMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(17).Value)
                    CBVENDORMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(18).Value)
                    CBCUSTOMERMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(19).Value)
                    CBPLANT.Checked = Convert.ToString(gvUser.Rows(index).Cells(20).Value)
                    CBGROUPINGREFERENCEMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(21).Value)
                    CBGROUPINGBAISISONREFERENCE.Checked = Convert.ToString(gvUser.Rows(index).Cells(22).Value)
                    CBCHANGEGROUPINGREFERENCE.Checked = Convert.ToString(gvUser.Rows(index).Cells(23).Value)
                    CBCANCELGROUPINGDATA.Checked = Convert.ToString(gvUser.Rows(index).Cells(24).Value)
                    CBLOCATIONMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(25).Value)
                    CBSCANNEDDOCPATHSETTINGS.Checked = Convert.ToString(gvUser.Rows(index).Cells(26).Value)
                    CBVEHICLETRANSFERTOOTHERLOCATION.Checked = Convert.ToString(gvUser.Rows(index).Cells(27).Value)
                    CBCANCELVEHICLEENTRY.Checked = Convert.ToString(gvUser.Rows(index).Cells(28).Value)
                    cbgateEntrySlip.Checked = Convert.ToString(gvUser.Rows(index).Cells(29).Value)
                    CBWEIGHMENTSLIP.Checked = Convert.ToString(gvUser.Rows(index).Cells(30).Value)
                    CBDETAILSEXPORTINEXCEL.Checked = Convert.ToString(gvUser.Rows(index).Cells(31).Value)
                    CBVEHICLEWTSPLITFORGROUPING.Checked = Convert.ToString(gvUser.Rows(index).Cells(32).Value)
                    CBVEHICLEACTIVITYCHANGE.Checked = Convert.ToString(gvUser.Rows(index).Cells(33).Value)
                    CBVEHICLENOCHANGE.Checked = Convert.ToString(gvUser.Rows(index).Cells(34).Value)
                    CBUPDATELINEITEMS.Checked = Convert.ToString(gvUser.Rows(index).Cells(35).Value)
                    CBWEIGHMENTSLIPDIRECT.Checked = Convert.ToString(gvUser.Rows(index).Cells(36).Value)
                    CBVENDORWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(37).Value)
                    CBCUSTOMERWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(38).Value)
                    CBPOWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(39).Value)
                    CBSOWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(40).Value)
                    CBMATERIALWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(41).Value)
                    cbVehicleTareWtAuto.Checked = Convert.ToString(gvUser.Rows(index).Cells(42).Value)
                    CBTRANSPORTERMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(43).Value)
                    CBDETAILSREPORT.Checked = Convert.ToString(gvUser.Rows(index).Cells(44).Value)
                    CBWEIGHMENTDATASPLITTINGOUTBOUND.Checked = Convert.ToString(gvUser.Rows(index).Cells(45).Value)
                    CBCONTRACTORITEMOUTPASS.Checked = Convert.ToString(gvUser.Rows(index).Cells(46).Value)

                Next i
            End If
        Catch ex As Exception
            'MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub gvUser_CellMouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvUser.CellMouseDoubleClick
        If e.RowIndex < 0 Then
            Exit Sub
        End If
        Dim intIndex As Integer = e.RowIndex
        gvUser.Rows(intIndex).Selected = True
        '-----------------------------------------------------
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvUser.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvUser.SelectedRows(i).Index)
                    txtUserId.Text = Convert.ToString(gvUser.Rows(index).Cells(1).Value)
                    txtUserName.Text = Convert.ToString(gvUser.Rows(index).Cells(2).Value)
                    txtPassword.Text = Convert.ToString(gvUser.Rows(index).Cells(3).Value)
                    ddlSAPUser.Text = Convert.ToString(gvUser.Rows(index).Cells(4).Value)
                    cbUserMaster.Checked = Convert.ToString(gvUser.Rows(index).Cells(5).Value)
                    cbImportMasterDatafromSAP.Checked = Convert.ToString(gvUser.Rows(index).Cells(6).Value)
                    cbVehicleTareWtManual.Checked = Convert.ToString(gvUser.Rows(index).Cells(7).Value)
                    cbDriverDetails.Checked = Convert.ToString(gvUser.Rows(index).Cells(8).Value)
                    cbSAPSERVERCONFIGURATION.Checked = Convert.ToString(gvUser.Rows(index).Cells(9).Value)
                    CBNODECLIENTCONFIG.Checked = Convert.ToString(gvUser.Rows(index).Cells(10).Value)
                    cbgateentry.Checked = Convert.ToString(gvUser.Rows(index).Cells(11).Value)
                    CBWEIGHMENT.Checked = Convert.ToString(gvUser.Rows(index).Cells(12).Value)
                    CBWEIGHMENTDATASPLITTINGINBOUND.Checked = Convert.ToString(gvUser.Rows(index).Cells(13).Value)
                    CBWEIGHMENTMANUALMODE.Checked = Convert.ToString(gvUser.Rows(index).Cells(14).Value)
                    CBCOMPANY.Checked = Convert.ToString(gvUser.Rows(index).Cells(15).Value)
                    CBTRANSACTIONPARAMETER.Checked = Convert.ToString(gvUser.Rows(index).Cells(16).Value)
                    CBMATERIALMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(17).Value)
                    CBVENDORMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(18).Value)
                    CBCUSTOMERMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(19).Value)
                    CBPLANT.Checked = Convert.ToString(gvUser.Rows(index).Cells(20).Value)
                    CBGROUPINGREFERENCEMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(21).Value)
                    CBGROUPINGBAISISONREFERENCE.Checked = Convert.ToString(gvUser.Rows(index).Cells(22).Value)
                    CBCHANGEGROUPINGREFERENCE.Checked = Convert.ToString(gvUser.Rows(index).Cells(23).Value)
                    CBCANCELGROUPINGDATA.Checked = Convert.ToString(gvUser.Rows(index).Cells(24).Value)
                    CBLOCATIONMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(25).Value)
                    CBSCANNEDDOCPATHSETTINGS.Checked = Convert.ToString(gvUser.Rows(index).Cells(26).Value)
                    CBVEHICLETRANSFERTOOTHERLOCATION.Checked = Convert.ToString(gvUser.Rows(index).Cells(27).Value)
                    CBCANCELVEHICLEENTRY.Checked = Convert.ToString(gvUser.Rows(index).Cells(28).Value)
                    cbgateentry.Checked = Convert.ToString(gvUser.Rows(index).Cells(29).Value)
                    CBWEIGHMENTSLIP.Checked = Convert.ToString(gvUser.Rows(index).Cells(30).Value)
                    CBDETAILSEXPORTINEXCEL.Checked = Convert.ToString(gvUser.Rows(index).Cells(31).Value)
                    CBVEHICLEWTSPLITFORGROUPING.Checked = Convert.ToString(gvUser.Rows(index).Cells(32).Value)
                    CBVEHICLEACTIVITYCHANGE.Checked = Convert.ToString(gvUser.Rows(index).Cells(33).Value)
                    CBVEHICLENOCHANGE.Checked = Convert.ToString(gvUser.Rows(index).Cells(34).Value)
                    CBUPDATELINEITEMS.Checked = Convert.ToString(gvUser.Rows(index).Cells(35).Value)
                    CBWEIGHMENTSLIPDIRECT.Checked = Convert.ToString(gvUser.Rows(index).Cells(36).Value)
                    CBVENDORWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(37).Value)
                    CBCUSTOMERWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(38).Value)
                    CBPOWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(39).Value)
                    CBSOWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(40).Value)
                    CBMATERIALWISE.Checked = Convert.ToString(gvUser.Rows(index).Cells(41).Value)
                    cbVehicleTareWtAuto.Checked = Convert.ToString(gvUser.Rows(index).Cells(42).Value)
                    CBTRANSPORTERMASTER.Checked = Convert.ToString(gvUser.Rows(index).Cells(43).Value)
                    CBDETAILSREPORT.Checked = Convert.ToString(gvUser.Rows(index).Cells(44).Value)
                    CBWEIGHMENTDATASPLITTINGOUTBOUND.Checked = Convert.ToString(gvUser.Rows(index).Cells(45).Value)
                    CBCONTRACTORITEMOUTPASS.Checked = Convert.ToString(gvUser.Rows(index).Cells(46).Value)

                Next i
            End If
        Catch ex As Exception
            'MessageBox.Show(ex.Message)
        End Try
    End Sub
End Class