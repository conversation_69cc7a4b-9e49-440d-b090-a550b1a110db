﻿Public Class frmVehicle
    Dim cc As New Class1
    Private Sub frmVehicle_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False

        'Headings
        ListView1.Columns.Add("Vehicle_no")
        ListView1.Columns.Add("Type_Of_Vehicle")
        ListView1.Columns.Add("EntryDateTime")

        ListView1.Items.Clear()

        Dim str As String = "select * from tbl_GE_Hdr where Vehicle_Status = 'IN' and Vehicle_No like '%" & Trim(txtSearch.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No"
        Try

            dr = cc.GetDataReader(str)
            While dr.Read

                Dim lvi As New ListViewItem

                lvi.Text = CStr(dr("Vehicle_no"))
                lvi.SubItems.Add(dr("Type_Of_Vehicle"))
                lvi.SubItems.Add(dr("EntryDateTime"))


                ListView1.Items.Add(lvi)

            End While

            If dr.HasRows Then

                ListView1.TopItem.Selected = True

                ListView1.TopItem.Focused = True

                ListView1.TopItem.EnsureVisible()

                ListView1.Select()

            End If

            For i As Integer = 0 To ListView1.Columns.Count - 1
                ListView1.Columns(i).Width = -2
            Next

        Catch ex As Exception

            MsgBox(ex.Message, MsgBoxStyle.Critical, "Error")

        Finally

            'connection.Close()

        End Try
        dr.Close()
    End Sub

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        On Error GoTo err
        Dim ExcelObj As Object

        Dim ExcelBook As Object

        Dim ExcelSheet As Object

        Dim i As Integer


        ExcelObj = CreateObject("Excel.Application")
        ExcelBook = ExcelObj.Workbooks.Add

        ExcelSheet = ExcelBook.Worksheets(1)



        With ExcelSheet

            For i = 0 To ListView1.Columns.Count - 1

                .Cells(1, i) = ListView1.Columns(i).Text

            Next i

            For i = 0 To ListView1.Items.Count - 1

                .Cells(i + 1, 1) = ListView1.Items(i).Text

                .Cells(i + 1, 2) = ListView1.Items(i).SubItems(1)

                .Cells(i + 1, 3) = ListView1.Items(i).SubItems(2)
            Next

        End With



        ExcelObj.Visible = True


err:
        Err.Clear()
    End Sub

    Private Sub ListView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView1.DoubleClick
        On Error GoTo err
        If CallFromVehTareWtMst = 1 Then

            frmVehicleWt.txtVehicleNo.Text = Trim(ListView1.SelectedItems(0).Text)

            CallFromVehTareWtMst = 0

        ElseIf Call_From_Check_Post = "1" Then
            'frmRouteMaster.Text6.Text = Trim(ListView1.SelectedItems(0).Text)
            'Call_From_Check_Post = "0"
        Else
            frmWM.txtVehicleNo.Text = Trim(ListView1.SelectedItems(0).Text)
            'frmWM.Text6.SetFocus
            vehicle_sel = 1
        End If
        Me.Close()
        'frmVehicleWt.Text6.BackColor = RGB(255, 255, 255)
err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub txtSearch_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtSearch.KeyPress
        btnSearch_Click(sender, e)
        txtSearch.Focus()
    End Sub
End Class