﻿Public Class frmVehicleStatus
    Dim cc As New Class1
    Private Sub frmVehicleStatus_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_vehicle_tare_wt_mst"
            ds = cc.GetDataset(str)
            gvVehicleStatus.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtVehicleNo.Text) <> "" Then
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_ins_tbl_vehicle_tare_wt_mst"
            cm.Parameters.AddWithValue("@val_Vehicle_No", Trim(txtVehicleNo.Text))
            cm.Parameters.AddWithValue("@val_Vehicle_Group", "")
            cm.Parameters.AddWithValue("@val_Vehicle_Tare_WT", 0 + Val(txtTareWt.Text))
            cm.Parameters.AddWithValue("@val_LastUpdatedOn", Format(Today.Date, "dd-MMM-yyyy"))
            cm.Parameters.AddWithValue("@val_UpdatedBY", User_ID)
            cm.Parameters.AddWithValue("@val_BlackLited", cbBlackListed.Checked)
            cm.Parameters.AddWithValue("@val_Warning", cbWarned.Checked)
            cm.Parameters.AddWithValue("@val_Remarks", Trim(txtRemarks.Text) & "")
            cm.Parameters.AddWithValue("@val_TareWtValidUpto", DateTimePicker1.Value)
            cm.ExecuteNonQuery()
            loadGrid()
            MsgBox("Vehicle Status updated successfully !", vbInformation, "Electrosteel Castings Limited.")
            txtVehicleNo.Enabled = True
            Call Clear_all()
        Else
            MsgBox("Invalid Vehicle Number", vbInformation, "Electrosteel Castings Limited.")
        End If

    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtVehicleNo.Enabled = True
        Call Clear_all()
    End Sub

    Private Sub btnExportBlacklisted_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExportBlacklisted.Click

    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub txtVehicleNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVehicleNo.KeyPress
        If AscW(e.KeyChar) = 13 Then
            dr = cc.GetDataReader("select * from tbl_vehicle_tare_wt_mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'")
            Try
                While dr.Read
                    txtVehicleGroup.Text = dr("Vehicle_Group")
                    txtTareWt.Text = dr("Vehicle_Tare_Wt")
                    txtUpdatedOn.Text = dr("Last_updated_on")
                    txtUpdatedBy.Text = dr("UpdatedBy")
                    txtRemarks.Text = dr("Remarks")
                    If dr("BlackListed") = 1 Then
                        cbBlackListed.Checked = 1
                    Else
                        cbBlackListed.Checked = 0
                    End If

                    If dr("Warning") = 1 Then
                        cbWarned.Checked = 1
                    Else
                        cbWarned.Checked = 0
                    End If

                    MsgBox("Vehicle Status already exists  !", vbInformation, "Electrosteel Castings Limited.")
                    txtVehicleNo.Enabled = False
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            btnUpdate.Focus()
        End If
    End Sub
    Private Sub Clear_all()
        txtRemarks.Text = ""
        txtVehicleNo.Text = ""
        cbBlackListed.Checked = 0
        cbWarned.Checked = 0

        txtVehicleGroup.Text = ""
        txtTareWt.Text = ""

        txtUpdatedOn.Text = ""

        txtUpdatedBy.Text = ""
    End Sub
    Private Sub txtVehicleNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVehicleNo.TextChanged
        txtVehicleNo.Text = UCase(Trim(txtVehicleNo.Text))
        txtVehicleNo.SelectionStart = Len(txtVehicleNo.Text)
    End Sub

    Private Sub gvVehicleStatus_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvVehicleStatus.CellMouseClick
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvVehicleStatus.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvVehicleStatus.SelectedRows(i).Index)

                    txtVehicleNo.Text = Convert.ToString(gvVehicleStatus.Rows(index).Cells(1).Value)
                    cbBlackListed.Checked = Convert.ToString(gvVehicleStatus.Rows(index).Cells(6).Value)
                    cbWarned.Checked = Convert.ToString(gvVehicleStatus.Rows(index).Cells(7).Value)
                    txtRemarks.Text = Convert.ToString(gvVehicleStatus.Rows(index).Cells(8).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Try
            Dim str As String = "SELECT * FROM tbl_vehicle_tare_wt_mst where Vehicle_No like '%" & txtSearch.Text & "%'"
            ds = cc.GetDataset(str)
            gvVehicleStatus.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
End Class