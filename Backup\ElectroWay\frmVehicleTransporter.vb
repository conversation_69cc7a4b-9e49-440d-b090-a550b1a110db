﻿Public Class frmVehicleTransporter
    Dim cc As New Class1
    Private Sub frmVehicleTransporter_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_Vehicle_Transporter"
            ds = cc.GetDataset(str)
            gvVehicleTransport.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        Try
            If Trim(txtVehicleNo.Text) <> "" Then
                ds = cc.GetDataset("select * from tbl_Vehicle_Transporter where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vendor_code = '" & Trim(txtVendorCode.Text) & "' and Customer_Code = '" & Trim(txtCustomerCode.Text) & "'")
                If ds.Tables(0).Rows.Count = 0 Then
                    'WB_ID = rec1.Fields(0)
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.Connection = con
                    cm.CommandType = CommandType.Text
                    cm.CommandText = "insert into tbl_Vehicle_Transporter values ('" & Trim(txtVehicleNo.Text) & "' , '" & Trim(txtTransporterCode.Text) & "' , '" & Trim(txtTransporterName.Text) & "', '" & Trim(txtVendorCode.Text) & "' , '" & Trim(txtCustomerCode.Text) & "' )"
                    cm.ExecuteNonQuery()
                    loadGrid()
                    MsgBox("Vehicle with Transporter has been updated successfully !", vbInformation, "ElectroWay")
                    txtTransporterCode.Text = ""
                    txtVendorCode.Text = ""
                    txtVehicleNo.Text = ""
                    txtCustomerCode.Text = ""
                    txtTransporterName.Text = ""
                Else
                    Dim ans = MsgBox("Vehicle with Transporter data already exists ! Do you want to update ?", vbYesNo, "ElectroWay")

                    If ans = vbYes Then
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        cm.Connection = con
                        cm.CommandType = CommandType.Text
                        cm.CommandText = "delete from tbl_Vehicle_Transporter where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vendor_code = '" & Trim(txtVendorCode.Text) & "' and Customer_Code = '" & Trim(txtCustomerCode.Text) & "'"
                        cm.ExecuteNonQuery()

                        cm.Connection = con
                        cm.CommandType = CommandType.Text
                        cm.CommandText = "insert into tbl_Vehicle_Transporter values ('" & Trim(txtVehicleNo.Text) & "' , '" & Trim(txtTransporterCode.Text) & "' , '" & Trim(txtTransporterName.Text) & "', '" & Trim(txtVendorCode.Text) & "' , '" & Trim(txtCustomerCode.Text) & "' )"
                        cm.ExecuteNonQuery()
                        loadGrid()
                        MsgBox("Vehicle with Transporter has been updated successfully !", vbInformation, "ElectroWay")
                        txtTransporterCode.Text = ""
                        txtVendorCode.Text = ""
                        txtVehicleNo.Text = ""
                        txtCustomerCode.Text = ""
                        txtTransporterName.Text = ""
                    End If
                End If
                ds.Dispose()
                ds.Clear()
            Else
                MsgBox("Blank Vehicle with Transporter Master Record cannot be updated !", vbInformation, "ElectroWay")
            End If
        Catch ex As Exception
            MsgBox(Err.Description)
        End Try
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtTransporterCode.Text = ""
        txtVendorCode.Text = ""
        txtVehicleNo.Text = ""
        txtCustomerCode.Text = ""
        txtTransporterName.Text = ""
        txtVehicleNo.Focus()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        Try
            Dim ans = MsgBox("Are you sure to delete this Vehicle with Transporter ?", vbYesNo, "ElectroWay")

            If ans = vbYes Then
                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "DELETE FROM tbl_vehicle_Transporter where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Transporter_Code ='" & Trim(txtTransporterCode.Text) & "' and Vendor_Code ='" & Trim(txtVendorCode.Text) & "' and Customer_Code = '" & Trim(txtCustomerCode.Text) & "'"
                cm.ExecuteNonQuery()
                loadGrid()
                MsgBox("Vehicle with Transporter mapping record has been Deleted successfully !", vbInformation, "ElectroWay")
                txtTransporterCode.Text = ""
                txtVendorCode.Text = ""
                txtVehicleNo.Text = ""
                txtCustomerCode.Text = ""
                txtTransporterName.Text = ""
                ''txtTransporterCode.SetFocus

            End If
            ''Command4.Enabled = False
        Catch ex As Exception

        End Try
    End Sub

    Private Sub txtTransporterCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransporterCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            'txtTransporterName.SetFocus
            dr = cc.GetDataReader("select * from tbl_Transporter_mst where Transporter_Code = '" & Trim(txtTransporterCode.Text) & "'")
            Try
                While dr.Read
                    txtTransporterName.Text = dr("Transporter_Name")
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub txtVehicleNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtVehicleNo.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "VEHICLE_TRANSPORTER"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub txtVehicleNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVehicleNo.KeyPress
        If AscW(e.KeyChar) = 13 Then
            Help_callfrom = "VEHICLE_TRANSPORTER"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub gvVehicleTransport_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvVehicleTransport.CellMouseClick
        Dim rowcount, index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvVehicleTransport.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvVehicleTransport.SelectedRows(i).Index)
                    txtVehicleNo.Text = Convert.ToString(gvVehicleTransport.Rows(index).Cells(0).Value)
                    txtTransporterCode.Text = Convert.ToString(gvVehicleTransport.Rows(index).Cells(1).Value)
                    txtTransporterName.Text = Convert.ToString(gvVehicleTransport.Rows(index).Cells(2).Value)
                    txtVendorCode.Text = Convert.ToString(gvVehicleTransport.Rows(index).Cells(3).Value)
                    txtCustomerCode.Text = Convert.ToString(gvVehicleTransport.Rows(index).Cells(4).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Try
            Dim str As String = "SELECT * FROM tbl_Vehicle_Transporter where Vehicle_No like '%" & txtSearch.Text & "%' or Transporter_Code like '%" & txtSearch.Text & "%' or Transporter_Name like '%" & txtSearch.Text & "%' or Vendor_Code like '%" & txtSearch.Text & "%' or Customer_Code like '%" & txtSearch.Text & "%'"
            ds = cc.GetDataset(str)
            gvVehicleTransport.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
End Class