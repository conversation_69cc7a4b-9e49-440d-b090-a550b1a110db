﻿Public Class frmVehicleWt
    'Dim MSComm1 As New MSCommLib.MSComm
    Dim cc As New Class1
    Dim lblMessage As String = ""

    '--------------------------------------
    Private readBuffer As String = String.Empty
    Private Bytenumber As Integer
    Private ByteToRead As Integer
    Private byteEnd(2) As Char
    Private comOpen As Boolean
    Private Sub frmVehicleWt_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim PortNum, BaudRateSettings As String
        Try
            txtUpdatedBy.Text = User_ID
            dtTareWtValidUpto.Value = Today.Date
            ''LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL---------2400,e,7,1
            dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Sys_loc_IP & "' and Node_Name = 'WEIGH BRIDGE'")
            Try
                While dr.Read
                    PortNum = dr("Port_Number")
                    BaudRateSettings = dr("Settings")
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            ''LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL
            'MSComm1.CommPort = PortNum  ' comm port no.
            'MSComm1.Settings = BaudRateSettings
            'MSComm1.RThreshold = 24    'no. of chr to recive
            'MSComm1.InputLen = 0  '  no. of chr on which oncomm  event fires
            'MSComm1.PortOpen = True
            '------------------------------------
            Dim Portnames As String() = System.IO.Ports.SerialPort.GetPortNames
            If Portnames Is Nothing Then
                MsgBox("There are no Com Ports detected!")
                'Me.Close()
            End If
            cboComPort.Items.AddRange(Portnames)
            cboComPort.Text = Portnames(0)
            cboBaudRate.Text = "2400"
            '--------------------------------------
            dtTareWtValidUpto.Value = Today.Date
            '----------------------------------------
            With SerialPort1

                .ParityReplace = &H3B                    ' replace ";" when parity error occurs 
                .PortName = cboComPort.Text
                .BaudRate = CInt(cboBaudRate.Text)
                .Parity = IO.Ports.Parity.None
                .DataBits = 7
                .StopBits = IO.Ports.StopBits.One
                .Handshake = IO.Ports.Handshake.None
                .RtsEnable = False
                .ReceivedBytesThreshold = 1             'threshold: one byte in buffer > event is fired 
                .NewLine = vbCr         ' CR must be the last char in frame. This terminates the SerialPort.readLine 
                .ReadTimeout = 10000

            End With

            ' check whether device is avaiable: 
            Try
                SerialPort1.Open()
                comOpen = SerialPort1.IsOpen
            Catch ex As Exception
                comOpen = False
                MsgBox("Error Open: " & ex.Message)
                'picOpen.BackColor = Color.Red
            End Try

            'If comOpen Then
            '    picOpen.BackColor = Color.Green
            '    cboComPort.Enabled = False
            '    cboBaudRate.Enabled = False
            'End If
            '---------------------------------------------------
        Catch ex As Exception
            If Err.Description <> "" Then
                MsgBox(Err.Number & " :- " & Err.Description)
            End If
        End Try
    End Sub
#Region "ComPort read data"

    ''' <summary> 
    ''' async read on secondary thread 
    ''' </summary> 
    Private Sub SerialPort1_DataReceived(ByVal sender As System.Object, _
                                         ByVal e As System.IO.Ports.SerialDataReceivedEventArgs) _
                                         Handles SerialPort1.DataReceived
        If comOpen Then
            Try
                byteEnd = SerialPort1.NewLine.ToCharArray

                ' get number off bytes in buffer 
                Bytenumber = SerialPort1.BytesToRead

                ' read one byte from buffer 
                'ByteToRead = SerialPort1.ReadByte() 

                ' read one char from buffer 
                'CharToRead = SerialPort1.ReadChar() 

                ' read until string "90" 
                'readBuffer1 = SerialPort1.ReadTo("90") 

                ' read entire string until .Newline  
                readBuffer = SerialPort1.ReadLine()

                'data to UI thread 
                Me.Invoke(New EventHandler(AddressOf DoUpdate))

            Catch ex As Exception
                MsgBox("read " & ex.Message)
            End Try
        End If
    End Sub

    ''' <summary> 
    ''' update received string in UI 
    ''' </summary> 
    ''' <remarks></remarks> 
    Public Sub DoUpdate(ByVal sender As Object, ByVal e As System.EventArgs)
        txtTareWt.Text = readBuffer
        'Timer1.Enabled = True
    End Sub

#End Region
    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtVehicleNo.Text) <> "" And IsNumeric(Trim(txtTareWt.Text)) = True Then
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_ins_tbl_vehicle_tare_wt_mst"
            cm.Parameters.AddWithValue("@val_Vehicle_No", txtVehicleNo.Text.Trim)
            cm.Parameters.AddWithValue("@val_Vehicle_Group", txtVehicleGroup.Text.Trim)
            cm.Parameters.AddWithValue("@val_Vehicle_Tare_WT", txtTareWt.Text.Trim)
            cm.Parameters.AddWithValue("@val_LastUpdatedOn", Format(Today.Date, "dd-MM-yyyy"))
            cm.Parameters.AddWithValue("@val_UpdatedBY", User_ID)
            cm.Parameters.AddWithValue("@val_BlackLited", cbBlackListed.Checked)
            cm.Parameters.AddWithValue("@val_Warning", cbWarned.Checked)
            cm.Parameters.AddWithValue("@val_Remarks", txtRemarks.Text.Trim)
            cm.Parameters.AddWithValue("@val_TareWtValidUpto", Format(dtTareWtValidUpto.Value, "dd-MM-yyyy"))
            cm.ExecuteNonQuery()
            MsgBox("Vehicle tare Wt. master updated successfully !", vbInformation, "Electrosteel Castings Limited.")
            txtVehicleNo.Enabled = True
            Call Clear_all()
        Else
            MsgBox("Invalid Vehicle tare Wt. data for master updation", vbInformation, "Electrosteel Castings Limited.")
        End If
    End Sub

    Private Sub txtVehicleNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtVehicleNo.KeyDown
        If e.KeyCode = 112 Or e.KeyCode = 40 Then
            'CallFromVehTareWtMst = 1
            '---------------------------------
            Dim frmTareWthelp1 As New frmTareWthelp
            frmTareWthelp1.Show()
            frmTareWthelp1.ListView1.Clear()
            frmTareWthelp1.ListView1.Items.Clear()
            frmTareWthelp1.ListView1.View = View.Details
            frmTareWthelp1.ListView1.GridLines = True
            frmTareWthelp1.ListView1.FullRowSelect = True
            frmTareWthelp1.ListView1.HideSelection = False
            frmTareWthelp1.ListView1.MultiSelect = False

            'Headings
            frmTareWthelp1.ListView1.Columns.Add("Vehicle_no")
            frmTareWthelp1.ListView1.Columns.Add("Vehicle_Tare_WT")
            frmTareWthelp1.ListView1.Columns.Add("TareWtValidUpto")
            'LV.Items.Clear()
            '---------------------------------
            dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst order by Vehicle_No")
            Try
                While dr.Read
                    Dim i As Integer = frmTareWthelp1.ListView1.Items.Count + 1
                    Dim lvi As New ListViewItem

                    lvi.Text = CStr(dr("Vehicle_no"))
                    'lvi.SubItems.Add(dr("Vehicle_no"))
                    lvi.SubItems.Add(dr("Vehicle_Tare_WT"))
                    lvi.SubItems.Add(dr("TareWtValidUpto"))

                    frmTareWthelp1.ListView1.Items.Add(lvi)
                    'frmTareWthelp.ListView1.Items.Add(i, dr("Vehicle_no"))
                    '            frmTareWthelp.ListView1.Items(i).ListSubItems.Add (1), , rec1.Fields("Vehicle_Tare_WT")
                    '            frmTareWthelp.ListView1.Items(i).ListSubItems.Add (2), , rec1.Fields("TareWtValidUpto")

                    '' ListView1.ListItems(k).ListSubItems.Add (m), , ""
                    ''ListView1.ListItems(i).ListSubItems.Add (1), , rec1.Fields("PO_NO") & ""
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            For i As Integer = 0 To frmTareWthelp1.ListView1.Columns.Count - 1
                frmTareWthelp1.ListView1.Columns(i).Width = -2
            Next
            If frmTareWthelp1.ListView1.Items.Count > 0 Then
                'frmTareWthelp.Show()
            Else
                MsgBox("No Vehicle exists !", vbInformation)
            End If
            'frmVehicle.Show vbModal
        End If
    End Sub

    Private Sub txtVehicleNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVehicleNo.KeyPress
        If AscW(e.KeyChar) = 13 Then
            dr = cc.GetDataReader("select * from tbl_vehicle_tare_wt_mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'")
            Try
                While dr.Read
                    txtVehicleGroup.Text = dr("Vehicle_Group")
                    txtTareWt.Text = dr("Vehicle_Tare_Wt")
                    txtLastUpdatedOn.Text = dr("Last_updated_on")
                    txtUpdatedBy.Text = dr("UpdatedBy")
                    txtRemarks.Text = dr("Remarks")
                    If dr("BlackListed") = 1 Then
                        cbBlackListed.Checked = 1
                    Else
                        cbBlackListed.Checked = 0
                    End If

                    If dr("Warning") = 1 Then
                        cbWarned.Checked = 1
                    Else
                        cbWarned.Checked = 0
                    End If

                    MsgBox("Vehicle No. already exists in Tare Wt master !", vbInformation, "Electrosteel Castings Limited.")
                    txtVehicleNo.Enabled = False
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        Else
            btnUpdate.Focus()
        End If
    End Sub

    Private Sub txtVehicleNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVehicleNo.TextChanged
        txtVehicleNo.Text = UCase(Trim(txtVehicleNo.Text))
        txtVehicleNo.SelectionStart = Len(txtVehicleNo.Text)
    End Sub

    'Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
    '    If lblMessage.ToString.Trim <> "" Then
    '        Exit Sub
    '    End If
    '    Try
    '        If OperatMode = "AUTO" Then
    '            ''''''''''''MsgBox MSComm1.Input
    '            'Text20.Text = Format(Time, "HH-MM-SS")

    '            Dim val_text As String = MSComm1.Input
    '            If Len(val_text) >= 24 Then       '''''''>= 11 Then    ---  set for OLD Weigh Bridge and tested working Fine
    '                ''''''''''
    '                Dim Weight As String = Mid(val_text, 4, 11)
    '                ''''''''''
    '                Text32.Text = Mid(val_text, 4, 11)
    '                ''''''''''
    '                Text33.Text = Trim(Text32.Text)
    '                ''''''''''
    '                ''''''''''       ''If Len(Text3.Text) > 0 Then
    '                Dim WtVal As String
    '                For num = 1 To Len(Text33.Text)

    '                    If (IsNumeric(Mid(Text33.Text, num, 1)) = True) Then
    '                        WtVal = WtVal & Mid(Text33.Text, num, 1)

    '                    ElseIf Mid(Text33.Text, num, 1) = " " And Mid(Text33.Text, num + 1, 1) = "k" Then
    '                        txtTareWt.Text = WtVal
    '                        Exit For
    '                    End If
    '                    ''''''''''
    '                    ''''''''''                 End If
    '                    ''''''''''
    '                Next num

    '                WtVal = ""
    '                val_text = ""
    '                Text32.Text = ""
    '                Text33.Text = 0
    '            End If
    '            val_text = ""
    '            Text32.Text = ""
    '            Text33.Text = 0
    '        End If
    '    Catch ex As Exception
    '        If Err.Number <> 0 Then
    '            lblMessage = ex.Message
    '            MsgBox(Err.Number & "   " & Err.Description)
    '        End If
    '    End Try
    'End Sub
    Private Sub Clear_all()
        txtVehicleGroup.Text = ""
        txtTareWt.Text = ""

        txtLastUpdatedOn.Text = ""
        txtRemarks.Text = ""
        txtUpdatedBy.Text = ""
        txtVehicleNo.Text = ""
        cbBlackListed.Checked = 0
        cbWarned.Checked = 0
    End Sub
End Class