﻿Imports System.Configuration
Imports System.Data.SqlClient

Public Class Class1

    Private ReadOnly connectionString As String = ConfigurationManager.ConnectionStrings("MyDatabase").ConnectionString

    ' Function to get a scalar value
    Public Function GetScalarValue(ByVal sql As String, Optional ByVal parameters As List(Of SqlParameter) = Nothing) As Object
        Using con As New SqlConnection(connectionString)
            Try
                Using cm As New SqlCommand(sql, con)
                    If parameters IsNot Nothing Then cm.Parameters.AddRange(parameters.ToArray())
                    con.Open()
                    Return cm.ExecuteScalar()
                End Using
            Catch ex As Exception
                Console.WriteLine("Error: " & ex.Message)
                SendFormattedErrorMail(ex, sql)
                Return Nothing
            End Try
        End Using
    End Function

    ' Function to get SqlDataReader
    Public Function GetDataReader(ByVal sql As String, Optional ByVal parameters As List(Of SqlParameter) = Nothing) As SqlDataReader
        Dim con As New SqlConnection(connectionString)
        Try
            Dim cm As New SqlCommand(sql, con)
            If parameters IsNot Nothing Then cm.Parameters.AddRange(parameters.ToArray())
            con.Open()
            Return cm.ExecuteReader(CommandBehavior.CloseConnection)
        Catch ex As Exception
            Console.WriteLine("Error: " & ex.Message)
            SendFormattedErrorMail(ex, sql)
            con.Close()
            Return Nothing
        End Try
    End Function

    ' Function to get DataSet
    Public Function GetDataset(ByVal sql As String, Optional ByVal parameters As List(Of SqlParameter) = Nothing) As DataSet
        Dim ds As New DataSet()
        Try
            Using con As New SqlConnection(connectionString)
                Using cm As New SqlCommand(sql, con)
                    If parameters IsNot Nothing Then cm.Parameters.AddRange(parameters.ToArray())
                    Using da As New SqlDataAdapter(cm)
                        con.Open()
                        da.Fill(ds)
                    End Using
                End Using
            End Using
        Catch ex As Exception
            Console.WriteLine("Error: " & ex.Message)
            SendFormattedErrorMail(ex, sql)
        End Try
        Return ds
    End Function

    ' Function to get DataTable
    Public Function GetDataTable(ByVal sql As String, Optional ByVal parameters As List(Of SqlParameter) = Nothing) As DataTable
        Dim dt As New DataTable()
        Try
            Using con As New SqlConnection(connectionString)
                Using cm As New SqlCommand(sql, con)
                    If parameters IsNot Nothing Then cm.Parameters.AddRange(parameters.ToArray())
                    Using da As New SqlDataAdapter(cm)
                        con.Open()
                        da.Fill(dt)
                    End Using
                End Using
            End Using
        Catch ex As Exception
            Console.WriteLine("Error: " & ex.Message)
            SendFormattedErrorMail(ex, sql)
        End Try
        Return dt
    End Function

    ' Function to get SqlDataAdapter
    Public Function GetDataAdapter(ByVal sql As String, Optional ByVal parameters As List(Of SqlParameter) = Nothing) As SqlDataAdapter
        Try
            Dim con As New SqlConnection(connectionString)
            Dim cm As New SqlCommand(sql, con)
            If parameters IsNot Nothing Then cm.Parameters.AddRange(parameters.ToArray())
            Return New SqlDataAdapter(cm)
        Catch ex As Exception
            SendFormattedErrorMail(ex, sql)
            Return Nothing
        End Try
    End Function

    ' Function to execute Non-Query SQL statements
    Public Function Execute(ByVal sql As String, Optional ByVal parameters As List(Of SqlParameter) = Nothing) As Boolean
        Try
            Using con As New SqlConnection(connectionString)
                Using cm As New SqlCommand(sql, con)
                    If parameters IsNot Nothing Then cm.Parameters.AddRange(parameters.ToArray())
                    con.Open()
                    Dim rowsAffected As Integer = cm.ExecuteNonQuery()
                    Return rowsAffected > 0
                End Using
            End Using
        Catch ex As Exception
            Console.WriteLine("Error: " & ex.Message)
            SendFormattedErrorMail(ex, sql)
            Return False
        End Try
    End Function
End Class
