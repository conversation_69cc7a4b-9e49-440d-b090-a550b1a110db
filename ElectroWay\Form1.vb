﻿Public Class Form1

    Private Sub Form1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim lvwItem As New ListViewItem()

        LV.Clear()
        LV.View = View.Details
        LV.GridLines = True
        LV.FullRowSelect = True
        LV.HideSelection = False
        LV.MultiSelect = False

        'Headings
        LV.Columns.Add("PO / SO No.")
        LV.Columns.Add("DO No.")
        LV.Columns.Add("DO/PO Line Item")
        LV.Columns.Add("Material Code")
        LV.Columns.Add("Material Description")
        LV.Columns.Add("DO/Ch Qty.")
        LV.Columns.Add("Unit")
        LV.Columns.Add("SAP Gate Entry No.")
        LV.Columns.Add("Ch. No.")
        LV.Columns.Add("Challan Date")
        LV.Columns.Add("RR No.")
        LV.Columns.Add("RR Date")
        LV.Columns.Add("LR No.")
        LV.Columns.Add("LR Date")
        LV.Columns.Add("Rake No.")
        LV.Columns.Add("SO Line Item")
        LV.Columns.Add("Customer/Vendor Name")

        LV.Items.Clear()
        '-----------------------------

        'Dim lvwItem As New ListViewItem()
        lvwItem.Text = "PO / SO No."
        lvwItem.SubItems.Add("DO No.")
        lvwItem.SubItems.Add("DO/PO Line Item")
        lvwItem.SubItems.Add("Material Code")
        lvwItem.SubItems.Add("Material Description")
        lvwItem.SubItems.Add("DO/Ch Qty.")
        lvwItem.SubItems.Add("Unit")
        lvwItem.SubItems.Add("SAP Gate Entry No.")
        lvwItem.SubItems.Add("Ch. No.")
        lvwItem.SubItems.Add("Challan Date")
        lvwItem.SubItems.Add("RR No.")
        lvwItem.SubItems.Add("RR Date")
        lvwItem.SubItems.Add("LR No.")
        lvwItem.SubItems.Add("LR Date")
        lvwItem.SubItems.Add("Rake No.")
        lvwItem.SubItems.Add("SO Line Item")
        lvwItem.SubItems.Add("Customer/Vendor Name")

        LV.Items.Add(lvwItem)

        For i As Integer = 0 To LV.Columns.Count - 1
            LV.Columns(i).Width = -2
        Next
    End Sub
End Class