﻿Imports System.IO
Imports System.Net.Mail
Imports System.Configuration
Imports System.Net
Imports System.DirectoryServices.AccountManagement
Imports System.DirectoryServices
Imports System.Text

Module ErrorHandling

    Public Sub LogMessage(ByVal message As String)
        Dim logEntry As String = $"[{DateTime.Now}] {message}"
        Console.WriteLine(logEntry)
        File.AppendAllText("ElectrowayErrors.txt", logEntry & Environment.NewLine)
    End Sub

    Public Sub SendFormattedErrorMail(ByVal ex As Exception, Optional ByVal lastSQLQuery As String = "", Optional ByVal additionalData As String = "")
        Try
            ' Extract details from stack trace
            Dim stackTrace As New System.Diagnostics.StackTrace(ex, True)
            Dim methodName As String = "Unknown Method"
            Dim formName As String = "Unknown Form"
            Dim lineNumber As String = "N/A"

            ' Parse stack trace to extract the correct method, form, and line number
            For Each frame As System.Diagnostics.StackFrame In stackTrace.GetFrames()
                If frame.GetFileLineNumber() > 0 Then
                    methodName = frame.GetMethod().Name
                    formName = frame.GetMethod().DeclaringType.Name
                    lineNumber = frame.GetFileLineNumber().ToString()
                    Exit For
                End If
            Next

            ' Capture system information
            Dim machineName As String = Environment.MachineName
            Dim userName As String = Environment.UserName
            Dim osVersion As String = Environment.OSVersion.ToString()
            Dim appVersion As String = Application.ProductVersion
            Dim localIP As String = GetLocalIPAddress()
            Dim publicIP As String = GetPublicIPAddress()

            ' Get AD User Info
            Dim adUser As ADUserInfo = GetADUserInfo(userName)

            ' Get AD User Info
            Dim LoggedadUser As ADUserInfo = GetADUserInfo(UserNameinDB)

            ' Prepare the error details in an HTML format
            Dim errorDetails As String = $"
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; font-size: 14px; }}
                .error-title {{ font-size: 16px; font-weight: bold; color: red; }}
                .error-details {{ margin-top: 10px; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; }}
                .highlight {{ font-weight: bold; color: blue; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 10px; }}
                th, td {{ border: 1px solid black; padding: 5px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <p>[{DateTime.Now:MM/dd/yyyy hh:mm:ss tt}]</p>

            <div class='error-details'>
                <p><b>Error Message:</b> {ex.Message}</p>
                <p><b>Inner Exception:</b> {If(ex.InnerException IsNot Nothing, ex.InnerException.Message, "N/A")}</p>
                <p><b>Form Name:</b> <span class='highlight'>{formName}</span></p>
                <p><b>Method Name:</b> <span class='highlight'>{methodName}</span></p>
                <p><b>Line Number:</b> <span class='highlight'>{lineNumber}</span></p>
                <p><b>Stack Trace:</b> <br> <pre>{ex.StackTrace.Replace(vbNewLine, "<br>")}</pre></p>
            </div>"

            ' Include System Information
            errorDetails &= $"
        <table>
            <tr><th colspan='2'>🖥 System Information</th></tr>
            <tr><td><b>Machine Name:</b></td><td>{machineName}</td></tr>
            <tr><td><b>OS Version:</b></td><td>{osVersion}</td></tr>
            <tr><td><b>Application Version:</b></td><td>{appVersion}</td></tr>
            <tr><td><b>Local IP:</b></td><td>{localIP}</td></tr>
            <tr><td><b>Public IP:</b></td><td>{publicIP}</td></tr>
        </table>"

            ' Include AD User Info
            errorDetails &= $"
        <table>
            <tr><th colspan='2'>👤 Application User Information</th></tr>
            <tr><td><b>Username:</b></td><td>{LoggedadUser.Username}</td></tr>
            <tr><td><b>Full Name:</b></td><td>{LoggedadUser.FullName}</td></tr>
            <tr><td><b>Email:</b></td><td>{LoggedadUser.Email}</td></tr>
            <tr><td><b>Mobile:</b></td><td>{LoggedadUser.Mobile}</td></tr>
            <tr><td><b>Department:</b></td><td>{LoggedadUser.Department}</td></tr>
            <tr><td><b>Manager:</b></td><td>{LoggedadUser.Manager}</td></tr>
        </table>"


            ' Include AD User Info
            errorDetails &= $"
        <table>
            <tr><th colspan='2'>👤 AD User Information</th></tr>
            <tr><td><b>Username:</b></td><td>{adUser.Username}</td></tr>
            <tr><td><b>Full Name:</b></td><td>{adUser.FullName}</td></tr>
            <tr><td><b>Email:</b></td><td>{adUser.Email}</td></tr>
            <tr><td><b>Mobile:</b></td><td>{adUser.Mobile}</td></tr>
            <tr><td><b>Department:</b></td><td>{adUser.Department}</td></tr>
            <tr><td><b>Manager:</b></td><td>{adUser.Manager}</td></tr>
        </table>"

            ' Include last executed SQL Query if provided
            If Not String.IsNullOrEmpty(lastSQLQuery) Then
                errorDetails &= $"
            <table>
                <tr><th>Last Executed SQL Query</th></tr>
                <tr><td><pre>{lastSQLQuery}</pre></td></tr>
            </table>"
            End If

            ' If additional data is provided, display it
            If Not String.IsNullOrEmpty(additionalData) Then
                errorDetails &= $"
            <table>
                <tr><th>Data Causing Error</th></tr>
                <tr><td>{additionalData}</td></tr>
            </table>"
            End If

            errorDetails &= "</body></html>"

            Dim smtpHost As String = ConfigurationManager.AppSettings("smtpHost")
            Dim smtpPort As Integer = Integer.Parse(ConfigurationManager.AppSettings("smtpPort"))
            Dim smtpUsername As String = ConfigurationManager.AppSettings("smtpUserName")
            Dim smtpPassword As String = ConfigurationManager.AppSettings("smtpPassword")
            Dim smtpEnableSSL As Boolean = Boolean.Parse(ConfigurationManager.AppSettings("smtpEnableSSL"))

            'Dim logEntry As String = $"[{DateTime.Now}] {Message}"

            Dim smtpClient = New SmtpClient(smtpHost) With {
            .Port = smtpPort,
            .Credentials = New System.Net.NetworkCredential(smtpUsername, New String(smtpPassword)),
            .EnableSsl = smtpEnableSSL
        }
            Dim mailMessage = New MailMessage With {
            .From = New MailAddress(smtpUsername),
            .Subject = $"🚨 Error in {formName} at {lineNumber} ({DateTime.Now:MM/dd/yyyy hh:mm:ss tt})",
            .Body = errorDetails,
            .IsBodyHtml = True
        }
            mailMessage.[To].Add("<EMAIL>")
            smtpClient.Send(mailMessage)
            'Array.Clear(smtpUsername, 0, smtpPassword.Length)

        Catch emailEx As Exception
            ' Log email sending failure
            MessageBox.Show("Failed to send error email: " & emailEx.Message, "Email Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' Function to get local IP address
    Private Function GetLocalIPAddress() As String
        Try
            Dim host As String = Dns.GetHostName()
            Dim ip As IPAddress() = Dns.GetHostAddresses(host)
            Return ip.FirstOrDefault(Function(x) x.AddressFamily = Net.Sockets.AddressFamily.InterNetwork)?.ToString()
        Catch ex As Exception
            Return "Unknown"
        End Try
    End Function

    ' Function to get public IP address
    Private Function GetPublicIPAddress() As String
        Try
            Dim client As New WebClient()
            Return client.DownloadString("https://api.ipify.org").Trim()
        Catch ex As Exception
            Return "Unknown"
        End Try
    End Function

    ' Class to store AD user details
    Public Class ADUserInfo
        Public Property Username As String = "Unknown"
        Public Property FullName As String = "Unknown"
        Public Property Email As String = "Unknown"
        Public Property Mobile As String = "Unknown"
        Public Property Department As String = "Unknown"
        Public Property Manager As String = "Unknown"
    End Class

    ' Function to get AD user details
    Private Function GetADUserInfo(ByVal username As String) As ADUserInfo
        Dim userInfo As New ADUserInfo()

        Try
            ' Connect to AD
            Using context As New PrincipalContext(ContextType.Domain)
                Dim user As UserPrincipal = UserPrincipal.FindByIdentity(context, username)

                If user IsNot Nothing Then
                    userInfo.Username = user.SamAccountName
                    userInfo.FullName = user.DisplayName
                    userInfo.Email = user.EmailAddress
                    userInfo.Mobile = user.VoiceTelephoneNumber

                    ' Use DirectorySearcher to get extra AD attributes
                    Using searcher As New DirectorySearcher(New DirectoryEntry("LDAP://*************"))
                        searcher.Filter = $"(&(objectClass=user)(sAMAccountName={username}))"
                        searcher.PropertiesToLoad.Add("department")
                        searcher.PropertiesToLoad.Add("manager")

                        Dim result As SearchResult = searcher.FindOne()
                        If result IsNot Nothing Then
                            If result.Properties.Contains("department") Then
                                userInfo.Department = result.Properties("department")(0).ToString()
                            End If
                            If result.Properties.Contains("manager") Then
                                userInfo.Manager = GetManagerName(result.Properties("manager")(0).ToString())
                            End If
                        End If
                    End Using
                End If
            End Using
        Catch ex As Exception
            ' Log error if AD lookup fails
        End Try

        Return userInfo
    End Function

    Private Function GetManagerName(ByVal managerDN As String) As String
        Try
            Using searcher As New DirectorySearcher(New DirectoryEntry("LDAP://*************"))
                searcher.Filter = $"(distinguishedName={managerDN})"
                searcher.PropertiesToLoad.Add("displayName")

                Dim result As SearchResult = searcher.FindOne()
                If result IsNot Nothing AndAlso result.Properties.Contains("displayName") Then
                    Return result.Properties("displayName")(0).ToString()
                End If
            End Using
        Catch ex As Exception
            ' Handle any errors
        End Try

        Return "Unknown"
    End Function

    ' Recursive function to handle nested dictionaries
    Public Sub AppendDictionaryContents(ByVal dict As Dictionary(Of String, Object), ByVal builder As StringBuilder, Optional ByVal indent As String = "")
        For Each kvp As KeyValuePair(Of String, Object) In dict
            If TypeOf kvp.Value Is Dictionary(Of String, Object) Then
                ' If value is a nested dictionary, process recursively
                builder.AppendLine(indent & kvp.Key & ":")
                AppendDictionaryContents(DirectCast(kvp.Value, Dictionary(Of String, Object)), builder, indent & "    ")

            ElseIf TypeOf kvp.Value Is IEnumerable(Of Dictionary(Of String, Object)) Then
                ' Handle list of dictionaries
                builder.AppendLine(indent & kvp.Key & ":")
                Dim items = DirectCast(kvp.Value, IEnumerable(Of Dictionary(Of String, Object)))
                Dim itemIndex As Integer = 0

                For Each item As Dictionary(Of String, Object) In items
                    builder.AppendLine(indent & "    [" & itemIndex & "]:")
                    AppendDictionaryContents(item, builder, indent & "        ")
                    itemIndex += 1
                Next

            ElseIf TypeOf kvp.Value Is IList AndAlso kvp.Value.GetType().IsGenericType Then
                ' Handle general lists/collections
                builder.AppendLine(indent & kvp.Key & ":")
                Dim list = DirectCast(kvp.Value, IList)
                Dim itemIndex As Integer = 0

                For Each item As Object In list
                    If TypeOf item Is Dictionary(Of String, Object) Then
                        ' Dictionary in list
                        builder.AppendLine(indent & "    [" & itemIndex & "]:")
                        AppendDictionaryContents(DirectCast(item, Dictionary(Of String, Object)), builder, indent & "        ")
                    Else
                        ' Simple value in list
                        builder.AppendLine(indent & "    [" & itemIndex & "]: " & (If(item IsNot Nothing, item.ToString(), "null")))
                    End If
                    itemIndex += 1
                Next

            Else
                ' Normal key-value pair
                builder.AppendLine(indent & kvp.Key & ": " & (If(kvp.Value IsNot Nothing, kvp.Value.ToString(), "null")))
            End If
        Next
    End Sub
End Module
