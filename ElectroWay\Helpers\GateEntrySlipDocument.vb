﻿Imports System.Configuration
Imports System.Data.SqlClient
Imports System.Drawing.Printing

Module GateEntrySlipDocument
    ' Custom class to hold our print data
    Public Class GateEntryPrintData
        Public Property HeaderData As Dictionary(Of String, String)
        Public Property MaterialData As List(Of String())

        Public Sub New(headerData As Dictionary(Of String, String), materialData As List(Of String()))
            Me.HeaderData = headerData
            Me.MaterialData = materialData
        End Sub
    End Class

    Public Sub CreateGateEntrySlip(transaction As String)
        Dim query As String
        Try
            ' Define connection string (replace with actual DB details)
            query = "Select GE_HDR_ID, EntryDateTime, Vehicle_No, Type_Of_Vehicle, Transpoter_Code, TransporterName, Unloading_No, PO_No, PO_Line_Item, DO_No, DO_Line_Item, SO_No, SO_Line_Item, Mat_Code, Mat_Desc, Challan_Date, Challan_No, DO_Challan_Qty, UOM, WayBill_No, Vendor_Code, Vendor_Name, Customer_Code, Customer_Name, GatePass_No from tbl_VIEW_GE_HDR_Details where GE_HDR_ID= @TransactionID"

            ' Data container
            Dim headerData As New Dictionary(Of String, String)
            Dim materialData As New List(Of String())

            ' Fetch data from database
            Using connection As New SqlConnection(ConfigurationManager.ConnectionStrings("MyDatabase").ConnectionString)
                Using command As New SqlCommand(query, connection)
                    command.Parameters.AddWithValue("@TransactionID", transaction)
                    connection.Open()

                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim isHeaderFilled As Boolean = False
                            If Not isHeaderFilled Then
                                headerData("GatePassNo") = GetDbValue(reader, "GE_HDR_ID")
                                headerData("GateEntryDate") = GetDbValue(reader, "EntryDateTime")
                                headerData("VehicleNo") = GetDbValue(reader, "Vehicle_No")
                                headerData("VehicleType") = GetDbValue(reader, "Type_Of_Vehicle")
                                headerData("TransporterCode") = GetDbValue(reader, "Transpoter_Code")
                                headerData("TransporterName") = GetDbValue(reader, "TransporterName")
                                isHeaderFilled = True
                            End If

                            ' Store material data (all rows)
                            materialData.Add(New String() {
                                GetDbValue(reader, "Unloading_No"),
                                GetDbValue(reader, "PO_No"),
                                GetDbValue(reader, "PO_Line_Item"),
                                GetDbValue(reader, "Mat_Code"),
                                GetDbValue(reader, "Mat_Desc"),
                                GetDbValue(reader, "Challan_No"),
                                GetDbValue(reader, "DO_Challan_Qty"),
                                GetDbValue(reader, "UOM"),
                                GetDbValue(reader, "Vendor_Name")
                            })
                        End While
                    End Using
                End Using
            End Using

            ' Generate PDF using the new module
            Dim outputPath As String = "D:\GateEntrySlip_" & transaction & ".pdf"

            GateEntrySlipPDF.GenerateGateEntrySlipPDF(SanitizeFileName(outputPath), headerData, materialData)

            ' Open the PDF after creation
            Process.Start(SanitizeFileName(outputPath))

        Catch ex As Exception
            SendFormattedErrorMail(ex, query)
        End Try
    End Sub

    ' Module or class level variable to store print data
    Private _currentPrintData As GateEntryPrintData
    Private _currentPageIndex As Integer = 0
    Private _rowsPerPage As Integer = 0

    ' Print page event handler
    Private Sub PrintGateEntrySlip_PrintPage(sender As Object, e As PrintPageEventArgs)
        Try
            ' Access the shared print data
            Dim headerData As Dictionary(Of String, String) = _currentPrintData.HeaderData
            Dim materialData As List(Of String()) = _currentPrintData.MaterialData

            ' Graphics object for drawing
            Dim g As Graphics = e.Graphics

            ' Fonts
            Dim titleFont As New Font("Arial", 14, FontStyle.Bold)
            Dim headerFont As New Font("Arial", 10, FontStyle.Bold)
            Dim normalFont As New Font("Arial", 9, FontStyle.Regular)

            ' Brushes
            Dim blackBrush As New SolidBrush(Color.Black)

            ' Pens
            Dim blackPen As New Pen(Color.Black, 1)

            ' Page settings
            Dim margin As Integer = 50
            Dim currentY As Integer = margin
            Dim pageWidth As Integer = e.PageBounds.Width - (2 * margin)

            ' Print title and header only on first page
            If _currentPageIndex = 0 Then
                ' Print title
                g.DrawString("GATE ENTRY SLIP", titleFont, blackBrush, margin, currentY)
                currentY += 30

                ' Print header information
                Dim leftX As Integer = margin
                Dim rightX As Integer = margin + (pageWidth / 2)

                ' Left side header info
                g.DrawString("Gate Pass No: " & headerData("GatePassNo"), headerFont, blackBrush, leftX, currentY)
                currentY += 20
                g.DrawString("Gate Entry Date: " & headerData("GateEntryDate"), headerFont, blackBrush, leftX, currentY)
                currentY += 20
                g.DrawString("Vehicle No: " & headerData("VehicleNo"), headerFont, blackBrush, leftX, currentY)

                ' Reset Y for right side header info
                currentY -= 40

                ' Right side header info
                g.DrawString("Vehicle Type: " & headerData("VehicleType"), headerFont, blackBrush, rightX, currentY)
                currentY += 20
                g.DrawString("Transporter Code: " & headerData("TransporterCode"), headerFont, blackBrush, rightX, currentY)
                currentY += 20
                g.DrawString("Transporter Name: " & headerData("TransporterName"), headerFont, blackBrush, rightX, currentY)

                ' Move down for table
                currentY += 40
            End If

            ' Table column headers
            Dim columnHeaders As String() = {"SAP Gate Entry No", "PO No.", "Line Item", "Mat Code", "Mat. Description", "Challan No", "Challan Qty", "UOM", "Vendor"}
            Dim columnWidths As Integer() = {80, 80, 60, 80, 120, 80, 70, 50, 120}
            Dim totalWidth As Integer = 0
            For Each width In columnWidths
                totalWidth += width
            Next

            ' Adjust column widths if needed to fit page
            If totalWidth > pageWidth Then
                Dim scaleFactor As Double = pageWidth / totalWidth
                For i As Integer = 0 To columnWidths.Length - 1
                    columnWidths(i) = CInt(columnWidths(i) * scaleFactor)
                Next
                totalWidth = pageWidth
            End If

            ' Calculate table start X to center the table
            Dim tableStartX As Integer = margin + (pageWidth - totalWidth) / 2
            Dim rowHeight As Integer = 25

            ' Draw table header
            Dim currentX As Integer = tableStartX
            For i As Integer = 0 To columnHeaders.Length - 1
                ' Draw cell border
                g.DrawRectangle(blackPen, currentX, currentY, columnWidths(i), rowHeight)

                ' Draw header text
                g.DrawString(columnHeaders(i), headerFont, blackBrush, currentX + 2, currentY + 2)

                currentX += columnWidths(i)
            Next

            currentY += rowHeight

            ' Calculate rows per page
            Dim availableHeight As Integer = e.PageBounds.Height - (currentY + margin)
            _rowsPerPage = CInt(availableHeight / rowHeight)

            ' Draw table data, starting from current page index
            Dim startIndex As Integer = _currentPageIndex * _rowsPerPage
            If startIndex > materialData.Count Then
                startIndex = 0
            End If

            Dim rowsDrawn As Integer = 0

            For i As Integer = startIndex To materialData.Count - 1
                If rowsDrawn >= _rowsPerPage Then
                    ' Need another page
                    _currentPageIndex += 1
                    e.HasMorePages = True
                    Return
                End If

                Dim row As String() = materialData(i)
                currentX = tableStartX

                For j As Integer = 0 To row.Length - 1
                    ' Draw cell border
                    g.DrawRectangle(blackPen, currentX, currentY, columnWidths(j), rowHeight)

                    ' Draw cell text
                    g.DrawString(row(j), normalFont, blackBrush, currentX + 2, currentY + 2)

                    currentX += columnWidths(j)
                Next

                currentY += rowHeight
                rowsDrawn += 1
            Next

            ' Last page - add signature section
            If (startIndex + rowsDrawn) >= materialData.Count Then
                currentY += 50

                ' Signature boxes
                Dim signatureLabels As String() = {"Signature", "Signature", "Signature", "Signature"}
                Dim signatureRoles As String() = {"IN Gate Security", "First Wt.", "Second Wt.", "Unloading/Loading Point"}
                Dim finalSignatures As String() = {"47-Khata IN Security", "47-Khata OUT Security", "OUT Gate - Security", "Store SAP Gate OUT"}

                Dim signatureBoxWidth As Integer = pageWidth / 4

                ' Draw signature boxes
                For i As Integer = 0 To 3
                    Dim boxX As Integer = margin + (i * signatureBoxWidth)

                    ' Draw box
                    g.DrawRectangle(blackPen, boxX, currentY, signatureBoxWidth, 80)

                    ' Draw lines for signature
                    g.DrawLine(blackPen, boxX, currentY + 60, boxX + signatureBoxWidth, currentY + 60)

                    ' Draw signature label
                    g.DrawString(signatureLabels(i), normalFont, blackBrush, boxX + 5, currentY + 65)

                    ' Draw role
                    g.DrawString(signatureRoles(i), normalFont, blackBrush, boxX + 5, currentY + 5)
                Next

                currentY += 100

                ' Draw final signature boxes
                For i As Integer = 0 To 3
                    Dim boxX As Integer = margin + (i * signatureBoxWidth)

                    ' Draw box
                    g.DrawRectangle(blackPen, boxX, currentY, signatureBoxWidth, 80)

                    ' Draw lines for signature
                    g.DrawLine(blackPen, boxX, currentY + 60, boxX + signatureBoxWidth, currentY + 60)

                    ' Draw signature label
                    g.DrawString(signatureLabels(i), normalFont, blackBrush, boxX + 5, currentY + 65)

                    ' Draw role
                    g.DrawString(finalSignatures(i), normalFont, blackBrush, boxX + 5, currentY + 5)
                Next

                ' Reset page index for future prints
                _currentPageIndex = 0
                e.HasMorePages = False
            End If

        Catch ex As Exception
            ' Handle error
            Debug.WriteLine("Error in print page event: " & ex.Message)
        End Try
    End Sub

    ' Helper function to safely get values from database
    Private Function GetDbValue(reader As SqlDataReader, columnName As String) As String
        Dim ordinal As Integer = reader.GetOrdinal(columnName)
        If reader.IsDBNull(ordinal) Then
            Return String.Empty
        Else
            Dim fieldType As Type = reader.GetFieldType(ordinal)

            ' Handle different data types
            If fieldType Is GetType(DateTime) Then
                Return reader.GetDateTime(ordinal).ToString("dd-MM-yyyy HH:mm")
            ElseIf fieldType Is GetType(Decimal) Or fieldType Is GetType(Double) Then
                Return reader.GetValue(ordinal).ToString()
            ElseIf fieldType Is GetType(Integer) Then
                Return reader.GetInt32(ordinal).ToString()
            Else
                ' Default to string
                Return reader.GetValue(ordinal).ToString()
            End If
        End If
    End Function
End Module
