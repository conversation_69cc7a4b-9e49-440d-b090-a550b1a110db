Imports System.IO
Imports iText.Kernel.Pdf
Imports iText.Layout
Imports iText.Layout.Element
Imports iText.Layout.Properties
Imports iText.Kernel.Font
Imports iText.IO.Font.Constants
Imports iText.Layout.Borders
Imports iText.Kernel.Colors

Public Module GateEntrySlipPDF
    ''' <summary>
    ''' Generates a Gate Entry Slip PDF in a professional format.
    ''' </summary>
    ''' <param name="outputPath">The file path to save the PDF.</param>
    ''' <param name="headerData">Dictionary with header info (GatePassNo, GateEntryDate, VehicleNo, VehicleType, TransporterCode, TransporterName).</param>
    ''' <param name="materialData">List of string arrays, each representing a table row.</param>
    Public Sub GenerateGateEntrySlipPDF(
        outputPath As String,
        headerData As Dictionary(Of String, String),
        materialData As List(Of String())
    )
        Try
            Dim writer = New PdfWriter(outputPath)
            Dim pdf = New PdfDocument(writer)
            Dim doc = New Document(pdf)
            doc.SetMargins(30, 30, 30, 30)

            ' Add watermark to every page
            Dim watermarkText As String = "Electro Steel Limited"
            Dim watermarkFont = PdfFontFactory.CreateFont(StandardFonts.HELVETICA_BOLD)
            Dim gs1 As New iText.Kernel.Pdf.Extgstate.PdfExtGState()
            gs1.SetFillOpacity(0.15F)
            Dim pageSize = pdf.GetDefaultPageSize()
            Dim pdfCanvas As New iText.Kernel.Pdf.Canvas.PdfCanvas(pdf.AddNewPage())
            pdfCanvas.SaveState()
            pdfCanvas.SetExtGState(gs1)
            Dim canvas As New iText.Layout.Canvas(pdfCanvas, pageSize)
            canvas.SetFontColor(ColorConstants.LIGHT_GRAY)
            canvas.SetFontSize(60)
            canvas.SetFont(watermarkFont)
            canvas.ShowTextAligned(
            watermarkText,
            pageSize.GetWidth() / 2,
            pageSize.GetHeight() / 2,
            TextAlignment.CENTER,
            Math.PI / 6
        )
            canvas.Close()
            pdf.RemovePage(pdf.GetNumberOfPages()) ' Remove the extra page added for watermark

            ' Header
            Dim font = PdfFontFactory.CreateFont(StandardFonts.HELVETICA_BOLD)
            Dim headerPara As New Paragraph("GATE ENTRY SLIP")
            With headerPara
                .SetFont(font)
                .SetFontSize(14)
                .SetTextAlignment(TextAlignment.CENTER)
                .SetBold()
            End With
            doc.Add(headerPara)
            doc.Add(New Paragraph(" "))

            ' Info Table (two columns)
            Dim infoTable = New Table(UnitValue.CreatePercentArray(New Single() {1, 1})).UseAllAvailableWidth()
            infoTable.AddCell(New Cell().Add(New Paragraph("Gate Pass No:").SetBold()).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph(headerData("GatePassNo"))).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph("Gate Entry Date:").SetBold()).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph(headerData("GateEntryDate"))).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph("Vehicle No:").SetBold()).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph(headerData("VehicleNo"))).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph("Vehicle Type:").SetBold()).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph(headerData("VehicleType"))).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph("Transporter Code:").SetBold()).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph(headerData("TransporterCode"))).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph("Transporter Name:").SetBold()).SetBorder(Border.NO_BORDER))
            infoTable.AddCell(New Cell().Add(New Paragraph(headerData("TransporterName"))).SetBorder(Border.NO_BORDER))
            doc.Add(infoTable)
            doc.Add(New Paragraph(" "))

            ' Table Header and Data
            Dim table = New Table(UnitValue.CreatePercentArray(New Single() {1.2F, 1.2F, 1.0F, 1.2F, 2.5F, 1.2F, 1.2F, 1.0F, 2.0F}))
            table.SetWidth(UnitValue.CreatePercentValue(100))
            table.SetHorizontalAlignment(HorizontalAlignment.CENTER)
            Dim headers As String() = {"SAP Gate Entry No", "PO No.", "Line Item", "Mat Code", "Mat. Description", "Challan No", "Challan Qty", "UOM", "Vendor"}
            For Each h As String In headers
                Dim para As New Paragraph(h)
                para.SetFontSize(8)
                Dim headerCell As New Cell()
                headerCell.Add(para)
                headerCell.SetBackgroundColor(ColorConstants.LIGHT_GRAY)
                table.AddHeaderCell(headerCell)
            Next
            For Each row In materialData
                For i As Integer = 0 To headers.Length - 1
                    Dim cellText As String = If(i < row.Length, row(i), "")
                    Dim para As New Paragraph(cellText)
                    para.SetFontSize(8)
                    Dim cell As New Cell()
                    cell.Add(para)
                    table.AddCell(cell)
                Next
            Next
            doc.Add(table)

            ' Space for signatures
            doc.Add(New Paragraph(" ").SetHeight(60))
            Dim sigTable = New Table(UnitValue.CreatePercentArray(New Single() {1, 1, 1, 1})).UseAllAvailableWidth()
            Dim sigRoles = {"Prepared By Security", "Checked By Supervisor", "Received By Driver", "Authorised By Security Head"}
            For Each r In sigRoles
                sigTable.AddCell(New Cell().Add(New Paragraph("")).SetMinHeight(30).SetBorder(Border.NO_BORDER))
            Next
            For Each r In sigRoles
                sigTable.AddCell(New Cell().Add(New Paragraph(r)).SetBorder(Border.NO_BORDER))
            Next
            doc.Add(sigTable)

            doc.Close()
        Catch ex As Exception
            SendFormattedErrorMail(ex)
        End Try
    End Sub
    ' Helper to sanitize file names for Windows
    Public Function SanitizeFileName(fileName As String) As String
        Dim invalidChars = System.IO.Path.GetInvalidFileNameChars()
        For Each c In invalidChars
            fileName = fileName.Replace(c, "_"c)
        Next
        Return fileName
    End Function

End Module
