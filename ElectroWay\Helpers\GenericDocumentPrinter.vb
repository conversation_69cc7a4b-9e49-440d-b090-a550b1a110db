﻿Imports System.Configuration
Imports System.Data.SqlClient
Imports System.Drawing
Imports System.Drawing.Printing
Imports ElectroWay.GenericDocumentPrinter

''' <summary>
''' Provides generic document printing functionality for tabular data with headers and signature panels
''' </summary>
Public Class GenericDocumentPrinter
#Region "Data Structures"
    ''' <summary>
    ''' Holds document header information as key-value pairs
    ''' </summary>
    Public Class HeaderInfo
        Public Property Title As String
        Public Property HeaderData As Dictionary(Of String, String)

        Public Sub New(title As String)
            Me.Title = title
            HeaderData = New Dictionary(Of String, String)
        End Sub
    End Class

    ''' <summary>
    ''' Represents a column in the table
    ''' </summary>
    Public Class ColumnDefinition
        Public Property Header As String
        Public Property Width As Integer
        Public Property DataField As String

        Public Sub New(header As String, width As Integer, dataField As String)
            Me.Header = header
            Me.Width = width
            Me.DataField = dataField
        End Sub
    End Class

    ''' <summary>
    ''' Represents a signature field in the footer
    ''' </summary>
    Public Class SignatureField
        Public Property Title As String
        Public Property Role As String

        Public Sub New(title As String, role As String)
            Me.Title = title
            Me.Role = role
        End Sub
    End Class
#End Region

#Region "Private Fields"
    Private _headerInfo As HeaderInfo
    Private _columns As List(Of ColumnDefinition)
    Private _rowsData As List(Of Dictionary(Of String, String))
    Private _signatureGroups As List(Of List(Of SignatureField))

    Private _currentPageIndex As Integer = 0
    Private _rowsPerPage As Integer = 0
    Private _documentTitle As String
#End Region

#Region "Public Properties"
    ''' <summary>
    ''' A4 paper width in hundredths of an inch (portrait)
    ''' </summary>
    Public ReadOnly Property A4Width As Integer = 827 ' 8.27 inches

    ''' <summary>
    ''' A4 paper height in hundredths of an inch (portrait)
    ''' </summary>
    Public ReadOnly Property A4Height As Integer = 1169 ' 11.69 inches

    ''' <summary>
    ''' Page margins in pixels
    ''' </summary>
    Public Property Margin As Integer = 50

    ''' <summary>
    ''' Row height for table data
    ''' </summary>
    Public Property RowHeight As Integer = 30
#End Region

#Region "Constructor"
    ''' <summary>
    ''' Creates a new instance of the GenericDocumentPrinter
    ''' </summary>
    ''' <param name="documentTitle">The title of the document</param>
    Public Sub New(documentTitle As String)
        _documentTitle = documentTitle
        _headerInfo = New HeaderInfo(documentTitle)
        _columns = New List(Of ColumnDefinition)
        _rowsData = New List(Of Dictionary(Of String, String))
        _signatureGroups = New List(Of List(Of SignatureField))
    End Sub
#End Region

#Region "Public Methods"
    ''' <summary>
    ''' Sets the header information for the document
    ''' </summary>
    ''' <param name="headerData">Dictionary of header labels and values</param>
    Public Sub SetHeaderInfo(headerData As Dictionary(Of String, String))
        _headerInfo.HeaderData = headerData
    End Sub

    ''' <summary>
    ''' Adds a header field with label and value
    ''' </summary>
    ''' <param name="label">The label for the header field</param>
    ''' <param name="value">The value for the header field</param>
    Public Sub AddHeaderField(label As String, value As String)
        _headerInfo.HeaderData(label) = value
    End Sub

    ''' <summary>
    ''' Defines the columns for the table
    ''' </summary>
    ''' <param name="columns">List of column definitions</param>
    Public Sub SetColumns(columns As List(Of ColumnDefinition))
        _columns = columns
    End Sub

    ''' <summary>
    ''' Adds a column definition to the table
    ''' </summary>
    ''' <param name="header">Column header text</param>
    ''' <param name="width">Column width in pixels</param>
    ''' <param name="dataField">Field name in data dictionary</param>
    Public Sub AddColumn(header As String, width As Integer, dataField As String)
        _columns.Add(New ColumnDefinition(header, width, dataField))
    End Sub

    ''' <summary>
    ''' Sets the data rows for the table
    ''' </summary>
    ''' <param name="rowsData">List of dictionaries containing row data</param>
    Public Sub SetData(rowsData As List(Of Dictionary(Of String, String)))
        _rowsData = rowsData
    End Sub

    ''' <summary>
    ''' Adds a single data row to the table
    ''' </summary>
    ''' <param name="rowData">Dictionary containing row data</param>
    Public Sub AddDataRow(rowData As Dictionary(Of String, String))
        _rowsData.Add(rowData)
    End Sub

    ''' <summary>
    ''' Adds a signature group (row of signature fields) to the document footer
    ''' </summary>
    ''' <param name="signatureFields">List of signature fields for this group</param>
    Public Sub AddSignatureGroup(signatureFields As List(Of SignatureField))
        _signatureGroups.Add(signatureFields)
    End Sub

    ''' <summary>
    ''' Prints the document
    ''' </summary>
    ''' <param name="showDialog">Whether to show the print dialog</param>
    Public Sub Print(Optional showDialog As Boolean = True)
        ' Reset page index
        _currentPageIndex = 0

        ' Create print document
        Dim printDoc As New PrintDocument()

        ' Set page settings for A4 portrait
        printDoc.DefaultPageSettings.PaperSize = New PaperSize("A4", A4Width, A4Height)
        printDoc.DefaultPageSettings.Landscape = False

        ' Set print event handler
        AddHandler printDoc.PrintPage, AddressOf PrintPage

        ' Show dialog if requested
        If showDialog Then
            Dim printDialog As New PrintDialog()
            printDialog.Document = printDoc
            If printDialog.ShowDialog() = DialogResult.OK Then
                printDoc.Print()
            End If
        Else
            printDoc.Print()
        End If
    End Sub

    ''' <summary>
    ''' Prints a preview of the document
    ''' </summary>
    Public Sub PrintPreview()
        ' Reset page index
        _currentPageIndex = 0

        ' Create print document
        Dim printDoc As New PrintDocument()

        ' Set page settings for A4 portrait
        printDoc.DefaultPageSettings.PaperSize = New PaperSize("A4", A4Width, A4Height)
        printDoc.DefaultPageSettings.Landscape = False

        ' Set print event handler
        AddHandler printDoc.PrintPage, AddressOf PrintPage

        ' Show print preview dialog
        Dim previewDialog As New PrintPreviewDialog()
        previewDialog.Document = printDoc
        previewDialog.ShowDialog()
    End Sub
#End Region

#Region "Private Methods"
    ''' <summary>
    ''' Print page event handler
    ''' </summary>
    Private Sub PrintPage(sender As Object, e As PrintPageEventArgs)
        Try
            ' Graphics object
            Dim g As Graphics = e.Graphics

            ' Fonts
            Dim titleFont As New Font("Arial", 14, FontStyle.Bold)
            Dim headerFont As New Font("Arial", 10, FontStyle.Bold)
            Dim normalFont As New Font("Arial", 9, FontStyle.Regular)

            ' Brushes and pens
            Dim blackBrush As New SolidBrush(Color.Black)
            Dim blackPen As New Pen(Color.Black, 1)

            ' Calculate usable page width
            Dim pageWidth As Integer = e.PageBounds.Width - (2 * Margin)

            ' Current Y position on page
            Dim currentY As Integer = Margin

            ' Print title and header only on first page
            If _currentPageIndex = 0 Then
                ' Print title
                g.DrawString(_headerInfo.Title, titleFont, blackBrush, Margin, currentY)
                currentY += 30

                ' Print header fields in two columns
                Dim headerFields As Dictionary(Of String, String) = _headerInfo.HeaderData
                Dim leftX As Integer = Margin
                Dim rightX As Integer = Margin + (pageWidth / 2)

                ' Count fields for balancing columns
                Dim fieldCount As Integer = headerFields.Count
                Dim leftCount As Integer = CInt(Math.Ceiling(fieldCount / 2.0))

                ' Draw header fields
                Dim fieldIndex As Integer = 0
                For Each field In headerFields
                    If fieldIndex < leftCount Then
                        ' Left column
                        g.DrawString(field.Key & ": " & field.Value, headerFont, blackBrush, leftX, currentY)
                        currentY += 20
                    Else
                        ' Right column
                        If fieldIndex = leftCount Then
                            ' Reset Y for right column
                            currentY -= (leftCount * 20)
                        End If

                        g.DrawString(field.Key & ": " & field.Value, headerFont, blackBrush, rightX, currentY)
                        currentY += 20
                    End If

                    fieldIndex += 1
                Next

                ' Adjust Y position for table
                If fieldIndex <= leftCount Then
                    ' Only left column was used
                    ' No adjustment needed
                Else
                    ' Both columns were used, get max Y position
                    Dim rightYMax As Integer = Margin + 30 + ((fieldIndex - leftCount) * 20)
                    If rightYMax > currentY Then
                        currentY = rightYMax
                    End If
                End If

                ' Add space before table
                currentY += 20
            End If

            ' Validate columns
            If _columns.Count = 0 Then
                Throw New InvalidOperationException("No columns defined for table")
            End If

            ' Calculate table width and adjust column widths if needed
            Dim totalColumnWidth As Integer = 0
            For Each col In _columns
                totalColumnWidth += col.Width
            Next

            ' Scale columns if needed
            If totalColumnWidth > pageWidth Then
                Dim scaleFactor As Double = pageWidth / totalColumnWidth
                For i As Integer = 0 To _columns.Count - 1
                    _columns(i).Width = CInt(_columns(i).Width * scaleFactor)
                Next
                totalColumnWidth = pageWidth
            End If

            ' Calculate table start X to center the table
            Dim tableStartX As Integer = Margin + (pageWidth - totalColumnWidth) / 2

            ' Draw table header
            Dim currentX As Integer = tableStartX
            For Each col In _columns
                ' Draw cell border
                g.DrawRectangle(blackPen, currentX, currentY, col.Width, RowHeight)

                ' Draw header text (centered)
                Dim headerSize As SizeF = g.MeasureString(col.Header, headerFont)
                Dim headerX As Single = currentX + ((col.Width - headerSize.Width) / 2)
                Dim headerY As Single = currentY + ((RowHeight - headerSize.Height) / 2)
                g.DrawString(col.Header, headerFont, blackBrush, headerX, headerY)

                currentX += col.Width
            Next

            currentY += RowHeight

            ' Calculate rows per page
            Dim availableHeight As Integer = e.PageBounds.Height - (currentY + Margin + (_signatureGroups.Count * 100))
            _rowsPerPage = Math.Max(1, CInt(availableHeight / RowHeight))

            ' Determine starting row for current page
            Dim startRow As Integer = _currentPageIndex * _rowsPerPage

            ' Draw data rows
            Dim rowsDrawn As Integer = 0
            Dim hasMorePages As Boolean = False

            For i As Integer = startRow To _rowsData.Count - 1
                ' Check if we need another page
                If rowsDrawn >= _rowsPerPage Then
                    hasMorePages = True
                    Exit For
                End If

                ' Get row data
                Dim rowData As Dictionary(Of String, String) = _rowsData(i)

                ' Draw row cells
                currentX = tableStartX
                For Each col In _columns
                    ' Get cell value
                    Dim cellValue As String = String.Empty
                    If rowData.ContainsKey(col.DataField) Then
                        cellValue = rowData(col.DataField)
                    End If

                    ' Draw cell border
                    g.DrawRectangle(blackPen, currentX, currentY, col.Width, RowHeight)

                    ' Draw cell text
                    Dim truncatedText As String = TruncateText(cellValue, normalFont, col.Width - 10, g)
                    g.DrawString(truncatedText, normalFont, blackBrush, currentX + 5, currentY + 5)

                    currentX += col.Width
                Next

                currentY += RowHeight
                rowsDrawn += 1
            Next

            ' Only draw signature areas on the last page
            If Not hasMorePages Then
                ' Add space before signature section
                currentY += 30

                ' Draw signature sections
                For groupIndex As Integer = 0 To _signatureGroups.Count - 1
                    Dim signatureFields As List(Of SignatureField) = _signatureGroups(groupIndex)
                    Dim fieldCount As Integer = signatureFields.Count
                    Dim fieldWidth As Integer = pageWidth / fieldCount

                    ' Draw signature fields
                    For fieldIndex As Integer = 0 To fieldCount - 1
                        Dim field As SignatureField = signatureFields(fieldIndex)
                        Dim boxX As Integer = Margin + (fieldIndex * fieldWidth)

                        ' Draw box
                        g.DrawRectangle(blackPen, boxX, currentY, fieldWidth, 80)

                        ' Draw role at top
                        g.DrawString(field.Role, normalFont, blackBrush, boxX + 5, currentY + 5)

                        ' Draw line for signature
                        g.DrawLine(blackPen, boxX, currentY + 60, boxX + fieldWidth, currentY + 60)

                        ' Draw signature label
                        g.DrawString(field.Title, normalFont, blackBrush, boxX + 5, currentY + 65)
                    Next

                    ' Move down for next signature group
                    currentY += 90
                Next

                ' Reset page index for future prints
                _currentPageIndex = 0
            Else
                ' Increment page index
                _currentPageIndex += 1
            End If

            ' Set HasMorePages flag
            e.HasMorePages = hasMorePages

        Catch ex As Exception
            ' Handle error
            Debug.WriteLine("Error in print page event: " & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' Truncates text to fit within a specified width
    ''' </summary>
    Private Function TruncateText(text As String, font As Font, width As Integer, g As Graphics) As String
        If String.IsNullOrEmpty(text) Then
            Return String.Empty
        End If

        If g.MeasureString(text, font).Width <= width Then
            Return text
        End If

        ' Truncate with ellipsis
        Dim result As String = text
        While g.MeasureString(result & "...", font).Width > width And result.Length > 0
            result = result.Substring(0, result.Length - 1)
        End While

        Return result & "..."
    End Function
#End Region
End Class

''' <summary>
''' Example usage class showing how to implement the generic document printer
''' </summary>
Public Class GateEntrySlipPrinter
    ''' <summary>
    ''' Creates and prints a gate entry slip for the specified transaction
    ''' </summary>
    Public Shared Sub PrintGateEntrySlip(transactionId As String)
        Try
            ' Create the document printer
            Dim printer As New GenericDocumentPrinter("GATE ENTRY SLIP")

            ' Define columns
            printer.AddColumn("SAP Gate Entry No", 100, "EntryNo")
            printer.AddColumn("PO No.", 100, "PONo")
            printer.AddColumn("Line Item", 65, "LineItem")
            printer.AddColumn("Mat Code", 100, "MatCode")
            printer.AddColumn("Mat. Description", 150, "MatDesc")
            printer.AddColumn("Challan No", 100, "ChallanNo")
            printer.AddColumn("Challan Qty", 80, "ChallanQty")
            printer.AddColumn("UOM", 60, "UOM")
            printer.AddColumn("Vendor", 120, "VendorName")

            ' Fetch data from database
            Dim headerData As Dictionary(Of String, String) = Nothing
            Dim rowsData As List(Of Dictionary(Of String, String)) = Nothing
            FetchGateEntryData(transactionId, headerData, rowsData)

            ' Set header data
            printer.SetHeaderInfo(headerData)

            ' Set row data
            printer.SetData(rowsData)

            ' Add signature groups
            Dim firstSignatureGroup As New List(Of SignatureField) From {
                New SignatureField("Signature", "IN Gate Security"),
                New SignatureField("Signature", "First Wt."),
                New SignatureField("Signature", "Second Wt."),
                New SignatureField("Signature", "Unloading/Loading Point")
            }

            Dim secondSignatureGroup As New List(Of SignatureField) From {
                New SignatureField("Signature", "47-Khata IN Security"),
                New SignatureField("Signature", "47-Khata OUT Security"),
                New SignatureField("Signature", "OUT Gate - Security"),
                New SignatureField("Signature", "Store SAP Gate OUT")
            }

            printer.AddSignatureGroup(firstSignatureGroup)
            printer.AddSignatureGroup(secondSignatureGroup)

            ' Print the document
            printer.Print()

        Catch ex As Exception
            MessageBox.Show("Error printing gate entry slip: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' Fetches gate entry data from the database
    ''' </summary>
    Private Shared Sub FetchGateEntryData(transactionId As String, ByRef headerData As Dictionary(Of String, String), ByRef rowsData As List(Of Dictionary(Of String, String)))
        ' Initialize collections
        headerData = New Dictionary(Of String, String)
        rowsData = New List(Of Dictionary(Of String, String))

        ' SQL query
        Dim query As String = "Select GE_HDR_ID, EntryDateTime, Vehicle_No, Type_Of_Vehicle, Transpoter_Code, TransporterName, " &
                             "Unloading_No, PO_No, PO_Line_Item, DO_No, DO_Line_Item, SO_No, SO_Line_Item, Mat_Code, Mat_Desc, " &
                             "Challan_Date, Challan_No, DO_Challan_Qty, UOM, WayBill_No, Vendor_Code, Vendor_Name, Customer_Code, " &
                             "Customer_Name, GatePass_No from tbl_VIEW_GE_HDR_Details where GE_HDR_ID= @TransactionID"

        ' Fetch data from database
        Using connection As New SqlConnection(ConfigurationManager.ConnectionStrings("MyDatabase").ConnectionString)
            Using command As New SqlCommand(query, connection)
                command.Parameters.AddWithValue("@TransactionID", transactionId)
                connection.Open()

                Using reader As SqlDataReader = command.ExecuteReader()
                    While reader.Read()
                        ' Populate header data (only from first row)
                        If headerData.Count = 0 Then
                            headerData.Add("Gate Pass No", GetDbValue(reader, "GE_HDR_ID"))
                            headerData.Add("Gate Entry Date", GetDbValue(reader, "EntryDateTime"))
                            headerData.Add("Vehicle No", GetDbValue(reader, "Vehicle_No"))
                            headerData.Add("Vehicle Type", GetDbValue(reader, "Type_Of_Vehicle"))
                            headerData.Add("Transporter Code", GetDbValue(reader, "Transpoter_Code"))
                            headerData.Add("Transporter Name", GetDbValue(reader, "TransporterName"))
                        End If

                        ' Add data row
                        Dim rowData As New Dictionary(Of String, String) From {
                            {"EntryNo", GetDbValue(reader, "Unloading_No")},
                            {"PONo", GetDbValue(reader, "PO_No")},
                            {"LineItem", GetDbValue(reader, "PO_Line_Item")},
                            {"MatCode", GetDbValue(reader, "Mat_Code")},
                            {"MatDesc", GetDbValue(reader, "Mat_Desc")},
                            {"ChallanNo", GetDbValue(reader, "Challan_No")},
                            {"ChallanQty", GetDbValue(reader, "DO_Challan_Qty")},
                            {"UOM", GetDbValue(reader, "UOM")},
                            {"VendorName", GetDbValue(reader, "Vendor_Name")}
                        }

                        rowsData.Add(rowData)
                    End While
                End Using
            End Using
        End Using
    End Sub

    ''' <summary>
    ''' Helper function to safely get values from database
    ''' </summary>
    Private Shared Function GetDbValue(reader As SqlDataReader, columnName As String) As String
        Dim ordinal As Integer = reader.GetOrdinal(columnName)
        If reader.IsDBNull(ordinal) Then
            Return String.Empty
        Else
            Dim fieldType As Type = reader.GetFieldType(ordinal)

            ' Handle different data types
            If fieldType Is GetType(DateTime) Then
                Return reader.GetDateTime(ordinal).ToString("dd-MM-yyyy HH:mm")
            ElseIf fieldType Is GetType(Decimal) Or fieldType Is GetType(Double) Then
                Return reader.GetValue(ordinal).ToString()
            ElseIf fieldType Is GetType(Integer) Then
                Return reader.GetInt32(ordinal).ToString()
            Else
                ' Default to string
                Return reader.GetValue(ordinal).ToString()
            End If
        End If
    End Function
End Class
