﻿Imports iText.IO.Font.Constants
Imports iText.Kernel.Font
Imports iText.Kernel.Pdf
Imports iText.Layout
Imports iText.Layout.Element
Imports iText.Layout.Properties
Imports iText.Layout.Borders
Imports System.Data.SqlClient
Imports System.Configuration
Imports System.IO

Module PDFHelpers
    Dim defaultFontSize As Single = 6 ' Set the default font size

    Sub CreateGateEntrySlipPDF(transaction As String)
        Dim query As String
        Try
            ' Define connection string (replace with actual DB details)

            query = "Select GE_HDR_ID, EntryDateTime, Vehicle_No, Type_Of_Vehicle, Transpoter_Code, TransporterName, Unloading_No, PO_No, PO_Line_Item, DO_No, DO_Line_Item, SO_No, SO_Line_Item, Mat_Code, Mat_Desc, Challan_Date, Challan_No, DO_Challan_Qty, UOM, 
          WayBill_No, Vendor_Code, Vendor_Name, Customer_Code, Customer_Name, GatePass_No from tbl_VIEW_GE_HDR_Details where GE_HDR_ID= @TransactionID"

            ' Data container
            Dim headerData As New Dictionary(Of String, String)
            Dim materialData As New List(Of String())

            ' Fetch data from database
            Using connection As New SqlConnection(ConfigurationManager.ConnectionStrings("MyDatabase").ConnectionString)
                Using command As New SqlCommand(query, connection)
                    command.Parameters.AddWithValue("@TransactionID", transaction)
                    connection.Open()

                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim isHeaderFilled As Boolean = False
                            If Not isHeaderFilled Then
                                headerData("GatePassNo") = GetDbValue(reader, "GE_HDR_ID")
                                headerData("GateEntryDate") = GetDbValue(reader, "EntryDateTime")
                                headerData("VehicleNo") = GetDbValue(reader, "Vehicle_No")
                                headerData("VehicleType") = GetDbValue(reader, "Type_Of_Vehicle")
                                headerData("TransporterCode") = GetDbValue(reader, "Transpoter_Code")
                                headerData("TransporterName") = GetDbValue(reader, "TransporterName")
                                isHeaderFilled = True
                            End If

                            ' Store material data (all rows)
                            materialData.Add(New String() {
                            GetDbValue(reader, "Unloading_No"),
                            GetDbValue(reader, "PO_No"),
                            GetDbValue(reader, "PO_Line_Item"),
                            GetDbValue(reader, "Mat_Code"),
                            GetDbValue(reader, "Mat_Desc"),
                            GetDbValue(reader, "Challan_No"),
                            GetDbValue(reader, "DO_Challan_Qty"),
                            GetDbValue(reader, "UOM"),
                            GetDbValue(reader, "Vendor_Name")
                            })

                        End While
                    End Using
                End Using
            End Using

            ' Create PDF in memory using MemoryStream
            Using memoryStream As New MemoryStream()
                Using writer As New PdfWriter(memoryStream)
                    Using pdf As New PdfDocument(writer)
                        Dim document As New Document(pdf)

                        ' Load font
                        Dim font As PdfFont = PdfFontFactory.CreateFont(StandardFonts.HELVETICA)
                        AddGateEntryHeader(document, font, headerData)

                        ' Material Details Table
                        Dim columnHeaders As String() = {"SAP Gate Entry No", "PO No.", "Line Item", "Mat Code", "Mat. Description", "Challan No", "Challan Qty", "UOM", "Vendor"}
                        Dim materialTable As Table = CreateTableWithDynamicColumnWidths(columnHeaders)
                        AddHeaderToTable(materialTable, font, defaultFontSize, columnHeaders)

                        ' Add fetched data rows
                        For Each row As String() In materialData
                            AddRowToTable(materialTable, font, defaultFontSize, row)
                        Next

                        document.Add(materialTable)

                        ' Signature Table
                        Dim signatureLabels As String() = {"Signature", "Signature", "Signature", "Signature"}
                        Dim signatureRoles As String() = {"IN Gate Security", "First Wt.", "Second Wt.", "Unloading/Loading Point"}
                        Dim finalSignatures As String() = {"47-Khata IN Security", "47-Khata OUT Security", "OUT Gate - Security", "Store SAP Gate OUT"}

                        Dim signatureTable As Table = CreateTable(4)

                        ' Add Empty Rows for formatting (adjust height instead)
                        For i As Integer = 1 To 5
                            AddRowToTable(signatureTable, font, defaultFontSize, {"", "", "", ""}, True)
                        Next

                        AddRowToTable(signatureTable, font, defaultFontSize, signatureLabels, True)
                        AddRowToTable(signatureTable, font, defaultFontSize, signatureRoles, True)

                        ' More Empty Rows (adjust as needed)
                        For i As Integer = 1 To 5
                            AddRowToTable(signatureTable, font, defaultFontSize, {"", "", "", ""}, True)
                        Next

                        AddRowToTable(signatureTable, font, defaultFontSize, signatureLabels, True)
                        AddRowToTable(signatureTable, font, defaultFontSize, finalSignatures, True)

                        document.Add(signatureTable)

                        ' Finalize document
                        document.Close()
                    End Using
                End Using

                ' Print the PDF directly from memory
                PDFPrinter.PrintPDFFromMemory(memoryStream.ToArray())
            End Using
        Catch ex As Exception
            SendFormattedErrorMail(ex, query)
        End Try
    End Sub



    Sub AddGateEntryHeader(document As Document, font As PdfFont, headerData As Dictionary(Of String, String))
        ' Add header information using helper method
        AddParagraph(document, "GATE ENTRY SLIP", font, defaultFontSize, TextAlignment.CENTER)
        AddParagraph(document, "ESL Steel Limited", font, defaultFontSize, TextAlignment.CENTER)

        ' Create table for header details
        Dim headerTable = CreateTable(6)

        ' Define header fields with their corresponding keys
        Dim headerFields As String(,) = {
        {"Gate Pass No.:", "GatePassNo", "", "", "Gate Entry Date:", "GateEntryDate"},
        {"Vehicle No.:", "VehicleNo", "", "", "Vehicle Type:", "VehicleType"},
        {"Transporter Code:", "TransporterCode", "", "", "Transporter Name:", "TransporterName"}
    }

        ' Add rows dynamically using key-value pairs
        For row As Integer = 0 To headerFields.GetLength(0) - 1
            Dim rowData(5) As String
            For col As Integer = 0 To headerFields.GetLength(1) - 1
                Dim key As String = headerFields(row, col)
                rowData(col) = If(headerData.ContainsKey(key), headerData(key), key) ' Use key value if exists
            Next
            AddRowToTable(headerTable, font, defaultFontSize, rowData, True)
        Next

        ' Add table to document
        document.Add(headerTable)
    End Sub

    Sub CreateWeighmentSlip(filePath As String, transaction As String)
        Dim query As String
        Try
            ' Define connection string (replace with actual DB details)

            query = "SELECT a.TRN_ID, a.GE_HDR_ID, a.Gate_No, a.Vehicle_No, a.Type_Of_Vehicle, a.Driver_name, a.Driver_LIC_no, a.Driver_LIC_ValidUpto, a.DL_Pic_name_Patch, a.Vehicle_Pic_name_path, a.Blue_book_Name_path, a.Transpoter_Code, 
                  a.TransporterName, a.Remarks_IN, a.Remarks_OUT, a.Vehicle_Status, a.Remarks_cancellation, a.Entry_DoneBy, a.EntryDateTime, a.OUT_DateTime, a.OUT_DoneBy, a.Plant_Code, a.TransferTo_PlantCode, a.Company_Code, 
                  a.Transfer_REF_GE_DET_ID, a.Seal_No, a.Party_Gross_WT, a.Party_Tare_WT, a.Party_Net_WT, b.GE_DET_Tran_ID, b.GE_HDR_TRAN_ID, b.GE_HDR_ID AS Expr1, b.Type_Of_Vehicle AS Expr2, b.Unloading_No, b.PO_No, b.PO_Line_Item, 
                  b.DO_No, b.DO_Line_Item, b.SO_No, b.SO_Line_Item, b.Mat_Code, b.Mat_Desc, b.Challan_Date, b.Challan_No, b.DO_Challan_Qty, b.UOM, b.WayBill_No, b.Vendor_Code, b.Vendor_Name, b.Customer_Code, b.Customer_Name, 
                  b.GatePass_No, b.Unloading_Remarks, b.CN_No, b.CN_Date, b.WB_Count_ID, b.F_WT_Node_IP, b.F_WT, b.F_WT_DoneBy, b.F_WT_DateTime, b.S_WT_Node_IP, b.S_WT, b.S_WT_DoneBy, b.S_WT_DateTime, b.NET_WT, 
                  b.DataUploadedIN_SAP, b.F_WT_Note, b.S_WT_Note, b.WB_Counter, b.Grouping_Ref_Code, b.Grouping_Vehicle_No, b.Grouping_Transaction_No, b.Grouping_ID
FROM     tbl_GE_Hdr AS a INNER JOIN
                  tbl_GE_Det AS b ON a.TRN_ID = b.GE_HDR_TRAN_ID AND a.GE_HDR_ID= @TransactionID"

            ' Data container
            Dim headerData As New Dictionary(Of String, String)
            Dim materialData As New List(Of String())

            ' Fetch data from database
            Using connection As New SqlConnection(ConfigurationManager.ConnectionStrings("MyDatabase").ConnectionString)
                Using command As New SqlCommand(query, connection)
                    command.Parameters.AddWithValue("@TransactionID", transaction)
                    connection.Open()

                    Using reader As SqlDataReader = command.ExecuteReader()
                        While reader.Read()
                            Dim isHeaderFilled As Boolean = False
                            If Not isHeaderFilled Then
                                headerData("GatePassNo") = GetDbValue(reader, "GE_HDR_ID")
                                headerData("GateEntryDate") = GetDbValue(reader, "EntryDateTime")
                                headerData("VehicleNo") = GetDbValue(reader, "Vehicle_No")
                                headerData("VehicleType") = GetDbValue(reader, "Type_Of_Vehicle")
                                headerData("TransporterCode") = GetDbValue(reader, "Transpoter_Code")
                                headerData("TransporterName") = GetDbValue(reader, "TransporterName")

                                headerData("First Wt Date Time") = GetDbValue(reader, "F_WT_DateTime")
                                headerData("Second Wt Date Time") = GetDbValue(reader, "S_WT_DateTime")
                                headerData("First Weight") = GetDbValue(reader, "F_WT")
                                headerData("Second Weight") = GetDbValue(reader, "S_WT")
                                headerData("Net Weight") = GetDbValue(reader, "NET_WT")
                                isHeaderFilled = True
                            End If

                            ' Store material data (all rows)
                            materialData.Add(New String() {
                                GetDbValue(reader, "Unloading_No"),
                                GetDbValue(reader, "PO_No"),
                                GetDbValue(reader, "PO_Line_Item"),
                                GetDbValue(reader, "Mat_Code"),
                                GetDbValue(reader, "Mat_Desc"),
                                GetDbValue(reader, "Challan_No"),
                                GetDbValue(reader, "DO_Challan_Qty"),
                                GetDbValue(reader, "UOM"),
                                GetDbValue(reader, "Vendor_Name")
                                })

                        End While
                    End Using
                End Using
            End Using

            ' Create PDF writer
            Using writer As New PdfWriter(filePath)
                Using pdf As New PdfDocument(writer)
                    Dim document As New Document(pdf)

                    ' Load font
                    Dim font As PdfFont = PdfFontFactory.CreateFont(StandardFonts.HELVETICA)
                    AddWeighmentSlipHeader(document, font, headerData)

                    ' Material Details Table
                    Dim columnHeaders As String() = {"SAP Gate Entry No", "PO No.", "Line Item", "Mat Code", "Mat. Description", "Challan No", "Challan Qty", "UOM", "Vendor"}
                    Dim materialTable As Table = CreateTableWithDynamicColumnWidths(columnHeaders)
                    AddHeaderToTable(materialTable, font, defaultFontSize, columnHeaders)

                    ' Add fetched data rows
                    For Each row As String() In materialData
                        AddRowToTable(materialTable, font, defaultFontSize, row)
                    Next

                    document.Add(materialTable)

                    ' Signature Table
                    Dim signatureLabels As String() = {"Signature", "Signature", "Signature", "Signature"}
                    Dim signatureRoles As String() = {"IN Gate Security", "First Wt.", "Second Wt.", "Unloading/Loading Point"}
                    Dim finalSignatures As String() = {"47-Khata IN Security", "47-Khata OUT Security", "OUT Gate - Security", "Store SAP Gate OUT"}

                    Dim signatureTable As Table = CreateTable(4)

                    ' Add Empty Rows for formatting (adjust height instead)
                    For i As Integer = 1 To 5
                        AddRowToTable(signatureTable, font, defaultFontSize, {"", "", "", ""}, True)
                    Next

                    AddRowToTable(signatureTable, font, defaultFontSize, signatureLabels, True)
                    AddRowToTable(signatureTable, font, defaultFontSize, signatureRoles, True)

                    ' More Empty Rows (adjust as needed)
                    For i As Integer = 1 To 5
                        AddRowToTable(signatureTable, font, defaultFontSize, {"", "", "", ""}, True)
                    Next

                    AddRowToTable(signatureTable, font, defaultFontSize, signatureLabels, True)
                    AddRowToTable(signatureTable, font, defaultFontSize, finalSignatures, True)

                    document.Add(signatureTable)

                    ' Finalize document
                    document.Close()
                End Using
            End Using

            'PrintPDFNative(filePath)
            PDFPrinter.PrintPDF(filePath)
        Catch ex As Exception
            SendFormattedErrorMail(ex, query)
        End Try
    End Sub

    Sub AddWeighmentSlipHeader(document As Document, font As PdfFont, headerData As Dictionary(Of String, String))
        ' Add header information using helper method
        AddParagraph(document, "WEIGHMENT SLIP", font, defaultFontSize, TextAlignment.CENTER)
        AddParagraph(document, "ESL Steel Limited", font, defaultFontSize, TextAlignment.CENTER)

        ' Create table for header details
        Dim headerTable = CreateTable(6)

        ' Define header fields with their corresponding keys
        Dim headerFields As String(,) = {
        {"Gate Pass No.:", "GatePassNo", "", "", "Gate Entry Date:", "GateEntryDate"},
        {"Vehicle No.:", "VehicleNo", "", "", "Vehicle Type:", "VehicleType"},
        {"Transporter Code:", "TransporterCode", "", "", "Transporter Name:", "TransporterName"},
        {"First Wt Date Time:", "First Wt Date Time", "", "", "Second Wt Date Time:", "Second Wt Date Time"},
        {"First Weight:", "First Weight", "", "", "Second Weight:", "Second Weight"},
        {"Net Weight:", "Net Weight", "", "", "", ""}
    }

        ' Add rows dynamically using key-value pairs
        For row As Integer = 0 To headerFields.GetLength(0) - 1
            Dim rowData(5) As String
            For col As Integer = 0 To headerFields.GetLength(1) - 1
                Dim key As String = headerFields(row, col)
                rowData(col) = If(headerData.ContainsKey(key), headerData(key), key) ' Use key value if exists
            Next
            AddRowToTable(headerTable, font, defaultFontSize, rowData, True)
        Next

        ' Add table to document
        document.Add(headerTable)
    End Sub

    Sub AddParagraph(document As Document, text As String, font As PdfFont, fontSize As Single, alignment As TextAlignment)
        document.Add(New Paragraph(text).SetFont(font).SetFontSize(fontSize).SetTextAlignment(alignment))
    End Sub




    Function CreateTable(columnCount As Integer) As Table
        ' Create a table with the specified number of columns
        Dim table As New Table(UnitValue.CreatePercentArray(columnCount))
        table.SetWidth(UnitValue.CreatePercentValue(100)) ' Use 100% of the page width
        Return table
    End Function

    Function CreateTableWithDynamicColumnWidths(headers() As String) As Table
        ' Calculate widths for each column based on header length or use predefined ratios
        Dim columnWidths(headers.Length - 1) As Single

        For i As Integer = 0 To headers.Length - 1
            ' Estimate column width by using header length (or adjust manually for specific columns)
            columnWidths(i) = headers(i).Length * 10 ' Adjust the multiplier as needed
        Next

        ' Create a table with the specified column widths
        Dim table As New Table(UnitValue.CreatePercentArray(columnWidths))
        table.SetWidth(UnitValue.CreatePercentValue(100)) ' Use 100% of the page width

        Return table
    End Function


    Sub AddHeaderToTable(table As Table, font As PdfFont, fontSize As Single, headers() As String)
        ' Add headers to the table
        For Each header As String In headers
            table.AddHeaderCell(New Cell().Add(New Paragraph(header).SetFont(font).SetFontSize(fontSize)))
        Next
    End Sub

    Sub AddRowToTable(table As Table, font As PdfFont, fontSize As Single, data() As String, Optional borderless As Boolean = False)
        ' Add a row of data to the table
        For Each cellData As String In data
            Dim cell As Cell = New Cell().Add(New Paragraph(cellData).SetFont(font).SetFontSize(fontSize))
            If borderless Then
                cell.SetBorder(Border.NO_BORDER)
            End If
            table.AddCell(cell)
        Next
    End Sub

    Sub OpenPDF(filePath As String)
        Try
            Process.Start(New ProcessStartInfo(filePath) With {
                .UseShellExecute = True
            })
        Catch ex As Exception
            Console.WriteLine("Error opening PDF: " & ex.Message)
        End Try
    End Sub

    Function GetDbValue(reader As SqlDataReader, column As String) As String
        Return If(Not reader.IsDBNull(reader.GetOrdinal(column)), reader(column).ToString(), "")
    End Function

    Sub PrintPDFNative(pdfFile As String)
        Dim process As New Process()
        process.StartInfo.FileName = "print"
        process.StartInfo.Arguments = $"""{pdfFile}"""
        process.StartInfo.Verb = "print"
        process.StartInfo.UseShellExecute = True
        process.Start()
    End Sub
End Module
