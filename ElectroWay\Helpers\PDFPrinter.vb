﻿Imports System.Runtime.InteropServices
Imports System.IO
Imports System.Drawing.Printing
Imports System.Windows.Forms

'Public Class PDFPrinter
'    <DllImport("winspool.drv", SetLastError:=True, CharSet:=CharSet.Auto)>
'    Private Shared Function OpenPrinter(ByVal pPrinterName As String, ByRef phPrinter As IntPtr, ByVal pDefault As IntPtr) As Boolean
'    End Function

'    <DllImport("winspool.drv", SetLastError:=True, CharSet:=CharSet.Auto)>
'    Private Shared Function ClosePrinter(ByVal hPrinter As IntPtr) As Boolean
'    End Function

'    <DllImport("winspool.drv", SetLastError:=True, CharSet:=CharSet.Auto)>
'    Private Shared Function StartDocPrinter(ByVal hPrinter As IntPtr, ByVal Level As Integer, ByRef pDocInfo As DOCINFO) As Boolean
'    End Function

'    <DllImport("winspool.drv", SetLastError:=True, CharSet:=CharSet.Auto)>
'    Private Shared Function EndDocPrinter(ByVal hPrinter As IntPtr) As Boolean
'    End Function

'    <DllImport("winspool.drv", SetLastError:=True, CharSet:=CharSet.Auto)>
'    Private Shared Function StartPagePrinter(ByVal hPrinter As IntPtr) As Boolean
'    End Function

'    <DllImport("winspool.drv", SetLastError:=True, CharSet:=CharSet.Auto)>
'    Private Shared Function EndPagePrinter(ByVal hPrinter As IntPtr) As Boolean
'    End Function

'    <DllImport("winspool.drv", SetLastError:=True, CharSet:=CharSet.Auto)>
'    Private Shared Function WritePrinter(ByVal hPrinter As IntPtr, ByVal pBytes As Byte(), ByVal dwCount As Integer, ByRef dwWritten As Integer) As Boolean
'    End Function

'    <StructLayout(LayoutKind.Sequential)>
'    Private Structure DOCINFO
'        <MarshalAs(UnmanagedType.LPStr)> Public pDocName As String
'        <MarshalAs(UnmanagedType.LPStr)> Public pOutputFile As String
'        <MarshalAs(UnmanagedType.LPStr)> Public pDataType As String
'    End Structure

'    ''' <summary>
'    ''' Shows a printer selection dialog and prints the PDF to the selected printer.
'    ''' </summary>
'    ''' <param name="pdfFilePath">Path to the PDF file</param>
'    Public Shared Sub PrintPDF(pdfFilePath As String)
'        Using printDialog As New PrintDialog()
'            printDialog.PrinterSettings = New PrinterSettings()

'            ' Show printer selection dialog
'            If printDialog.ShowDialog() = DialogResult.OK Then
'                Dim selectedPrinter As String = printDialog.PrinterSettings.PrinterName
'                SendToPrinter(pdfFilePath, selectedPrinter)
'            End If
'        End Using
'    End Sub

'    ''' <summary>
'    ''' Sends the PDF file to the selected printer.
'    ''' </summary>
'    ''' <param name="pdfFilePath">Path to the PDF file</param>
'    ''' <param name="printerName">Selected printer name</param>
'    Private Shared Sub SendToPrinter(pdfFilePath As String, printerName As String)
'        Dim hPrinter As IntPtr = IntPtr.Zero
'        Dim di As DOCINFO = New DOCINFO()
'        di.pDocName = "PDF Print Job"
'        di.pDataType = "RAW"

'        ' Open the selected printer
'        If Not OpenPrinter(printerName, hPrinter, IntPtr.Zero) Then
'            Throw New System.ComponentModel.Win32Exception(Marshal.GetLastWin32Error())
'        End If

'        Try
'            ' Start the print job
'            If Not StartDocPrinter(hPrinter, 1, di) Then Throw New System.ComponentModel.Win32Exception(Marshal.GetLastWin32Error())
'            If Not StartPagePrinter(hPrinter) Then Throw New System.ComponentModel.Win32Exception(Marshal.GetLastWin32Error())

'            ' Read the PDF file into a byte array
'            Dim fileBytes As Byte() = File.ReadAllBytes(pdfFilePath)
'            Dim dwWritten As Integer = 0

'            ' Send data to printer
'            If Not WritePrinter(hPrinter, fileBytes, fileBytes.Length, dwWritten) Then
'                Throw New System.ComponentModel.Win32Exception(Marshal.GetLastWin32Error())
'            End If

'            ' End print job
'            EndPagePrinter(hPrinter)
'            EndDocPrinter(hPrinter)
'        Catch ex As Exception
'            SendFormattedErrorMail(ex)
'        Finally

'            ClosePrinter(hPrinter)
'        End Try
'    End Sub
'End Class

Imports System.Diagnostics

Public Class PDFPrinter
    ''' <summary>
    ''' Prints a PDF file using Adobe Acrobat Reader.
    ''' </summary>
    ''' 
        ' New method to print PDF directly from memory

    Public Shared Sub PrintPDFFromMemory(pdfBytes As Byte())
        ' Create a temporary file name but don't save it
        Dim tempFileName As String = Path.GetTempFileName()

        Try
            ' Write bytes to temporary file
            File.WriteAllBytes(tempFileName, pdfBytes)

            ' Print the temporary file
            PDFPrinter.PrintPDF(tempFileName)
        Finally
            ' Delete the temporary file after printing
            If File.Exists(tempFileName) Then
                File.Delete(tempFileName)
            End If
        End Try
    End Sub
    ''' <param name="pdfFilePath">Path to the PDF file</param>
    ''' <returns>True if print command was successfully sent</returns>
    Public Shared Function PrintPDF(pdfFilePath As String) As Boolean
        Try
            ' Verify PDF file exists
            If Not IO.File.Exists(pdfFilePath) Then
                Throw New IO.FileNotFoundException("PDF file not found.", pdfFilePath)
            End If

            ' Find Adobe Reader executable
            Dim acrobatPath As String = FindAdobeReaderPath()

            ' Setup the process
            Dim psi As New ProcessStartInfo()
            psi.FileName = acrobatPath
            psi.Arguments = $"/t ""{pdfFilePath}"" ""{My.Computer.Registry.GetValue("HKEY_CURRENT_USER\Software\Microsoft\Windows NT\CurrentVersion\Windows", "Device", "")?.ToString().Split(","c)(0)}"""
            psi.WindowStyle = ProcessWindowStyle.Hidden
            psi.UseShellExecute = False
            psi.CreateNoWindow = True

            ' Start the process
            Using process As Process = Process.Start(psi)
                ' Wait for a reasonable amount of time for the print job to be sent
                Return process.WaitForExit(10000)
            End Using

            Return True
        Catch ex As Exception
            ' Log the exception or handle it appropriately
            System.Diagnostics.Debug.WriteLine($"Error printing PDF: {ex.Message}")
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Finds the path to Adobe Reader executable
    ''' </summary>
    ''' <returns>Path to Adobe Reader executable</returns>
    Private Shared Function FindAdobeReaderPath() As String
        ' Try common installation paths - both 32-bit and 64-bit versions
        Dim possiblePaths As String() = {
            "C:\Program Files (x86)\Adobe\Reader 11.0\Reader\AcroRd32.exe",
            "C:\Program Files\Adobe\Reader 11.0\Reader\AcroRd32.exe",
            "C:\Program Files\Adobe\Reader 11.0\Reader\AcroRd64.exe",
            "C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
            "C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe",
            "C:\Program Files\Adobe\Acrobat Reader DC\Reader\AcroRd64.exe",
            "C:\Program Files (x86)\Adobe\Acrobat\Reader\AcroRd32.exe",
            "C:\Program Files\Adobe\Acrobat\Reader\AcroRd32.exe",
            "C:\Program Files\Adobe\Acrobat\Reader\AcroRd64.exe",
            "C:\Program Files (x86)\Adobe\Acrobat DC\Acrobat\Acrobat.exe",
            "C:\Program Files\Adobe\Acrobat DC\Acrobat\Acrobat.exe"
        }

        For Each path In possiblePaths
            If IO.File.Exists(path) Then
                Return path
            End If
        Next

        ' Try to find via registry
        Try
            Dim regPath As String = My.Computer.Registry.GetValue(
                "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\AcroRd32.exe",
                "", "")?.ToString()

            If Not String.IsNullOrEmpty(regPath) AndAlso IO.File.Exists(regPath) Then
                Return regPath
            End If

            ' Also try registry for 64-bit version
            regPath = My.Computer.Registry.GetValue(
                "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\AcroRd64.exe",
                "", "")?.ToString()

            If Not String.IsNullOrEmpty(regPath) AndAlso IO.File.Exists(regPath) Then
                Return regPath
            End If
        Catch
            ' Registry lookup failed, continue with search
        End Try

        ' If we get here, we couldn't find Adobe Reader
        Throw New Exception("Adobe Acrobat Reader is not installed or could not be found.")
    End Function
End Class

