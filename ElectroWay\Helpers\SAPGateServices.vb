﻿
Module SAPGateServices

    Public Function FetchVBRPData(ByVal vgbel As String) As SAPHelper.RFCExecutionResult(Of DataTable)
        ' Prepare RFC call parameters
        Dim rfcParams = New Dictionary(Of String, Object) From {
                    {"QUERY_TABLE", "VBRP"},
                    {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {
                            {"TEXT", $"VGBEL = '{vgbel}'"}
                        }
                    }},
                    {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
                        New Dictionary(Of String, Object) From {{"FIELDNAME", "VBELN"}},
                        New Dictionary(Of String, Object) From {{"FIELDNAME", "ERDAT"}},
                        New Dictionary(Of String, Object) From {{"FIELDNAME", "ERZET"}}
                    }}
                }

        Dim result As SAPHelper.RFCExecutionResult(Of DataTable) = SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)

        Return result
    End Function

    Public Function FetchGatePassData(Optional ByVal gatePass As String = "", Optional ByVal vehicleNo As String = "") As SAPHelper.RFCExecutionResult(Of DataTable)
        ' Build filter condition dynamically
        Dim conditions As New List(Of String)
        If Not String.IsNullOrWhiteSpace(gatePass) Then
            conditions.Add($"GATEPASS = '{gatePass.Trim()}'")
        End If
        If Not String.IsNullOrWhiteSpace(vehicleNo) Then
            conditions.Add($"VEHINO = '{vehicleNo.Trim()}'")
        End If

        Dim whereClause As String = String.Join(" AND ", conditions)

        ' Prepare parameters
        'Dim rfcParams As New Dictionary(Of String, Object) From {
        '    {"QUERY_TABLE", "ZTM_HGATE_PASS"},
        '    {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
        '        New Dictionary(Of String, Object) From {
        '            {"TEXT", whereClause}
        '        }
        '    }},
        '    {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
        '        New Dictionary(Of String, Object) From {{"FIELDNAME", "GATEPASS"}},
        '        New Dictionary(Of String, Object) From {{"FIELDNAME", "CREATE_DATE"}},
        '        New Dictionary(Of String, Object) From {{"FIELDNAME", "TRANSNAME"}},
        '        New Dictionary(Of String, Object) From {{"FIELDNAME", "CREATED_BY"}},
        '        New Dictionary(Of String, Object) From {{"FIELDNAME", "VEHINO"}}
        '    }}
        '}

        Dim rfcParams As New Dictionary(Of String, Object) From {
            {"QUERY_TABLE", "ZTM_HGATE_PASS"},
            {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {
                    {"TEXT", whereClause}
                }
            }},
            {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
                New Dictionary(Of String, Object) From {{"FIELDNAME", "GATEPASS"}}
            }}
        }

        ' Execute RFC and return the result
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)
    End Function

    Public Function FetchIGatePassItems(ByVal gatePass As String) As SAPHelper.RFCExecutionResult(Of DataTable)
        ' Prepare RFC parameters
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"QUERY_TABLE", "ZTM_IGATE_PASS"},
        {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {
                {"TEXT", $"GATEPASS = '{gatePass.Trim()}'"}
            }
        }},
        {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {{"FIELDNAME", "EBELN"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "EBELP"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MATNR"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MAKTX"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MENGE"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MEINS"}}
        }}
    }

        ' Call the SAP function
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)
    End Function
    Public Function FetchVendorFromPO(ByVal poNumber As String) As SAPHelper.RFCExecutionResult(Of DataTable)

        ' Prepare parameters for RFC_READ_TABLE on EKKO
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"QUERY_TABLE", "EKKO"},
        {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {
                {"TEXT", $"EBELN = '{poNumber.Trim()}'"}
            }
        }},
        {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {{"FIELDNAME", "LIFNR"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "RESWK"}}
        }}
    }

        ' Call RFC using default connection functionCtrl
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)

    End Function
    Public Function FetchVendorName(ByVal vendorCode As String) As SAPHelper.RFCExecutionResult(Of DataTable)

        ' Prepare parameters for RFC_READ_TABLE on LFA1
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"QUERY_TABLE", "LFA1"},
        {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {
                {"TEXT", $"LIFNR = '{vendorCode.Trim()}'"}
            }
        }},
        {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {{"FIELDNAME", "NAME1"}}
        }}
    }

        ' Execute the RFC call using the helper
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)

    End Function

    Public Function FetchCustomerInfoFromLIKP(ByVal deliveryNo As String) As SAPHelper.RFCExecutionResult(Of DataTable)

        ' Prepare parameters for RFC_READ_TABLE on LIKP
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"QUERY_TABLE", "LIKP"},
        {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {
                {"TEXT", $"VBELN = '{deliveryNo.Trim()}'"}
            }
        }},
        {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {{"FIELDNAME", "VBELN"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "KUNAG"}}, ' Sold-To Party
            New Dictionary(Of String, Object) From {{"FIELDNAME", "KUNNR"}}  ' Ship-To Party
        }}
    }

        ' Execute the RFC call using the helper
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)

    End Function

    Public Function FetchCustomerNameFromKNA1(ByVal soldToParty As String, ByVal shipToParty As String) As SAPHelper.RFCExecutionResult(Of DataTable)

        ' Decide which customer number to use
        Dim customerNumber As String = If(Not String.IsNullOrWhiteSpace(soldToParty), soldToParty.Trim(), shipToParty.Trim())

        ' Prepare RFC parameters
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"QUERY_TABLE", "KNA1"},
        {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {
                {"TEXT", $"KUNNR = '{customerNumber}'"}
            }
        }},
        {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {{"FIELDNAME", "NAME1"}}
        }}
    }

        ' Execute the RFC call
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)

    End Function

    Public Function FetchVehicleNumber(ByVal gateEntryNumber As String) As SAPHelper.RFCExecutionResult(Of DataTable)

        ' Prepare parameters for RFC_READ_TEXT
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"TDOBJECT", "VBBK"},
        {"TDNAME", gateEntryNumber.Trim()},
        {"TDID", "ZDL2"},
        {"TDSPRAS", "E"} ' Language key (E = English), adjust if needed
    }

        ' Execute the RFC call using helper
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TEXT", rfcParams)

    End Function

    Public Function FetchSalesDeliveryItems(gateEntryNumber As String) As SAPHelper.RFCExecutionResult(Of DataTable)
        ' Prepare parameters for RFC_READ_TABLE on LIPS
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"QUERY_TABLE", "LIPS"},
        {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {
                {"TEXT", $"VBELN = '{gateEntryNumber.Trim()}'"}
            }
        }},
        {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {{"FIELDNAME", "VGBEL"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "VBELN"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "POSNR"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MATNR"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "ARKTX"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "LFIMG"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "VRKME"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "VGPOS"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "WERKS"}}
        }}
    }

        ' Execute RFC and return result
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)
    End Function

    Public Function FetchTransporterInfo(gateEntryNumber As String) As SAPHelper.RFCExecutionResult(Of DataTable)
        ' Prepare parameters for RFC_READ_TABLE on VBPA
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"QUERY_TABLE", "VBPA"},
        {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {
                {"TEXT", $"VBELN = '{gateEntryNumber.Trim()}' AND (PARVW LIKE 'T%' OR PARVW LIKE 'S%')"}
            }
        }},
        {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {{"FIELDNAME", "VBELN"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "PARVW"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "LIFNR"}}
        }}
    }

        ' Execute the RFC call
        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)
    End Function

    Public Function FetchGateEntryIssueDetails(gateEntryNo As String, vehicleNo As String) As SAPHelper.RFCExecutionResult(Of DataTable)
        Dim rfcParams As New Dictionary(Of String, Object) From {
        {"QUERY_TABLE", "ZSECSL_ISSUE"},
        {"OPTIONS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {
                {"TEXT", $"ZORDER_NO = '{gateEntryNo.Trim()}' AND VEH_NO = '{vehicleNo.Trim()}'"}
            }
        }},
        {"FIELDS", New List(Of Dictionary(Of String, Object)) From {
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MATNR"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MAKTX"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MENGE_ISS"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "MAINS_ISS"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "TRNS_NAME"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "DRV_NAME"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "KUNNR"}},
            New Dictionary(Of String, Object) From {{"FIELDNAME", "NAME1"}}
        }}
    }

        Return SAPHelper.ExecuteRFC(Of DataTable)("RFC_READ_TABLE", rfcParams)
    End Function

End Module