﻿Imports System.Configuration
Imports System.Reflection
Imports System.Text
Imports SAP.Middleware.Connector

Public Class SAPHelper
    Private Shared _sapDestination As RfcDestination

    ''' <summary>
    ''' Connection result class to represent connection status
    ''' </summary>
    Public Class ConnectionResult
        Public Property IsSuccessful As Boolean
        Public Property Message As String
    End Class

    ''' <summary>
    ''' Generic RFC Execution Result class
    ''' </summary>
    Public Class RFCExecutionResult(Of T)
        Public Property IsSuccessful As Boolean
        Public Property Data As T
        Public Property ErrorMessage As String
    End Class

    ''' <summary>
    ''' Reads SAP connection parameters from app.config
    ''' </summary>
    Private Shared Function GetSAPConfig() As RfcConfigParameters
        Dim config As New RfcConfigParameters()
        config.Add(RfcConfigParameters.Name, ConfigurationManager.AppSettings("SAP_Name"))
        config.Add(RfcConfigParameters.AppServerHost, ConfigurationManager.AppSettings("SAP_ASHOST"))
        config.Add(RfcConfigParameters.SystemNumber, ConfigurationManager.AppSettings("SAP_SYSNR"))
        config.Add(RfcConfigParameters.Client, ConfigurationManager.AppSettings("SAP_CLIENT"))
        config.Add(RfcConfigParameters.User, ConfigurationManager.AppSettings("SAP_USER"))
        config.Add(RfcConfigParameters.Password, ConfigurationManager.AppSettings("SAP_PASSWD"))
        config.Add(RfcConfigParameters.Language, ConfigurationManager.AppSettings("SAP_LANG"))
        Return config
    End Function

    ''' <summary>
    ''' Checks if a parameter exists in the function module metadata
    ''' </summary>
    Private Shared Function ParameterExists(ByVal functionModule As IRfcFunction, ByVal parameterName As String) As Boolean
        Dim metadata As RfcFunctionMetadata = functionModule.Metadata
        For i As Integer = 0 To metadata.ParameterCount - 1
            If metadata.Item(i).Name = parameterName Then
                Return True
            End If
        Next
        Return False
    End Function

    ''' <summary>
    ''' Establishes and validates SAP connection with comprehensive error handling
    ''' </summary>
    Public Shared Function EstablishSAPConnection() As ConnectionResult
        Dim result As New ConnectionResult()
        Try
            Dim config As RfcConfigParameters = GetSAPConfig()
            _sapDestination = RfcDestinationManager.GetDestination(config)

            ' Minimal connection validation
            If _sapDestination IsNot Nothing Then
                result.IsSuccessful = True
                result.Message = "SAP Connection established successfully"
            Else
                result.IsSuccessful = False
                result.Message = "Unable to establish SAP connection"
            End If
        Catch ex As Exception
            result.IsSuccessful = False
            result.Message = $"Connection error: {ex.Message}"
        End Try
        Return result
    End Function

    ''' <summary>
    ''' Dynamically sets input parameters for RFC function module
    ''' </summary>
    Private Shared Sub SetInputParameters(ByVal functionModule As IRfcFunction, ByVal inputParameters As Object)
        If inputParameters Is Nothing Then Return

        If TypeOf inputParameters Is Dictionary(Of String, Object) Then
            Dim dict = DirectCast(inputParameters, Dictionary(Of String, Object))
            For Each kvp In dict
                If ParameterExists(functionModule, kvp.Key) Then
                    Dim paramValue = kvp.Value

                    ' Handle TABLE parameters correctly
                    If functionModule.Metadata.Item(kvp.Key).DataType = RfcDataType.TABLE Then
                        Dim table As IRfcTable = functionModule.GetTable(kvp.Key)

                        ' Handle List of Dictionary (expected format for FIELDS & OPTIONS)
                        If TypeOf paramValue Is List(Of Dictionary(Of String, Object)) Then
                            For Each rowDict As Dictionary(Of String, Object) In DirectCast(paramValue, List(Of Dictionary(Of String, Object)))
                                table.Append() ' Append a new row
                                For Each field In rowDict
                                    table.SetValue(field.Key, field.Value) ' Set values for the new row
                                Next
                            Next
                        End If
                    Else
                        functionModule.SetValue(kvp.Key, paramValue)
                    End If
                End If
            Next
        End If
    End Sub

    ''' <summary>
    ''' Maps RFC output to a strongly-typed object
    ''' </summary>
    Private Shared Function MapOutputToType(Of T As New)(
        ByVal functionModule As IRfcFunction,
        ByVal outputType As Type) As T

        Dim result As New T()
        Dim metadata As RfcFunctionMetadata = functionModule.Metadata

        For i As Integer = 0 To metadata.ParameterCount - 1
            Dim paramMetadata As RfcParameterMetadata = metadata.Item(i)
            Dim propertyInfo As PropertyInfo = outputType.GetProperty(
                paramMetadata.Name,
                BindingFlags.Public Or BindingFlags.Instance Or BindingFlags.IgnoreCase
            )

            If propertyInfo IsNot Nothing AndAlso
               (paramMetadata.Direction = RfcDirection.EXPORT OrElse
                paramMetadata.Direction = RfcDirection.CHANGING) Then

                Dim value As Object = functionModule.GetValue(paramMetadata.Name)
                propertyInfo.SetValue(result, value)
            End If
        Next

        Return result
    End Function

    ''' <summary>
    ''' Maps RFC output to a dynamic object if no specific type is provided
    ''' </summary>
    Private Shared Function MapOutputWithEnhancedTyping(Of T As New)(
        ByVal functionModule As IRfcFunction) As T

        Dim result As New Dictionary(Of String, Object)()
        Dim metadata As RfcFunctionMetadata = functionModule.Metadata

        For i As Integer = 0 To metadata.ParameterCount - 1
            Dim paramMetadata As RfcParameterMetadata = metadata.Item(i)

            Try
                Select Case paramMetadata.Direction
                    Case RfcDirection.EXPORT, RfcDirection.CHANGING
                        result(paramMetadata.Name) = ConvertSAPValue(
                        functionModule.GetValue(paramMetadata.Name),
                        paramMetadata.DataType)

                    Case RfcDirection.TABLES
                        result(paramMetadata.Name) = ConvertTableWithTyping(
                        functionModule.GetTable(paramMetadata.Name))
                End Select
            Catch ex As Exception
                ' Optionally log conversion errors
            End Try
        Next

        Return DirectCast(CType(result, Object), T)
    End Function

    ''' <summary>
    ''' Advanced RFC execution with comprehensive support for all RFC parameter types and directions
    ''' </summary>
    Public Shared Function ExecuteRFC(Of T As New)(
    ByVal functionName As String,
    ByVal inputParameters As Object,
    Optional ByVal outputType As Type = Nothing) As RFCExecutionResult(Of T)

        Dim result As New RFCExecutionResult(Of T)()
        result.IsSuccessful = True ' Default to success unless an error occurs

        Try
            ' Validate connection
            Dim connectionResult = EstablishSAPConnection()
            If Not connectionResult.IsSuccessful Then
                result.ErrorMessage = connectionResult.Message
                result.IsSuccessful = False
                Return result
            End If

            ' Create function module
            Dim functionModule As IRfcFunction = _sapDestination.Repository.CreateFunction(functionName)

            ' Set input parameters
            SetInputParameters(functionModule, inputParameters)

            ' Execute RFC
            functionModule.Invoke(_sapDestination)

            ' Process output
            If outputType IsNot Nothing Then
                result.Data = MapOutputToType(Of T)(functionModule, outputType)
            Else
                ' This could be a dictionary or another object type
                Dim initialData = MapOutputWithEnhancedTyping(Of Object)(functionModule)

                ' Check if we need to process FIELDS/DATA structure
                Dim responseDict As Dictionary(Of String, Object) = TryCast(initialData, Dictionary(Of String, Object))
                If responseDict IsNot Nothing AndAlso
               responseDict.ContainsKey("FIELDS") AndAlso
               responseDict.ContainsKey("DATA") Then

                    ' Process based on the requested return type
                    If GetType(T) Is GetType(DataTable) Then
                        ' Process and convert to DataTable
                        Dim processedData = ProcessSAPTabularData(responseDict)
                        Dim dataTable = ConvertToDataTable(processedData)
                        result.Data = DirectCast(CObj(dataTable), T)
                    ElseIf GetType(T) Is GetType(String) Then
                        ' Process and convert to JSON string
                        Dim processedData = ProcessSAPTabularData(responseDict)
                        Dim jsonString = SerializeToJson(processedData)
                        result.Data = DirectCast(CObj(jsonString), T)
                    Else
                        ' For other types, just return the mapped data
                        result.Data = DirectCast(CObj(initialData), T)
                    End If
                Else
                    ' If not a FIELDS/DATA structure, try direct conversion
                    result.Data = DirectCast(CObj(initialData), T)
                End If
            End If

            ' Check for SAP errors
            CheckForSAPErrors(functionModule, result)

        Catch ex As InvalidCastException

            SendFormattedErrorMail(ex)
            result.IsSuccessful = False
        Catch ex As RfcBaseException
            Dim inputP As StringBuilder = New StringBuilder()

            ' Process dictionary
            AppendDictionaryContents(inputParameters, inputP)

            ' Sending formatted email with structured input parameters
            SendFormattedErrorMail(ex, , String.Concat("Function Name: ", functionName, ", Input Param: ", inputP.ToString()))
            result.IsSuccessful = False
        Catch ex As Exception
            SendFormattedErrorMail(ex)
            result.IsSuccessful = False
        End Try

        Return result
    End Function

    ''' <summary>
    ''' Processes SAP tabular data with FIELDS and DATA structure
    ''' </summary>
    Private Shared Function ProcessSAPTabularData(ByVal responseData As Dictionary(Of String, Object)) As List(Of Dictionary(Of String, String))
        ' Get fields and data from response
        Dim fieldsList = TryCast(responseData("FIELDS"), List(Of Dictionary(Of String, Object)))
        Dim dataList = TryCast(responseData("DATA"), List(Of Dictionary(Of String, Object)))

        If fieldsList Is Nothing OrElse dataList Is Nothing Then
            Throw New ArgumentException("Invalid FIELDS or DATA format in response")
        End If

        ' Convert fields to dictionary for quick lookup
        Dim fieldMapping = fieldsList.ToDictionary(
        Function(f) f("FIELDNAME").ToString(),
        Function(f) New With {
            .Offset = Convert.ToInt32(f("OFFSET")),
            .Length = Convert.ToInt32(f("LENGTH"))
        }
    )

        ' Process records
        Dim processedRecords As New List(Of Dictionary(Of String, String))
        For Each record In dataList
            Dim extractedValues As New Dictionary(Of String, String)

            Dim rawData As String = record("WA").ToString()
            For Each kvp In fieldMapping
                ' Ensure we don't go beyond the string length
                Dim fieldOffset = kvp.Value.Offset

                ' Skip if offset is beyond the string length
                If fieldOffset >= rawData.Length Then
                    extractedValues(kvp.Key) = String.Empty
                    Continue For
                End If

                Dim fieldLength = Math.Min(kvp.Value.Length, rawData.Length - fieldOffset)
                Dim extractedValue = rawData.Substring(fieldOffset, fieldLength)
                extractedValues(kvp.Key) = extractedValue.Trim()
            Next

            processedRecords.Add(extractedValues)
        Next

        Return processedRecords
    End Function

    ''' <summary>
    ''' Serializes data to JSON
    ''' </summary>
    Private Shared Function SerializeToJson(ByVal data As List(Of Dictionary(Of String, String))) As String
        ' Try to use Newtonsoft.Json if available
        Try
            ' This will throw an exception if Newtonsoft.Json is not available
            Dim jsonConvert = GetType(Newtonsoft.Json.JsonConvert)
            Return Newtonsoft.Json.JsonConvert.SerializeObject(data)
        Catch ex As Exception
            ' Fallback to manual serialization
            Return ManualJsonSerialize(data)
        End Try
    End Function

    ''' <summary>
    ''' Simple manual JSON serializer
    ''' </summary>
    Private Shared Function ManualJsonSerialize(ByVal data As List(Of Dictionary(Of String, String))) As String
        Dim sb As New System.Text.StringBuilder("[")

        For i As Integer = 0 To data.Count - 1
            If i > 0 Then sb.Append(",")
            sb.Append("{")

            Dim keyCount As Integer = 0
            For Each kvp In data(i)
                If keyCount > 0 Then sb.Append(",")

                ' Escape the value - basic version
                Dim escapedValue As String = kvp.Value.Replace("\", "\\").Replace("""", "\""").Replace(vbCr, "\\r").Replace(vbLf, "\\n")

                sb.Append($"""{kvp.Key}"":""{escapedValue}""")
                keyCount += 1
            Next

            sb.Append("}")
        Next

        sb.Append("]")
        Return sb.ToString()
    End Function

    ''' <summary>
    ''' Converts processed SAP data to DataTable
    ''' </summary>
    Private Shared Function ConvertToDataTable(ByVal data As List(Of Dictionary(Of String, String))) As DataTable
        If data.Count = 0 Then
            Return New DataTable()
        End If

        ' Create DataTable structure from the first record
        Dim dataTable As New DataTable()
        For Each field In data(0).Keys
            dataTable.Columns.Add(field, GetType(String))
        Next

        ' Add data rows
        For Each record In data
            Dim dataRow = dataTable.NewRow()
            For Each kvp In record
                dataRow(kvp.Key) = kvp.Value
            Next
            dataTable.Rows.Add(dataRow)
        Next

        Return dataTable
    End Function
    ''' <summary>
    ''' Checks for SAP-specific error indicators in function module
    ''' </summary>
    Private Shared Sub CheckForSAPErrors(Of T)(
        ByVal functionModule As IRfcFunction,
        ByRef result As RFCExecutionResult(Of T))

        ' Check for common SAP error parameters
        Dim errorCheckParameters() As String = {"RETURN", "ET_RETURN", "EV_ERROR", "ES_RETURN"}

        For Each paramName In errorCheckParameters
            If ParameterExists(functionModule, paramName) Then
                Dim returnValue As Object = Nothing
                Try
                    returnValue = functionModule.GetValue(paramName)
                Catch
                    Continue For
                End Try

                ' Handle different return types
                If returnValue IsNot Nothing Then
                    ' For table returns (multiple error messages)
                    If TypeOf returnValue Is IRfcTable Then
                        Dim errorTable As IRfcTable = DirectCast(returnValue, IRfcTable)
                        If errorTable.RowCount > 0 Then
                            Dim errorMessages As New List(Of String)()
                            For i As Integer = 0 To errorTable.RowCount - 1
                                errorTable.CurrentIndex = i
                                ' Adapt these field names based on your SAP system's standard
                                Dim message = errorTable.GetString("MESSAGE")
                                If Not String.IsNullOrEmpty(message) Then
                                    errorMessages.Add(message)
                                End If
                            Next

                            If errorMessages.Count > 0 Then
                                result.IsSuccessful = False
                                result.ErrorMessage = String.Join("; ", errorMessages)
                            End If
                        End If
                        ' For single string/structure returns
                    ElseIf TypeOf returnValue Is String AndAlso Not String.IsNullOrEmpty(returnValue.ToString()) Then
                        result.IsSuccessful = False
                        result.ErrorMessage = returnValue.ToString()
                    End If
                End If
            End If
        Next
    End Sub

    ''' <summary>
    ''' Converts SAP-specific date format to .NET DateTime
    ''' </summary>
    Private Shared Function ConvertSAPDate(ByVal sapDate As Object) As DateTime
        If sapDate Is Nothing OrElse String.IsNullOrWhiteSpace(sapDate.ToString()) Then
            Return DateTime.MinValue
        End If

        Try
            ' Typical SAP date format is "YYYYMMDD"
            Dim dateString As String = sapDate.ToString().Trim()
            If dateString.Length = 8 Then
                Return New DateTime(
                    Integer.Parse(dateString.Substring(0, 4)),
                    Integer.Parse(dateString.Substring(4, 2)),
                    Integer.Parse(dateString.Substring(6, 2))
                )
            End If
        Catch
            ' Return minimal date if parsing fails
        End Try

        Return DateTime.MinValue
    End Function

    ''' <summary>
    ''' Converts SAP-specific time format to TimeSpan
    ''' </summary>
    Private Shared Function ConvertSAPTime(ByVal sapTime As Object) As TimeSpan
        If sapTime Is Nothing OrElse String.IsNullOrWhiteSpace(sapTime.ToString()) Then
            Return TimeSpan.Zero
        End If

        Try
            ' Typical SAP time format is "HHMMSS"
            Dim timeString As String = sapTime.ToString().Trim()
            If timeString.Length = 6 Then
                Return New TimeSpan(
                    Integer.Parse(timeString.Substring(0, 2)),
                    Integer.Parse(timeString.Substring(2, 2)),
                    Integer.Parse(timeString.Substring(4, 2))
                )
            End If
        Catch
            ' Return zero TimeSpan if parsing fails
        End Try

        Return TimeSpan.Zero
    End Function

    ''' <summary>
    ''' Converts SAP values to appropriate .NET types
    ''' </summary>
    Private Shared Function ConvertSAPValue(
    ByVal value As Object,
    ByVal sapDataType As RfcDataType) As Object

        ' Implement intelligent type conversion logic
        Select Case sapDataType
            Case RfcDataType.NUM
                Return Convert.ToInt64(value)
            Case RfcDataType.CHAR, RfcDataType.STRING
                Return value.ToString().Trim()
            Case RfcDataType.DATE
                Return ConvertSAPDate(value)
            Case RfcDataType.TIME
                Return ConvertSAPTime(value)
            Case RfcDataType.FLOAT
                Return Convert.ToDouble(value)
            Case Else
                Return value
        End Select
    End Function

    ''' <summary>
    ''' Converts SAP table with type-specific conversions
    ''' </summary>
    Private Shared Function ConvertTableWithTyping(
    ByVal table As IRfcTable) As List(Of Dictionary(Of String, Object))

        Dim tableData As New List(Of Dictionary(Of String, Object))()

        For i As Integer = 0 To table.RowCount - 1
            table.CurrentIndex = i
            Dim row As New Dictionary(Of String, Object)()

            For j As Integer = 0 To table.Metadata.LineType.FieldCount - 1
                Dim fieldMetadata = table.Metadata.LineType.Item(j)
                row(fieldMetadata.Name) = ConvertSAPValue(
                table.GetValue(fieldMetadata.Name),
                fieldMetadata.DataType)
            Next

            tableData.Add(row)
        Next

        Return tableData
    End Function

    ''' <summary>
    ''' Safely retrieves a value from the RFC result dictionary
    ''' </summary>
    Public Shared Function GetValue(Of T)(
        result As RFCExecutionResult(Of Dictionary(Of String, Object)),
        key As String) As T

        If result Is Nothing OrElse result.Data Is Nothing Then
            Return Nothing
        End If

        If result.Data.ContainsKey(key) Then
            Return DirectCast(result.Data(key), T)
        End If

        Return Nothing
    End Function

    ''' <summary>
    ''' Retrieves a list of dictionaries from a table-type result
    ''' </summary>
    Public Shared Function GetTableData(
        result As RFCExecutionResult(Of Dictionary(Of String, Object)),
        tableName As String) As List(Of Dictionary(Of String, Object))

        Dim tableData = GetValue(Of List(Of Dictionary(Of String, Object)))(result, tableName)
        Return If(tableData, New List(Of Dictionary(Of String, Object)))
    End Function

    ''' <summary>
    ''' Prints detailed information about the RFC result for debugging
    ''' </summary>
    Public Shared Sub PrintResultDetails(
        result As RFCExecutionResult(Of Dictionary(Of String, Object)))

        If Not result.IsSuccessful Then
            Console.WriteLine($"RFC Call Failed: {result.ErrorMessage}")
            Return
        End If

        Console.WriteLine("RFC Result Details:")
        For Each kvp In result.Data
            Console.WriteLine($"Key: {kvp.Key}")
            Console.WriteLine($"Value Type: {kvp.Value?.GetType().Name}")

            If TypeOf kvp.Value Is List(Of Dictionary(Of String, Object)) Then
                Dim list = DirectCast(kvp.Value, List(Of Dictionary(Of String, Object)))
                Console.WriteLine($"List Count: {list.Count}")
            End If
        Next
    End Sub


End Class