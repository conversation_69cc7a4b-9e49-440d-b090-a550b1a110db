﻿Imports DocumentFormat.OpenXml.Spreadsheet

Public Class SLExcelStatus
    Public Property Message As String
    Public ReadOnly Property Success As Boolean
        Get
            Return String.IsNullOrWhiteSpace(Message)
        End Get
    End Property
End Class

Public Class SLExcelData
    Public Property Status As SLExcelStatus
    Public Property ColumnConfigurations As Columns
    Public Property Headers As List(Of String)
    Public Property DataRows As List(Of List(Of String))
    Public Property SheetName As String

    Public Sub New()
        Status = New SLExcelStatus()
        Headers = New List(Of String)()
        DataRows = New List(Of List(Of String))()
    End Sub
End Class

