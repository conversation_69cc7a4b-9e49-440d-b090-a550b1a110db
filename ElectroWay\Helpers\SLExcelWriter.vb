﻿Imports DocumentFormat.OpenXml
Imports DocumentFormat.OpenXml.Packaging
Imports DocumentFormat.OpenXml.Spreadsheet
Imports System.IO
Imports System

Public Module Helpers
    Public Sub ExportDataSet(ds As DataSet, destination As String)
        Using workbook = SpreadsheetDocument.Create(destination, SpreadsheetDocumentType.Workbook)
            Dim workbookPart = workbook.AddWorkbookPart()

            workbook.WorkbookPart.Workbook = New Workbook With {
                .Sheets = New Sheets()
            }

            For Each table As DataTable In ds.Tables

                Dim sheetPart = workbook.WorkbookPart.AddNewPart(Of WorksheetPart)()
                Dim sheetData = New SheetData()
                sheetPart.Worksheet = New Worksheet(sheetData)

                Dim sheets As Sheets = workbook.WorkbookPart.Workbook.GetFirstChild(Of Sheets)()
                Dim relationshipId As String = workbook.WorkbookPart.GetIdOfPart(sheetPart)

                Dim sheetId As UInteger = 1
                If sheets.Elements(Of Sheet)().Count() > 0 Then
                    sheetId = sheets.Elements(Of Sheet)().[Select](Function(s) s.SheetId.Value).Max() + 1
                End If

                Dim sheet As Sheet = New Sheet() With {
                .Id = relationshipId,
                .SheetId = sheetId,
                .Name = table.TableName
            }
                sheets.Append(sheet)

                Dim headerRow As Row = New Row()

                Dim columns As List(Of String) = New List(Of String)()
                For Each column As Data.DataColumn In table.Columns
                    columns.Add(column.ColumnName)

                    Dim cell As New Cell With {
                        .DataType = CellValues.[String],
                        .CellValue = New CellValue(column.ColumnName)
                    }
                    headerRow.AppendChild(cell)
                Next


                sheetData.AppendChild(headerRow)

                For Each dsrow As Data.DataRow In table.Rows
                    Dim newRow As New Row()
                    For Each col In columns
                        Dim cell As New Cell With {
                            .DataType = CellValues.[String],
                            .CellValue = New CellValue(dsrow(col).ToString()) '
                            }
                        newRow.AppendChild(cell)
                    Next

                    sheetData.AppendChild(newRow)
                Next

            Next
        End Using
    End Sub
End Module

