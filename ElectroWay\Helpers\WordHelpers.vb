﻿Imports DocumentFormat.OpenXml
Imports DocumentFormat.OpenXml.Packaging
Imports DocumentFormat.OpenXml.Wordprocessing

Module WordHelpers

    Sub GateEntrySlipCreate()


        ' Define the file path where the Word document will be saved
        Dim filePath As String = "GateEntrySlip.docx"

            ' Create a Word document
            Using wordDoc As WordprocessingDocument = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document)
                ' Add a main document part
                Dim mainPart As MainDocumentPart = wordDoc.AddMainDocumentPart()
                mainPart.Document = New Document()
                Dim body As Body = mainPart.Document.AppendChild(New Body())

                ' Header Information
                body.AppendChild(CreateParagraph("GATE ENTRY SLIP", JustificationValues.Center, True))
                body.AppendChild(CreateParagraph("ESL Steel Limited", JustificationValues.Left, True))
                body.AppendChild(CreateParagraph("Gate Pass No.: VED\ES01\2024\2225385", JustificationValues.Left, False))
                body.AppendChild(CreateParagraph("Gate Entry Date: 08-Oct-2024 6:45:26 pm", JustificationValues.Left, False))
                body.AppendChild(CreateParagraph("Vehicle No.: RJ14GT8115", JustificationValues.Left, False))
                body.AppendChild(CreateParagraph("Vehicle Type: SELF", JustificationValues.Left, False))
                body.AppendChild(CreateParagraph("Transporter Code: 0000200373", JustificationValues.Left, False))
                body.AppendChild(CreateParagraph("Transporter Name: ISHIKA MINERALS", JustificationValues.Left, False))


            ' Divider (Horizontal Rule)
            Dim dividerParagraph As New Paragraph(New Run(New Text("––––––––––––––––––––––––––––––––––––––––––––")))
                dividerParagraph.ParagraphProperties = New ParagraphProperties(New Justification() With {.Val = JustificationValues.Center})
                body.AppendChild(dividerParagraph)

                ' Material Details in a Table
                Dim materialTable As New Table()

                ' Set table properties (borders)
                Dim tableProperties As New TableProperties(
                New TableBorders(
                    New TopBorder() With {.Val = BorderValues.Single, .Size = 6},
                    New BottomBorder() With {.Val = BorderValues.Single, .Size = 6},
                    New LeftBorder() With {.Val = BorderValues.Single, .Size = 6},
                    New RightBorder() With {.Val = BorderValues.Single, .Size = 6},
                    New InsideHorizontalBorder() With {.Val = BorderValues.Single, .Size = 6},
                    New InsideVerticalBorder() With {.Val = BorderValues.Single, .Size = 6}
                )
            )
                materialTable.AppendChild(tableProperties)

            ' Add header row for material details
            Dim headerRow As New TableRow()
            headerRow.Append(CreateTableCell("SAP Gate Entry No"))
            headerRow.Append(CreateTableCell("PO No."))
            headerRow.Append(CreateTableCell("Line Item"))
            headerRow.Append(CreateTableCell("Mat Code"))
            headerRow.Append(CreateTableCell("Mat. Description"))
            headerRow.Append(CreateTableCell("Challan No"))
            headerRow.Append(CreateTableCell("Challan Qty"))
            headerRow.Append(CreateTableCell("UOM"))
            headerRow.Append(CreateTableCell("Vendor"))
                materialTable.Append(headerRow)

            ' Add data row with material details
            Dim materialDataRow As New TableRow()
            materialDataRow.Append(CreateTableCell("2024071934"))
            materialDataRow.Append(CreateTableCell("2024071934"))
            materialDataRow.Append(CreateTableCell("00010"))
            materialDataRow.Append(CreateTableCell("MTX111116080"))
            materialDataRow.Append(CreateTableCell("LIMESTN PULVRZD; CALCINED"))
            materialDataRow.Append(CreateTableCell("IM/24-25/511"))
            materialDataRow.Append(CreateTableCell("41.0000"))
            materialDataRow.Append(CreateTableCell("TON"))
            materialDataRow.Append(CreateTableCell("ISHIKA MINERALS"))
                materialTable.Append(materialDataRow)

                ' Append the table to the document body
                body.AppendChild(materialTable)

            ' Divider (Horizontal Rule for Signature Section)
            'body.AppendChild(CreateParagraph("––––––––––––––––––––––––––––––––––––––––––––", JustificationValues.Center, False))

            ' Signature Table (No borders, two rows)
            Dim signatureTable As New Table()

                ' No borders for the signature table
                Dim noBorderTableProperties As New TableProperties(
                New TableBorders(
                    New TopBorder() With {.Val = BorderValues.None},
                    New BottomBorder() With {.Val = BorderValues.None},
                    New LeftBorder() With {.Val = BorderValues.None},
                    New RightBorder() With {.Val = BorderValues.None},
                    New InsideHorizontalBorder() With {.Val = BorderValues.None},
                    New InsideVerticalBorder() With {.Val = BorderValues.None}
                )
            )
                signatureTable.AppendChild(noBorderTableProperties)

                ' First row for OUT Gate - Security
                Dim signatureRow1 As New TableRow()
                signatureRow1.Append(CreateTableCell("OUT Security"))
                signatureRow1.Append(CreateTableCell("OUT Gate - Security Store"))
                signatureRow1.Append(CreateTableCell("SAP Gate OUT"))
                signatureTable.Append(signatureRow1)

                ' Second row for IN Gate - Security
                Dim signatureRow2 As New TableRow()
                signatureRow2.Append(CreateTableCell("IN Gate Security"))
                signatureRow2.Append(CreateTableCell("First Weight"))
                signatureRow2.Append(CreateTableCell("Second Weight"))
                signatureRow2.Append(CreateTableCell("Unloading/Loading Point"))
                signatureTable.Append(signatureRow2)

                ' Append the signature table to the document body
                body.AppendChild(signatureTable)

                ' Final Save
                wordDoc.MainDocumentPart.Document.Save()
            End Using

            Console.WriteLine("Word document created: " & filePath)
        End Sub

        ' Helper function to create a paragraph with specified text and alignment
        Function CreateParagraph(text As String, alignment As JustificationValues, isBold As Boolean) As Paragraph
            Dim paragraph As New Paragraph()
            Dim run As New Run()
            Dim textElement As New Text(text)

            ' Make text bold if specified
            If isBold Then
                run.Append(New Bold())
            End If

            run.Append(textElement)
            paragraph.Append(run)

            ' Set paragraph alignment
            Dim paragraphProperties As New ParagraphProperties()
            Dim justification As New Justification() With {.Val = alignment}
            paragraphProperties.Append(justification)
            paragraph.Append(paragraphProperties)

            Return paragraph
        End Function

        ' Helper function to create a table cell with text content
        Function CreateTableCell(text As String) As TableCell
            Dim cell As New TableCell()
            Dim paragraph As New Paragraph(New Run(New Text(text)))
            cell.Append(paragraph)

            ' Set cell properties (optional, can add borders, alignment, etc.)
            Dim cellProperties As New TableCellProperties(
            New TableCellWidth() With {.Type = TableWidthUnitValues.Auto}
        )
            cell.Append(cellProperties)

            Return cell
        End Function
    End Module
