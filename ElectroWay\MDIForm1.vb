﻿Imports System.Windows.Forms
Imports System.Net
Imports System.Configuration

Public Class MDIForm1
    Dim cc As New Class1
    Dim statusBarMain As New StatusBar
    Dim statusBarDate = New StatusBarPanel
    Dim statusBarTime = New StatusBarPanel
    Dim statusBarCAPS = New StatusBarPanel

    Private Sub MDIForm1_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        Application.Exit()
    End Sub


    <Obsolete>
    Private Sub MDIForm1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Create a StatusBar
        'Dim statusBarMain As New StatusBar

        statusBarMain.Name = "StatusBar"
        statusBarMain.ShowPanels = True

        'Create the panels
        Dim statusBarCompany = New StatusBarPanel With {
            .Name = "StatusBarCompany",
            .Text = "Designed & Developed by ELECTROSTEEL ",
            .AutoSize = StatusBarPanelAutoSize.None,
            .Width = 800
        }
        statusBarMain.Panels.Add(statusBarCompany)
        '--------------------------------------------------
        Dim statusBarUser = New StatusBarPanel With {
            .Name = "StatusBarUser",
            .Text = User_ID,
            .AutoSize = StatusBarPanelAutoSize.Contents
        }
        statusBarMain.Panels.Add(statusBarUser)
        '----------------------------------------------------

        Dim statusBarIP = New StatusBarPanel With {
            .Name = "StatusBarIP",
            .Text = ipaddress,
            .AutoSize = StatusBarPanelAutoSize.Contents
        }
        statusBarMain.Panels.Add(statusBarIP)

        Sys_loc_IP = ipaddress
        '----------------------------------------------------
        'Dim statusBarDate = New StatusBarPanel
        statusBarDate.Name = "StatusBarDate"
        statusBarDate.Text = FormatDateTime(Now(), DateFormat.LongDate)
        statusBarDate.AutoSize = StatusBarPanelAutoSize.Contents
        statusBarMain.Panels.Add(statusBarDate)
        '----------------------------------------------------
        'Dim statusBarTime = New StatusBarPanel
        statusBarTime.Name = "StatusBarTime"
        statusBarTime.Text = FormatDateTime(Now(), DateFormat.LongTime)
        statusBarTime.AutoSize = StatusBarPanelAutoSize.Contents
        statusBarMain.Panels.Add(statusBarTime)
        '--------------------------------------------------
        'Dim statusBarCAPS = New StatusBarPanel
        statusBarCAPS.Name = "StatusBarCAPS"
        If Control.IsKeyLocked(Keys.CapsLock) Then
            statusBarCAPS.Text = "CAPS ON"
        Else
            statusBarCAPS.Text = "CAPS OFF"
        End If
        statusBarCAPS.AutoSize = StatusBarPanelAutoSize.Contents
        statusBarMain.Panels.Add(statusBarCAPS)
        '---------------------
        'Dim statusBarNUMS = New StatusBarPanel
        'statusBarNUMS.Name = "StatusBarNUMS"

        'If GetKeyState(Keys.NumLock) = 1 Then
        '    statusBarNUMS.Text = "NumLock ON"
        'Else
        '    statusBarNUMS.Text = "NumLock OFF"
        'End If

        'statusBarNUMS.AutoSize = StatusBarPanelAutoSize.Contents
        'statusBarMain.Panels.Add(statusBarNUMS)
        '--------------------------
        'Add all teh controls to the form
        Me.Controls.Add(statusBarMain)

        ''Set up a refresh timer
        'Dim timer As New Timer
        'timer.Interval = 1000
        'timer.Start()
        'AddHandler timer.Tick, AddressOf Timer1_Tick
        '----------------------------------------------------------------

        '------------------------------------------------------------------------------------
        Try
            '********************************

            ''Right$("TEST\SUCESS", 4)

            ApplicationPath = Application.StartupPath

            dr = cc.GetDataReader("select * from tbl_Plant_mst where Plant_Code ='KWWK'")
            If dr.Read Then
                PLANT_CODE_UPL_ZWT_BG = "KWWK"
            Else
                PLANT_CODE_UPL_ZWT_BG = ""
            End If
            dr.Close()

            If (User_ID <> "admin" And User_ID <> "superadmin") Then
                Sys_loc_IP = ipaddress
                dr = cc.GetDataReader("select * from tbl_User_Mst where User_ID = '" & User_ID & "'")
                If dr.Read Then
                    If dr("Per_User_mst") = 1 Then
                        UserMasterToolStripMenuItem.Visible = True
                    Else
                        UserMasterToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Imp_mst_data_SAP") = 1 Then
                        ImportMasterDatafromSAPToolStripMenuItem.Visible = True
                    Else
                        ImportMasterDatafromSAPToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Veh_tare_WT") = 1 Then
                        VehicleTareWeightManualModeToolStripMenuItem.Visible = True
                    Else
                        VehicleTareWeightManualModeToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_driver_det") = 1 Then
                        DriverDetailsToolStripMenuItem.Visible = True
                    Else
                        DriverDetailsToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_SAP_Server_Config") = 1 Then
                        SAPServerConfigToolStripMenuItem.Visible = True
                    Else
                        SAPServerConfigToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_Node_Client_Config") = 1 Then
                        NodeClientConfigurationToolStripMenuItem.Visible = True
                    Else
                        NodeClientConfigurationToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Gate_Entry") = 1 Then
                        ModifyChallanNoTransporterToolStripMenuItem.Visible = True
                        GateEntryToolStripMenuItem.Visible = True
                        DocumentsValidityToolStripMenuItem.Visible = True
                        ContractorItemOUTPASSToolStripMenuItem1.Visible = True
                    Else
                        ModifyChallanNoTransporterToolStripMenuItem.Visible = False
                        GateEntryToolStripMenuItem.Visible = False
                        ContractorItemOUTPASSToolStripMenuItem1.Visible = False
                        CheckContractorItem47KhataOUTPASSToolStripMenuItem.Visible = False
                        DocumentsValidityToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Weighment") = 1 Then
                        WeighmentToolStripMenuItem.Visible = True
                    Else
                        WeighmentToolStripMenuItem.Visible = False
                    End If

                    'If dr("Per_WB_Report") = 1 Then
                    '    MDIForm1.Details_Report_HALDIA.Visible = True
                    'Else
                    '    MDIForm1.Details_Report_HALDIA.Visible = False
                    'End If


                    If dr("Per_Weight_data_Splitting") = 1 Then
                        WeighmentDataSplittingToolStripMenuItem.Visible = True
                        AuthForOUTBOUNDSplitting = "INBOUNDSPLITTING"
                    Else
                        WeighmentDataSplittingToolStripMenuItem.Visible = False
                    End If


                    If dr("Per_Manual_Weighment") = 1 Then
                        WeighmentManualModeToolStripMenuItem.Visible = True
                    Else
                        WeighmentManualModeToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Company") = 1 Then
                        CompanyMasterToolStripMenuItem.Visible = True
                    Else
                        CompanyMasterToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_TransactionPar") = 1 Then
                        TransactionToolStripMenuItem1.Visible = True
                    Else
                        TransactionToolStripMenuItem1.Visible = False
                    End If

                    If dr("Per_Material") = 1 Then
                        MaterialDetailsToolStripMenuItem.Visible = True
                    Else
                        MaterialDetailsToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Vendor") = 1 Then
                        VendorDetailsToolStripMenuItem.Visible = True
                    Else
                        VendorDetailsToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Customer") = 1 Then
                        CustomerDetailsToolStripMenuItem.Visible = True
                    Else
                        CustomerDetailsToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_PlantMaster") = 1 Then
                        PlantMasterToolStripMenuItem.Visible = True
                    Else
                        PlantMasterToolStripMenuItem.Visible = False
                    End If
                    ''
                    If dr("Per_Grouping_Ref_Master") = 1 Then
                        RakeGroupingReferenceToolStripMenuItem.Visible = True
                    Else
                        RakeGroupingReferenceToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_Grouping_basis_on_Ref") = 1 Then
                        WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem.Visible = True
                    Else
                        WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_Change_Grouping_Ref") = 1 Then
                        ChangeGroupingReferenceToolStripMenuItem.Visible = True
                    Else
                        ChangeGroupingReferenceToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_Cancel_Grouping_Data") = 1 Then
                        CancelGroupingDataToolStripMenuItem.Visible = True
                    Else
                        CancelGroupingDataToolStripMenuItem.Visible = False
                    End If

                    ''???????????
                    If dr("Per_Location_master") = 1 Then
                        LocationToolStripMenuItem.Visible = True
                    Else
                        LocationToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_Scan_Doc_Path_setting") = 1 Then
                        ScannedDocumentPathSettingsToolStripMenuItem.Visible = True
                    Else
                        ScannedDocumentPathSettingsToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_Vehicle_Transfer_To_Other_Location") = 1 Then
                        VehicleTransferToOtherLocationToolStripMenuItem.Visible = True
                    Else
                        VehicleTransferToOtherLocationToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_Cancel_Vehicle_Entry") = 1 Then
                        CancelVehicleEntryToolStripMenuItem.Visible = True
                    Else
                        CancelVehicleEntryToolStripMenuItem.Visible = False
                    End If
                    If dr("Per_GateEntry_Slip") = 1 Then
                        GatePassSlipToolStripMenuItem.Enabled = True
                    Else
                        GatePassSlipToolStripMenuItem.Enabled = False
                    End If
                    If dr("Per_Weighment_Slip") = 1 Then
                        WeighmentSlipToolStripMenuItem.Enabled = True
                    Else
                        WeighmentSlipToolStripMenuItem.Enabled = False
                    End If
                    'If dr("Per_Export_In_Excel") = 1 Then
                    '    MDIForm1.Details_Report_in_Excel_Worksheet.Visible = True
                    'Else
                    '    MDIForm1.Details_Report_in_Excel_Worksheet.Visible = False
                    'End If

                    If dr("Per_Vehicle_WT_Split") = 1 Then
                        VehicleNetWTSplitForGroupingToolStripMenuItem.Visible = True
                    Else
                        VehicleNetWTSplitForGroupingToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Details_Report") = 1 Then
                        DetailsReportToolStripMenuItem.Visible = True
                    Else
                        DetailsReportToolStripMenuItem.Visible = False
                    End If


                    If dr("Per_Gate_Entry") = 1 Then
                        CheckPostEntryToolStripMenuItem.Visible = True
                    Else
                        CheckPostEntryToolStripMenuItem.Visible = False
                    End If


                    If dr("Per_Cont_Item_OUTPASS") = 1 Then
                        ContractorItemOUTPassToolStripMenuItem.Visible = True
                    Else
                        ContractorItemOUTPassToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Weight_data_Splitting") <> 1 Then
                        If dr("Per_Splitting_OUTBOUND") = 1 Then
                            WeighmentDataSplittingToolStripMenuItem.Visible = True
                            AuthForOUTBOUNDSplitting = "OUTBOUNDSPLITTING"
                        Else
                            WeighmentDataSplittingToolStripMenuItem.Visible = False
                        End If
                    End If

                    ''AuthForOUTBOUNDSplitting

                    ''Per_Vehicle_Status_Blacklist

                    Try
                        If dr("Per_Vehicle_Status_Blacklist") = 1 Then
                            VehicleStatusBlacklistWarnedToolStripMenuItem.Visible = True
                        Else
                            VehicleStatusBlacklistWarnedToolStripMenuItem.Visible = False
                        End If
                    Catch ex As Exception
                        VehicleStatusBlacklistWarnedToolStripMenuItem.Visible = False
                    End Try

                    If dr("Per_UpdateLine_Items") = 1 Then
                        UpdateLineItemsToolStripMenuItem.Visible = True
                    Else
                        UpdateLineItemsToolStripMenuItem.Visible = False
                    End If

                    '''''''''''''''''''''''''''''''''''''''''
                    ''
                    '               If dr("Per_Weighment_Slip_Direct") = 1 Then
                    ''                   MDIForm1.Update_line_Items.Visible = True
                    ''               Else
                    ''                    MDIForm1.Update_line_Items.Visible = False
                    ''               End If

                    '''''                If dr("Per_Vendor_Wise") = 1 Then
                    '''''                    MDIForm1.Vendor_Wise.Visible = True
                    '''''                Else
                    '''''                    MDIForm1.Vendor_Wise.Visible = False
                    '''''                End If
                    '''''
                    '''''                If dr("Per_Customer_Wise") = 1 Then
                    '''''                    MDIForm1.Customer_Wise.Visible = True
                    '''''                Else
                    '''''                    MDIForm1.Customer_Wise.Visible = False
                    '''''                End If
                    '''''
                    '''''                If dr("Per_PO_Wise") = 1 Then
                    '''''                    MDIForm1.PO_Wise.Visible = True
                    '''''                Else
                    '''''                    MDIForm1.PO_Wise.Visible = False
                    '''''                End If
                    '''''
                    '''''                If dr("Per_SO_Wise") = 1 Then
                    '''''                    MDIForm1.SO_Wise.Visible = True
                    '''''                Else
                    '''''                    MDIForm1.SO_Wise.Visible = False
                    '''''                End If
                    ''''
                    ''''                If dr("Per_Material_Wise") = 1 Then
                    ''''                    MDIForm1.Details_Report_Material_Wise.Visible = True
                    ''''                Else
                    ''''                    MDIForm1.Details_Report_Material_Wise.Visible = False
                    ''''                End If

                    If dr("Per_Vehicle_Tare_Wt_Auto") = 1 Then
                        VehicleTareWeightAutoToolStripMenuItem.Visible = True
                    Else
                        VehicleTareWeightAutoToolStripMenuItem.Visible = False
                    End If


                    If dr("Per_Vehicle_Tare_Wt_Auto") = 1 Then
                        VehicleTareWTValidityExtensionToolStripMenuItem.Visible = True
                    Else
                        VehicleTareWTValidityExtensionToolStripMenuItem.Visible = False
                    End If


                    If dr("Per_Transporter_Mst") = 1 Then
                        TransporterDetailsToolStripMenuItem.Visible = True
                    Else
                        TransporterDetailsToolStripMenuItem.Visible = False
                    End If


                    If dr("Per_Transporter_Mst") = 1 Then
                        VehicleWithTransporterToolStripMenuItem.Visible = True
                    Else
                        VehicleWithTransporterToolStripMenuItem.Visible = False
                    End If

                    If dr("Per_Weight_data_Splitting") = 1 Then
                        UpdateDONoBeforeFinalWeighmentToolStripMenuItem.Visible = False
                    Else
                        UpdateDONoBeforeFinalWeighmentToolStripMenuItem.Visible = False
                    End If


                    'If dr("Per_Vehicle_Activity_change") = 1 Or User_ID = "superadmin" Then
                    '    vehicle.Visible = True
                    'Else
                    '    MDIForm1.Vehicle_Activity_Change.Visible = False
                    'End If


                    'If dr("Per_Vehicle_Number_Change") = 1 Or User_ID = "superadmin" Then
                    '    MDIForm1.Vehicle_Number_Change.Visible = True
                    'Else
                    '    MDIForm1.Vehicle_Number_Change.Visible = False
                    'End If

                    If dr("Per_Weighment_Slip") = 1 Then
                        WeighmentSlipDirectToolStripMenuItem.Enabled = True
                    Else
                        WeighmentSlipDirectToolStripMenuItem.Enabled = False
                    End If


                    If dr("Per_Weighment_Slip") = 1 Then
                        WeighmentSlipViewToolStripMenuItem.Enabled = True
                    Else
                        WeighmentSlipViewToolStripMenuItem.Enabled = False
                    End If

                    If dr("Per_Gate_Entry") = 1 Then
                        CheckPostEntryToolStripMenuItem.Visible = True
                    Else
                        CheckPostEntryToolStripMenuItem.Visible = False
                    End If

                    ''Per_Cont_Mat_Approval

                    Try
                        If dr("Per_Cont_Mat_Approval") = 1 Then
                            ContractorItemStoresApprovalToolStripMenuItem.Visible = True
                        Else
                            ContractorItemStoresApprovalToolStripMenuItem.Visible = False
                        End If
                    Catch ex As Exception
                        ContractorItemStoresApprovalToolStripMenuItem.Visible = False
                    End Try

                End If
                dr.Close()


                ''''''''''    If rec.Fields("Node_Name") = "GATE" Then
                ''''''''''        MDIForm1.Gate_Enty.Enabled = True
                ''''''''''    Else
                ''''''''''        MDIForm1.Gate_Enty.Enabled = False
                ''''''''''    End If
                ''''''''''
                ''''''''''    If rec.Fields("Node_Name") = "WEIGH BRIDGE" Then
                ''''''''''        MDIForm1.WeighMent.Enabled = True
                ''''''''''        MDIForm1.Weighment_Data_Splitting.Enabled = True
                ''''''''''    Else
                ''''''''''        MDIForm1.WeighMent.Enabled = False
                ''''''''''        MDIForm1.Weighment_Data_Splitting.Enabled = False
                ''''''''''    End If


                ''            rec1.ActiveConnection = con
                ''            rec1.Open "select * from tbl_Node_Mst where Node_IP = '" & Winsock1.LocalIP & "'"
                ''          If rec1.EOF = True And User_ID <> "admin" Then

                'rec.Close
            ElseIf User_ID = "admin" Or User_ID = "superadmin" Then

                If User_ID = "superadmin" Then
                    VehicleActivityChangeToolStripMenuItem.Visible = True
                Else
                    VehicleActivityChangeToolStripMenuItem.Visible = False
                End If


                If User_ID = "superadmin" Then
                    VehicleNumberChangeToolStripMenuItem.Visible = True
                Else
                    VehicleNumberChangeToolStripMenuItem.Visible = False
                End If


            End If
            dr.Close()
            'End If

            If User_ID <> "admin" Then

                dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP ='" & Sys_loc_IP & "' and Node_Name = 'ROUTE/CHECK POST' ")
                If dr.Read Then
                    CheckPostEntryToolStripMenuItem.Visible = True
                Else
                    CheckPostEntryToolStripMenuItem.Visible = False
                End If
                dr.Close()

                'dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP ='" & Sys_loc_IP & "' and Node_Name = 'GATE' ")
                'If dr.Read Then
                '    GateEntryToolStripMenuItem.Visible = True
                '    DocumentsValidityToolStripMenuItem.Visible = True
                'Else
                '    GateEntryToolStripMenuItem.Visible = False
                '    DocumentsValidityToolStripMenuItem.Visible = False
                'End If
                'dr.Close()

            End If

        Catch ex As Exception
            MessageBox.Show(String.Concat("Error: ", ex.Message, vbNewLine, "InnerException: ", ex.InnerException), "Error")
        End Try

    End Sub

    Private Sub MDIForm1_Disposed(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Disposed
        Try
            If MdiParent IsNot Nothing Then
                For Each child As Form In Me.MdiParent.MdiChildren
                    child.Close()
                Next child
            End If
        Catch ex As Exception
            MessageBox.Show(String.Concat("Error: ", ex.Message, vbNewLine, "InnerException: ", ex.InnerException), "Error")
        End Try
      
        Application.Exit()
    End Sub

    Private Sub CompanyMasterToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CompanyMasterToolStripMenuItem.Click
        Dim frmCompanymaster1 As New frmCompanymaster With {
            .MdiParent = Me
        }
        frmCompanymaster1.Show()
    End Sub

    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        statusBarDate.Text = FormatDateTime(Now(), DateFormat.LongDate)
        statusBarTime.Text = FormatDateTime(Now(), DateFormat.LongTime)
        If Control.IsKeyLocked(Keys.CapsLock) Then
            statusBarCAPS.Text = "CAPS ON"
        Else
            statusBarCAPS.Text = "CAPS OFF"
        End If
    End Sub

    Private Sub PlantMasterToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PlantMasterToolStripMenuItem.Click
        Dim frmPlantmaster1 As New frmPlantmaster With {
            .MdiParent = Me
        }
        frmPlantmaster1.Show()
    End Sub

    Private Sub LocationToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles LocationToolStripMenuItem.Click
        Dim frmLocationMaster1 As New frmLocationMaster With {
            .MdiParent = Me
        }
        frmLocationMaster1.Show()
    End Sub

    Private Sub TransactionToolStripMenuItem1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TransactionToolStripMenuItem1.Click
        Dim frmTransactionMaster1 As New frmTransactionMaster With {
            .MdiParent = Me
        }
        frmTransactionMaster1.Show()
    End Sub

    Private Sub ScannedDocumentPathSettingsToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ScannedDocumentPathSettingsToolStripMenuItem.Click
        Dim frmPathSettings1 As New frmPathSettings With {
            .MdiParent = Me
        }
        frmPathSettings1.Show()
    End Sub

    Private Sub ImportMasterDatafromSAPToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ImportMasterDatafromSAPToolStripMenuItem.Click
        Dim frmGetMasterFromSAP1 As New frmGetMasterFromSAP With {
            .MdiParent = Me
        }
        frmGetMasterFromSAP1.Show()
    End Sub

    Private Sub VehicleTareWeightManualModeToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles VehicleTareWeightManualModeToolStripMenuItem.Click
        OperatMode = ""
        Dim frmVehicleWt1 As New frmVehicleWt With {
            .MdiParent = Me
        }
        frmVehicleWt1.Show()
    End Sub

    Private Sub VehicleTareWeightAutoToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles VehicleTareWeightAutoToolStripMenuItem.Click
        OperatMode = "AUTO"
        Dim frmVehicleWt1 As New frmVehicleWt With {
            .MdiParent = Me
        }
        frmVehicleWt1.Show()
        frmVehicleWt1.txtTareWt.Enabled = False
    End Sub

    Private Sub VehicleTareWTValidityExtensionToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles VehicleTareWTValidityExtensionToolStripMenuItem.Click
        OperatMode = ""
        Dim frmVehicleWt1 As New frmVehicleWt With {
            .MdiParent = Me
        }
        frmVehicleWt1.Show()
        frmVehicleWt1.txtTareWt.Enabled = False
    End Sub

    Private Sub DriverDetailsToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DriverDetailsToolStripMenuItem.Click
        Dim frmDriver1 As New frmDriver With {
            .MdiParent = Me
        }
        frmDriver1.Show()
    End Sub

    Private Sub SAPServerConfigToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles SAPServerConfigToolStripMenuItem.Click
        Dim frmServerConfig1 As New frmServerConfig With {
            .MdiParent = Me
        }
        frmServerConfig1.Show()
    End Sub

    Private Sub NodeClientConfigurationToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles NodeClientConfigurationToolStripMenuItem.Click
        Dim frmNode1 As New frmNode With {
            .MdiParent = Me
        }
        frmNode1.Show()
    End Sub


    Private Sub UserMasterToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles UserMasterToolStripMenuItem.Click
        Dim frmUser1 As New frmUser
        frmUser.MdiParent = Me
        frmUser.Show()
    End Sub

    Private Sub MaterialDetailsToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MaterialDetailsToolStripMenuItem.Click
        Dim frmMaterial1 As New frmMaterial With {
            .MdiParent = Me
        }
        frmMaterial1.Show()
    End Sub

    Private Sub VendorDetailsToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles VendorDetailsToolStripMenuItem.Click
        Dim frmVendor1 As New frmVendor With {
            .MdiParent = Me
        }
        frmVendor1.Show()
    End Sub

    Private Sub CustomerDetailsToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CustomerDetailsToolStripMenuItem.Click
        Dim frmCustomer1 As New frmCustomer With {
            .MdiParent = Me
        }
        frmCustomer1.Show()
    End Sub

    Private Sub TransporterDetailsToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TransporterDetailsToolStripMenuItem.Click
        Dim frmTransporter1 As New frmTransporter1 With {
            .MdiParent = Me
        }
        frmTransporter1.Show()
    End Sub

    Private Sub RakeGroupingReferenceToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RakeGroupingReferenceToolStripMenuItem.Click
        Dim frmReferenceMaster1 As New frmReferenceMaster With {
            .MdiParent = Me
        }
        frmReferenceMaster1.Show()
    End Sub

    Private Sub VehicleWithTransporterToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles VehicleWithTransporterToolStripMenuItem.Click
        Dim frmVehicleTransporter1 As New frmVehicleTransporter With {
            .MdiParent = Me
        }
        frmVehicleTransporter1.Show()
    End Sub

    Private Sub ChangePasswordToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChangePasswordToolStripMenuItem.Click
        Dim frmPassword1 As New frmPassword With {
            .MdiParent = Me
        }
        frmPassword1.Show()
    End Sub

    Private Sub VehicleStatusBlacklistWarnedToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles VehicleStatusBlacklistWarnedToolStripMenuItem.Click
        Dim frmVehicleStatus1 As New frmVehicleStatus With {
            .MdiParent = Me
        }
        frmVehicleStatus1.Show()
    End Sub

    Private Sub ExitToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ExitToolStripMenuItem.Click
        Application.Exit()
    End Sub

    Private Sub GateEntryToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GateEntryToolStripMenuItem.Click
        Dim frmGateEntry1 As New frmGateEntry With {
            .MdiParent = Me
        }
        frmGateEntry1.Show()
    End Sub
    Private Sub WeighmentToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WeighmentToolStripMenuItem.Click
        OperationType = "AUTO"
        Dim frmWM1 As New frmWM With {
            .MdiParent = Me
        }
        frmWM1.Show()
    End Sub

    Private Sub WeighmentManualModeToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WeighmentManualModeToolStripMenuItem.Click
        Dim frmManualWeighmentAuthorisation1 As New frmManualWeighmentAuthorisation With {
            .MdiParent = Me
        }
        frmManualWeighmentAuthorisation1.Show()

        If FirstLevelAuth = 1 Then
            MsgBox("Authorised for the Manual Weighment !", vbInformation, "ElectroWay")
            Dim frmWM1 As New frmWM With {
                .MdiParent = Me
            }
            frmWM1.Show()
            FirstLevelAuth = 0
        End If
    End Sub

    Private Sub CheckPostEntryToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckPostEntryToolStripMenuItem.Click
        Dim frmRouteMaster1 As New frmRouteMaster With {
            .MdiParent = Me
        }
        frmRouteMaster1.Show()
    End Sub

    Private Sub WeighmentDataSplittingToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WeighmentDataSplittingToolStripMenuItem.Click
        Dim frmSplitting1 As New frmSplitting With {
            .MdiParent = Me
        }
        frmSplitting1.Show()
    End Sub

    Private Sub WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WeighmentDataGroupingonthebasisOfReferenceToolStripMenuItem.Click
        Dim frmGouping1 As New FrmGouping With {
            .MdiParent = Me
        }
        frmGouping1.Show()
    End Sub

    Private Sub VehicleTransferToOtherLocationToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles VehicleTransferToOtherLocationToolStripMenuItem.Click
        Dim frmTransferToPlant1 As New frmTransferToPlant With {
            .MdiParent = Me
        }
        frmTransferToPlant1.Show()
    End Sub

    Private Sub ChangeGroupingReferenceToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChangeGroupingReferenceToolStripMenuItem.Click
        Dim frmChangeGroupRef1 As New frmChangeGroupRef With {
            .MdiParent = Me
        }
        frmChangeGroupRef1.Show()
    End Sub

    Private Sub CancelVehicleEntryToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CancelVehicleEntryToolStripMenuItem.Click
        Dim frmCancelVehicle1 As New frmCancelVehicle With {
            .MdiParent = Me
        }
        frmCancelVehicle1.Show()
    End Sub

    Private Sub CancelGroupingDataToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CancelGroupingDataToolStripMenuItem.Click
        Dim frmGroupingCancel1 As New frmGroupingCancel With {
            .MdiParent = Me
        }
        frmGroupingCancel1.Show()
    End Sub

    Private Sub VehicleNetWTSplitForGroupingToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles VehicleNetWTSplitForGroupingToolStripMenuItem.Click
        Dim frmRe_Grouping1 As New frmRe_Grouping With {
            .MdiParent = Me
        }
        frmRe_Grouping1.Show()
    End Sub

    Private Sub UpdateDONoBeforeFinalWeighmentToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles UpdateDONoBeforeFinalWeighmentToolStripMenuItem.Click
        Dim frmUnloadingDOUpdation1 As New frmUnloadingDOUpdation With {
            .MdiParent = Me
        }
        frmUnloadingDOUpdation1.Show()
    End Sub

    Private Sub UpdateLineItemsToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles UpdateLineItemsToolStripMenuItem.Click
        Dim frmUpdateLineItems1 As New frmUpdateLineItems With {
            .MdiParent = Me
        }
        frmUpdateLineItems1.Show()
    End Sub

    Private Sub ContractorItemStoresApprovalToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ContractorItemStoresApprovalToolStripMenuItem.Click
        Dim frmContractorMatApproval1 As New frmContractorMatApproval With {
            .MdiParent = Me
        }
        frmContractorMatApproval1.Show()
    End Sub

    Private Sub ContractorItemOUTPassToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ContractorItemOUTPassToolStripMenuItem.Click
        Dim frmContractorMaterial1 As New frmContractorMaterial With {
            .MdiParent = Me
        }
        frmContractorMaterial1.Show()
    End Sub

    Private Sub ModifyChallanNoTransporterToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ModifyChallanNoTransporterToolStripMenuItem.Click
        Dim frmUpdateChallanTransporter1 As New frmUpdateChallanTransporter With {
            .MdiParent = Me
        }
        frmUpdateChallanTransporter1.Show()
    End Sub

    Private Sub GatePassSlipToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GatePassSlipToolStripMenuItem.Click
        Dim frmReport1 As New frmReport
        frmReport1.gbRB.Visible = False
        frmReport1.btnWeighmentSlip.Visible = False
        frmReport1.MdiParent = Me
        frmReport1.Show()
        frmReport1.Refresh()
    End Sub

    Private Sub WeighmentSlipToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WeighmentSlipToolStripMenuItem.Click
        Dim frmReport1 As New frmReport With {
            .MdiParent = Me
        }
        frmReport1.Show()
        frmReport1.gbRB.Visible = False
        frmReport1.btnGateEntrySlip.Visible = False
    End Sub

    Private Sub WeighmentSlipDirectToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WeighmentSlipDirectToolStripMenuItem.Click
        Dim frmSlipPrintDirect1 As New frmSlipPrintDirect
        frmSlipPrintDirect1.Show()
    End Sub

    Private Sub WeighmentSlipViewToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WeighmentSlipViewToolStripMenuItem.Click
        Dim frmSlipPrint1 As New frmSlipPrint
        frmSlipPrint1.Show()
    End Sub

    Private Sub DetailsReportToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DetailsReportToolStripMenuItem.Click
        Dim frmDetailsReport1 As New frmDetailsReport
        frmDetailsReport1.Show()
    End Sub

    Private Sub GateCheckPostINToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles GateCheckPostINToolStripMenuItem.Click
        Dim frmCheckPostReport1 As New frmCheckPostReport
        frmCheckPostReport1.show()
    End Sub

    Private Sub ClearSecondWeighmentToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ClearSecondWeighmentToolStripMenuItem.Click
        Dim UserF As Boolean = False
        If User_ID = "100210" Then
            UserF = True
        End If
        If User_ID = "101338" Then
            UserF = True
        End If

        If UserF = False Then
            Exit Sub
        End If

        Dim frmClearSecondWeighment1 As New frmClearSecondWeighment
        frmClearSecondWeighment1.Show()
    End Sub

    Private Sub ContractorItemOUTPASSToolStripMenuItem1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ContractorItemOUTPASSToolStripMenuItem1.Click
        Dim frmCheckPostReport1 As New frmContractorMaterialDetails_OutPass
        frmCheckPostReport1.Show()
    End Sub
    Private Sub CheckContractorItem47KhataOUTPASSToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckContractorItem47KhataOUTPASSToolStripMenuItem.Click
        Dim frmCheckPostReport1 As New frmContractorMaterialDetails_47KOutPass
        frmCheckPostReport1.Show()
    End Sub

    Private Sub DetailsReportNewToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DetailsReportNewToolStripMenuItem.Click
        Dim frmDetailsReportNew1 As New frmDetailsReportNew
        frmDetailsReportNew1.Show()
    End Sub

    Private Sub DocumentsValidityToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DocumentsValidityToolStripMenuItem.Click
        Dim frmDocumentDetails1 As New frmDocumentDetails
        frmDocumentDetails1.Show()
    End Sub
End Class
