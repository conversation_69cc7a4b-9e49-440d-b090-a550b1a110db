﻿Imports System
Imports System.Configuration
Imports System.Data
Imports System.Data.SqlClient
Module Module1
    Public UserNameinDB As String = String.Empty
    '' ''------------------------
    Public con As New SqlConnection(ConfigurationManager.ConnectionStrings("MyDatabase").ConnectionString)
    ' ''--------------------------------------------------------
    Public cm As SqlCommand
    Public ds As DataSet
    Public dr As SqlDataReader
    Public da As SqlDataAdapter
    Public dt As DataTable
    Public dc As DataColumn
    '------------------------
    Public ipaddress As String
    Public CallFromVehTareWtMst As Integer
    '------------------------------------
    Public User_ID As String
    Public Sys_loc_IP As String
    Public OperatMode As String
    Public OperationType As String
    Public vehicle_sel As Integer
    Public FirstLevelAuth As Integer
    Public SecondLevelAuth As Integer
    Public selrec1 As String
    Public ApplicationPath As String
    Public dataRep_close As Integer
    Public AuthForOUTBOUNDSplitting As String
    '------------SAP-------------------

    '--------------------------------
    ''OOOOOOOOOOOOOOOOOOOOOOOO
    Public VehicleImagePathCamera As String
    Public VehicleImagePathServer As String
    Public BlueBookPathServer As String
    Public DLICPathServer As String
    Public Help_callfrom As String
    Public Call_From_Veh_WT As String
    Public PLANT_CODE_UPL_ZWT_BG As String
    Public Call_From_Check_Post As String
End Module
