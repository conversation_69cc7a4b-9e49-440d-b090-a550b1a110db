﻿Imports System.Data.SqlClient
Imports System.Data
Imports CrystalDecisions.CrystalReports.Engine
Imports CrystalDecisions.Shared
Imports System.Configuration
Public Class GateEntry_Slip
    Dim cc As New Class1
    Dim CrystalReportViewer As New CrystalDecisions.Windows.Forms.CrystalReportViewer
    Friend Sub ViewReport(ByVal ReportName As String, ByVal TableName() As String, ByVal QueryString() As String, Optional ByVal [Parameter] As String = "")

        Try

            If Not UBound(TableName).Equals(UBound(QueryString)) Then MessageBox.Show("Passed Variable Are Not Correct", "Message", MessageBoxButtons.OK, MessageBoxIcon.Information) : Exit Sub

            Dim Report As New CrystalDecisions.CrystalReports.Engine.ReportDocument

            'Dim CrystalReportViewer As CrystalDecisions.Windows.Forms.CrystalReportViewer = New CrystalDecisions.Windows.Forms.CrystalReportViewer

            CrystalReportViewer.ActiveViewIndex = 0

            CrystalReportViewer.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle

            CrystalReportViewer.DisplayGroupTree = False

            CrystalReportViewer.Dock = System.Windows.Forms.DockStyle.Fill

            CrystalReportViewer.Location = New System.Drawing.Point(0, 0)

            CrystalReportViewer.Name = "CrystalReportViewer"
            Dim Adapter As New SqlDataAdapter

            Dim DataSet As New DataSet

            For I As Integer = 0 To UBound(TableName)
                Adapter = cc.GetDataAdapter(QueryString(I))
                Adapter.Fill(DataSet, TableName(I))
            Next

            '---------------------
            Report.Load(Application.StartupPath & "\" & ReportName)

            Report.SetDataSource(DataSet)

            CrystalReportViewer.ReportSource = Report
            Dim connectionInfo As New ConnectionInfo With {
                .DatabaseName = ConfigurationManager.AppSettings("DB_Server"),
                .ServerName = ConfigurationManager.AppSettings("DB_Server"),
                .UserID = ConfigurationManager.AppSettings("DBUser_ID"),
                .Password = ConfigurationManager.AppSettings("DB_Name")
            }
            SetDBLogonForReport(connectionInfo, Report)
            CrystalReportViewer.ReportSource = Report
            'Report.PrintOptions.PaperSize = CrystalDecisions.Shared.PaperSize.PaperA4Small
            'Report.PrintOptions.ApplyPageMargins(New CrystalDecisions.Shared.PageMargins(0, 0, 0, 25))
            Me.Panel1.Controls.Add(CrystalReportViewer)
        Catch ex As Exception
            SendFormattedErrorMail(ex)
            MessageBox.Show(String.Concat("Error: ", ex.Message, vbNewLine, "InnerException: ", ex.InnerException), "Error")
        End Try
    End Sub
    Private Sub SetDBLogonForReport(ByVal connectionInfo As ConnectionInfo, ByVal reportDocument As ReportDocument)
        Dim tables As Tables = reportDocument.Database.Tables
        For Each table As CrystalDecisions.CrystalReports.Engine.Table In tables
            Dim tableLogonInfo As TableLogOnInfo = table.LogOnInfo
            tableLogonInfo.ConnectionInfo = connectionInfo
            table.ApplyLogOnInfo(tableLogonInfo)
        Next
    End Sub

    Private Sub GateEntry_Slip_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyDown
        If e.KeyCode = Keys.P Then
            CrystalReportViewer.PrintReport()
        End If
        If e.KeyCode = Keys.Space Then
            CrystalReportViewer.PrintReport()
        End If
    End Sub
End Class