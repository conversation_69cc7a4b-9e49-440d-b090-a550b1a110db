﻿Imports System.Data
Imports System.Data.SqlClient
Imports System.Drawing
Imports System.Drawing.Printing
Imports Microsoft.Win32.SafeHandles
Imports System.Runtime.InteropServices
Imports System.IO

Public Class frmSlipPrint
    Dim PlantCodePrn As String
    Dim cc As New Class1
    Private Sub frmSlipPrint_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ''-----------ListView--------------
        Dim lvwItem As New ListViewItem()

        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False

        'Headings
        ListView1.Columns.Add("GE_DET_TRAN_ID", 250, HorizontalAlignment.Left)
        ListView1.Columns.Add("Mat_Desc")
        ListView1.Columns.Add("Vendor/customer")
        ListView1.Columns.Add("F WT")
        ListView1.Columns.Add("F WT Date & Time")
        ListView1.Columns.Add("S WT")
        ListView1.Columns.Add("S WT Date & Time")
        ListView1.Columns.Add("NET WT")
        ListView1.Columns.Add("F WT Note")
        ListView1.Columns.Add("S WT Note")
        '-----------------------------
        For i As Integer = 0 To ListView1.Columns.Count - 1
            ListView1.Columns(i).Width = -2
        Next
    End Sub
    'Public Class myPrinter
    '    Friend TextToBePrinted As String
    '    Dim settings As PrinterSettings = New PrinterSettings()
    '    Public Sub prt(ByVal text As String)
    '        TextToBePrinted = text
    '        Dim prn As New Printing.PrintDocument
    '        Using (prn)
    '            'prn.PrinterSettings.PrinterName = "Kyocera FS-1035MFP KX"
    '            prn.PrinterSettings.PrinterName = settings.PrinterName
    '            AddHandler prn.PrintPage, _
    '               AddressOf Me.PrintPageHandler
    '            prn.Print()
    '            RemoveHandler prn.PrintPage, _
    '               AddressOf Me.PrintPageHandler
    '        End Using
    '    End Sub
    '    Private Sub PrintPageHandler(ByVal sender As Object, _
    '       ByVal args As Printing.PrintPageEventArgs)
    '        Dim myFont As New Font("Microsoft San Serif", 10)
    '        args.Graphics.DrawString(TextToBePrinted, _
    '           New Font(myFont, FontStyle.Regular), _
    '           Brushes.Black, 50, 50)
    '    End Sub
    'End Class
    Private Function SearchData(ByVal Str As String) As String
        Dim ValueName As String = ""
        dr = cc.GetDataReader(Str)
        Try
            While dr.Read
                If ValueName = "" Then
                    ValueName = dr(0).ToString
                Else
                    ValueName = ValueName & "," & dr(0).ToString
                End If

            End While
        Catch ex As Exception

        End Try
        dr.Close()
        Return ValueName
    End Function
    Private Sub CancelVehiclePrint()
        Dim GE_HDR_ID As String = txtTransactionNo.Text.Trim
        Dim Vehicle_No As String = String.Empty, Customer_Name As String = String.Empty, Mat_Desc As String = String.Empty, Challan_No As String = String.Empty, DO_Challan_Qty As String = String.Empty, TransporterName As String = String.Empty, F_WT As String = String.Empty, F_WT_DateTime As String = String.Empty
        Dim str As String = "select distinct A.GE_HDR_ID,Vehicle_No,B.Customer_Name,B.Vendor_Name,B.Mat_Desc,B.Challan_No ,B.DO_Challan_Qty,A.TransporterName,B.F_WT,B.F_WT_DateTime from tbl_GE_hdr_Cancellation A,tbl_GE_Det_Cancellation B where A.GE_HDR_ID = B.GE_HDR_ID and A.GE_HDR_ID = '" & GE_HDR_ID & "' and B.F_WT > 0"
        dr = cc.GetDataReader(str)
        Try
            While dr.Read
                Vehicle_No = dr("Vehicle_No").ToString
                Customer_Name = dr("Customer_Name").ToString
                Mat_Desc = dr("Mat_Desc").ToString
                Challan_No = dr("Challan_No").ToString
                DO_Challan_Qty = dr("DO_Challan_Qty").ToString
                TransporterName = dr("TransporterName").ToString
                F_WT = dr("F_WT").ToString
                F_WT_DateTime = dr("F_WT_DateTime").ToString
            End While
        Catch ex As Exception

        End Try
        '----------------------
        Dim lines As String = ""

        For ncp = 1 To 1
            Try

                lines = "             ELECTROSTEEL STEELS LIMITED ( ESL - WORKS )"
                lines = lines & vbCrLf & "                           WEIGHMENT SLIP "
                lines = lines & vbCrLf & "--------------------------------------------------------------------------------"

                lines = lines & vbCrLf & " Gate Pass No              : " & GE_HDR_ID & ""
                lines = lines & vbCrLf & " VEHICLE NO                : " & Vehicle_No & ""
                lines = lines & vbCrLf & " Vendor/Customer           : " & Customer_Name & ""
                lines = lines & vbCrLf & " Material                  : " & Mat_Desc & ""

                lines = lines & vbCrLf & " Challan/DO No.            : " & Challan_No & ""
                lines = lines & vbCrLf & " Challan Qty.              : " & DO_Challan_Qty & ""

                lines = lines & vbCrLf & " Transporter Name          : " & TransporterName
                lines = lines & vbCrLf & Chr(13) & Chr(5)
                'printer.prt(Chr(13) & Chr(5))

                lines = lines & vbCrLf & " 1st WT. Date & Time       : " & F_WT_DateTime
                lines = lines & vbCrLf & " 2nd WT. Date & Time       : "

                lines = lines & vbCrLf & Chr(13) & Chr(5)

                lines = lines & vbCrLf & " 1st WT. (KG)              : " & F_WT


                lines = lines & vbCrLf & " 2nd WT. (KG)              : "
                lines = lines & vbCrLf & " Net WT. (KG)              : "


                lines = lines & vbCrLf & "                                                          (Signature)"
                lines = lines & vbCrLf & "-------------------------------------------------------------------------------"
                lines = lines & vbCrLf & Chr(13) & Chr(5)
                Print2LPT.Print(lines)
            Catch ex As Exception
                MessageBox.Show(ex.Message)
            End Try
            '------------------------------
        Next
    End Sub
    Private Sub btnPrint_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrint.Click
        Dim Vehicle_Status As String = SearchData("select Vehicle_Status from tbl_GE_Hdr where Vehicle_No = '" & txtVehicleNo.Text.Trim & "' and Vehicle_Status = 'CANCEL' and Type_Of_Vehicle = 'SALES'")
        If Vehicle_Status = "CANCEL" Then
            CancelVehiclePrint()
            Exit Sub
        End If
        Dim lines As String = ""
        'Dim printer As New myPrinter

        For ia = 0 To ListView1.Items.Count - 1
            If (ListView1.Items(ia).Checked = True) Then

                ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
                '-----------------------------------------
                Try
                    lines = "             ELECTROSTEEL STEELS LIMITED ( " & PlantCodePrn & " - WORKS )"
                    lines = lines & vbCrLf & "                           WEIGHMENT SLIP  -  ( " & txtVehicleType.Text & " )"
                    lines = lines & vbCrLf & " ---------------------------------------------------------------------------------"

                    lines = lines & vbCrLf & " Gate Pass No             : " & txtTransactionNo.Text & ""
                    lines = lines & vbCrLf & " VEHICLE NO               :" & txtVehicleNo.Text & ""
                    lines = lines & vbCrLf & " Party NET WT : " & Trim(txtPartyNetWt.Text)
                    lines = lines & vbCrLf & " Vendor/Customer          :" & ListView1.Items(ia).SubItems(2).Text
                    lines = lines & vbCrLf & "Material                 :" & ListView1.Items(ia).SubItems(1).Text

                    'lines = lines & vbCrLf & "Challan/DO No.           : " & ds.Tables(0).Rows(ia).Item("Mat_Desc") & " " & ds.Tables(0).Rows(ia).Item("Mat_Desc") & ""
                    'lines = lines & vbCrLf & "Challan Qty.             : " & ds.Tables(0).Rows(ia).Item("Mat_Desc") & "" & ds.Tables(0).Rows(ia).Item("Mat_Desc")

                    'lines = lines & vbCrLf & "Transporter Name         : " & (Text14.Text)
                    lines = lines & vbCrLf & Chr(13) & Chr(5)
                    'printer.prt(Chr(13) & Chr(5))

                    'lines = lines & vbCrLf & "1st WT. Date & Time      : " & (ListView1.Items(ia).SubItems(4).Text)
                    'lines = lines & vbCrLf & "2nd WT. Date & Time      : " & ListView1.Items(ia).SubItems(6).Text ' Tab(72); S_WT_Print; ""

                    'lines = lines & vbCrLf & Chr(13) & Chr(5)

                    lines = lines & vbCrLf & "1st WT. (KG)             : " & ListView1.Items(ia).SubItems(4).Text ' Tab(72); F_WT_Print; ""
                    lines = lines & vbCrLf & "2nd WT. (KG)             : " & ListView1.Items(ia).SubItems(6).Text ' Tab(72); S_WT_Print; ""
                    lines = lines & vbCrLf & "Net WT. (KG)             : " & ListView1.Items(ia).SubItems(7).Text ' Tab(72); S_WT_Print; ""


                    lines = lines & vbCrLf & "                                                          (Signature)"
                    lines = lines & vbCrLf & "-------------------------------------------------------------------------------"
                    lines = lines & vbCrLf & Chr(13) & Chr(5)
                    'Close(nPrinter)
                    '''''
                    lines = lines & vbCrLf & "(F.WT)Note:" & Chr(21) & Trim(ListView1.Items(ia).SubItems(8).Text)
                    'Printer.Print " "
                    lines = lines & vbCrLf & "(S.WT)Note:" & Chr(21) & Trim(ListView1.Items(ia).SubItems(9).Text) & Chr(72) & "Seal No: " & Trim(txtSealNo.Text)
                    Print2LPT.Print(lines)
                    'printer.prt(lines)
                Catch ex As Exception

                End Try
                '------------------------------------------
                ''OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
            End If
        Next
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        On Error GoTo err
        txtTransactionNo.Text = ""
        txtVehicleNo.Text = ""
        txtVehicleType.Text = ""
        txtSealNo.Text = ""
        txtPartyNetWt.Text = ""
        ListView1.Items.Clear()
        txtTransactionNo.Enabled = True

err:
        Err.Clear()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub txtTransactionNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransactionNo.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "SLIP_PRINT_VIEW"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub txtTransactionNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo.KeyPress
        Try
            If AscW(e.KeyChar) = 13 And Trim(txtTransactionNo.Text) <> "" Then
                dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "'")
                If dr.Read Then
                    txtVehicleNo.Text = dr("Vehicle_NO")
                    txtVehicleType.Text = dr("Type_Of_Vehicle")
                    PlantCodePrn = dr("Plant_Code")
                    txtSealNo.Text = dr("Seal_No")
                    txtPartyNetWt.Text = dr("Party_NET_WT")
                End If
                dr.Close()

                ds = cc.GetDataset("select distinct WB_Count_ID , GE_DET_TRAN_ID   from tbl_SPLIT_DET where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' and F_WT > 0 and S_WT > 0 and WB_Count_ID > 0 group by WB_Count_ID , GE_DET_TRAN_ID order by WB_Count_ID")
                If ds.Tables(0).Rows.Count > 0 Then
                    Dim ds1 As DataSet = cc.GetDataset("select distinct WB_Count_ID , GE_DET_TRAN_ID   from tbl_SPLIT_DET where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' and F_WT > 0 and S_WT > 0 and WB_Count_ID > 0 group by WB_Count_ID , GE_DET_TRAN_ID order by WB_Count_ID")
                    For i As Integer = 0 To ds1.Tables(0).Rows.Count - 1
                        Dim cm3 As SqlCommand = New SqlCommand("select * from tbl_SPLIT_DET where GE_DET_TRAN_ID = " & ds1.Tables(0).Rows(i).Item("GE_DET_TRAN_ID") & " and GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' and F_WT > 0 and S_WT > 0 and WB_Count_ID > 0 order by WB_Count_ID", con)
                        Dim dr3 As SqlDataReader
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        dr3 = cm3.ExecuteReader
                        If dr3.Read Then
                            Dim lvi As New ListViewItem
                            Dim ik As Integer = ListView1.Items.Count + 1
                            lvi.Text = dr3("GE_DET_TRAN_ID")
                            'ListView1.ListItems.Add(ik, , rec3.Fields("GE_DET_TRAN_ID"))
                            lvi.SubItems.Add(dr3("Mat_Desc"))
                            If dr3("Type_OF_Vehicle") = "PURCH" Or dr3("Type_OF_Vehicle") = "INTRDEPT" Or dr3("Type_OF_Vehicle") = "SALESRET" Then
                                lvi.SubItems.Add(dr3("Vendor_Name"))
                            ElseIf dr3("Type_OF_Vehicle") = "SALES" Or dr3("Type_OF_Vehicle") = "STKTROUT" Then
                                lvi.SubItems.Add(dr3("Customer_Name"))
                            End If

                            'ListView1.ListItems(ik).ListSubItems.Add 2, , rec3.Fields("Vendor_Code")
                            lvi.SubItems.Add(dr3("F_WT"))
                            lvi.SubItems.Add(dr3("F_WT_DateTime"))
                            lvi.SubItems.Add(dr3("S_WT"))
                            lvi.SubItems.Add(dr3("S_WT_DateTime"))
                            lvi.SubItems.Add(dr3("NET_WT"))
                            lvi.SubItems.Add(dr3("F_WT_Note"))
                            lvi.SubItems.Add(dr3("S_WT_Note"))
                            ListView1.Items.Add(lvi)
                        End If
                        dr3.Close()
                    Next
                Else
                    ds = cc.GetDataset("select distinct WB_Count_ID , GE_DET_TRAN_ID   from tbl_GE_DET where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' and F_WT > 0 and S_WT > 0 and WB_Count_ID > 0 group by WB_Count_ID , GE_DET_TRAN_ID order by WB_Count_ID")
                    For i As Integer = 0 To ds.Tables(0).Rows.Count - 1
                        Dim cm3 As SqlCommand = New SqlCommand("select * from temp1 where GE_DET_TRAN_ID = " & ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID") & " and GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' and F_WT > 0 and S_WT > 0 and WB_Count_ID > 0 order by WB_Count_ID", con)
                        Dim dr3 As SqlDataReader
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        dr3 = cm3.ExecuteReader

                        If dr3.Read Then
                            'ik = ListView1.ListItems.Count + 1
                            Dim lvi As New ListViewItem
                            lvi.Text = dr3("GE_DET_TRAN_ID")
                            lvi.SubItems.Add(dr3("Mat_Desc"))
                            If dr3("Type_OF_Vehicle") = "PURCH" Or dr3("Type_OF_Vehicle") = "INTRDEPT" Or dr3("Type_OF_Vehicle") = "SALESRET" Then
                                lvi.SubItems.Add(dr3("Vendor_Name"))
                            ElseIf dr3("Type_OF_Vehicle") = "SALES" Or dr3("Type_OF_Vehicle") = "STKTROUT" Then
                                lvi.SubItems.Add(dr3("Customer_Name"))
                            End If

                            'ListView1.ListItems(ik).ListSubItems.Add 2, , rec3.Fields("Vendor_Code")
                            lvi.SubItems.Add(dr3("F_WT"))
                            lvi.SubItems.Add(dr3("F_WT_DateTime"))
                            lvi.SubItems.Add(dr3("S_WT"))
                            lvi.SubItems.Add(dr3("S_WT_DateTime"))
                            lvi.SubItems.Add(dr3("NET_WT"))
                            lvi.SubItems.Add(dr3("F_WT_Note"))
                            lvi.SubItems.Add(dr3("S_WT_Note"))
                            ListView1.Items.Add(lvi)
                        End If
                        dr3.Close()
                        'rec1.MoveNext()
                    Next
                End If
                txtTransactionNo.Enabled = False
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
    Public NotInheritable Class Print2LPT
        Private Sub New()
        End Sub
        <DllImport("kernel32.dll", SetLastError:=True)> _
        Private Shared Function CreateFile(ByVal lpFileName As String, ByVal dwDesiredAccess As FileAccess, ByVal dwShareMode As UInteger, ByVal lpSecurityAttributes As IntPtr, ByVal dwCreationDisposition As FileMode, ByVal dwFlagsAndAttributes As UInteger, _
         ByVal hTemplateFile As IntPtr) As SafeFileHandle
        End Function

        Public Shared Function Print(ByVal SText As String) As Boolean
            Dim nl As String = Convert.ToChar(13).ToString() + Convert.ToChar(10).ToString()
            Dim IsConnected As Boolean = False

            Dim sampleText As String = (Convert.ToString(SText) & nl)
            Try
                Dim buffer As [Byte]() = New Byte(sampleText.Length - 1) {}
                buffer = System.Text.Encoding.ASCII.GetBytes(sampleText)

                Dim fh As SafeFileHandle = CreateFile("LPT1:", FileAccess.Write, 0, IntPtr.Zero, FileMode.OpenOrCreate, 0, _
                 IntPtr.Zero)
                If Not fh.IsInvalid Then
                    IsConnected = True
                    Dim lpt1 As New FileStream(fh, FileAccess.ReadWrite)
                    lpt1.Write(buffer, 0, buffer.Length)
                    lpt1.Close()

                End If
            Catch ex As Exception
                MessageBox.Show(ex.Message)
            End Try

            Return IsConnected
        End Function
    End Class

    Private Sub txtTransactionNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTransactionNo.TextChanged

    End Sub
End Class