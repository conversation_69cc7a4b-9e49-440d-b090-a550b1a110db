﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Extensions.Logging.Abstractions</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Extensions.Logging.Abstractions.LogEntry`1">
      <typeparam name="TState" />
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.LogEntry`1.#ctor(Microsoft.Extensions.Logging.LogLevel,System.String,Microsoft.Extensions.Logging.EventId,`0,System.Exception,System.Func{`0,System.Exception,System.String})">
      <param name="logLevel" />
      <param name="category" />
      <param name="eventId" />
      <param name="state" />
      <param name="exception" />
      <param name="formatter" />
    </member>
    <member name="P:Microsoft.Extensions.Logging.Abstractions.LogEntry`1.Category" />
    <member name="P:Microsoft.Extensions.Logging.Abstractions.LogEntry`1.EventId" />
    <member name="P:Microsoft.Extensions.Logging.Abstractions.LogEntry`1.Exception" />
    <member name="P:Microsoft.Extensions.Logging.Abstractions.LogEntry`1.Formatter" />
    <member name="P:Microsoft.Extensions.Logging.Abstractions.LogEntry`1.LogLevel" />
    <member name="P:Microsoft.Extensions.Logging.Abstractions.LogEntry`1.State" />
    <member name="T:Microsoft.Extensions.Logging.Abstractions.NullLogger">
      <summary>Minimalistic logger that does nothing.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLogger.BeginScope``1(``0)">
      <summary>Begins a logical operation scope.</summary>
      <param name="state" />
      <typeparam name="TState" />
      <returns>A disposable object that ends the logical operation scope on dispose.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLogger.IsEnabled(Microsoft.Extensions.Logging.LogLevel)">
      <summary>Checks if the given <paramref name="logLevel" /> is enabled.</summary>
      <param name="logLevel" />
      <returns>
        <see langword="true" /> if enabled; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLogger.Log``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.Func{``0,System.Exception,System.String})">
      <summary>Writes a log entry.</summary>
      <param name="logLevel" />
      <param name="eventId" />
      <param name="state" />
      <param name="exception" />
      <param name="formatter" />
      <typeparam name="TState" />
    </member>
    <member name="P:Microsoft.Extensions.Logging.Abstractions.NullLogger.Instance">
      <summary>Returns the shared instance of <see cref="T:Microsoft.Extensions.Logging.Abstractions.NullLogger" />.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Logging.Abstractions.NullLogger`1">
      <summary>Minimalistic logger that does nothing.</summary>
      <typeparam name="T" />
    </member>
    <member name="F:Microsoft.Extensions.Logging.Abstractions.NullLogger`1.Instance">
      <summary>Returns an instance of <see cref="T:Microsoft.Extensions.Logging.Abstractions.NullLogger`1" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLogger`1.#ctor" />
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLogger`1.BeginScope``1(``0)">
      <summary>Begins a logical operation scope.</summary>
      <param name="state" />
      <typeparam name="TState" />
      <returns>A disposable object that ends the logical operation scope on dispose.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLogger`1.IsEnabled(Microsoft.Extensions.Logging.LogLevel)">
      <summary>Checks if the given <paramref name="logLevel" /> is enabled.</summary>
      <param name="logLevel" />
      <returns>
        <see langword="true" /> if enabled; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLogger`1.Log``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.Func{``0,System.Exception,System.String})">
      <summary>Writes a log entry.</summary>
      <param name="logLevel" />
      <param name="eventId" />
      <param name="state" />
      <param name="exception" />
      <param name="formatter" />
      <typeparam name="TState" />
    </member>
    <member name="T:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory">
      <summary>An <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" /> used to create instance of
            <see cref="T:Microsoft.Extensions.Logging.Abstractions.NullLogger" /> that logs nothing.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory.Instance">
      <summary>Returns the shared instance of <see cref="T:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory.#ctor">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory" /> instance.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory.AddProvider(Microsoft.Extensions.Logging.ILoggerProvider)">
      <summary>Adds an <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> to the logging system.</summary>
      <param name="provider" />
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory.CreateLogger(System.String)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance.</summary>
      <param name="name" />
      <returns>A new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLoggerFactory.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Logging.Abstractions.NullLoggerProvider">
      <summary>Provider for the <see cref="T:Microsoft.Extensions.Logging.Abstractions.NullLogger" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLoggerProvider.CreateLogger(System.String)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance.</summary>
      <param name="categoryName" />
      <returns>The instance of <see cref="T:Microsoft.Extensions.Logging.ILogger" /> that was created.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Abstractions.NullLoggerProvider.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.Abstractions.NullLoggerProvider.Instance">
      <summary>Returns an instance of <see cref="T:Microsoft.Extensions.Logging.Abstractions.NullLoggerProvider" />.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Logging.EventId">
      <summary>Identifies a logging event. The primary identifier is the "Id" property, with the "Name" property providing a short description of this type of event.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.EventId.#ctor(System.Int32,System.String)">
      <summary>Initializes an instance of the <see cref="T:Microsoft.Extensions.Logging.EventId" /> struct.</summary>
      <param name="id">The numeric identifier for this event.</param>
      <param name="name">The name of this event.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.EventId.Equals(Microsoft.Extensions.Logging.EventId)">
      <summary>Indicates whether the current object is equal to another object of the same type. Two events are equal if they have the same id.</summary>
      <param name="other">An object to compare with this object.</param>
      <returns>
        <see langword="true" /> if the current object is equal to the other parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.EventId.Equals(System.Object)">
      <param name="obj" />
    </member>
    <member name="M:Microsoft.Extensions.Logging.EventId.GetHashCode" />
    <member name="M:Microsoft.Extensions.Logging.EventId.op_Equality(Microsoft.Extensions.Logging.EventId,Microsoft.Extensions.Logging.EventId)">
      <summary>Checks if two specified <see cref="T:Microsoft.Extensions.Logging.EventId" /> instances have the same value. They are equal if they have the same Id.</summary>
      <param name="left">The first <see cref="T:Microsoft.Extensions.Logging.EventId" />.</param>
      <param name="right">The second <see cref="T:Microsoft.Extensions.Logging.EventId" />.</param>
      <returns>
        <see langword="true" /> if the objects are equal.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.EventId.op_Implicit(System.Int32)~Microsoft.Extensions.Logging.EventId">
      <summary>Implicitly creates an EventId from the given <see cref="T:System.Int32" />.</summary>
      <param name="i">The <see cref="T:System.Int32" /> to convert to an EventId.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.EventId.op_Inequality(Microsoft.Extensions.Logging.EventId,Microsoft.Extensions.Logging.EventId)">
      <summary>Checks if two specified <see cref="T:Microsoft.Extensions.Logging.EventId" /> instances have different values.</summary>
      <param name="left">The first <see cref="T:Microsoft.Extensions.Logging.EventId" />.</param>
      <param name="right">The second <see cref="T:Microsoft.Extensions.Logging.EventId" />.</param>
      <returns>
        <see langword="true" /> if the objects are not equal.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.EventId.ToString" />
    <member name="P:Microsoft.Extensions.Logging.EventId.Id">
      <summary>Gets the numeric identifier for this event.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.EventId.Name">
      <summary>Gets the name of this event.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Logging.IExternalScopeProvider">
      <summary>Represents a storage of common scope data.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.IExternalScopeProvider.ForEachScope``1(System.Action{System.Object,``0},``0)">
      <summary>Executes callback for each currently active scope objects in order of creation.
            All callbacks are guaranteed to be called inline from this method.</summary>
      <param name="callback">The callback to be executed for every scope object</param>
      <param name="state">The state object to be passed into the callback</param>
      <typeparam name="TState">The type of state to accept.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Logging.IExternalScopeProvider.Push(System.Object)">
      <summary>Adds scope object to the list.</summary>
      <param name="state">The scope object</param>
      <returns>The <see cref="T:System.IDisposable" /> token that removes scope on dispose.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Logging.ILogger">
      <summary>Represents a type used to perform logging.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.ILogger.BeginScope``1(``0)">
      <summary>Begins a logical operation scope.</summary>
      <param name="state">The identifier for the scope.</param>
      <typeparam name="TState">The type of the state to begin scope for.</typeparam>
      <returns>A disposable object that ends the logical operation scope on dispose.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.ILogger.IsEnabled(Microsoft.Extensions.Logging.LogLevel)">
      <summary>Checks if the given <paramref name="logLevel" /> is enabled.</summary>
      <param name="logLevel">level to be checked.</param>
      <returns>
        <see langword="true" /> if enabled; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.ILogger.Log``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.Func{``0,System.Exception,System.String})">
      <summary>Writes a log entry.</summary>
      <param name="logLevel">Entry will be written on this level.</param>
      <param name="eventId">Id of the event.</param>
      <param name="state">The entry to be written. Can be also an object.</param>
      <param name="exception">The exception related to this entry.</param>
      <param name="formatter">Function to create a <see cref="T:System.String" /> message of the <paramref name="state" /> and <paramref name="exception" />.</param>
      <typeparam name="TState">The type of the object to be written.</typeparam>
    </member>
    <member name="T:Microsoft.Extensions.Logging.ILogger`1">
      <summary>A generic interface for logging where the category name is derived from the specified
            <typeparamref name="TCategoryName" /> type name.
            Generally used to enable activation of a named <see cref="T:Microsoft.Extensions.Logging.ILogger" /> from dependency injection.</summary>
      <typeparam name="TCategoryName">The type who's name is used for the logger category name.</typeparam>
    </member>
    <member name="T:Microsoft.Extensions.Logging.ILoggerFactory">
      <summary>Represents a type used to configure the logging system and create instances of <see cref="T:Microsoft.Extensions.Logging.ILogger" /> from
            the registered <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />s.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.ILoggerFactory.AddProvider(Microsoft.Extensions.Logging.ILoggerProvider)">
      <summary>Adds an <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> to the logging system.</summary>
      <param name="provider">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.ILoggerFactory.CreateLogger(System.String)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance.</summary>
      <param name="categoryName">The category name for messages produced by the logger.</param>
      <returns>A new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Logging.ILoggerProvider">
      <summary>Represents a type that can create instances of <see cref="T:Microsoft.Extensions.Logging.ILogger" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.ILoggerProvider.CreateLogger(System.String)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance.</summary>
      <param name="categoryName">The category name for messages produced by the logger.</param>
      <returns>The instance of <see cref="T:Microsoft.Extensions.Logging.ILogger" /> that was created.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Logging.ISupportExternalScope">
      <summary>Represents a <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> that is able to consume external scope information.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.ISupportExternalScope.SetScopeProvider(Microsoft.Extensions.Logging.IExternalScopeProvider)">
      <summary>Sets external scope information source for logger provider.</summary>
      <param name="scopeProvider">The provider of scope data.</param>
    </member>
    <member name="T:Microsoft.Extensions.Logging.Logger`1">
      <summary>Delegates to a new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance using the full name of the given type, created by the
            provided <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" />.</summary>
      <typeparam name="T">The type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Logger`1.#ctor(Microsoft.Extensions.Logging.ILoggerFactory)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.Logger`1" />.</summary>
      <param name="factory">The factory.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Logger`1.Microsoft#Extensions#Logging#ILogger#BeginScope``1(``0)">
      <summary>Begins a logical operation scope.</summary>
      <param name="state" />
      <typeparam name="TState" />
      <returns>A disposable object that ends the logical operation scope on dispose.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Logger`1.Microsoft#Extensions#Logging#ILogger#IsEnabled(Microsoft.Extensions.Logging.LogLevel)">
      <summary>Checks if the given <paramref name="logLevel" /> is enabled.</summary>
      <param name="logLevel" />
      <returns>
        <see langword="true" /> if enabled; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.Logger`1.Microsoft#Extensions#Logging#ILogger#Log``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.Func{``0,System.Exception,System.String})">
      <summary>Writes a log entry.</summary>
      <param name="logLevel" />
      <param name="eventId" />
      <param name="state" />
      <param name="exception" />
      <param name="formatter" />
      <typeparam name="TState" />
    </member>
    <member name="T:Microsoft.Extensions.Logging.LoggerExtensions">
      <summary>ILogger extension methods for common scenarios.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.BeginScope(Microsoft.Extensions.Logging.ILogger,System.String,System.Object[])">
      <summary>Formats the message and creates a scope.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to create the scope in.</param>
      <param name="messageFormat">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
      <returns>A disposable scope object. Can be null.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.Log(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a log message at the specified log level.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="logLevel">Entry will be written on this level.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message.</param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.Log(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.String,System.Object[])">
      <summary>Formats and writes a log message at the specified log level.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="logLevel">Entry will be written on this level.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="message">Format string of the log message.</param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.Log(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.LogLevel,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a log message at the specified log level.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="logLevel">Entry will be written on this level.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message.</param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.Log(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.LogLevel,System.String,System.Object[])">
      <summary>Formats and writes a log message at the specified log level.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="logLevel">Entry will be written on this level.</param>
      <param name="message">Format string of the log message.</param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a critical log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.String,System.Object[])">
      <summary>Formats and writes a critical log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(Microsoft.Extensions.Logging.ILogger,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a critical log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(Microsoft.Extensions.Logging.ILogger,System.String,System.Object[])">
      <summary>Formats and writes a critical log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogDebug(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a debug log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogDebug(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.String,System.Object[])">
      <summary>Formats and writes a debug log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogDebug(Microsoft.Extensions.Logging.ILogger,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a debug log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogDebug(Microsoft.Extensions.Logging.ILogger,System.String,System.Object[])">
      <summary>Formats and writes a debug log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogError(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes an error log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogError(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.String,System.Object[])">
      <summary>Formats and writes an error log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogError(Microsoft.Extensions.Logging.ILogger,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes an error log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogError(Microsoft.Extensions.Logging.ILogger,System.String,System.Object[])">
      <summary>Formats and writes an error log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes an informational log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.String,System.Object[])">
      <summary>Formats and writes an informational log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(Microsoft.Extensions.Logging.ILogger,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes an informational log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(Microsoft.Extensions.Logging.ILogger,System.String,System.Object[])">
      <summary>Formats and writes an informational log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogTrace(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a trace log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogTrace(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.String,System.Object[])">
      <summary>Formats and writes a trace log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogTrace(Microsoft.Extensions.Logging.ILogger,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a trace log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogTrace(Microsoft.Extensions.Logging.ILogger,System.String,System.Object[])">
      <summary>Formats and writes a trace log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a warning log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.EventId,System.String,System.Object[])">
      <summary>Formats and writes a warning log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="eventId">The event id associated with the log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(Microsoft.Extensions.Logging.ILogger,System.Exception,System.String,System.Object[])">
      <summary>Formats and writes a warning log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="exception">The exception to log.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(Microsoft.Extensions.Logging.ILogger,System.String,System.Object[])">
      <summary>Formats and writes a warning log message.</summary>
      <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> to write to.</param>
      <param name="message">Format string of the log message in message template format. Example: <code>"User {User} logged in from {Address}"</code></param>
      <param name="args">An object array that contains zero or more objects to format.</param>
    </member>
    <member name="T:Microsoft.Extensions.Logging.LoggerExternalScopeProvider">
      <summary>Default implementation of <see cref="T:Microsoft.Extensions.Logging.IExternalScopeProvider" /></summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExternalScopeProvider.#ctor">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerExternalScopeProvider" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExternalScopeProvider.ForEachScope``1(System.Action{System.Object,``0},``0)">
      <summary>Executes callback for each currently active scope objects in order of creation.
 All callbacks are guaranteed to be called inline from this method.</summary>
      <param name="callback" />
      <param name="state" />
      <typeparam name="TState" />
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerExternalScopeProvider.Push(System.Object)">
      <summary>Adds scope object to the list.</summary>
      <param name="state" />
      <returns>The <see cref="T:System.IDisposable" /> token that removes scope on dispose.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Logging.LoggerFactoryExtensions">
      <summary>ILoggerFactory extension methods for common scenarios.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactoryExtensions.CreateLogger(Microsoft.Extensions.Logging.ILoggerFactory,System.Type)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance using the full name of the given <paramref name="type" />.</summary>
      <param name="factory">The factory.</param>
      <param name="type">The type.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactoryExtensions.CreateLogger``1(Microsoft.Extensions.Logging.ILoggerFactory)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instance using the full name of the given type.</summary>
      <param name="factory">The factory.</param>
      <typeparam name="T">The type.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> that was created.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Logging.LoggerMessage">
      <summary>Creates delegates which can be later cached to log messages in a performant way.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.Define(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.String)">
      <summary>Creates a delegate which can be invoked for logging a message.</summary>
      <param name="logLevel">The <see cref="T:Microsoft.Extensions.Logging.LogLevel" /></param>
      <param name="eventId">The event id</param>
      <param name="formatString">The named format string</param>
      <returns>A delegate which when invoked creates a log message.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.Define``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.String)">
      <summary>Creates a delegate which can be invoked for logging a message.</summary>
      <param name="logLevel">The <see cref="T:Microsoft.Extensions.Logging.LogLevel" /></param>
      <param name="eventId">The event id</param>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log message.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.Define``2(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.String)">
      <summary>Creates a delegate which can be invoked for logging a message.</summary>
      <param name="logLevel">The <see cref="T:Microsoft.Extensions.Logging.LogLevel" /></param>
      <param name="eventId">The event id</param>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <typeparam name="T2">The type of the second parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log message.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.Define``3(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.String)">
      <summary>Creates a delegate which can be invoked for logging a message.</summary>
      <param name="logLevel">The <see cref="T:Microsoft.Extensions.Logging.LogLevel" /></param>
      <param name="eventId">The event id</param>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <typeparam name="T2">The type of the second parameter passed to the named format string.</typeparam>
      <typeparam name="T3">The type of the third parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log message.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.Define``4(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.String)">
      <summary>Creates a delegate which can be invoked for logging a message.</summary>
      <param name="logLevel">The <see cref="T:Microsoft.Extensions.Logging.LogLevel" /></param>
      <param name="eventId">The event id</param>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <typeparam name="T2">The type of the second parameter passed to the named format string.</typeparam>
      <typeparam name="T3">The type of the third parameter passed to the named format string.</typeparam>
      <typeparam name="T4">The type of the fourth parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log message.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.Define``5(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.String)">
      <summary>Creates a delegate which can be invoked for logging a message.</summary>
      <param name="logLevel">The <see cref="T:Microsoft.Extensions.Logging.LogLevel" /></param>
      <param name="eventId">The event id</param>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <typeparam name="T2">The type of the second parameter passed to the named format string.</typeparam>
      <typeparam name="T3">The type of the third parameter passed to the named format string.</typeparam>
      <typeparam name="T4">The type of the fourth parameter passed to the named format string.</typeparam>
      <typeparam name="T5">The type of the fifth parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log message.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.Define``6(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,System.String)">
      <summary>Creates a delegate which can be invoked for logging a message.</summary>
      <param name="logLevel">The <see cref="T:Microsoft.Extensions.Logging.LogLevel" /></param>
      <param name="eventId">The event id</param>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <typeparam name="T2">The type of the second parameter passed to the named format string.</typeparam>
      <typeparam name="T3">The type of the third parameter passed to the named format string.</typeparam>
      <typeparam name="T4">The type of the fourth parameter passed to the named format string.</typeparam>
      <typeparam name="T5">The type of the fifth parameter passed to the named format string.</typeparam>
      <typeparam name="T6">The type of the sixth parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log message.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.DefineScope(System.String)">
      <summary>Creates a delegate which can be invoked to create a log scope.</summary>
      <param name="formatString">The named format string</param>
      <returns>A delegate which when invoked creates a log scope.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.DefineScope``1(System.String)">
      <summary>Creates a delegate which can be invoked to create a log scope.</summary>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log scope.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.DefineScope``2(System.String)">
      <summary>Creates a delegate which can be invoked to create a log scope.</summary>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <typeparam name="T2">The type of the second parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log scope.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.DefineScope``3(System.String)">
      <summary>Creates a delegate which can be invoked to create a log scope.</summary>
      <param name="formatString">The named format string</param>
      <typeparam name="T1">The type of the first parameter passed to the named format string.</typeparam>
      <typeparam name="T2">The type of the second parameter passed to the named format string.</typeparam>
      <typeparam name="T3">The type of the third parameter passed to the named format string.</typeparam>
      <returns>A delegate which when invoked creates a log scope.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.DefineScope``4(System.String)">
      <param name="formatString" />
      <typeparam name="T1" />
      <typeparam name="T2" />
      <typeparam name="T3" />
      <typeparam name="T4" />
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.DefineScope``5(System.String)">
      <param name="formatString" />
      <typeparam name="T1" />
      <typeparam name="T2" />
      <typeparam name="T3" />
      <typeparam name="T4" />
      <typeparam name="T5" />
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerMessage.DefineScope``6(System.String)">
      <param name="formatString" />
      <typeparam name="T1" />
      <typeparam name="T2" />
      <typeparam name="T3" />
      <typeparam name="T4" />
      <typeparam name="T5" />
      <typeparam name="T6" />
    </member>
    <member name="T:Microsoft.Extensions.Logging.LogLevel">
      <summary>Defines logging severity levels.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Logging.LogLevel.Critical">
      <summary>Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
            immediate attention.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Logging.LogLevel.Debug">
      <summary>Logs that are used for interactive investigation during development.  These logs should primarily contain
            information useful for debugging and have no long-term value.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Logging.LogLevel.Error">
      <summary>Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
            failure in the current activity, not an application-wide failure.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Logging.LogLevel.Information">
      <summary>Logs that track the general flow of the application. These logs should have long-term value.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Logging.LogLevel.None">
      <summary>Not used for writing log messages. Specifies that a logging category should not write any messages.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Logging.LogLevel.Trace">
      <summary>Logs that contain the most detailed messages. These messages may contain sensitive application data.
            These messages are disabled by default and should never be enabled in a production environment.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Logging.LogLevel.Warning">
      <summary>Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
            application execution to stop.</summary>
    </member>
  </members>
</doc>