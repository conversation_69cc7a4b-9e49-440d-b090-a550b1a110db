﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Extensions.Logging</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions">
      <summary>Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions.AddLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds logging services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions.AddLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
      <summary>Adds logging services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
      <param name="configure">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> configuration delegate.</param>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Logging.ActivityTrackingOptions" />
    <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.None" />
    <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.ParentId" />
    <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.SpanId" />
    <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceFlags" />
    <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceId" />
    <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceState" />
    <member name="T:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions">
      <summary>Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="levelFilter">The filter to be added.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="categoryLevelFilter">The filter to be added.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="filter">The filter to be added.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,Microsoft.Extensions.Logging.LogLevel)">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="category">The category to filter.</param>
      <param name="level">The level to filter.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="category">The category to filter.</param>
      <param name="levelFilter">The filter function to apply.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="levelFilter">The filter function to apply.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="categoryLevelFilter">The filter function to apply.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="filter">The filter function to apply.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,Microsoft.Extensions.Logging.LogLevel)">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="category">The category to filter.</param>
      <param name="level">The level to filter.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter to the factory.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="category">The category to filter.</param>
      <param name="levelFilter">The filter function to apply.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="levelFilter">The filter to be added.</param>
      <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> which this filter will be added for.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="categoryLevelFilter">The filter to be added.</param>
      <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> which this filter will be added for.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,Microsoft.Extensions.Logging.LogLevel)">
      <summary>Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="category">The category to filter.</param>
      <param name="level">The level to filter.</param>
      <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> which this filter will be added for.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="category">The category to filter.</param>
      <param name="levelFilter">The filter function to apply.</param>
      <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> which this filter will be added for.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="levelFilter">The filter function to apply.</param>
      <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> which this filter will be added for.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="categoryLevelFilter">The filter function to apply.</param>
      <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> which this filter will be added for.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,Microsoft.Extensions.Logging.LogLevel)">
      <summary>Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="category">The category to filter.</param>
      <param name="level">The level to filter.</param>
      <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> which this filter will be added for.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" />.</summary>
      <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> to add the filter to.</param>
      <param name="category">The category to filter.</param>
      <param name="levelFilter">The filter function to apply.</param>
      <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> which this filter will be added for.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that additional calls can be chained.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Logging.ILoggingBuilder">
      <summary>An interface for configuring logging providers.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.ILoggingBuilder.Services">
      <summary>Gets the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> where Logging services are configured.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Logging.LoggerFactory">
      <summary>Produces instances of <see cref="T:Microsoft.Extensions.Logging.ILogger" /> classes based on the given providers.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory" /> instance.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider})">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory" /> instance.</summary>
      <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instances.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Logging.LoggerFilterOptions)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory" /> instance.</summary>
      <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instances.</param>
      <param name="filterOptions">The filter options to use.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.LoggerFilterOptions})">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory" /> instance.</summary>
      <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instances.</param>
      <param name="filterOption">The filter option to use.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.LoggerFilterOptions},Microsoft.Extensions.Options.IOptions{Microsoft.Extensions.Logging.LoggerFactoryOptions})">
      <param name="providers" />
      <param name="filterOption" />
      <param name="options" />
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.AddProvider(Microsoft.Extensions.Logging.ILoggerProvider)">
      <summary>Adds the given provider to those used in creating <see cref="T:Microsoft.Extensions.Logging.ILogger" /> instances.</summary>
      <param name="provider">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> to add.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.CheckDisposed">
      <summary>Check if the factory has been disposed.</summary>
      <returns>
        <see langword="true" /> when <see cref="M:Microsoft.Extensions.Logging.LoggerFactory.Dispose" /> as been called</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.Create(System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
      <summary>Creates new instance of <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" /> configured using provided <paramref name="configure" /> delegate.</summary>
      <param name="configure">A delegate to configure the <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" />.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" /> that was created.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.CreateLogger(System.String)">
      <summary>Creates an <see cref="T:Microsoft.Extensions.Logging.ILogger" /> with the given <paramref name="categoryName" />.</summary>
      <param name="categoryName">The category name for messages produced by the logger.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Logging.ILogger" /> that was created.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFactory.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Logging.LoggerFactoryOptions" />
    <member name="M:Microsoft.Extensions.Logging.LoggerFactoryOptions.#ctor" />
    <member name="P:Microsoft.Extensions.Logging.LoggerFactoryOptions.ActivityTrackingOptions" />
    <member name="T:Microsoft.Extensions.Logging.LoggerFilterOptions">
      <summary>The options for a LoggerFilter.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFilterOptions.#ctor">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions" /> instance.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.CaptureScopes">
      <summary>Gets or sets value indicating whether logging scopes are being captured. Defaults to <see langword="true" /></summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.MinLevel">
      <summary>Gets or sets the minimum level of log messages if none of the rules match.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.Rules">
      <summary>Gets the collection of <see cref="T:Microsoft.Extensions.Logging.LoggerFilterRule" /> used for filtering log messages.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Logging.LoggerFilterRule">
      <summary>Defines a rule used to filter log messages.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFilterRule.#ctor(System.String,System.String,System.Nullable{Microsoft.Extensions.Logging.LogLevel},System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFilterRule" /> instance.</summary>
      <param name="providerName">The provider name to use in this filter rule.</param>
      <param name="categoryName">The category name to use in this filter rule.</param>
      <param name="logLevel">The <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel" /> to use in this filter rule.</param>
      <param name="filter">The filter to apply.</param>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggerFilterRule.ToString" />
    <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.CategoryName">
      <summary>Gets the logger category this rule applies to.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.Filter">
      <summary>Gets the filter delegate that would be applied to messages that passed the <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel" />.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel">
      <summary>Gets the minimum <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel" /> of messages.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.ProviderName">
      <summary>Gets the logger provider type or alias this rule applies to.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Logging.LoggingBuilderExtensions">
      <summary>Provides extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.AddConfiguration(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.Extensions.Configuration.IConfiguration)">
      <summary>Configures logger filter options from an instance of <see cref="T:Microsoft.Extensions.Configuration.IConfiguration" />.</summary>
      <param name="builder">The logging builder to configure logger filter options for.</param>
      <param name="configuration">The filter options to add.</param>
      <returns>The logging builder with filter options configured.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.AddProvider(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.Extensions.Logging.ILoggerProvider)">
      <summary>Adds the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> to the <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" /> so that calls can be chained.</summary>
      <param name="builder">The logging builder to add the <paramref name="provider" /> to.</param>
      <param name="provider">The logger provider to add to the <paramref name="builder" />.</param>
      <returns>A logging builder that you can chain additional calls to.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.ClearProviders(Microsoft.Extensions.Logging.ILoggingBuilder)">
      <summary>Removes all logger providers from <paramref name="builder" />.</summary>
      <param name="builder">The logging builder to remove logging providers from.</param>
      <returns>The logging builder with logger providers removed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.Configure" />
    <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.SetMinimumLevel(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.Extensions.Logging.LogLevel)">
      <summary>Sets a minimum <see cref="T:Microsoft.Extensions.Logging.LogLevel" /> requirement for log messages to be logged.</summary>
      <param name="builder">The logging builder to set the minimum level on.</param>
      <param name="level">One of the enumeration values to set as the minimum logging severity level.</param>
      <returns>The logging builder with minimum log level set.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Logging.ProviderAliasAttribute">
      <summary>Defines alias for <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider" /> implementation to be used in filtering rules.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Logging.ProviderAliasAttribute.#ctor(System.String)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.Logging.ProviderAliasAttribute" /> instance.</summary>
      <param name="alias">The alias to set.</param>
    </member>
    <member name="P:Microsoft.Extensions.Logging.ProviderAliasAttribute.Alias">
      <summary>The alias of the provider.</summary>
    </member>
  </members>
</doc>