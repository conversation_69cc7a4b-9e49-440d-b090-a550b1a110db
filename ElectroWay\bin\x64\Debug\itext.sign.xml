<?xml version="1.0"?>
<doc>
    <assembly>
        <name>itext.sign</name>
    </assembly>
    <members>
        <member name="T:iText.Signatures.AccessPermissions">
            <summary>Access permissions value to be set to certification signature as a part of DocMDP configuration.</summary>
        </member>
        <member name="F:iText.Signatures.AccessPermissions.UNSPECIFIED">
            <summary>Unspecified access permissions value which makes signature "approval" rather than "certification".
                </summary>
        </member>
        <member name="F:iText.Signatures.AccessPermissions.NO_CHANGES_PERMITTED">
            <summary>Access permissions level 1 which indicates that no changes are permitted except for DSS and DTS creation.
                </summary>
        </member>
        <member name="F:iText.Signatures.AccessPermissions.FORM_FIELDS_MODIFICATION">
            <summary>
            Access permissions level 2 which indicates that permitted changes, with addition to level 1, are:
            filling in forms, instantiating page templates, and signing.
            </summary>
        </member>
        <member name="F:iText.Signatures.AccessPermissions.ANNOTATION_MODIFICATION">
            <summary>
            Access permissions level 3 which indicates that permitted changes, with addition to level 2, are:
            annotation creation, deletion and modification.
            </summary>
        </member>
        <member name="T:iText.Signatures.AsymmetricAlgorithmSignature">
             <summary>
             This class allows you to sign with either an RSACryptoServiceProvider/DSACryptoServiceProvider from a X509Certificate2,
             or from manually created RSACryptoServiceProvider/DSACryptoServiceProvider.
             Depending on the certificate's CSP, sometimes you will not be able to sign with SHA-256/SHA-512 hash algorithm with 
             RSACryptoServiceProvider taken directly from the certificate.
             This class allows you to use a workaround in this case and sign with certificate's private key and SHA-256/SHA-512 anyway.
             
             An example of a workaround for CSP that does not support SHA-256/SHA-512:
             <code>
                        if (certificate.PrivateKey is RSACryptoServiceProvider)
                        {                
                            RSACryptoServiceProvider rsa = (RSACryptoServiceProvider)certificate.PrivateKey;
            
                            // Modified by J. Arturo
                            // Workaround for SHA-256 and SHA-512
            
                            if (rsa.CspKeyContainerInfo.ProviderName == "Microsoft Strong Cryptographic Provider" ||
                                            rsa.CspKeyContainerInfo.ProviderName == "Microsoft Enhanced Cryptographic Provider v1.0" ||
                                            rsa.CspKeyContainerInfo.ProviderName == "Microsoft Base Cryptographic Provider v1.0")
                            {
                                string providerName = "Microsoft Enhanced RSA and AES Cryptographic Provider";
                                int providerType = 24;
            
                                Type CspKeyContainerInfo_Type = typeof(CspKeyContainerInfo);
            
                                FieldInfo CspKeyContainerInfo_m_parameters = CspKeyContainerInfo_Type.GetField("m_parameters", BindingFlags.NonPublic | BindingFlags.Instance);
                                CspParameters parameters = (CspParameters)CspKeyContainerInfo_m_parameters.GetValue(rsa.CspKeyContainerInfo);
            
                                var cspparams = new CspParameters(providerType, providerName, rsa.CspKeyContainerInfo.KeyContainerName);
                                cspparams.Flags = parameters.Flags;
            
                                using (var rsaKey = new RSACryptoServiceProvider(cspparams))
                                {
                                    // use rsaKey now
                                }
                            }
                            else
                            {
                                // Use rsa directly
                            }
                        }
             </code>
             
             </summary>
             <see cref="!:https://blogs.msdn.microsoft.com/shawnfa/2008/08/25/using-rsacryptoserviceprovider-for-rsa-sha256-signatures/"/>
             <see cref="!:http://stackoverflow.com/questions/7444586/how-can-i-sign-a-file-using-rsa-and-sha256-with-net"/>
             <see cref="!:http://stackoverflow.com/questions/5113498/can-rsacryptoserviceprovider-nets-rsa-use-sha256-for-encryption-not-signing"/>
             <see cref="!:http://stackoverflow.com/questions/31553523/how-can-i-properly-verify-a-file-using-rsa-and-sha256-with-net"/>
        </member>
        <member name="F:iText.Signatures.AsymmetricAlgorithmSignature.digestAlgorithm">
            The hash algorithm. 
        </member>
        <member name="F:iText.Signatures.AsymmetricAlgorithmSignature.signatureAlgorithm">
            The encryption algorithm (obtained from the private key) 
        </member>
        <member name="T:iText.Signatures.BouncyCastleDigest">
            <summary>
            Implementation for digests accessed directly from the BouncyCastle library.
            </summary>
        </member>
        <member name="M:iText.Signatures.BouncyCastleDigest.GetMessageDigest(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Signatures.CertificateInfo">
            <summary>
            Class containing static methods that allow you to get information from
            an X509 Certificate: the issuer and the subject.
            </summary>
        </member>
        <member name="T:iText.Signatures.CertificateInfo.X500Name">
            <summary>Class that holds an X509 name.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.C">
            <summary>Country code - StringType(SIZE(2)).</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.O">
            <summary>Organization - StringType(SIZE(1..64)).</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.OU">
            <summary>Organizational unit name - StringType(SIZE(1..64)).</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.T">
            <summary>Title.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.CN">
            <summary>Common name - StringType(SIZE(1..64)).</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.SN">
            <summary>Device serial number name - StringType(SIZE(1..64)).</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.L">
            <summary>Locality name - StringType(SIZE(1..64)).</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.ST">
            <summary>State, or province name - StringType(SIZE(1..64)).</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.SURNAME">
            <summary>Naming attribute of type X520name.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.GIVENNAME">
            <summary>Naming attribute of type X520name.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.INITIALS">
            <summary>Naming attribute of type X520name.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.GENERATION">
            <summary>Naming attribute of type X520name.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.UNIQUE_IDENTIFIER">
            <summary>Naming attribute of type X520name.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.EmailAddress">
            <summary>Email address (RSA PKCS#9 extension) - IA5String.</summary>
            <remarks>
            Email address (RSA PKCS#9 extension) - IA5String.
            <para />
            Note: if you're trying to be ultra orthodox, don't use this! It shouldn't be in here.
            </remarks>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.E">
            <summary>Email address in Verisign certificates.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.DC">
            <summary>Object identifier.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.UID">
            <summary>LDAP User id.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.DefaultSymbols">
            <summary>A Map with default symbols.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateInfo.X500Name.values">
            <summary>A Map with values.</summary>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.X500Name.#ctor(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary>Constructs an X509 name.</summary>
            <param name="seq">an ASN1 Sequence</param>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.X500Name.#ctor(System.String)">
            <summary>Constructs an X509 name.</summary>
            <param name="dirName">a directory name</param>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.X500Name.GetField(System.String)">
            <summary>Gets the first entry from the field array retrieved from the values Map.</summary>
            <param name="name">the field name</param>
            <returns>the (first) field value</returns>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.X500Name.GetFieldArray(System.String)">
            <summary>Gets a field array from the values Map.</summary>
            <param name="name">The field name</param>
            <returns>List</returns>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.X500Name.GetFields">
            <summary>Getter for values.</summary>
            <returns>Map with the fields of the X509 name</returns>
        </member>
        <member name="T:iText.Signatures.CertificateInfo.X509NameTokenizer">
            <summary>
            Class for breaking up an X500 Name into it's component tokens, similar to
            <see cref="T:iText.Commons.Utils.StringTokenizer"/>.
            </summary>
            <remarks>
            Class for breaking up an X500 Name into it's component tokens, similar to
            <see cref="T:iText.Commons.Utils.StringTokenizer"/>.
            We need this class as some of the lightweight Java environments don't support classes such as StringTokenizer.
            </remarks>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.X509NameTokenizer.#ctor(System.String)">
            <summary>Creates an X509NameTokenizer.</summary>
            <param name="oid">the oid that needs to be parsed</param>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.X509NameTokenizer.HasMoreTokens">
            <summary>Checks if the tokenizer has any tokens left.</summary>
            <returns>true if there are any tokens left, false if there aren't</returns>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.X509NameTokenizer.NextToken">
            <summary>Returns the next token.</summary>
            <returns>the next token</returns>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.GetIssuerFields(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Get the issuer fields from an X509 Certificate.</summary>
            <param name="cert">an X509Certificate</param>
            <returns>an X500Name</returns>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.GetIssuer(System.Byte[])">
            <summary>Get the "issuer" from the TBSCertificate bytes that are passed in.</summary>
            <param name="enc">a TBSCertificate in a byte array</param>
            <returns>an IASN1Primitive</returns>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.GetSubjectFields(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Get the subject fields from an X509 Certificate.</summary>
            <param name="cert">an X509Certificate</param>
            <returns>an X500Name</returns>
        </member>
        <member name="M:iText.Signatures.CertificateInfo.GetSubject(System.Byte[])">
            <summary>Get the "subject" from the TBSCertificate bytes that are passed in.</summary>
            <param name="enc">A TBSCertificate in a byte array</param>
            <returns>a IASN1Primitive</returns>
        </member>
        <member name="T:iText.Signatures.CertificateUtil">
            <summary>
            This class contains a series of static methods that
            allow you to retrieve information from a Certificate.
            </summary>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetCRL(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Gets a CRL from an X509 certificate.</summary>
            <param name="certificate">the X509Certificate to extract the CRL from</param>
            <returns>CRL or null if there's no CRL available</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetCRLs(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Gets a CRLs from the X509 certificate.</summary>
            <param name="certificate">the X509Certificate to extract the CRLs from</param>
            <returns>CRL list or null if there's no CRL available</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetCRLURL(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Gets the URL of the Certificate Revocation List for a Certificate</summary>
            <param name="certificate">the Certificate</param>
            <returns>the String where you can check if the certificate was revoked.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetCRLURLs(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Gets the list of the Certificate Revocation List URLs for a Certificate.</summary>
            <param name="certificate">the Certificate to get CRL URLs for</param>
            <returns>the list of URL strings where you can check if the certificate is revoked.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetDistributionPointByName(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName)">
            <summary>
            Gets the Distribution Point from the certificate by name specified in the Issuing Distribution Point from the
            Certificate Revocation List for a Certificate.
            </summary>
            <param name="certificate">the certificate to retrieve Distribution Points</param>
            <param name="issuingDistributionPointName">distributionPointName retrieved from the IDP of the CRL</param>
            <returns>distribution point withthe same name as specified in the IDP.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetCRL(System.String)">
            <summary>Gets the CRL object using a CRL URL.</summary>
            <param name="url">the URL where the CRL is located</param>
            <returns>CRL object</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.ParseCrlFromStream(System.IO.Stream)">
            <summary>Parses a CRL from an InputStream.</summary>
            <param name="input">the InputStream holding the unparsed CRL</param>
            <returns>the parsed CRL object.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.ParseCrlFromBytes(System.Byte[])">
            <summary>Parses a CRL from bytes.</summary>
            <param name="crlBytes">the bytes holding the unparsed CRL</param>
            <returns>the parsed CRL object.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetIssuerCertURL(iText.Commons.Bouncycastle.Cert.IX509Crl)">
            <summary>Retrieves the URL for the issuer certificate for the given CRL.</summary>
            <param name="crl">the CRL response</param>
            <returns>the URL or null.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetOCSPURL(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Retrieves the OCSP URL from the given certificate.</summary>
            <param name="certificate">the certificate</param>
            <returns>the URL or null</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetIssuerCertURL(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Retrieves the URL for the issuer lists certificates for the given certificate.</summary>
            <param name="certificate">the certificate</param>
            <returns>the URL or null.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetTSAURL(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Gets the URL of the TSA if it's available on the certificate</summary>
            <param name="certificate">a certificate</param>
            <returns>a TSA URL</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GenerateCertificate(System.IO.Stream)">
            <summary>Generates a certificate object and initializes it with the data read from the input stream inStream.
                </summary>
            <param name="data">the input stream with the certificates.</param>
            <returns>a certificate object initialized with the data from the input stream.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.RetrieveRevocationInfoFromSignedData(iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject,System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Crl},System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse},System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Asn1.IAsn1Sequence})">
            <summary>Try to retrieve CRL and OCSP responses from the signed data crls field.</summary>
            <param name="taggedObj">
            signed data crls field as
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject"/>.
            </param>
            <param name="crls">collection to store retrieved CRL responses.</param>
            <param name="ocsps">
            collection of
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            wrappers to store retrieved
            OCSP responses.
            </param>
            <param name="otherRevocationInfoFormats">
            collection of revocation info other than OCSP and CRL responses,
            e.g. SCVP Request and Response, stored as
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Sequence"/>.
            </param>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.CreateRevocationInfoChoices(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Crl},System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse},System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Asn1.IAsn1Sequence})">
            <summary>
            Creates the revocation info (crls field) for SignedData structure:
            RevocationInfoChoices ::= SET OF RevocationInfoChoice
            RevocationInfoChoice ::= CHOICE {
            crl CertificateList,
            other [1] IMPLICIT OtherRevocationInfoFormat }
            OtherRevocationInfoFormat ::= SEQUENCE {
            otherRevInfoFormat OBJECT IDENTIFIER,
            otherRevInfo ANY DEFINED BY otherRevInfoFormat }
            CertificateList  ::=  SEQUENCE  {
            tbsCertList          TBSCertList,
            signatureAlgorithm   AlgorithmIdentifier,
            signatureValue       BIT STRING  }
            </summary>
            <seealso><a href="https://datatracker.ietf.org/doc/html/rfc5652#section-10.2.1">RFC 5652 §10.2.1</a></seealso>
            <param name="crls">collection of CRL revocation status information.</param>
            <param name="ocsps">collection of OCSP revocation status information.</param>
            <param name="otherRevocationInfoFormats">
            collection of revocation info other than OCSP and CRL responses,
            e.g. SCVP Request and Response, stored as
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Sequence"/>.
            </param>
            <returns>
            
            <c>crls [1] RevocationInfoChoices</c>
            field of SignedData structure. Null if SignedData has
            no revocation data.
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.CheckIfIssuersMatch(iText.Commons.Bouncycastle.Cert.Ocsp.ICertID,iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>
            Checks if the issuer of the provided certID (specified in the OCSP response) and provided issuer of the
            certificate in question matches, i.e. checks that issuerNameHash and issuerKeyHash fields of the certID
            is the hash of the issuer's name and public key.
            </summary>
            <remarks>
            Checks if the issuer of the provided certID (specified in the OCSP response) and provided issuer of the
            certificate in question matches, i.e. checks that issuerNameHash and issuerKeyHash fields of the certID
            is the hash of the issuer's name and public key.
            <para />
            SingleResp contains the basic information of the status of the certificate identified by the certID. The issuer
            name and serial number identify a unique certificate, so if serial numbers of the certificate in question and
            certID serial number are equals and issuers match, then SingleResp contains the information about the status of
            the certificate in question.
            </remarks>
            <param name="certID">certID specified in the OCSP response</param>
            <param name="issuerCert">the issuer of the certificate in question</param>
            <returns>true if the issuers are the same, false otherwise.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetExtensionValueByOid(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Retrieves certificate extension value by its OID.</summary>
            <param name="certificate">to get extension from</param>
            <param name="id">extension OID to retrieve</param>
            <returns>encoded extension value.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.IsSignatureValid(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Checks if an OCSP response is genuine.</summary>
            <param name="ocspResp">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            the OCSP response wrapper
            </param>
            <param name="responderCert">the responder certificate</param>
            <returns>true if the OCSP response verifies against the responder certificate.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.IsIssuerCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Checks if the certificate is signed by provided issuer certificate.</summary>
            <param name="subjectCertificate">a certificate to check</param>
            <param name="issuerCertificate">an issuer certificate to check</param>
            <returns>true if the first passed certificate is signed by next passed certificate.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.IsSelfSigned(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Checks if the certificate is self-signed.</summary>
            <param name="certificate">a certificate to check</param>
            <returns>true if the certificate is self-signed.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetExtensionValue(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Gets certificate extension value.</summary>
            <param name="certificate">the certificate from which we need the ExtensionValue</param>
            <param name="oid">the Object Identifier value for the extension</param>
            <returns>
            the extension value as an
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            object.
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetExtensionValue(iText.Commons.Bouncycastle.Cert.IX509Crl,System.String)">
            <summary>Gets CRL extension value.</summary>
            <param name="crl">the CRL from which we need the ExtensionValue</param>
            <param name="oid">the Object Identifier value for the extension</param>
            <returns>
            the extension value as an
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            object.
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetExtensionValueFromByteArray(System.Byte[])">
            <summary>
            Converts extension value represented as byte array to
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            object.
            </summary>
            <param name="extensionValue">the extension value as byte array</param>
            <returns>
            the extension value as an
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            object.
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetStringFromGeneralName(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>Gets a String from an ASN1Primitive</summary>
            <param name="names">
            the
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            primitive wrapper
            </param>
            <returns>a human-readable String</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.GetValueFromAIAExtension(iText.Commons.Bouncycastle.Asn1.IAsn1Object,System.String)">
            <summary>Retrieves accessLocation value for specified accessMethod from the Authority Information Access extension.
                </summary>
            <param name="extensionValue">Authority Information Access extension value</param>
            <param name="accessMethod">accessMethod OID; usually id-ad-caIssuers or id-ad-ocsp</param>
            <returns>the location (URI) of the information.</returns>
        </member>
        <member name="M:iText.Signatures.CertificateUtil.CreateOcsp(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary>
            Helper method that creates the
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            object from the response bytes.
            </summary>
            <param name="seq">response bytes.</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            object.
            </returns>
        </member>
        <member name="T:iText.Signatures.CertificateVerification">
            <summary>This class consists of some methods that allow you to verify certificates.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateVerification.LOGGER">
            <summary>The Logger instance.</summary>
        </member>
        <member name="M:iText.Signatures.CertificateVerification.VerifyCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Crl})">
            <summary>Verifies a single certificate for the current date.</summary>
            <param name="cert">the certificate to verify</param>
            <param name="crls">the certificate revocation list or <c>null</c></param>
            <returns>
            a <c>String</c> with the error description or <c>null</c>
            if no error
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateVerification.VerifyCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Crl},System.DateTime)">
            <summary>Verifies a single certificate.</summary>
            <param name="cert">the certificate to verify</param>
            <param name="crls">the certificate revocation list or <c>null</c></param>
            <param name="calendar">the date, shall not be null</param>
            <returns>
            a <c>String</c> with the error description or <c>null</c>
            if no error
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateVerification.VerifyCertificates(iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.List{iText.Commons.Bouncycastle.Cert.IX509Certificate},System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Crl})">
            <summary>Verifies a certificate chain against a KeyStore for the current date.</summary>
            <param name="certs">the certificate chain</param>
            <param name="keystore">the <c>KeyStore</c></param>
            <param name="crls">the certificate revocation list or <c>null</c></param>
            <returns>
            empty list if the certificate chain could be validated or a
            <c>Object[]{cert,error}</c> where <c>cert</c> is the
            failed certificate and <c>error</c> is the error message
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateVerification.VerifyCertificates(iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.List{iText.Commons.Bouncycastle.Cert.IX509Certificate},System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Crl},System.DateTime)">
            <summary>Verifies a certificate chain against a KeyStore.</summary>
            <param name="certs">the certificate chain</param>
            <param name="keystore">the <c>KeyStore</c></param>
            <param name="crls">the certificate revocation list or <c>null</c></param>
            <param name="calendar">the date, shall not be null</param>
            <returns>
            empty list if the certificate chain could be validated or a
            <c>Object[]{cert,error}</c> where <c>cert</c> is the
            failed certificate and <c>error</c> is the error message
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateVerification.VerifyCertificates(iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.List{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Verifies a certificate chain against a KeyStore for the current date.</summary>
            <param name="certs">the certificate chain</param>
            <param name="keystore">the <c>KeyStore</c></param>
            <returns>
            <c>null</c> if the certificate chain could be validated or a
            <c>Object[]{cert,error}</c> where <c>cert</c> is the
            failed certificate and <c>error</c> is the error message
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateVerification.VerifyCertificates(iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.List{iText.Commons.Bouncycastle.Cert.IX509Certificate},System.DateTime)">
            <summary>Verifies a certificate chain against a KeyStore.</summary>
            <param name="certs">the certificate chain</param>
            <param name="keystore">the <c>KeyStore</c></param>
            <param name="calendar">the date, shall not be null</param>
            <returns>
            <c>null</c> if the certificate chain could be validated or a
            <c>Object[]{cert,error}</c> where <c>cert</c> is the
            failed certificate and <c>error</c> is the error message
            </returns>
        </member>
        <member name="M:iText.Signatures.CertificateVerification.VerifyOcspCertificates(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,System.Collections.Generic.List{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Verifies an OCSP response against a KeyStore.</summary>
            <param name="ocsp">the OCSP response</param>
            <param name="keystore">the <c>KeyStore</c></param>
            <returns><c>true</c> is a certificate was found</returns>
        </member>
        <member name="M:iText.Signatures.CertificateVerification.VerifyTimestampCertificates(iText.Commons.Bouncycastle.Tsp.ITimeStampToken,System.Collections.Generic.List{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Verifies a time stamp against a KeyStore.</summary>
            <param name="ts">the time stamp</param>
            <param name="keystore">the <c>KeyStore</c></param>
            <returns><c>true</c> is a certificate was found</returns>
        </member>
        <member name="T:iText.Signatures.CertificateVerifier">
            <summary>
            Superclass for a series of certificate verifiers that will typically
            be used in a chain.
            </summary>
            <remarks>
            Superclass for a series of certificate verifiers that will typically
            be used in a chain. It wraps another <c>CertificateVerifier</c>
            that is the next element in the chain of which the <c>verify()</c>
            method will be called.
            </remarks>
        </member>
        <member name="F:iText.Signatures.CertificateVerifier.verifier">
            <summary>The previous CertificateVerifier in the chain of verifiers.</summary>
        </member>
        <member name="F:iText.Signatures.CertificateVerifier.onlineCheckingAllowed">
            <summary>Indicates if going online to verify a certificate is allowed.</summary>
        </member>
        <member name="M:iText.Signatures.CertificateVerifier.#ctor(iText.Signatures.CertificateVerifier)">
            <summary>Creates the final CertificateVerifier in a chain of verifiers.</summary>
            <param name="verifier">the previous verifier in the chain</param>
        </member>
        <member name="M:iText.Signatures.CertificateVerifier.SetOnlineCheckingAllowed(System.Boolean)">
            <summary>Decide whether or not online checking is allowed.</summary>
            <param name="onlineCheckingAllowed">a boolean indicating whether the certificate can be verified using online verification results.
                </param>
        </member>
        <member name="M:iText.Signatures.CertificateVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>
            Checks the validity of the certificate, and calls the next
            verifier in the chain, if any.
            </summary>
            <param name="signCert">the certificate that needs to be checked</param>
            <param name="issuerCert">its issuer</param>
            <param name="signDate">the date the certificate needs to be valid</param>
            <returns>a list of <c>VerificationOK</c> objects. The list will be empty if the certificate couldn't be verified.
                </returns>
        </member>
        <member name="T:iText.Signatures.Cms.AlgorithmIdentifier">
            <summary>This class represents algorithm identifier structure.</summary>
        </member>
        <member name="M:iText.Signatures.Cms.AlgorithmIdentifier.#ctor(System.String)">
            <summary>Creates an Algorithm identifier structure without parameters.</summary>
            <param name="algorithmId">the Object id of the algorithm</param>
        </member>
        <member name="M:iText.Signatures.Cms.AlgorithmIdentifier.#ctor(System.String,iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>Creates an Algorithm identifier structure with parameters.</summary>
            <param name="algorithmId">the Object id of the algorithm</param>
            <param name="parameters">the algorithm parameters as an ASN1 structure</param>
        </member>
        <member name="M:iText.Signatures.Cms.AlgorithmIdentifier.#ctor(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Creates an Algorithm identifier structure with parameters.</summary>
            <param name="asnStruct">asn1 encodable to retrieve algorithm identifier</param>
        </member>
        <member name="M:iText.Signatures.Cms.AlgorithmIdentifier.GetAlgorithmOid">
            <summary>Return the OID of the algorithm.</summary>
            <returns>the OID of the algorithm.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.AlgorithmIdentifier.GetParameters">
            <summary>Return the parameters for the algorithm.</summary>
            <returns>the parameters for the algorithm.</returns>
        </member>
        <member name="T:iText.Signatures.Cms.CmsAttribute">
            <summary>This class represents Attribute structure.</summary>
        </member>
        <member name="M:iText.Signatures.Cms.CmsAttribute.#ctor(System.String,iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>Creates an attribute.</summary>
            <param name="type">the type of the attribute</param>
            <param name="value">the value</param>
        </member>
        <member name="M:iText.Signatures.Cms.CmsAttribute.GetType">
            <summary>Returns the type of the attribute.</summary>
            <returns>the type of the attribute.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.CmsAttribute.GetValue">
            <summary>Returns the value of the attribute.</summary>
            <returns>the value of the attribute.</returns>
        </member>
        <member name="T:iText.Signatures.Cms.CMSContainer">
            <summary>
            The CMS container which represents SignedData structure from
            <a href="https://datatracker.ietf.org/doc/html/rfc5652#section-5.1">rfc5652 Cryptographic Message Syntax (CMS)</a>
            </summary>
        </member>
        <member name="F:iText.Signatures.Cms.CMSContainer.otherRevocationInfo">
            <summary>Collection to store revocation info other than OCSP and CRL responses, e.g. SCVP Request and Response.
                </summary>
        </member>
        <member name="F:iText.Signatures.Cms.CMSContainer.crls">
            <summary>Optional.</summary>
            <remarks>
            Optional.
            <para />
            It is a collection of CRL revocation status information.
            </remarks>
        </member>
        <member name="F:iText.Signatures.Cms.CMSContainer.ocsps">
            <summary>Optional.</summary>
            <remarks>
            Optional.
            <para />
            It is a collection of CRL revocation status information.
            </remarks>
        </member>
        <member name="F:iText.Signatures.Cms.CMSContainer.encapContentInfo">
            <summary>This represents the signed content.</summary>
            <remarks>
            This represents the signed content.
            In the case of a signed PDF document this will of type data with no content.
            </remarks>
        </member>
        <member name="F:iText.Signatures.Cms.CMSContainer.certificates">
            <summary>Optional.</summary>
            <remarks>
            Optional.
            <para />
            It is intended to add all certificates to be able to validate the entire chain.
            </remarks>
        </member>
        <member name="F:iText.Signatures.Cms.CMSContainer.signerInfo">
            <summary>This class only supports one signer per signature field.</summary>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.#ctor">
            <summary>Creates an empty SignedData structure.</summary>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.#ctor(System.Byte[])">
            <summary>Creates a SignedData structure from a serialized ASN1 structure.</summary>
            <param name="encodedCMSdata">the serialized CMS container</param>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.SetSignerInfo(iText.Signatures.Cms.SignerInfo)">
            <summary>This class only supports one signer per signature field.</summary>
            <param name="signerInfo">the singerInfo</param>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetSignerInfo">
            <summary>This class only supports one signer per signature field.</summary>
            <returns>the singerInfo</returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetSizeEstimation">
            <summary>
            When all fields except for signer.signedAttributes.digest and signer.signature are completed
            it is possible to calculate the eventual size of the signature by serializing except for the signature
            (that depends on the digest and cypher but is set at 1024 bytes) and later added unsigned attributes like
            timestamps.
            </summary>
            <returns>
            the estimated size of the complete CMS container before signature is added, size for the signature is
            added, size for other attributes like timestamps is not.
            </returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetCmsVersion">
            <summary>Only version 1 is supported by this class.</summary>
            <returns>1 as CMSversion</returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetDigestAlgorithm">
            <summary>The digest algorithm OID and parameters used by the signer.</summary>
            <remarks>
            The digest algorithm OID and parameters used by the signer.
            This class only supports one signer for use in pdf signatures, so only one digest algorithm is supported.
            <para />
            This field is set when adding the signerInfo.
            </remarks>
            <returns>
            
            <see cref="T:iText.Signatures.Cms.AlgorithmIdentifier"/>
            digest algorithm.
            </returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetEncapContentInfo">
            <summary>This represents the signed content.</summary>
            <remarks>
            This represents the signed content.
            In the case of a signed PDF document this will be of type data with no content.
            </remarks>
            <returns>a representation of the data to be signed.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.SetEncapContentInfo(iText.Signatures.Cms.EncapsulatedContentInfo)">
            <summary>This represents the signed content.</summary>
            <remarks>
            This represents the signed content.
            In the case of a signed PDF document this will be of type data with no content.
            Defaults to 1.2.840.113549.1.7.1 {iso(1) member-body(2) us(840) rsadsi(113549) pkcs(1) pkcs-7(7) id-data(1)}
            </remarks>
            <param name="encapContentInfo">a representation of the data to be signed.</param>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.AddCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Adds a certificate.</summary>
            <param name="cert">the certificate to be added</param>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.AddCertificates(iText.Commons.Bouncycastle.Cert.IX509Certificate[])">
            <summary>Adds a set of certificates.</summary>
            <param name="certs">the certificates to be added</param>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetCertificates">
            <summary>Retrieves a copy of the list of certificates.</summary>
            <returns>the list of certificates to be used for signing and certificate validation</returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetCrls">
            <summary>Retrieves a copy of the list of CRLs.</summary>
            <returns>the list of CRL revocation info.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.AddCrl(iText.Commons.Bouncycastle.Cert.IX509Crl)">
            <summary>Adds a CRL response to the CMS container.</summary>
            <param name="crl">the CRL response to be added.</param>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetOcsps">
            <summary>Retrieves a copy of the list of OCSPs.</summary>
            <returns>the list of OCSP revocation info.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.AddOcsp(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse)">
            <summary>Adds an OCSP response to the CMS container.</summary>
            <param name="ocspResponse">the OCSP response to be added.</param>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.SetSerializedSignedAttributes(System.Byte[])">
            <summary>Sets the Signed Attributes of the signer info to this serialized version.</summary>
            <remarks>
            Sets the Signed Attributes of the signer info to this serialized version.
            The signed attributes will become read-only.
            </remarks>
            <param name="signedAttributesData">the serialized Signed Attributes</param>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.GetSerializedSignedAttributes">
            <summary>Retrieves the encoded signed attributes of the signer info.</summary>
            <remarks>
            Retrieves the encoded signed attributes of the signer info.
            This makes the signed attributes read only.
            </remarks>
            <returns>the encoded signed attributes of the signer info.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.CMSContainer.Serialize">
            <summary>Serializes the SignedData structure and makes the signer infos signed attributes read only.</summary>
            <returns>the encoded DignedData structure.</returns>
        </member>
        <member name="T:iText.Signatures.Cms.EncapsulatedContentInfo">
            <summary>This class represents the signed content.</summary>
        </member>
        <member name="F:iText.Signatures.Cms.EncapsulatedContentInfo.eContentType">
            <summary>Object identifier of the content field</summary>
        </member>
        <member name="F:iText.Signatures.Cms.EncapsulatedContentInfo.eContent">
            <summary>Optional.</summary>
            <remarks>
            Optional.
            <para />
            The actual content as an octet string. Does not have to be DER encoded.
            </remarks>
        </member>
        <member name="M:iText.Signatures.Cms.EncapsulatedContentInfo.#ctor(System.String,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary>Creates an EncapsulatedContentInfo with contenttype and content.</summary>
            <param name="eContentType">the content type Oid (object id)</param>
            <param name="eContent">the content</param>
        </member>
        <member name="M:iText.Signatures.Cms.EncapsulatedContentInfo.#ctor(System.String)">
            <summary>Creates an EncapsulatedContentInfo with contenttype.</summary>
            <param name="eContentType">the content type Oid (object id)</param>
        </member>
        <member name="M:iText.Signatures.Cms.EncapsulatedContentInfo.#ctor">
            <summary>Creates a default EncapsulatedContentInfo.</summary>
        </member>
        <member name="M:iText.Signatures.Cms.EncapsulatedContentInfo.GetContentType">
            <summary>Returns the contenttype oid.</summary>
            <returns>the contenttype oid.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.EncapsulatedContentInfo.GetContent">
            <summary>Returns the content.</summary>
            <returns>the content.</returns>
        </member>
        <member name="T:iText.Signatures.Cms.SignerInfo">
            <summary>
            This class represents the SignerInfo structure from
            <a href="https://datatracker.ietf.org/doc/html/rfc5652#section-5.3">rfc5652   Cryptographic Message Syntax (CMS)</a>
            </summary>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.#ctor">
            <summary>Creates an empty SignerInfo structure.</summary>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.#ctor(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable,System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Creates a SignerInfo structure from an ASN1 structure.</summary>
            <param name="signerInfoStructure">the ASN1 structure containing signerInfo</param>
            <param name="certificates">the certificates of the CMS, it should contain the signing certificate</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetDigestAlgorithm">
            <summary>Returns the algorithmId to create the digest of the data to sign.</summary>
            <returns>the OID of the digest algorithm.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetDigestAlgorithm(iText.Signatures.Cms.AlgorithmIdentifier)">
            <summary>Sets the algorithmId to create the digest of the data to sign.</summary>
            <param name="algorithmId">the OID of the algorithm</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetMessageDigest(System.Byte[])">
            <summary>Adds or replaces the message digest signed attribute.</summary>
            <param name="digest">ASN.1 type MessageDigest</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetSigningCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Sets the certificate that is used to sign.</summary>
            <param name="certificate">the certificate that is used to sign</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetSigningCertificate">
            <summary>Gets the certificate that is used to sign.</summary>
            <returns>the certificate that is used to sign.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetSignatureData">
            <summary>Gets the signature data.</summary>
            <returns>the signature data.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetSigningCertificateAndAddToSignedAttributes(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Sets the certificate that is used to sign a document and adds it to the signed attributes.</summary>
            <param name="certificate">the certificate that is used to sign</param>
            <param name="digestAlgorithmOid">the oid of the digest algorithm to be added to the signed attributes</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetOcspResponses(System.Collections.Generic.ICollection{System.Byte[]})">
            <summary>Adds a set of OCSP responses as signed attributes.</summary>
            <param name="ocspResponses">a set of binary representations of OCSP responses.</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetCrlResponses(System.Collections.Generic.ICollection{System.Byte[]})">
            <summary>Adds a set of CRL responses as signed attributes.</summary>
            <param name="crlResponses">a set of binary representations of CRL responses.</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.AddSignerCertificateToSignedAttributes(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Adds the signer certificate to the signed attributes as a SigningCertificateV2 structure.</summary>
            <param name="cert">the certificate to add</param>
            <param name="digestAlgorithmOid">the digest algorithm oid that will be used</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetSignature(System.Byte[])">
            <summary>Sets the actual signature.</summary>
            <param name="signatureData">a byte array containing the signature</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetSignatureAlgorithm(iText.Signatures.Cms.AlgorithmIdentifier)">
            <summary>Optional.</summary>
            <remarks>
            Optional.
            Sets the OID and parameters of the algorithm that will be used to create the signature.
            This will be overwritten when setting the signing certificate.
            </remarks>
            <param name="algorithm">The OID and parameters of the algorithm that will be used to create the signature.
                </param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetCmsVersion">
            <summary>Value 0 when no signerIdentifier is available.</summary>
            <remarks>
            Value 0 when no signerIdentifier is available.
            Value 1 when signerIdentifier is of type issuerAndSerialNumber.
            Value 3 when signerIdentifier is of type subjectKeyIdentifier.
            </remarks>
            <returns>CMS version.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetSignedAttributes">
            <summary>Optional.</summary>
            <remarks>
            Optional.
            <para />
            Attributes that should be part of the signed content
            optional, but it MUST be present if the content type of
            the EncapsulatedContentInfo value being signed is not id-data.
            In that case it must at least contain the following two attributes:
            <para />
            A content-type attribute having as its value the content type
            of the EncapsulatedContentInfo value being signed.  Section
            11.1 defines the content-type attribute.  However, the
            content-type attribute MUST NOT be used as part of a
            countersignature unsigned attribute as defined in Section 11.4.
            <para />
            A message-digest attribute, having as its value the message
            digest of the content.  Section 11.2 defines the message-digest
            attribute.
            </remarks>
            <returns>collection of the signed attributes.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.AddSignedAttribute(iText.Signatures.Cms.CmsAttribute)">
            <summary>Adds a new attribute to the signed attributes.</summary>
            <remarks>
            Adds a new attribute to the signed attributes.
            This become readonly after retrieving the serialized version
            <see cref="M:iText.Signatures.Cms.SignerInfo.SerializeSignedAttributes"/>.
            </remarks>
            <param name="attribute">the attribute to add</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetUnSignedAttributes">
            <summary>Retrieves the optional unsigned attributes.</summary>
            <returns>the optional unsigned attributes.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.AddUnSignedAttribute(iText.Signatures.Cms.CmsAttribute)">
            <summary>Optional.</summary>
            <remarks>
            Optional.
            <para />
            Adds attribute that should not or can not be part of the signed content.
            </remarks>
            <param name="attribute">the attribute to add</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SerializeSignedAttributes">
            <summary>Retrieves the encoded signed attributes of the signer info.</summary>
            <remarks>
            Retrieves the encoded signed attributes of the signer info.
            This makes the signed attributes read only.
            </remarks>
            <returns>the encoded signed attributes of the signer info.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.SetSerializedSignedAttributes(System.Byte[])">
            <summary>Sets the signed attributes from a serialized version.</summary>
            <remarks>
            Sets the signed attributes from a serialized version.
            This makes the signed attributes read only.
            </remarks>
            <param name="serializedSignedAttributes">the encoded signed attributes.</param>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetEstimatedSize">
            <summary>Calculates an estimate size for the SignerInfo structure.</summary>
            <remarks>
            Calculates an estimate size for the SignerInfo structure.
            This takes into account the values added including the signature, but does not account for unset items like
            a timestamp response added after actual signing.
            </remarks>
            <returns>the estimated size of the structure.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetAsDerSequence">
            <summary>Serializes the SignerInfo structure and makes the signed attributes readonly.</summary>
            <returns>the encoded SignerInfo structure.</returns>
        </member>
        <member name="M:iText.Signatures.Cms.SignerInfo.GetAsDerSequence(System.Boolean)">
            <summary>Serializes the SignerInfo structure and makes the signed attributes readonly.</summary>
            <remarks>
            Serializes the SignerInfo structure and makes the signed attributes readonly.
            With the possibility to skip making the signed attributes read only for estimation purposes.
            </remarks>
            <param name="estimationRun">set to true to not make signed attributes read only</param>
            <returns>the encoded SignerInfo structure.</returns>
        </member>
        <member name="T:iText.Signatures.CrlClientOffline">
            <summary>
            An implementation of the CrlClient that handles offline
            Certificate Revocation Lists.
            </summary>
        </member>
        <member name="F:iText.Signatures.CrlClientOffline.crls">
            <summary>The CRL as a byte array.</summary>
        </member>
        <member name="M:iText.Signatures.CrlClientOffline.#ctor(System.Byte[])">
            <summary>
            Creates an instance of a CrlClient in case you
            have a local cache of the Certificate Revocation List.
            </summary>
            <param name="crlEncoded">the CRL bytes</param>
        </member>
        <member name="M:iText.Signatures.CrlClientOffline.#ctor(iText.Commons.Bouncycastle.Cert.IX509Crl)">
            <summary>
            Creates an instance of a CrlClient in case you
            have a local cache of the Certificate Revocation List.
            </summary>
            <param name="crl">a CRL object</param>
        </member>
        <member name="M:iText.Signatures.CrlClientOffline.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Returns the CRL bytes (the parameters are ignored).</summary>
            <seealso cref="M:iText.Signatures.ICrlClient.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)"/>
        </member>
        <member name="T:iText.Signatures.CrlClientOnline">
            <summary>
            An implementation of the CrlClient that fetches the CRL bytes
            from an URL.
            </summary>
        </member>
        <member name="F:iText.Signatures.CrlClientOnline.LOGGER">
            <summary>The Logger instance.</summary>
        </member>
        <member name="F:iText.Signatures.CrlClientOnline.urls">
            <summary>The URLs of the CRLs.</summary>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.#ctor">
            <summary>
            Creates a CrlClientOnline instance that will try to find
            a single CRL by walking through the certificate chain.
            </summary>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.#ctor(System.String[])">
            <summary>Creates a CrlClientOnline instance using one or more URLs.</summary>
            <param name="crls">the CRLs as Strings</param>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.#ctor(System.Uri[])">
            <summary>Creates a CrlClientOnline instance using one or more URLs.</summary>
            <param name="crls">the CRLs as URLs</param>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.#ctor(iText.Commons.Bouncycastle.Cert.IX509Certificate[])">
            <summary>Creates a CrlClientOnline instance using a certificate chain.</summary>
            <param name="chain">a certificate chain</param>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Fetches the CRL bytes from an URL.</summary>
            <remarks>
            Fetches the CRL bytes from an URL.
            If no url is passed as parameter, the url will be obtained from the certificate.
            If you want to load a CRL from a local file, subclass this method and pass an
            URL with the path to the local file to this method. An other option is to use
            the CrlClientOffline class.
            </remarks>
            <seealso cref="M:iText.Signatures.ICrlClient.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)"/>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.GetCrlResponse(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.Uri)">
            <summary>
            Get CRL response represented as
            <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="cert">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            certificate to get CRL response for
            </param>
            <param name="urlt">
            
            <see cref="T:System.Uri"/>
            link, which is expected to be used to get CRL response from
            </param>
            <returns>
            CRL response bytes, represented as
            <see cref="T:System.IO.Stream"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.AddUrl(System.String)">
            <summary>Adds an URL to the list of CRL URLs</summary>
            <param name="url">an URL in the form of a String</param>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.AddUrl(System.Uri)">
            <summary>Adds an URL to the list of CRL URLs</summary>
            <param name="url">an URL object</param>
        </member>
        <member name="M:iText.Signatures.CrlClientOnline.GetUrlsSize">
            <summary>Get an amount of URLs provided for this CRL.</summary>
            <returns>
            
            <c>int</c>
            number of URLs
            </returns>
        </member>
        <member name="T:iText.Signatures.CRLVerifier">
            <summary>
            Class that allows you to verify a certificate against
            one or more Certificate Revocation Lists.
            </summary>
        </member>
        <member name="F:iText.Signatures.CRLVerifier.LOGGER">
            <summary>The Logger instance</summary>
        </member>
        <member name="F:iText.Signatures.CRLVerifier.crls">
            <summary>The list of CRLs to check for revocation date.</summary>
        </member>
        <member name="M:iText.Signatures.CRLVerifier.#ctor(iText.Signatures.CertificateVerifier,System.Collections.Generic.IList{iText.Commons.Bouncycastle.Cert.IX509Crl})">
            <summary>Creates a CRLVerifier instance.</summary>
            <param name="verifier">the next verifier in the chain</param>
            <param name="crls">a list of CRLs</param>
        </member>
        <member name="M:iText.Signatures.CRLVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Verifies whether a valid CRL is found for the certificate.</summary>
            <remarks>
            Verifies whether a valid CRL is found for the certificate.
            If this method returns false, it doesn't mean the certificate isn't valid.
            It means we couldn't verify it against any CRL that was available.
            </remarks>
            <param name="signCert">the certificate that needs to be checked</param>
            <param name="issuerCert">its issuer</param>
            <returns>
            a list of <c>VerificationOK</c> objects.
            The list will be empty if the certificate couldn't be verified.
            </returns>
            <seealso cref="M:iText.Signatures.RootStoreVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)"/>
        </member>
        <member name="M:iText.Signatures.CRLVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Crl,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Verifies a certificate against a single CRL.</summary>
            <param name="crl">the Certificate Revocation List</param>
            <param name="signCert">a certificate that needs to be verified</param>
            <param name="issuerCert">its issuer</param>
            <param name="signDate">the sign date</param>
            <returns>true if the verification succeeded</returns>
        </member>
        <member name="M:iText.Signatures.CRLVerifier.GetCRL(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Fetches a CRL for a specific certificate online (without further checking).</summary>
            <param name="signCert">the certificate</param>
            <param name="issuerCert">its issuer left for backwards compatibility</param>
            <returns>an X509CRL object.</returns>
        </member>
        <member name="M:iText.Signatures.CRLVerifier.IsSignatureValid(iText.Commons.Bouncycastle.Cert.IX509Crl,iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Checks if a CRL verifies against the issuer certificate or a trusted anchor.</summary>
            <param name="crl">the CRL</param>
            <param name="crlIssuer">the trusted anchor</param>
            <returns>true if the CRL can be trusted</returns>
        </member>
        <member name="T:iText.Signatures.DefaultIssuingCertificateRetriever">
            <summary>
            Empty
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            implementation for compatibility with the older code.
            </summary>
        </member>
        <member name="M:iText.Signatures.DefaultIssuingCertificateRetriever.#ctor">
            <summary>
            Creates
            <see cref="T:iText.Signatures.DefaultIssuingCertificateRetriever"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Signatures.DefaultIssuingCertificateRetriever.RetrieveMissingCertificates(iText.Commons.Bouncycastle.Cert.IX509Certificate[])">
            <summary><inheritDoc/></summary>
            <param name="chain">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Signatures.DefaultIssuingCertificateRetriever.GetCrlIssuerCertificates(iText.Commons.Bouncycastle.Cert.IX509Crl)">
            <summary><inheritDoc/></summary>
            <param name="crl">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Signatures.DefaultIssuingCertificateRetriever.SetTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary><inheritDoc/></summary>
            <param name="certificates">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="T:iText.Signatures.DigestAlgorithms">
            <summary>Class that contains a map with the different message digest algorithms.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.SHA1">
            <summary>Algorithm available for signatures since PDF 1.3.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.SHA256">
            <summary>Algorithm available for signatures since PDF 1.6.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.SHA384">
            <summary>Algorithm available for signatures since PDF 1.7.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.SHA512">
            <summary>Algorithm available for signatures since PDF 1.7.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.RIPEMD160">
            <summary>Algorithm available for signatures since PDF 1.7.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.SHA3_256">
            <summary>
            Algorithm available for signatures since PDF 2.0
            extended by ISO/TS 32001.
            </summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.SHA3_512">
            <summary>
            Algorithm available for signatures since PDF 2.0
            extended by ISO/TS 32001.
            </summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.SHA3_384">
            <summary>
            Algorithm available for signatures since PDF 2.0
            extended by ISO/TS 32001.
            </summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.SHAKE256">
            <summary>
            Algorithm available for signatures since PDF 2.0
            extended by ISO/TS 32001.
            </summary>
            <remarks>
            Algorithm available for signatures since PDF 2.0
            extended by ISO/TS 32001.
            <para />
            The output length is fixed at 512 bits (64 bytes).
            </remarks>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.digestNames">
            <summary>Maps the digest IDs with the human-readable name of the digest algorithm.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.fixNames">
            <summary>Maps digest algorithm that are unknown by the JDKs MessageDigest object to a known one.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.allowedDigests">
            <summary>Maps the name of a digest algorithm with its ID.</summary>
        </member>
        <member name="F:iText.Signatures.DigestAlgorithms.bitLengths">
            <summary>Maps algorithm names to output lengths in bits.</summary>
        </member>
        <member name="M:iText.Signatures.DigestAlgorithms.GetMessageDigestFromOid(System.String)">
            <summary>Get a digest algorithm.</summary>
            <param name="digestOid">oid of the digest algorithm</param>
            <returns>MessageDigest object</returns>
        </member>
        <member name="M:iText.Signatures.DigestAlgorithms.GetMessageDigest(System.String)">
            <summary>Creates a MessageDigest object that can be used to create a hash.</summary>
            <param name="hashAlgorithm">the algorithm you want to use to create a hash</param>
            <returns>a MessageDigest object</returns>
        </member>
        <member name="M:iText.Signatures.DigestAlgorithms.Digest(System.IO.Stream,System.String)">
            <summary>Creates a hash using a specific digest algorithm and a provider.</summary>
            <param name="data">the message of which you want to create a hash</param>
            <param name="hashAlgorithm">the algorithm used to create the hash</param>
            <returns>the hash</returns>
        </member>
        <member name="M:iText.Signatures.DigestAlgorithms.Digest(System.IO.Stream,iText.Commons.Bouncycastle.Crypto.IDigest)">
            <summary>Create a digest based on the inputstream.</summary>
            <param name="data">data to be digested</param>
            <param name="messageDigest">algorithm to be used</param>
            <returns>digest of the data</returns>
        </member>
        <member name="M:iText.Signatures.DigestAlgorithms.Digest(System.IO.Stream,System.String,iText.Signatures.IExternalDigest)">
            <summary>Create a digest based on the inputstream.</summary>
            <param name="data">data to be digested</param>
            <param name="hashAlgorithm">algorithm to be used</param>
            <param name="externalDigest">external digest to be used</param>
            <returns>digest of the data</returns>
        </member>
        <member name="M:iText.Signatures.DigestAlgorithms.GetDigest(System.String)">
            <summary>Gets the digest name for a certain id.</summary>
            <param name="oid">an id (for instance "1.2.840.113549.2.5")</param>
            <returns>a digest name (for instance "MD5")</returns>
        </member>
        <member name="M:iText.Signatures.DigestAlgorithms.GetAllowedDigest(System.String)">
            <summary>
            Returns the id of a digest algorithms that is allowed in PDF,
            or null if it isn't allowed.
            </summary>
            <param name="name">The name of the digest algorithm.</param>
            <returns>An oid.</returns>
        </member>
        <member name="M:iText.Signatures.DigestAlgorithms.GetOutputBitLength(System.String)">
            <summary>Retrieve the output length in bits of the given digest algorithm.</summary>
            <param name="name">the name of the digest algorithm</param>
            <returns>the length of the output of the algorithm in bits</returns>
        </member>
        <member name="T:iText.Signatures.Exceptions.SignExceptionMessageConstant">
            <summary>Class that bundles all the error message templates as constants.</summary>
        </member>
        <member name="T:iText.Signatures.ExternalBlankSignatureContainer">
            <summary>Produces a blank (or empty) signature.</summary>
            <remarks>
            Produces a blank (or empty) signature. Useful for deferred signing with
            MakeSignature.signExternalContainer().
            </remarks>
        </member>
        <member name="M:iText.Signatures.ExternalBlankSignatureContainer.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Creates an ExternalBlankSignatureContainer.</summary>
            <param name="sigDic">PdfDictionary containing signature iformation. /SubFilter and /Filter aren't set in this constructor.
                </param>
        </member>
        <member name="M:iText.Signatures.ExternalBlankSignatureContainer.#ctor(iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.PdfName)">
            <summary>Creates an ExternalBlankSignatureContainer.</summary>
            <remarks>
            Creates an ExternalBlankSignatureContainer. This constructor will create the PdfDictionary for the
            signature information and will insert the  /Filter and /SubFilter values into this dictionary.
            </remarks>
            <param name="filter">PdfName of the signature handler to use when validating this signature</param>
            <param name="subFilter">PdfName that describes the encoding of the signature</param>
        </member>
        <member name="T:iText.Signatures.IApplicableSignatureParams">
            <summary>
            Extension interface of
            <see cref="T:iText.Signatures.ISignatureMechanismParams"/>
            that also supports applying the parameters to
            a
            <see cref="T:iText.Commons.Bouncycastle.Crypto.ISigner"/>.
            </summary>
        </member>
        <member name="M:iText.Signatures.IApplicableSignatureParams.Apply(iText.Commons.Bouncycastle.Crypto.ISigner)">
            <summary>
            Apply the parameters to a
            <see cref="T:iText.Commons.Bouncycastle.Crypto.ISigner"/>.
            </summary>
            <param name="signature">
            an uninitialised
            <see cref="T:iText.Commons.Bouncycastle.Crypto.ISigner"/>
            object
            </param>
        </member>
        <member name="T:iText.Signatures.ICrlClient">
            <summary>
            Interface that needs to be implemented if you want to embed
            Certificate Revocation Lists (CRL) into your PDF.
            </summary>
        </member>
        <member name="M:iText.Signatures.ICrlClient.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Gets an encoded byte array.</summary>
            <param name="checkCert">The certificate which a CRL URL can be obtained from.</param>
            <param name="url">A CRL url if you don't want to obtain it from the certificate.</param>
            <returns>A collection of byte array each representing a crl. It may return null or an empty collection.</returns>
        </member>
        <member name="T:iText.Signatures.IExternalDigest">
            <summary>
            ExternalDigest allows the use of implementations of
            <see cref="T:iText.Commons.Digest.IMessageDigest"/>
            other than
            <see cref="T:iText.Signatures.BouncyCastleDigest"/>.
            </summary>
        </member>
        <member name="M:iText.Signatures.IExternalDigest.GetMessageDigest(System.String)">
            <summary>Returns the MessageDigest associated with the provided hashing algorithm.</summary>
            <param name="hashAlgorithm">String value representing the hashing algorithm</param>
            <returns>MessageDigest                MessageDigest object</returns>
        </member>
        <member name="T:iText.Signatures.IExternalSignature">
            <summary>Interface that needs to be implemented to do the actual signing.</summary>
            <remarks>
            Interface that needs to be implemented to do the actual signing.
            For instance: you'll have to implement this interface if you want
            to sign a PDF using a smart card.
            </remarks>
        </member>
        <member name="M:iText.Signatures.IExternalSignature.GetDigestAlgorithmName">
            <summary>Returns the digest algorithm.</summary>
            <returns>The digest algorithm (e.g. "SHA-1", "SHA-256,...").</returns>
        </member>
        <member name="M:iText.Signatures.IExternalSignature.GetSignatureAlgorithmName">
            <summary>Returns the signature algorithm used for signing, disregarding the digest function.</summary>
            <returns>The signature algorithm ("RSA", "DSA", "ECDSA", "Ed25519" or "Ed448").</returns>
        </member>
        <member name="M:iText.Signatures.IExternalSignature.GetSignatureMechanismParameters">
            <summary>Return the algorithm parameters that need to be encoded together with the signature mechanism identifier.
                </summary>
            <remarks>
            Return the algorithm parameters that need to be encoded together with the signature mechanism identifier.
            If there are no parameters, return `null`.
            A non-null value is required for RSASSA-PSS; see
            <see cref="T:iText.Signatures.RSASSAPSSMechanismParams"/>.
            </remarks>
            <returns>algorithm parameters</returns>
        </member>
        <member name="M:iText.Signatures.IExternalSignature.Sign(System.Byte[])">
            <summary>
            Signs the given message using the encryption algorithm in combination
            with the hash algorithm.
            </summary>
            <param name="message">The message you want to be hashed and signed.</param>
            <returns>A signed message digest.</returns>
        </member>
        <member name="T:iText.Signatures.IExternalSignatureContainer">
            <summary>Interface to sign a document.</summary>
            <remarks>Interface to sign a document. The signing is fully done externally, including the container composition.
                </remarks>
        </member>
        <member name="M:iText.Signatures.IExternalSignatureContainer.Sign(System.IO.Stream)">
            <summary>Produces the container with the signature.</summary>
            <param name="data">the data to sign</param>
            <returns>a container with the signature and other objects, like CRL and OCSP. The container will generally be a PKCS7 one.
                </returns>
        </member>
        <member name="M:iText.Signatures.IExternalSignatureContainer.ModifySigningDictionary(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Modifies the signature dictionary to suit the container.</summary>
            <remarks>
            Modifies the signature dictionary to suit the container. At least the keys
            <see cref="F:iText.Kernel.Pdf.PdfName.Filter"/>
            and
            <see cref="F:iText.Kernel.Pdf.PdfName.SubFilter"/>
            will have to be set.
            </remarks>
            <param name="signDic">the signature dictionary</param>
        </member>
        <member name="T:iText.Signatures.IIssuingCertificateRetriever">
            <summary>
            Interface helper to support retrieving CAIssuers certificates from Authority Information Access (AIA) Extension in
            order to support certificate chains with missing certificates and getting CRL response issuer certificates.
            </summary>
        </member>
        <member name="M:iText.Signatures.IIssuingCertificateRetriever.RetrieveMissingCertificates(iText.Commons.Bouncycastle.Cert.IX509Certificate[])">
            <summary>Retrieves missing certificates in chain using certificate Authority Information Access (AIA) Extension.
                </summary>
            <param name="chain">certificate chain to restore with at least signing certificate.</param>
            <returns>
            full chain of trust or maximum chain that could be restored in case missing certificates cannot be
            retrieved from AIA extension.
            </returns>
        </member>
        <member name="M:iText.Signatures.IIssuingCertificateRetriever.GetCrlIssuerCertificates(iText.Commons.Bouncycastle.Cert.IX509Crl)">
            <summary>
            Retrieves certificates that can be used to verify the signature on the CRL response using CRL
            Authority Information Access (AIA) Extension.
            </summary>
            <param name="crl">CRL response to retrieve issuer for.</param>
            <returns>certificates retrieved from CRL AIA extension or an empty list in case certificates cannot be retrieved.
                </returns>
        </member>
        <member name="M:iText.Signatures.IIssuingCertificateRetriever.SetTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Sets trusted certificate list to be used for the missing certificates retrieving by the issuer name.
                </summary>
            <param name="certificates">
            certificate list for getting missing certificates in chain
            or CRL response issuer certificates.
            </param>
        </member>
        <member name="T:iText.Signatures.IOcspClient">
            <summary>Interface for the Online Certificate Status Protocol (OCSP) Client.</summary>
        </member>
        <member name="M:iText.Signatures.IOcspClient.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Fetch a DER-encoded BasicOCSPResponse from an OCSP responder.</summary>
            <remarks>
            Fetch a DER-encoded BasicOCSPResponse from an OCSP responder. The method should not throw
            an exception.
            <para />
            Note: do not pass in the full DER-encoded OCSPResponse object obtained from the responder,
            only the DER-encoded BasicOCSPResponse value contained in the response data.
            </remarks>
            <param name="checkCert">Certificate to check.</param>
            <param name="issuerCert">The parent certificate.</param>
            <param name="url">
            The URL of the OCSP responder endpoint. If null, implementations can
            attempt to obtain a URL from the AuthorityInformationAccess extension of
            the certificate, or from another implementation-specific source.
            </param>
            <returns>
            a byte array containing a DER-encoded BasicOCSPResponse structure or null if one
            could not be obtained
            </returns>
            <seealso><a href="https://datatracker.ietf.org/doc/html/rfc6960#section-4.2.1">RFC 6960 § 4.2.1</a></seealso>
        </member>
        <member name="T:iText.Signatures.ISignatureMechanismParams">
            <summary>Interface to encode the parameters to a signature algorithm for inclusion in a signature object.</summary>
            <remarks>
            Interface to encode the parameters to a signature algorithm for inclusion in a signature object.
            See
            <see cref="T:iText.Signatures.RSASSAPSSMechanismParams"/>
            for an example.
            </remarks>
        </member>
        <member name="M:iText.Signatures.ISignatureMechanismParams.ToEncodable">
            <summary>
            Represent the parameters as an
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable"/>
            for inclusion in a signature object.
            </summary>
            <returns>
            an
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable"/>
            object
            </returns>
        </member>
        <member name="T:iText.Signatures.IssuingCertificateRetriever">
            <summary>
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            default implementation.
            </summary>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.#ctor">
            <summary>
            Creates
            <see cref="T:iText.Signatures.IssuingCertificateRetriever"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.RetrieveMissingCertificates(iText.Commons.Bouncycastle.Cert.IX509Certificate[])">
            <summary><inheritDoc/></summary>
            <param name="chain">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.RetrieveIssuerCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Retrieve issuer certificate for the provided certificate.</summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            for which issuer certificate shall be retrieved
            </param>
            <returns>
            issuer certificate.
            <see langword="null"/>
            if there is no issuer certificate, or it cannot be retrieved.
            </returns>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.RetrieveOCSPResponderCertificate(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse)">
            <summary>
            Retrieves OCSP responder certificate either from the response certs or
            trusted store in case responder certificate isn't found in /Certs.
            </summary>
            <param name="ocspResp">basic OCSP response to get responder certificate for</param>
            <returns>retrieved OCSP responder certificate or null in case it wasn't found.</returns>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.GetCrlIssuerCertificates(iText.Commons.Bouncycastle.Cert.IX509Crl)">
            <summary><inheritDoc/></summary>
            <param name="crl">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.SetTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Sets trusted certificate list to be used as certificates trusted for any possible usage.</summary>
            <remarks>
            Sets trusted certificate list to be used as certificates trusted for any possible usage.
            In case more specific trusted is desired to be configured
            <see cref="M:iText.Signatures.IssuingCertificateRetriever.GetTrustedCertificatesStore"/>
            method is expected to be used.
            </remarks>
            <param name="certificates">certificate list to be used as certificates trusted for any possible usage.</param>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.AddTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Add trusted certificates collection to trusted certificates storage.</summary>
            <param name="certificates">
            certificates
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            to be added
            </param>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.AddKnownCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Add certificates collection to known certificates storage, which is used for issuer certificates retrieval.
                </summary>
            <param name="certificates">
            certificates
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            to be added
            </param>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.GetTrustedCertificatesStore">
            <summary>
            Gets
            <see cref="T:iText.Signatures.Validation.V1.TrustedCertificatesStore"/>
            to be used to provide more complex trusted certificates configuration.
            </summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.TrustedCertificatesStore"/>
            storage
            </returns>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.IsCertificateTrusted(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if provided certificate is present in trusted certificates storage.</summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to be checked
            </param>
            <returns>
            
            <see langword="true"/>
            if certificate is present in trusted certificates storage,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.GetIssuerCertByURI(System.String)">
            <summary>
            Get CA issuers certificates represented as
            <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="uri">
            
            <see cref="T:System.Uri"/>
            URI, which is expected to be used to get issuer certificates from. Usually
            CA Issuers value from Authority Information Access (AIA) certificate extension.
            </param>
            <returns>
            CA issuer certificate (or chain) bytes, represented as
            <see cref="T:System.IO.Stream"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.IssuingCertificateRetriever.ParseCertificates(System.IO.Stream)">
            <summary>Parses certificates represented as byte array.</summary>
            <param name="certsData">stream which contains one or more X509 certificates.</param>
            <returns>a (possibly empty) collection of the certificates read from the given byte array.</returns>
        </member>
        <member name="T:iText.Signatures.ITSAClient">
            <summary>Time Stamp Authority client (caller) interface.</summary>
            <remarks>
            Time Stamp Authority client (caller) interface.
            <para />
            Interface used by the PdfPKCS7 digital signature builder to call
            Time Stamp Authority providing RFC 3161 compliant time stamp token.
            </remarks>
        </member>
        <member name="M:iText.Signatures.ITSAClient.GetTokenSizeEstimate">
            <summary>Get the time stamp estimated token size.</summary>
            <remarks>
            Get the time stamp estimated token size.
            Implementation must return value large enough to accommodate the
            entire token returned by
            <see cref="M:iText.Signatures.ITSAClient.GetTimeStampToken(System.Byte[])"/>
            prior
            to actual
            <see cref="M:iText.Signatures.ITSAClient.GetTimeStampToken(System.Byte[])"/>
            call.
            </remarks>
            <returns>an estimate of the token size</returns>
        </member>
        <member name="M:iText.Signatures.ITSAClient.GetMessageDigest">
            <summary>
            Returns the
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IDigest"/>
            to digest the data imprint
            </summary>
            <returns>
            The
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IDigest"/>
            object.
            </returns>
        </member>
        <member name="M:iText.Signatures.ITSAClient.GetTimeStampToken(System.Byte[])">
            <summary>Returns RFC 3161 timeStampToken.</summary>
            <param name="imprint">byte[] - data imprint to be time-stamped</param>
            <returns>byte[] - encoded, TSA signed data of the timeStampToken</returns>
        </member>
        <member name="T:iText.Signatures.ITSAInfoBouncyCastle">
            <summary>
            Interface you can implement and pass to TSAClientBouncyCastle in case
            you want to do something with the information returned
            </summary>
        </member>
        <member name="M:iText.Signatures.ITSAInfoBouncyCastle.InspectTimeStampTokenInfo(iText.Commons.Bouncycastle.Tsp.ITimeStampTokenInfo)">
            <summary>
            When a timestamp is created using TSAClientBouncyCastle,
            this method is triggered passing an object that contains
            info about the timestamp and the time stamping authority.
            </summary>
            <param name="info">a ITimeStampTokenInfo object</param>
        </member>
        <member name="T:iText.Signatures.Logs.SignLogMessageConstant">
            <summary>Class which contains constants to be used in logging inside sign module.</summary>
        </member>
        <member name="T:iText.Signatures.LtvVerification">
            <summary>Add verification according to PAdES-LTV (part 4).</summary>
        </member>
        <member name="T:iText.Signatures.LtvVerification.Level">
            <summary>What type of verification to include.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.Level.OCSP">
            <summary>Include only OCSP.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.Level.CRL">
            <summary>Include only CRL.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.Level.OCSP_CRL">
            <summary>Include both OCSP and CRL.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.Level.OCSP_OPTIONAL_CRL">
            <summary>Include CRL only if OCSP can't be read.</summary>
        </member>
        <member name="T:iText.Signatures.LtvVerification.CertificateOption">
            <summary>Options for how many certificates to include.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.CertificateOption.SIGNING_CERTIFICATE">
            <summary>Include verification just for the signing certificate.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.CertificateOption.WHOLE_CHAIN">
            <summary>Include verification for the whole chain of certificates.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.CertificateOption.ALL_CERTIFICATES">
            <summary>
            Include verification for the whole certificates chain, certificates used to create OCSP responses,
            CRL response certificates and timestamp certificates included in the signatures.
            </summary>
        </member>
        <member name="T:iText.Signatures.LtvVerification.CertificateInclusion">
            <summary>
            Certificate inclusion in the DSS and VRI dictionaries in the CERT and CERTS
            keys.
            </summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.CertificateInclusion.YES">
            <summary>Include certificates in the DSS and VRI dictionaries.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.CertificateInclusion.NO">
            <summary>Do not include certificates in the DSS and VRI dictionaries.</summary>
        </member>
        <member name="T:iText.Signatures.LtvVerification.RevocationDataNecessity">
            <summary>Option to determine whether revocation information is required for the signing certificate.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.RevocationDataNecessity.REQUIRED_FOR_SIGNING_CERTIFICATE">
            <summary>Require revocation information for the signing certificate.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerification.RevocationDataNecessity.OPTIONAL">
            <summary>Revocation data for the signing certificate may be optional.</summary>
        </member>
        <member name="M:iText.Signatures.LtvVerification.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>The verification constructor.</summary>
            <remarks>
            The verification constructor. This class should only be created with
            PdfStamper.getLtvVerification() otherwise the information will not be
            added to the Pdf.
            </remarks>
            <param name="document">
            The
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            to apply the validation to.
            </param>
        </member>
        <member name="M:iText.Signatures.LtvVerification.SetRevocationDataNecessity(iText.Signatures.LtvVerification.RevocationDataNecessity)">
            <summary>
            Sets
            <see cref="T:iText.Signatures.LtvVerification.RevocationDataNecessity"/>
            option to specify the necessity of revocation data.
            </summary>
            <remarks>
            Sets
            <see cref="T:iText.Signatures.LtvVerification.RevocationDataNecessity"/>
            option to specify the necessity of revocation data.
            <para />
            Default value is
            <see cref="F:iText.Signatures.LtvVerification.RevocationDataNecessity.OPTIONAL"/>.
            </remarks>
            <param name="revocationDataNecessity">
            
            <see cref="T:iText.Signatures.LtvVerification.RevocationDataNecessity"/>
            value to set
            </param>
            <returns>
            this
            <see cref="T:iText.Signatures.LtvVerification"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.LtvVerification.SetIssuingCertificateRetriever(iText.Signatures.IIssuingCertificateRetriever)">
            <summary>
            Sets
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            instance needed to get CRL issuer certificates (using AIA extension).
            </summary>
            <remarks>
            Sets
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            instance needed to get CRL issuer certificates (using AIA extension).
            <para />
            Default value is
            <see cref="T:iText.Signatures.DefaultIssuingCertificateRetriever"/>.
            </remarks>
            <param name="issuingCertificateRetriever">
            
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            instance to set
            </param>
            <returns>
            this
            <see cref="T:iText.Signatures.LtvVerification"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.LtvVerification.AddVerification(System.String,iText.Signatures.IOcspClient,iText.Signatures.ICrlClient,iText.Signatures.LtvVerification.CertificateOption,iText.Signatures.LtvVerification.Level,iText.Signatures.LtvVerification.CertificateInclusion)">
            <summary>Add verification for a particular signature.</summary>
            <param name="signatureName">the signature to validate (it may be a timestamp)</param>
            <param name="ocsp">the interface to get the OCSP</param>
            <param name="crl">the interface to get the CRL</param>
            <param name="certOption">options as to how many certificates to include</param>
            <param name="level">the validation options to include</param>
            <param name="certInclude">certificate inclusion options</param>
            <returns>true if a validation was generated, false otherwise</returns>
        </member>
        <member name="M:iText.Signatures.LtvVerification.AddVerification(System.String,System.Collections.Generic.ICollection{System.Byte[]},System.Collections.Generic.ICollection{System.Byte[]},System.Collections.Generic.ICollection{System.Byte[]})">
            <summary>Adds verification to the signature.</summary>
            <param name="signatureName">name of the signature</param>
            <param name="ocsps">collection of DER-encoded BasicOCSPResponses</param>
            <param name="crls">collection of DER-encoded CRLs</param>
            <param name="certs">collection of DER-encoded certificates</param>
            <returns>boolean</returns>
        </member>
        <member name="M:iText.Signatures.LtvVerification.Merge">
            <summary>Merges the validation with any validation already in the document or creates a new one.</summary>
        </member>
        <member name="M:iText.Signatures.LtvVerification.ConvertToHex(System.Byte[])">
            <summary>Converts an array of bytes to a String of hexadecimal values</summary>
            <param name="bytes">a byte array</param>
            <returns>the same bytes expressed as hexadecimal values</returns>
        </member>
        <member name="M:iText.Signatures.LtvVerification.GetParent(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate[])">
            <summary>Get the issuing certificate for a child certificate.</summary>
            <param name="cert">the certificate for which we search the parent</param>
            <param name="certs">an array with certificates that contains the parent</param>
            <returns>the parent certificate</returns>
        </member>
        <member name="T:iText.Signatures.LtvVerifier">
            <summary>Verifies the signatures in an LTV document.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.LOGGER">
            <summary>The Logger instance</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.option">
            <summary>Option to specify level of verification; signing certificate only or the entire chain.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.verifyRootCertificate">
            <summary>Verify root.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.document">
            <summary>A document object for the revision that is being verified.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.acroForm">
            <summary>The fields in the revision that is being verified.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.signDate">
            <summary>The date the revision was signed, or <c>null</c> for the highest revision.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.signatureName">
            <summary>The signature that covers the revision.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.pkcs7">
            <summary>The PdfPKCS7 object for the signature.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.latestRevision">
            <summary>Indicates if we're working with the latest revision.</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.dss">
            <summary>The document security store for the revision that is being verified</summary>
        </member>
        <member name="F:iText.Signatures.LtvVerifier.metaInfo">
            <summary>The meta info</summary>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>Creates a VerificationData object for a PdfReader</summary>
            <param name="document">The document we want to verify.</param>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.SetVerifier(iText.Signatures.CertificateVerifier)">
            <summary>Sets an extra verifier.</summary>
            <param name="verifier">the verifier to set</param>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.SetCertificateOption(iText.Signatures.LtvVerification.CertificateOption)">
            <summary>Sets the certificate option.</summary>
            <param name="option">Either CertificateOption.SIGNING_CERTIFICATE (default) or CertificateOption.WHOLE_CHAIN
                </param>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.SetVerifyRootCertificate(System.Boolean)">
            <summary>Set the verifyRootCertificate to false if you can't verify the root certificate.</summary>
            <param name="verifyRootCertificate">false if you can't verify the root certificate, otherwise true</param>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.SetEventCountingMetaInfo(iText.Commons.Actions.Contexts.IMetaInfo)">
            <summary>
            Sets the
            <see cref="T:iText.Commons.Actions.Contexts.IMetaInfo"/>
            that will be used during
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            creation.
            </summary>
            <param name="metaInfo">meta info to set</param>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.Verify(System.Collections.Generic.IList{iText.Signatures.VerificationOK})">
            <summary>Verifies all the document-level timestamps and all the signatures in the document.</summary>
            <param name="result">
            a list of
            <see cref="T:iText.Signatures.VerificationOK"/>
            objects
            </param>
            <returns>
            a list of all
            <see cref="T:iText.Signatures.VerificationOK"/>
            objects after verification
            </returns>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.VerifySignature">
            <summary>Verifies a document level timestamp.</summary>
            <returns>
            a list of
            <see cref="T:iText.Signatures.VerificationOK"/>
            objects
            </returns>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.VerifyChain(iText.Commons.Bouncycastle.Cert.IX509Certificate[])">
            <summary>
            Checks the certificates in a certificate chain:
            are they valid on a specific date, and
            do they chain up correctly?
            </summary>
            <param name="chain">the certificate chain</param>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Verifies certificates against a list of CRLs and OCSP responses.</summary>
            <param name="signCert">the signing certificate</param>
            <param name="issuerCert">the issuer's certificate</param>
            <returns>
            a list of <c>VerificationOK</c> objects.
            The list will be empty if the certificate couldn't be verified.
            </returns>
            <seealso cref="M:iText.Signatures.RootStoreVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)"/>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.SwitchToPreviousRevision">
            <summary>Switches to the previous revision.</summary>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.GetCRLsFromDSS">
            <summary>Gets a list of X509CRL objects from a Document Security Store.</summary>
            <returns>a list of CRLs</returns>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.GetOCSPResponsesFromDSS">
            <summary>Gets OCSP responses from the Document Security Store.</summary>
            <returns>a list of IBasicOCSPResp objects</returns>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.InitLtvVerifier(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Initialize
            <see cref="T:iText.Signatures.LtvVerifier"/>
            object by using provided document.
            </summary>
            <remarks>
            Initialize
            <see cref="T:iText.Signatures.LtvVerifier"/>
            object by using provided document.
            This method reads all the existing signatures and mathematically validates the last one.
            </remarks>
            <param name="document">
            
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to be verified
            </param>
        </member>
        <member name="M:iText.Signatures.LtvVerifier.CoversWholeDocument">
            <summary>
            Checks if the signature covers the whole document
            and throws an exception if the document was altered
            </summary>
            <returns>a PdfPKCS7 object</returns>
        </member>
        <member name="T:iText.Signatures.OcspClientBouncyCastle">
            <summary>OcspClient implementation using BouncyCastle.</summary>
        </member>
        <member name="F:iText.Signatures.OcspClientBouncyCastle.LOGGER">
            <summary>The Logger instance.</summary>
        </member>
        <member name="M:iText.Signatures.OcspClientBouncyCastle.#ctor(iText.Signatures.OCSPVerifier)">
            <summary>
            Creates
            <c>OcspClient</c>.
            </summary>
            <param name="verifier">will be used for response verification.</param>
        </member>
        <member name="M:iText.Signatures.OcspClientBouncyCastle.#ctor">
            <summary>
            Creates new
            <see cref="T:iText.Signatures.OcspClientBouncyCastle"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Signatures.OcspClientBouncyCastle.GetBasicOCSPResp(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Gets OCSP response.</summary>
            <remarks>
            Gets OCSP response. If
            <see cref="T:iText.Signatures.OCSPVerifier"/>
            was set, the response will be checked.
            </remarks>
            <param name="checkCert">the certificate to check</param>
            <param name="rootCert">parent certificate</param>
            <param name="url">to get the verification</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            an OCSP response wrapper
            </returns>
        </member>
        <member name="M:iText.Signatures.OcspClientBouncyCastle.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Signatures.OcspClientBouncyCastle.GenerateOCSPRequest(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary>Generates an OCSP request using BouncyCastle.</summary>
            <param name="issuerCert">certificate of the issues</param>
            <param name="serialNumber">serial number</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspRequest"/>
            an OCSP request wrapper
            </returns>
        </member>
        <member name="M:iText.Signatures.OcspClientBouncyCastle.GetCertificateStatus(System.Byte[])">
            <summary>Retrieves certificate status from the OCSP response.</summary>
            <param name="basicOcspRespBytes">encoded basic OCSP response</param>
            <returns>good, revoked or unknown certificate status retrieved from the OCSP response, or null if an error occurs.
                </returns>
        </member>
        <member name="M:iText.Signatures.OcspClientBouncyCastle.GetOcspResponse(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Gets an OCSP response object using BouncyCastle.</summary>
            <param name="checkCert">to certificate to check</param>
            <param name="rootCert">the parent certificate</param>
            <param name="url">
            to get the verification. If it's null it will be taken
            from the check cert or from other implementation specific source
            </param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponse"/>
            an OCSP response wrapper
            </returns>
        </member>
        <member name="M:iText.Signatures.OcspClientBouncyCastle.CreateRequestAndResponse(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>
            Create OCSP request and get the response for this request, represented as
            <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="checkCert">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            certificate to get OCSP response for
            </param>
            <param name="rootCert">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            root certificate from which OCSP request will be built
            </param>
            <param name="url">
            
            <see cref="T:System.Uri"/>
            link, which is expected to be used to get OCSP response from
            </param>
            <returns>
            OCSP response bytes, represented as
            <see cref="T:System.IO.Stream"/>
            </returns>
        </member>
        <member name="T:iText.Signatures.OCSPVerifier">
            <summary>
            Class that allows you to verify a certificate against
            one or more OCSP responses.
            </summary>
        </member>
        <member name="F:iText.Signatures.OCSPVerifier.LOGGER">
            <summary>The Logger instance</summary>
        </member>
        <member name="F:iText.Signatures.OCSPVerifier.ocsps">
            <summary>
            The list of
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            OCSP response wrappers.
            </summary>
        </member>
        <member name="F:iText.Signatures.OCSPVerifier.ocspClient">
            <summary>Ocsp client to check OCSP Authorized Responder's revocation data.</summary>
        </member>
        <member name="F:iText.Signatures.OCSPVerifier.crlClient">
            <summary>Ocsp client to check OCSP Authorized Responder's revocation data.</summary>
        </member>
        <member name="M:iText.Signatures.OCSPVerifier.#ctor(iText.Signatures.CertificateVerifier,System.Collections.Generic.IList{iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse})">
            <summary>Creates an OCSPVerifier instance.</summary>
            <param name="verifier">the next verifier in the chain</param>
            <param name="ocsps">
            a list of
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            OCSP response wrappers for the certificate verification
            </param>
        </member>
        <member name="M:iText.Signatures.OCSPVerifier.SetOcspClient(iText.Signatures.IOcspClient)">
            <summary>
            Sets OCSP client to provide OCSP responses for verifying of the OCSP signer's certificate (an Authorized
            Responder).
            </summary>
            <remarks>
            Sets OCSP client to provide OCSP responses for verifying of the OCSP signer's certificate (an Authorized
            Responder). Also, should be used in case responder's certificate doesn't have any method of revocation checking.
            <para />
            See RFC6960 4.2.2.2.1. Revocation Checking of an Authorized Responder.
            <para />
            Optional. Default one is
            <see cref="T:iText.Signatures.OcspClientBouncyCastle"/>.
            </remarks>
            <param name="ocspClient">
            
            <see cref="T:iText.Signatures.IOcspClient"/>
            to provide an Authorized Responder revocation data.
            </param>
        </member>
        <member name="M:iText.Signatures.OCSPVerifier.SetCrlClient(iText.Signatures.ICrlClient)">
            <summary>
            Sets CRL client to provide CRL responses for verifying of the OCSP signer's certificate (an Authorized Responder)
            that also should be used in case responder's certificate doesn't have any method of revocation checking.
            </summary>
            <remarks>
            Sets CRL client to provide CRL responses for verifying of the OCSP signer's certificate (an Authorized Responder)
            that also should be used in case responder's certificate doesn't have any method of revocation checking.
            <para />
            See RFC6960 4.2.2.2.1. Revocation Checking of an Authorized Responder.
            <para />
            Optional. Default one is
            <see cref="T:iText.Signatures.CrlClientOnline"/>.
            </remarks>
            <param name="crlClient">
            
            <see cref="T:iText.Signatures.ICrlClient"/>
            to provide an Authorized Responder revocation data.
            </param>
        </member>
        <member name="M:iText.Signatures.OCSPVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Verifies if a valid OCSP response is found for the certificate.</summary>
            <remarks>
            Verifies if a valid OCSP response is found for the certificate.
            If this method returns false, it doesn't mean the certificate isn't valid.
            It means we couldn't verify it against any OCSP response that was available.
            </remarks>
            <param name="signCert">the certificate that needs to be checked</param>
            <param name="issuerCert">issuer of the certificate to be checked</param>
            <param name="signDate">the date the certificate needs to be valid</param>
            <returns>
            a list of <c>VerificationOK</c> objects.
            The list will be empty if the certificate couldn't be verified.
            </returns>
            <seealso cref="M:iText.Signatures.RootStoreVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)"/>
        </member>
        <member name="M:iText.Signatures.OCSPVerifier.Verify(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Verifies a certificate against a single OCSP response.</summary>
            <param name="ocspResp">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            the OCSP response wrapper for a certificate verification
            </param>
            <param name="signCert">the certificate that needs to be checked</param>
            <param name="issuerCert">
            the certificate that issued signCert – immediate parent. This certificate is considered
            trusted and valid by this method.
            </param>
            <param name="signDate">sign date (or the date the certificate needs to be valid)</param>
            <returns>
            
            <see langword="true"/>
            in case check is successful, false otherwise.
            </returns>
        </member>
        <member name="M:iText.Signatures.OCSPVerifier.IsValidResponse(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Verifies if an OCSP response is genuine.</summary>
            <remarks>
            Verifies if an OCSP response is genuine.
            If it doesn't verify against the issuer certificate and response's certificates, it may verify
            using a trusted anchor or cert.
            </remarks>
            <param name="ocspResp">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            the OCSP response wrapper
            </param>
            <param name="issuerCert">the issuer certificate. This certificate is considered trusted and valid by this method.
                </param>
            <param name="signDate">sign date for backwards compatibility</param>
        </member>
        <member name="M:iText.Signatures.OCSPVerifier.IsSignatureValid(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Checks if an OCSP response is genuine.</summary>
            <param name="ocspResp">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            the OCSP response wrapper
            </param>
            <param name="responderCert">the responder certificate</param>
            <returns>true if the OCSP response verifies against the responder certificate.</returns>
        </member>
        <member name="M:iText.Signatures.OCSPVerifier.GetOcspResponse(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Gets an OCSP response online and returns it without further checking.</summary>
            <param name="signCert">the signing certificate</param>
            <param name="issuerCert">the issuer certificate</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            an OCSP response wrapper.
            </returns>
        </member>
        <member name="T:iText.Signatures.OID">
            <summary>Class containing all the OID values used by iText.</summary>
        </member>
        <member name="T:iText.Signatures.OID.X509Extensions">
            <summary>Contains all OIDs used by iText in the context of Certificate Extensions.</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.AUTHORITY_KEY_IDENTIFIER">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
            <remarks>
            One of the standard extensions from https://tools.ietf.org/html/rfc5280
            <para />
            "Conforming CAs MUST mark this extension as non-critical."
            </remarks>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.SUBJECT_KEY_IDENTIFIER">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
            <remarks>
            One of the standard extensions from https://tools.ietf.org/html/rfc5280
            <para />
            "Conforming CAs MUST mark this extension as non-critical."
            </remarks>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.KEY_USAGE">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.CERTIFICATE_POLICIES">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.POLICY_MAPPINGS">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.SUBJECT_ALTERNATIVE_NAME">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.ISSUER_ALTERNATIVE_NAME">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.SUBJECT_DIRECTORY_ATTRIBUTES">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
            <remarks>
            One of the standard extensions from https://tools.ietf.org/html/rfc5280
            <para />
            "Conforming CAs MUST mark this extension as non-critical."
            </remarks>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.BASIC_CONSTRAINTS">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.NAME_CONSTRAINTS">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.POLICY_CONSTRAINTS">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.EXTENDED_KEY_USAGE">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.CRL_DISTRIBUTION_POINTS">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.INHIBIT_ANY_POLICY">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.FRESHEST_CRL">
            <summary>One of the standard extensions from https://tools.ietf.org/html/rfc5280</summary>
            <remarks>
            One of the standard extensions from https://tools.ietf.org/html/rfc5280
            <para />
            "The extension MUST be marked as non-critical by conforming CAs."
            </remarks>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.AUTHORITY_INFO_ACCESS">
            <summary>One of the Internet Certificate Extensions also from https://tools.ietf.org/html/rfc5280</summary>
            <remarks>
            One of the Internet Certificate Extensions also from https://tools.ietf.org/html/rfc5280
            <para />
            "The extension MUST be marked as non-critical by conforming CAs."
            </remarks>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.SUBJECT_INFO_ACCESS">
            <summary>One of the Internet Certificate Extensions also from https://tools.ietf.org/html/rfc5280</summary>
            <remarks>
            One of the Internet Certificate Extensions also from https://tools.ietf.org/html/rfc5280
            <para />
            "Conforming CAs MUST mark this extension as non-critical."
            </remarks>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.ID_KP_TIMESTAMPING">
            <summary>
            One of the
            <see cref="F:iText.Signatures.OID.X509Extensions.EXTENDED_KEY_USAGE"/>
            purposes from https://www.ietf.org/rfc/rfc2459.txt
            </summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.ID_PKIX_OCSP_NOCHECK">
            <summary>
            Extension for OCSP responder certificate
            from https://www.ietf.org/rfc/rfc2560.txt.
            </summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.VALIDITY_ASSURED_SHORT_TERM">
            <summary>Extension for certificates from ETSI EN 319 412-1 V1.4.4.</summary>
        </member>
        <member name="F:iText.Signatures.OID.X509Extensions.SUPPORTED_CRITICAL_EXTENSIONS">
            <summary>According to https://tools.ietf.org/html/rfc5280 4.2.</summary>
            <remarks>
            According to https://tools.ietf.org/html/rfc5280 4.2. "Certificate Extensions":
            "A certificate-using system MUST reject the certificate if it encounters a critical extension it
            does not recognize or a critical extension that contains information that it cannot process."
            <para />
            This set consists of standard extensions which are defined in RFC specifications and are not mentioned
            as forbidden to be marked as critical.
            </remarks>
        </member>
        <member name="T:iText.Signatures.PadesTwoPhaseSigningHelper">
            <summary>Helper class to perform signing operation in two steps.</summary>
            <remarks>
            Helper class to perform signing operation in two steps.
            <para />
            Firstly
            <see cref="M:iText.Signatures.PadesTwoPhaseSigningHelper.CreateCMSContainerWithoutSignature(iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.String,iText.Kernel.Pdf.PdfReader,System.IO.Stream,iText.Signatures.SignerProperties)"/>
            prepares document and placeholder
            for future signature without actual signing process.
            <para />
            Secondly follow-up step signs prepared document with corresponding PAdES Baseline profile.
            </remarks>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.#ctor">
            <summary>
            Create instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>.
            </summary>
            <remarks>
            Create instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>.
            <para />
            Same instance shall not be used for different signing operations, but can be used for both
            <see cref="M:iText.Signatures.PadesTwoPhaseSigningHelper.CreateCMSContainerWithoutSignature(iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.String,iText.Kernel.Pdf.PdfReader,System.IO.Stream,iText.Signatures.SignerProperties)"/>
            and follow-up signing.
            </remarks>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetOcspClient(iText.Signatures.IOcspClient)">
            <summary>
            Set
            <see cref="T:iText.Signatures.IOcspClient"/>
            to be used for LTV Verification.
            </summary>
            <remarks>
            Set
            <see cref="T:iText.Signatures.IOcspClient"/>
            to be used for LTV Verification.
            <para />
            This setter is only relevant if Baseline-LT Profile level or higher is used.
            <para />
            If none is set, there will be an attempt to create default OCSP Client instance using the certificate chain.
            </remarks>
            <param name="ocspClient">
            
            <see cref="T:iText.Signatures.IOcspClient"/>
            instance to be used for LTV Verification
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetTrustedCertificates(System.Collections.Generic.IList{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>
            Set certificate list to be used by the
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            to retrieve missing certificates.
            </summary>
            <param name="certificateList">
            certificate list for getting missing certificates in chain
            or CRL response issuer certificates.
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetCrlClient(iText.Signatures.ICrlClient)">
            <summary>
            Set
            <see cref="T:iText.Signatures.ICrlClient"/>
            to be used for LTV Verification.
            </summary>
            <remarks>
            Set
            <see cref="T:iText.Signatures.ICrlClient"/>
            to be used for LTV Verification.
            <para />
            This setter is only relevant if Baseline-LT Profile level or higher is used.
            <para />
            If none is set, there will be an attempt to create default CRL Client instance using the certificate chain.
            </remarks>
            <param name="crlClient">
            
            <see cref="T:iText.Signatures.ICrlClient"/>
            instance to be used for LTV Verification
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetTSAClient(iText.Signatures.ITSAClient)">
            <summary>
            Set
            <see cref="T:iText.Signatures.ITSAClient"/>
            to be used for timestamp signature creation.
            </summary>
            <remarks>
            Set
            <see cref="T:iText.Signatures.ITSAClient"/>
            to be used for timestamp signature creation.
            <para />
            This client has to be set for Baseline-T Profile level and higher.
            </remarks>
            <param name="tsaClient">
            
            <see cref="T:iText.Signatures.ITSAClient"/>
            instance to be used for timestamp signature creation.
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetIssuingCertificateRetriever(iText.Signatures.IIssuingCertificateRetriever)">
            <summary>
            Set
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            to be used before main signing operation.
            </summary>
            <remarks>
            Set
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            to be used before main signing operation.
            <para />
            If none is set,
            <see cref="T:iText.Signatures.IssuingCertificateRetriever"/>
            instance will be used instead.
            </remarks>
            <param name="issuingCertificateRetriever">
            
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            instance to be used for getting missing
            certificates in chain or CRL response issuer certificates.
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetEstimatedSize(System.Int32)">
            <summary>Set estimated size of a signature to be applied.</summary>
            <remarks>
            Set estimated size of a signature to be applied.
            <para />
            This parameter represents estimated amount of bytes to be preserved for the signature.
            <para />
            If none is set, 0 will be used and the required space will be calculated during the signing.
            </remarks>
            <param name="estimatedSize">amount of bytes to be used as estimated value</param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetTemporaryDirectoryPath(System.String)">
            <summary>Set temporary directory to be used for temporary files creation.</summary>
            <remarks>
            Set temporary directory to be used for temporary files creation.
            <para />
            If none is set, temporary documents will be created in memory.
            </remarks>
            <param name="temporaryDirectoryPath">
            
            <see cref="T:System.String"/>
            representing relative or absolute path to the directory
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetTimestampSignatureName(System.String)">
            <summary>Set the name to be used for timestamp signature creation.</summary>
            <remarks>
            Set the name to be used for timestamp signature creation.
            <para />
            This setter is only relevant if
            <see cref="M:iText.Signatures.PdfPadesSigner.SignWithBaselineLTAProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Signatures.IExternalSignature,iText.Signatures.ITSAClient)"/>
            or
            <see cref="M:iText.Signatures.PdfPadesSigner.ProlongSignatures"/>
            methods are used.
            <para />
            If none is set, randomly generated signature name will be used.
            </remarks>
            <param name="timestampSignatureName">
            
            <see cref="T:System.String"/>
            representing the name of a timestamp signature to be applied
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SetStampingProperties(iText.Kernel.Pdf.StampingProperties)">
            <summary>Set stamping properties to be used during main signing operation.</summary>
            <remarks>
            Set stamping properties to be used during main signing operation.
            <para />
            If none is set, stamping properties with append mode enabled will be used
            </remarks>
            <param name="stampingProperties">
            
            <see cref="T:iText.Kernel.Pdf.StampingProperties"/>
            instance to be used during main signing operation
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.CreateCMSContainerWithoutSignature(iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.String,iText.Kernel.Pdf.PdfReader,System.IO.Stream,iText.Signatures.SignerProperties)">
            <summary>Creates CMS container compliant with PAdES level.</summary>
            <remarks>
            Creates CMS container compliant with PAdES level. Prepares document and placeholder for the future signature
            without actual signing process.
            </remarks>
            <param name="certificates">certificates to be added to the CMS container</param>
            <param name="digestAlgorithm">the algorithm to generate the digest with</param>
            <param name="inputDocument">
            reader
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            instance to read original PDF file
            </param>
            <param name="outputStream">
            
            <see cref="T:System.IO.Stream"/>
            output stream to write the resulting PDF file into
            </param>
            <param name="signerProperties">properties to be used in the signing operations</param>
            <returns>prepared CMS container without signature.</returns>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SignCMSContainerWithBaselineBProfile(iText.Signatures.IExternalSignature,iText.Kernel.Pdf.PdfReader,System.IO.Stream,System.String,iText.Signatures.Cms.CMSContainer)">
            <summary>Follow-up step that signs prepared document with PAdES Baseline-B profile.</summary>
            <param name="externalSignature">external signature to do the actual signing</param>
            <param name="inputDocument">
            reader
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            instance to read prepared document
            </param>
            <param name="outputStream">the output PDF</param>
            <param name="signatureFieldName">the field to sign</param>
            <param name="cmsContainer">the finalized CMS container (e.g. created in the first step)</param>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SignCMSContainerWithBaselineTProfile(iText.Signatures.IExternalSignature,iText.Kernel.Pdf.PdfReader,System.IO.Stream,System.String,iText.Signatures.Cms.CMSContainer)">
            <summary>Follow-up step that signs prepared document with PAdES Baseline-T profile.</summary>
            <param name="externalSignature">external signature to do the actual signing</param>
            <param name="inputDocument">
            reader
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            instance to read prepared document
            </param>
            <param name="outputStream">the output PDF</param>
            <param name="signatureFieldName">the field to sign</param>
            <param name="cmsContainer">the finalized CMS container (e.g. created in the first step)</param>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SignCMSContainerWithBaselineLTProfile(iText.Signatures.IExternalSignature,iText.Kernel.Pdf.PdfReader,System.IO.Stream,System.String,iText.Signatures.Cms.CMSContainer)">
            <summary>Follow-up step that signs prepared document with PAdES Baseline-LT profile.</summary>
            <param name="externalSignature">external signature to do the actual signing</param>
            <param name="inputDocument">
            reader
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            instance to read prepared document
            </param>
            <param name="outputStream">the output PDF</param>
            <param name="signatureFieldName">the field to sign</param>
            <param name="cmsContainer">the finalized CMS container (e.g. created in the first step)</param>
        </member>
        <member name="M:iText.Signatures.PadesTwoPhaseSigningHelper.SignCMSContainerWithBaselineLTAProfile(iText.Signatures.IExternalSignature,iText.Kernel.Pdf.PdfReader,System.IO.Stream,System.String,iText.Signatures.Cms.CMSContainer)">
            <summary>Follow-up step that signs prepared document with PAdES Baseline-LTA profile.</summary>
            <param name="externalSignature">external signature to do the actual signing</param>
            <param name="inputDocument">
            reader
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            instance to read prepared document
            </param>
            <param name="outputStream">the output PDF</param>
            <param name="signatureFieldName">the field to sign</param>
            <param name="cmsContainer">the finalized CMS container (e.g. created in the first step)</param>
        </member>
        <member name="T:iText.Signatures.PdfPadesSigner">
            <summary>This class performs signing with PaDES related profiles using provided parameters.</summary>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.#ctor(iText.Kernel.Pdf.PdfReader,System.IO.Stream)">
            <summary>Create an instance of PdfPadesSigner class.</summary>
            <remarks>Create an instance of PdfPadesSigner class. One instance shall be used for one signing operation.
                </remarks>
            <param name="reader">
            
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            instance to read original PDF file
            </param>
            <param name="outputStream">
            
            <see cref="T:System.IO.Stream"/>
            output stream to write the resulting PDF file into
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SignWithBaselineBProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Signatures.IExternalSignature)">
            <summary>
            Sign the document provided in
            <see cref="T:iText.Signatures.PdfSigner"/>
            instance with PaDES Baseline-B Profile.
            </summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="chain">the chain of certificates to be used for signing operation</param>
            <param name="externalSignature">
            
            <see cref="T:iText.Signatures.IExternalSignature"/>
            instance to be used for main signing operation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SignWithBaselineBProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Commons.Bouncycastle.Crypto.IPrivateKey)">
            <summary>
            Sign the document provided in
            <see cref="T:iText.Signatures.PdfSigner"/>
            instance with PaDES Baseline-B Profile.
            </summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="chain">the chain of certificates to be used for signing operation</param>
            <param name="privateKey">
            
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPrivateKey"/>
            instance to be used for main signing operation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SignWithBaselineTProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Signatures.IExternalSignature,iText.Signatures.ITSAClient)">
            <summary>
            Sign the document provided in
            <see cref="T:iText.Signatures.PdfSigner"/>
            instance with PaDES Baseline-T Profile.
            </summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="chain">the chain of certificates to be used for signing operation</param>
            <param name="externalSignature">
            
            <see cref="T:iText.Signatures.IExternalSignature"/>
            instance to be used for main signing operation
            </param>
            <param name="tsaClient">
            
            <see cref="T:iText.Signatures.ITSAClient"/>
            instance to be used for timestamp creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SignWithBaselineTProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Signatures.ITSAClient)">
            <summary>
            Sign the document provided in
            <see cref="T:iText.Signatures.PdfSigner"/>
            instance with PaDES Baseline-T Profile.
            </summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="chain">the chain of certificates to be used for signing operation</param>
            <param name="privateKey">
            
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPrivateKey"/>
            instance to be used for main signing operation
            </param>
            <param name="tsaClient">
            
            <see cref="T:iText.Signatures.ITSAClient"/>
            instance to be used for timestamp creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SignWithBaselineLTProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Signatures.IExternalSignature,iText.Signatures.ITSAClient)">
            <summary>
            Sign the document provided in
            <see cref="T:iText.Signatures.PdfSigner"/>
            instance with PaDES Baseline-LT Profile.
            </summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="chain">the chain of certificates to be used for signing operation</param>
            <param name="externalSignature">
            
            <see cref="T:iText.Signatures.IExternalSignature"/>
            instance to be used for main signing operation
            </param>
            <param name="tsaClient">
            
            <see cref="T:iText.Signatures.ITSAClient"/>
            instance to be used for timestamp creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SignWithBaselineLTProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Signatures.ITSAClient)">
            <summary>
            Sign the document provided in
            <see cref="T:iText.Signatures.PdfSigner"/>
            instance with PaDES Baseline-LT Profile.
            </summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="chain">the chain of certificates to be used for signing operation</param>
            <param name="privateKey">
            
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPrivateKey"/>
            instance to be used for main signing operation
            </param>
            <param name="tsaClient">
            
            <see cref="T:iText.Signatures.ITSAClient"/>
            instance to be used for timestamp creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SignWithBaselineLTAProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Signatures.IExternalSignature,iText.Signatures.ITSAClient)">
            <summary>
            Sign the document provided in
            <see cref="T:iText.Signatures.PdfSigner"/>
            instance with PaDES Baseline-LTA Profile.
            </summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="chain">the chain of certificates to be used for signing operation</param>
            <param name="externalSignature">
            
            <see cref="T:iText.Signatures.IExternalSignature"/>
            instance to be used for main signing operation
            </param>
            <param name="tsaClient">
            
            <see cref="T:iText.Signatures.ITSAClient"/>
            instance to be used for timestamp creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SignWithBaselineLTAProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Signatures.ITSAClient)">
            <summary>
            Sign the document provided in
            <see cref="T:iText.Signatures.PdfSigner"/>
            instance with PaDES Baseline-LTA Profile.
            </summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="chain">the chain of certificates to be used for signing operation</param>
            <param name="privateKey">
            
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPrivateKey"/>
            instance to be used for main signing operation
            </param>
            <param name="tsaClient">
            
            <see cref="T:iText.Signatures.ITSAClient"/>
            instance to be used for timestamp creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.ProlongSignatures(iText.Signatures.ITSAClient)">
            <summary>Add revocation information for all the signatures which could be found in the provided document.</summary>
            <remarks>
            Add revocation information for all the signatures which could be found in the provided document.
            Also add timestamp signature on top of that.
            </remarks>
            <param name="tsaClient">
            
            <see cref="T:iText.Signatures.ITSAClient"/>
            TSA Client to be used for timestamp signature creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.ProlongSignatures">
            <summary>Add revocation information for all the signatures which could be found in the provided document.</summary>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetTemporaryDirectoryPath(System.String)">
            <summary>Set temporary directory to be used for temporary files creation.</summary>
            <remarks>
            Set temporary directory to be used for temporary files creation.
            <para />
            If none is set, temporary documents will be created in memory.
            </remarks>
            <param name="temporaryDirectoryPath">
            
            <see cref="T:System.String"/>
            representing relative or absolute path to the directory
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetCertificationLevel(iText.Signatures.AccessPermissions)">
            <summary>Set certification level which specifies DocMDP level which is expected to be set.</summary>
            <param name="accessPermissions">
            
            <see cref="T:iText.Signatures.AccessPermissions"/>
            certification level
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetSignatureFieldLock(iText.Forms.PdfSigFieldLock)">
            <summary>Set FieldMDP rules to be applied for this signature.</summary>
            <param name="fieldLock">
            
            <see cref="T:iText.Forms.PdfSigFieldLock"/>
            field lock dictionary.
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetTimestampSignatureName(System.String)">
            <summary>Set the name to be used for timestamp signature creation.</summary>
            <remarks>
            Set the name to be used for timestamp signature creation.
            <para />
            This setter is only relevant if
            <see cref="M:iText.Signatures.PdfPadesSigner.SignWithBaselineLTAProfile(iText.Signatures.SignerProperties,iText.Commons.Bouncycastle.Cert.IX509Certificate[],iText.Signatures.IExternalSignature,iText.Signatures.ITSAClient)"/>
            or
            <see cref="M:iText.Signatures.PdfPadesSigner.ProlongSignatures"/>
            methods are used.
            <para />
            If none is set, randomly generated signature name will be used.
            </remarks>
            <param name="timestampSignatureName">
            
            <see cref="T:System.String"/>
            representing the name of a timestamp signature to be applied
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetStampingProperties(iText.Kernel.Pdf.StampingProperties)">
            <summary>Set stamping properties to be used during main signing operation.</summary>
            <remarks>
            Set stamping properties to be used during main signing operation.
            <para />
            If none is set, stamping properties with append mode enabled will be used
            </remarks>
            <param name="stampingProperties">
            
            <see cref="T:iText.Kernel.Pdf.StampingProperties"/>
            instance to be used during main signing operation
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetEstimatedSize(System.Int32)">
            <summary>Set estimated size of a signature to be applied.</summary>
            <remarks>
            Set estimated size of a signature to be applied.
            <para />
            This parameter represents estimated amount of bytes to be preserved for the signature.
            <para />
            If none is set, 0 will be used and the required space will be calculated during the signing.
            </remarks>
            <param name="estimatedSize">amount of bytes to be used as estimated value</param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetOcspClient(iText.Signatures.IOcspClient)">
            <summary>
            Set
            <see cref="T:iText.Signatures.IOcspClient"/>
            to be used for LTV Verification.
            </summary>
            <remarks>
            Set
            <see cref="T:iText.Signatures.IOcspClient"/>
            to be used for LTV Verification.
            <para />
            This setter is only relevant if Baseline-LT Profile level or higher is used.
            <para />
            If none is set, there will be an attempt to create default OCSP Client instance using the certificate chain.
            </remarks>
            <param name="ocspClient">
            
            <see cref="T:iText.Signatures.IOcspClient"/>
            instance to be used for LTV Verification
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetCrlClient(iText.Signatures.ICrlClient)">
            <summary>
            Set
            <see cref="T:iText.Signatures.ICrlClient"/>
            to be used for LTV Verification.
            </summary>
            <remarks>
            Set
            <see cref="T:iText.Signatures.ICrlClient"/>
            to be used for LTV Verification.
            <para />
            This setter is only relevant if Baseline-LT Profile level or higher is used.
            <para />
            If none is set, there will be an attempt to create default CRL Client instance using the certificate chain.
            </remarks>
            <param name="crlClient">
            
            <see cref="T:iText.Signatures.ICrlClient"/>
            instance to be used for LTV Verification
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetExternalDigest(iText.Signatures.IExternalDigest)">
            <summary>
            Set
            <see cref="T:iText.Signatures.IExternalDigest"/>
            to be used for main signing operation.
            </summary>
            <remarks>
            Set
            <see cref="T:iText.Signatures.IExternalDigest"/>
            to be used for main signing operation.
            <para />
            If none is set,
            <see cref="T:iText.Signatures.BouncyCastleDigest"/>
            instance will be used instead.
            </remarks>
            <param name="externalDigest">
            
            <see cref="T:iText.Signatures.IExternalDigest"/>
            to be used for main signing operation.
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetIssuingCertificateRetriever(iText.Signatures.IIssuingCertificateRetriever)">
            <summary>
            Set
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            to be used before main signing operation.
            </summary>
            <remarks>
            Set
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            to be used before main signing operation.
            <para />
            If none is set,
            <see cref="T:iText.Signatures.IssuingCertificateRetriever"/>
            instance will be used instead.
            </remarks>
            <param name="issuingCertificateRetriever">
            
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            instance to be used for getting missing
            certificates in chain or CRL response issuer certificates.
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPadesSigner.SetTrustedCertificates(System.Collections.Generic.IList{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>
            Set certificate list to be used by the
            <see cref="T:iText.Signatures.IIssuingCertificateRetriever"/>
            to retrieve missing certificates.
            </summary>
            <param name="certificateList">
            certificate list for getting missing certificates in chain
            or CRL response issuer certificates.
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfPadesSigner"/>.
            </returns>
        </member>
        <member name="T:iText.Signatures.PdfPKCS7">
            <summary>
            This class does all the processing related to signing
            and verifying a PKCS#7 / CMS signature.
            </summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.signName">
            <summary>Holds value of property signName.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.reason">
            <summary>Holds value of property reason.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.location">
            <summary>Holds value of property location.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.signDate">
            <summary>Holds value of property signDate.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.signedDataRevocationInfo">
            <summary>Collection to store revocation info other than OCSP and CRL responses, e.g. SCVP Request and Response.
                </summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.#ctor(iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.String,iText.Signatures.IExternalDigest,System.Boolean)">
            <summary>Assembles all the elements needed to create a signature, except for the data.</summary>
            <param name="privKey">the private key</param>
            <param name="certChain">the certificate chain</param>
            <param name="interfaceDigest">the interface digest</param>
            <param name="hashAlgorithm">the hash algorithm</param>
            <param name="provider">the provider or <c>null</c> for the default provider</param>
            <param name="hasEncapContent"><c>true</c> if the sub-filter is adbe.pkcs7.sha1</param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.#ctor(iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.String,System.Boolean)">
            <summary>Assembles all the elements needed to create a signature, except for the data.</summary>
            <param name="privKey">the private key</param>
            <param name="certChain">the certificate chain</param>
            <param name="hashAlgorithm">the hash algorithm</param>
            <param name="provider">the provider or <c>null</c> for the default provider</param>
            <param name="hasEncapContent"><c>true</c> if the sub-filter is adbe.pkcs7.sha1</param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.#ctor(System.Byte[],System.Byte[])">
            <summary>Use this constructor if you want to verify a signature using the sub-filter adbe.x509.rsa_sha1.</summary>
            <param name="contentsKey">the /Contents key</param>
            <param name="certsKey">the /Cert key</param>
            <param name="provider">the provider or <c>null</c> for the default provider</param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.#ctor(System.Byte[],iText.Kernel.Pdf.PdfName)">
            <summary>Use this constructor if you want to verify a signature.</summary>
            <param name="contentsKey">the /Contents key</param>
            <param name="filterSubtype">the filtersubtype</param>
            <param name="provider">the provider or <c>null</c> for the default provider</param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SetSignaturePolicy(iText.Signatures.SignaturePolicyInfo)">
            <summary>Set signature policy identifier to be used during signature creation.</summary>
            <param name="signaturePolicy">
            
            <see cref="T:iText.Signatures.SignaturePolicyInfo"/>
            to be used during signature creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SetSignaturePolicy(iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyIdentifier)">
            <summary>Set signature policy identifier to be used during signature creation.</summary>
            <param name="signaturePolicy">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyIdentifier"/>
            to be used during signature creation
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSignName">
            <summary>Getter for property sigName.</summary>
            <returns>Value of property sigName.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SetSignName(System.String)">
            <summary>Setter for property sigName.</summary>
            <param name="signName">New value of property sigName.</param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetReason">
            <summary>Getter for property reason.</summary>
            <returns>Value of property reason.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SetReason(System.String)">
            <summary>Setter for property reason.</summary>
            <param name="reason">New value of property reason.</param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetLocation">
            <summary>Getter for property location.</summary>
            <returns>Value of property location.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SetLocation(System.String)">
            <summary>Setter for property location.</summary>
            <param name="location">New value of property location.</param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSignDate">
            <summary>Getter for property signDate.</summary>
            <returns>Value of property signDate.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SetSignDate(System.DateTime)">
            <summary>Setter for property signDate.</summary>
            <param name="signDate">New value of property signDate.</param>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.version">
            <summary>Version of the PKCS#7 object</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.signerversion">
            <summary>Version of the PKCS#7 "SignerInfo" object.</summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetVersion">
            <summary>Get the version of the PKCS#7 object.</summary>
            <returns>the version of the PKCS#7 object.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSigningInfoVersion">
            <summary>Get the version of the PKCS#7 "SignerInfo" object.</summary>
            <returns>the version of the PKCS#7 "SignerInfo" object.</returns>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.digestAlgorithmOid">
            <summary>The ID of the digest algorithm, e.g. "2.16.840.1.101.3.4.2.1".</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.messageDigest">
            <summary>The object that will create the digest</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.digestalgos">
            <summary>The digest algorithms</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.digestAttr">
            <summary>The digest attributes</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.signatureMechanismOid">
            <summary>The signature algorithm.</summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetDigestAlgorithmOid">
            <summary>Getter for the ID of the digest algorithm, e.g. "2.16.840.1.101.3.4.2.1".</summary>
            <remarks>
            Getter for the ID of the digest algorithm, e.g. "2.16.840.1.101.3.4.2.1".
            See ISO-32000-1, section 12.8.3.3 PKCS#7 Signatures as used in ISO 32000
            </remarks>
            <returns>the ID of the digest algorithm</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetDigestAlgorithmName">
            <summary>Returns the name of the digest algorithm, e.g. "SHA256".</summary>
            <returns>the digest algorithm name, e.g. "SHA256"</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSignatureMechanismOid">
            <summary>Getter for the signature algorithm OID.</summary>
            <remarks>
            Getter for the signature algorithm OID.
            See ISO-32000-1, section 12.8.3.3 PKCS#7 Signatures as used in ISO 32000
            </remarks>
            <returns>the signature algorithm OID</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSignatureMechanismName">
            <summary>
            Get the signature mechanism identifier, including both the digest function
            and the signature algorithm, e.g. "SHA1withRSA".
            </summary>
            <remarks>
            Get the signature mechanism identifier, including both the digest function
            and the signature algorithm, e.g. "SHA1withRSA".
            See ISO-32000-1, section 12.8.3.3 PKCS#7 Signatures as used in ISO 32000
            </remarks>
            <returns>the algorithm used to calculate the signature</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSignatureAlgorithmName">
            <summary>Returns the name of the signature algorithm only (disregarding the digest function, if any).</summary>
            <returns>the name of an encryption algorithm</returns>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.externalSignatureValue">
            <summary>The signature value or signed digest, if created outside this class</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.externalEncapMessageContent">
            <summary>Externally specified encapsulated message content.</summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SetExternalSignatureValue(System.Byte[],System.Byte[],System.String)">
            <summary>Sets the signature to an externally calculated value.</summary>
            <param name="signatureValue">the signature value</param>
            <param name="signedMessageContent">the extra data that goes into the data tag in PKCS#7</param>
            <param name="signatureAlgorithm">
            the signature algorithm. It must be <c>null</c> if the
            <c>signatureValue</c> is also <c>null</c>.
            If the <c>signatureValue</c> is not <c>null</c>,
            possible values include "RSA", "DSA", "ECDSA", "Ed25519" and "Ed448".
            </param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SetExternalSignatureValue(System.Byte[],System.Byte[],System.String,iText.Signatures.ISignatureMechanismParams)">
            <summary>Sets the signature to an externally calculated value.</summary>
            <param name="signatureValue">the signature value</param>
            <param name="signedMessageContent">the extra data that goes into the data tag in PKCS#7</param>
            <param name="signatureAlgorithm">
            the signature algorithm. It must be <c>null</c> if the
            <c>signatureValue</c> is also <c>null</c>.
            If the <c>signatureValue</c> is not <c>null</c>,
            possible values include "RSA", "RSASSA-PSS", "DSA",
            "ECDSA", "Ed25519" and "Ed448".
            </param>
            <param name="signatureMechanismParams">parameters for the signature mechanism, if required</param>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.sig">
            <summary>Class from the Java SDK that provides the functionality of a digital signature algorithm.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.signatureValue">
            <summary>The raw signature value as calculated by this class (or extracted from an existing PDF)</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.encapMessageContent">
            <summary>The content to which the signature applies, if encapsulated in the PKCS #7 payload.</summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.Update(System.Byte[],System.Int32,System.Int32)">
            <summary>Update the digest with the specified bytes.</summary>
            <remarks>
            Update the digest with the specified bytes.
            This method is used both for signing and verifying
            </remarks>
            <param name="buf">the data buffer</param>
            <param name="off">the offset in the data buffer</param>
            <param name="len">the data length</param>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetEncodedPKCS1">
            <summary>Gets the bytes for the PKCS#1 object.</summary>
            <returns>a byte array</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetEncodedPKCS7">
            <summary>Gets the bytes for the PKCS7SignedData object.</summary>
            <returns>the bytes for the PKCS7SignedData object</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetEncodedPKCS7(System.Byte[])">
            <summary>Gets the bytes for the PKCS7SignedData object.</summary>
            <remarks>
            Gets the bytes for the PKCS7SignedData object. Optionally the authenticatedAttributes
            in the signerInfo can also be set. If either of the parameters is <c>null</c>, none will be used.
            </remarks>
            <param name="secondDigest">the digest in the authenticatedAttributes</param>
            <returns>the bytes for the PKCS7SignedData object</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetEncodedPKCS7(System.Byte[],iText.Signatures.PdfSigner.CryptoStandard,iText.Signatures.ITSAClient,System.Collections.Generic.ICollection{System.Byte[]},System.Collections.Generic.ICollection{System.Byte[]})">
            <summary>Gets the bytes for the PKCS7SignedData object.</summary>
            <remarks>
            Gets the bytes for the PKCS7SignedData object. Optionally the authenticatedAttributes
            in the signerInfo can also be set, and/or a time-stamp-authority client
            may be provided.
            </remarks>
            <param name="secondDigest">the digest in the authenticatedAttributes</param>
            <param name="sigtype">
            specifies the PKCS7 standard flavor to which created PKCS7SignedData object will adhere:
            either basic CMS or CAdES
            </param>
            <param name="tsaClient">TSAClient - null or an optional time stamp authority client</param>
            <param name="ocsp">
            collection of DER-encoded BasicOCSPResponses for the  certificate in the signature
            certificates
            chain, or null if OCSP revocation data is not to be added.
            </param>
            <param name="crlBytes">
            collection of DER-encoded CRL for certificates from the signature certificates chain,
            or null if CRL revocation data is not to be added.
            </param>
            <returns>byte[] the bytes for the PKCS7SignedData object</returns>
            <seealso><a href="https://datatracker.ietf.org/doc/html/rfc6960#section-4.2.1">RFC 6960 § 4.2.1</a></seealso>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.BuildUnauthenticatedAttributes(System.Byte[])">
            <summary>
            Added by Aiken Sam, 2006-11-15, modifed by Martin Brunecky 07/12/2007
            to start with the timeStampToken (signedData 1.2.840.113549.1.7.2).
            </summary>
            <remarks>
            Added by Aiken Sam, 2006-11-15, modifed by Martin Brunecky 07/12/2007
            to start with the timeStampToken (signedData 1.2.840.113549.1.7.2).
            Token is the TSA response without response status, which is usually
            handled by the (vendor supplied) TSA request/response interface).
            </remarks>
            <param name="timeStampToken">byte[] - time stamp token, DER encoded signedData</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetAuthenticatedAttributeBytes(System.Byte[],iText.Signatures.PdfSigner.CryptoStandard,System.Collections.Generic.ICollection{System.Byte[]},System.Collections.Generic.ICollection{System.Byte[]})">
            <summary>When using authenticatedAttributes the authentication process is different.</summary>
            <remarks>
            When using authenticatedAttributes the authentication process is different.
            The document digest is generated and put inside the attribute. The signing is done over the DER encoded
            authenticatedAttributes. This method provides that encoding and the parameters must be
            exactly the same as in
            <see cref="M:iText.Signatures.PdfPKCS7.GetEncodedPKCS7(System.Byte[])"/>.
            <para />
            Note: do not pass in the full DER-encoded OCSPResponse object obtained from the responder,
            only the DER-encoded IBasicOCSPResponse value contained in the response data.
            <para />
            A simple example:
            <pre>
            Calendar cal = Calendar.getInstance();
            PdfPKCS7 pk7 = new PdfPKCS7(key, chain, null, "SHA1", null, false);
            MessageDigest messageDigest = MessageDigest.getInstance("SHA1");
            byte[] buf = new byte[8192];
            int n;
            InputStream inp = sap.getRangeStream();
            while ((n = inp.read(buf)) &gt; 0) {
            messageDigest.update(buf, 0, n);
            }
            byte[] hash = messageDigest.digest();
            byte[] sh = pk7.getAuthenticatedAttributeBytes(hash, cal);
            pk7.update(sh, 0, sh.length);
            byte[] sg = pk7.getEncodedPKCS7(hash, cal);
            </pre>
            </remarks>
            <param name="secondDigest">the content digest</param>
            <param name="sigtype">
            specifies the PKCS7 standard flavor to which created PKCS7SignedData object will adhere:
            either basic CMS or CAdES
            </param>
            <param name="ocsp">
            collection of DER-encoded BasicOCSPResponses for the  certificate in the signature
            certificates
            chain, or null if OCSP revocation data is not to be added.
            </param>
            <param name="crlBytes">
            collection of DER-encoded CRL for certificates from the signature certificates chain,
            or null if CRL revocation data is not to be added.
            </param>
            <returns>the byte array representation of the authenticatedAttributes ready to be signed</returns>
            <seealso><a href="https://datatracker.ietf.org/doc/html/rfc6960#section-4.2.1">RFC 6960 § 4.2.1</a></seealso>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetAuthenticatedAttributeSet(System.Byte[],System.Collections.Generic.ICollection{System.Byte[]},System.Collections.Generic.ICollection{System.Byte[]},iText.Signatures.PdfSigner.CryptoStandard)">
            <summary>
            This method provides that encoding and the parameters must be
            exactly the same as in
            <see cref="M:iText.Signatures.PdfPKCS7.GetEncodedPKCS7(System.Byte[])"/>.
            </summary>
            <param name="secondDigest">the content digest</param>
            <returns>the byte array representation of the authenticatedAttributes ready to be signed</returns>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.sigAttr">
            <summary>Signature attributes</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.sigAttrDer">
            <summary>Signature attributes (maybe not necessary, but we use it as fallback)</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.encContDigest">
            <summary>encrypted digest</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.verified">
            <summary>Indicates if a signature has already been verified</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.verifyResult">
            <summary>The result of the verification</summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.VerifySignatureIntegrityAndAuthenticity">
            <summary>
            Verifies that signature integrity is intact (or in other words that signed data wasn't modified)
            by checking that embedded data digest corresponds to the calculated one.
            </summary>
            <remarks>
            Verifies that signature integrity is intact (or in other words that signed data wasn't modified)
            by checking that embedded data digest corresponds to the calculated one. Also ensures that signature
            is genuine and is created by the owner of private key that corresponds to the declared public certificate.
            <para />
            Even though signature can be authentic and signed data integrity can be intact,
            one shall also always check that signed data is not only a part of PDF contents but is actually a complete PDF
            file.
            In order to check that given signature covers the current
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            please
            use
            <see cref="M:iText.Signatures.SignatureUtil.SignatureCoversWholeDocument(System.String)"/>
            method.
            </remarks>
            <returns><c>true</c> if the signature checks out, <c>false</c> otherwise</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.VerifyTimestampImprint">
            <summary>Checks if the timestamp refers to this document.</summary>
            <returns>true if it checks false otherwise</returns>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.certs">
            <summary>All the X.509 certificates in no particular order.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.signCerts">
            <summary>All the X.509 certificates used for the main signature.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.signCert">
            <summary>The X.509 certificate that is used to sign the digest.</summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetCertificates">
            <summary>Get all the X.509 certificates associated with this PKCS#7 object in no particular order.</summary>
            <remarks>
            Get all the X.509 certificates associated with this PKCS#7 object in no particular order.
            Other certificates, from OCSP for example, will also be included.
            </remarks>
            <returns>the X.509 certificates associated with this PKCS#7 object</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetTimestampCertificates">
            <summary>Get all X.509 certificates associated with this PKCS#7 object timestamp in no particular order.</summary>
            <returns>
            
            <see>Certificate[]</see>
            array
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSignCertificateChain">
            <summary>Get the X.509 sign certificate chain associated with this PKCS#7 object.</summary>
            <remarks>
            Get the X.509 sign certificate chain associated with this PKCS#7 object.
            Only the certificates used for the main signature will be returned, with
            the signing certificate first.
            </remarks>
            <returns>the X.509 certificates associated with this PKCS#7 object</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSigningCertificate">
            <summary>Get the X.509 certificate actually used to sign the digest.</summary>
            <returns>the X.509 certificate actually used to sign the digest</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.SignCertificateChain">
            <summary>
            Helper method that creates the collection of certificates
            used for the main signature based on the complete list
            of certificates and the sign certificate.
            </summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetCRLs">
            <summary>Get the X.509 certificate revocation lists associated with this PKCS#7 object (stored in Signer Info).
                </summary>
            <returns>the X.509 certificate revocation lists associated with this PKCS#7 object.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSignedDataCRLs">
            <summary>Get the X.509 certificate revocation lists associated with this PKCS#7 Signed Data object.</summary>
            <returns>the X.509 certificate revocation lists associated with this PKCS#7 Signed Data object.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.FindCRL(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary>Helper method that tries to construct the CRLs.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.basicResp">
            <summary>BouncyCastle IBasicOCSPResponse</summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetSignedDataOcsps">
            <summary>Gets the OCSP basic response collection retrieved from SignedData structure.</summary>
            <returns>the OCSP basic response collection.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetOcsp">
            <summary>Gets the OCSP basic response from the SignerInfo if there is one.</summary>
            <returns>the OCSP basic response or null.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.IsRevocationValid">
            <summary>Checks if OCSP revocation refers to the document signing certificate.</summary>
            <returns>true if it checks, false otherwise</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.FindOcsp(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary>Helper method that creates the IBasicOCSPResp object.</summary>
            <param name="seq">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Sequence"/>
            wrapper
            </param>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.isTsp">
            <summary>True if there's a PAdES LTV time stamp.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.isCades">
            <summary>True if it's a CAdES signature type.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.timestampSignatureContainer">
            <summary>Inner timestamp signature container.</summary>
        </member>
        <member name="F:iText.Signatures.PdfPKCS7.timeStampTokenInfo">
            <summary>BouncyCastle TSTInfo.</summary>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.IsTsp">
            <summary>Check if it's a PAdES-LTV time stamp.</summary>
            <returns>true if it's a PAdES-LTV time stamp, false otherwise</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetTimestampSignatureContainer">
            <summary>Retrieves inner timestamp signature container if there is one.</summary>
            <returns>timestamp signature container or null.</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetTimeStampTokenInfo">
            <summary>Gets the timestamp token info if there is one.</summary>
            <returns>the timestamp token info or null</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetTimeStampDate">
            <summary>Gets the timestamp date.</summary>
            <remarks>
            Gets the timestamp date.
            <para />
            In case the signed document doesn't contain timestamp,
            <see cref="F:iText.Signatures.TimestampConstants.UNDEFINED_TIMESTAMP_DATE"/>
            will be returned.
            </remarks>
            <returns>the timestamp date</returns>
        </member>
        <member name="M:iText.Signatures.PdfPKCS7.GetFilterSubtype">
            <summary>Getter for the filter subtype.</summary>
            <returns>the filter subtype</returns>
        </member>
        <member name="T:iText.Signatures.PdfSignature">
            <summary>Represents the signature dictionary.</summary>
        </member>
        <member name="M:iText.Signatures.PdfSignature.#ctor">
            <summary>Creates new PdfSignature.</summary>
        </member>
        <member name="M:iText.Signatures.PdfSignature.#ctor(iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.PdfName)">
            <summary>Creates new PdfSignature.</summary>
            <param name="filter">PdfName of the signature handler to use when validating this signature</param>
            <param name="subFilter">PdfName that describes the encoding of the signature</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Creates new
            <see cref="T:iText.Signatures.PdfSignature"/>
            instance from the provided
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>.
            </summary>
            <param name="sigDictionary">
            
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to create new
            <see cref="T:iText.Signatures.PdfSignature"/>
            instance from
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetSubFilter">
            <summary>A name that describes the encoding of the signature value and key information in the signature dictionary.
                </summary>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            which usually has a value either
            <see cref="F:iText.Kernel.Pdf.PdfName.Adbe_pkcs7_detached"/>
            or
            <see cref="F:iText.Kernel.Pdf.PdfName.ETSI_CAdES_DETACHED"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetType">
            <summary>
            The type of PDF object that the wrapped dictionary describes; if present, shall be
            <see cref="F:iText.Kernel.Pdf.PdfName.Sig"/>
            for a signature
            dictionary or
            <see cref="F:iText.Kernel.Pdf.PdfName.DocTimeStamp"/>
            for a timestamp signature dictionary.
            </summary>
            <remarks>
            The type of PDF object that the wrapped dictionary describes; if present, shall be
            <see cref="F:iText.Kernel.Pdf.PdfName.Sig"/>
            for a signature
            dictionary or
            <see cref="F:iText.Kernel.Pdf.PdfName.DocTimeStamp"/>
            for a timestamp signature dictionary. Shall be not null if it's value
            is
            <see cref="F:iText.Kernel.Pdf.PdfName.DocTimeStamp"/>
            . The default value is:
            <see cref="F:iText.Kernel.Pdf.PdfName.Sig"/>.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            that identifies type of the wrapped dictionary,
            returns null if it is not explicitly specified.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetByteRange(System.Int32[])">
            <summary>Sets the /ByteRange.</summary>
            <param name="range">
            an array of pairs of integers that specifies the byte range used in the digest calculation.
            A pair consists of the starting byte offset and the length
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetByteRange">
            <summary>Gets the /ByteRange.</summary>
            <returns>
            an array of pairs of integers that specifies the byte range used in the digest calculation.
            A pair consists of the starting byte offset and the length.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetContents(System.Byte[])">
            <summary>Sets the /Contents value to the specified byte[].</summary>
            <param name="contents">a byte[] representing the digest</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetContents">
            <summary>Gets the /Contents entry value.</summary>
            <remarks>
            Gets the /Contents entry value.
            See ISO 32000-1 12.8.1, Table 252 – Entries in a signature dictionary.
            </remarks>
            <returns>the signature content</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetCert(System.Byte[])">
            <summary>Sets the /Cert value of this signature.</summary>
            <param name="cert">the byte[] representing the certificate chain</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetCert">
            <summary>Gets the /Cert entry value of this signature.</summary>
            <remarks>
            Gets the /Cert entry value of this signature.
            See ISO 32000-1 12.8.1, Table 252 – Entries in a signature dictionary.
            </remarks>
            <returns>the signature cert</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetCertObject">
            <summary>Gets the /Cert entry value of this signature.</summary>
            <remarks>
            Gets the /Cert entry value of this signature.
            /Cert entry required when SubFilter is adbe.x509.rsa_sha1. May be array or byte string.
            </remarks>
            <returns>the signature cert value</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetName(System.String)">
            <summary>Sets the /Name of the person signing the document.</summary>
            <param name="name">name of the person signing the document</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetName">
            <summary>gets the /Name of the person signing the document.</summary>
            <returns>name of the person signing the document.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetDate(iText.Kernel.Pdf.PdfDate)">
            <summary>Sets the /M value.</summary>
            <remarks>Sets the /M value. Should only be used if the time of signing is not available in the signature.</remarks>
            <param name="date">time of signing</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetDate">
            <summary>Gets the /M value.</summary>
            <remarks>Gets the /M value. Should only be used if the time of signing is not available in the signature.</remarks>
            <returns>
            
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            which denotes time of signing.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetLocation(System.String)">
            <summary>Sets the /Location value.</summary>
            <param name="location">physical location of signing</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetLocation">
            <summary>Gets the /Location entry value.</summary>
            <returns>physical location of signing.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetReason(System.String)">
            <summary>Sets the /Reason value.</summary>
            <param name="reason">reason for signing</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetReason">
            <summary>Gets the /Reason value.</summary>
            <returns>reason for signing</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetSignatureCreator(System.String)">
            <summary>
            Sets the signature creator name in the
            <see cref="T:iText.Signatures.PdfSignatureBuildProperties"/>
            dictionary.
            </summary>
            <param name="signatureCreator">name of the signature creator</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.SetContact(System.String)">
            <summary>Sets the /ContactInfo value.</summary>
            <param name="contactInfo">information to contact the person who signed this document</param>
        </member>
        <member name="M:iText.Signatures.PdfSignature.Put(iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.PdfObject)">
            <summary>Add new key-value pair to the signature dictionary.</summary>
            <param name="key">
            
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            to be added as a key
            </param>
            <param name="value">
            
            <see cref="T:iText.Kernel.Pdf.PdfObject"/>
            to be added as a value
            </param>
            <returns>
            the same
            <see cref="T:iText.Signatures.PdfSignature"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.IsWrappedObjectMustBeIndirect">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSignature.GetPdfSignatureBuildProperties">
            <summary>
            Gets the
            <see cref="T:iText.Signatures.PdfSignatureBuildProperties"/>
            instance if it exists, if
            not it adds a new one and returns this.
            </summary>
            <returns>
            
            <see cref="T:iText.Signatures.PdfSignatureBuildProperties"/>
            </returns>
        </member>
        <member name="T:iText.Signatures.PdfSignatureApp">
            <summary>A dictionary that stores the name of the application that signs the PDF.</summary>
        </member>
        <member name="M:iText.Signatures.PdfSignatureApp.#ctor">
            <summary>Creates a new PdfSignatureApp</summary>
        </member>
        <member name="M:iText.Signatures.PdfSignatureApp.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Creates a new PdfSignatureApp.</summary>
            <param name="pdfObject">PdfDictionary containing initial values</param>
        </member>
        <member name="M:iText.Signatures.PdfSignatureApp.SetSignatureCreator(System.String)">
            <summary>
            Sets the signature created property in the Prop_Build dictionary's App
            dictionary.
            </summary>
            <param name="name">String name of the application creating the signature</param>
        </member>
        <member name="T:iText.Signatures.PdfSignatureAppearance">
            <summary>Provides convenient methods to make a signature appearance.</summary>
            <remarks>
            Provides convenient methods to make a signature appearance. Use it in conjunction with
            <see cref="T:iText.Signatures.PdfSigner"/>.
            </remarks>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.document">
            <summary>The document to be signed.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.modelElement">
            <summary>Signature model element.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.page">
            <summary>The page where the signature will appear.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.rect">
            <summary>
            The coordinates of the rectangle for a visible signature,
            or a zero-width, zero-height rectangle for an invisible signature.
            </summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.pageRect">
            <summary>Rectangle that represent the position and dimension of the signature in the page.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.renderingMode">
            <summary>The rendering mode chosen for visible signatures.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.reason">
            <summary>The reason for signing.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.reasonCaption">
            <summary>The caption for the reason for signing.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.location">
            <summary>Holds value of property location.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.locationCaption">
            <summary>The caption for the location of signing.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.signatureCreator">
            <summary>Holds value of the application that creates the signature.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.contact">
            <summary>The contact name of the signer.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.signDate">
            <summary>Holds value of property signDate.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.signCertificate">
            <summary>The signing certificate.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.signatureGraphic">
            <summary>The image that needs to be used for a visible signature.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.image">
            <summary>A background image for the text in layer 2.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.imageScale">
            <summary>The scaling to be applied to the background image.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.description">
            <summary>The text that goes in Layer 2 of the signature appearance.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.font">
            <summary>Font for the text in Layer 2.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.fontProvider">
            <summary>Font provider for the text.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.fontFamilyNames">
            <summary>Font family for the text.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.fontSize">
            <summary>Font size for the font of Layer 2.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.fontColor">
            <summary>Font color for the font of Layer 2.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.n0">
            <summary>Zero level of the signature appearance.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.n2">
            <summary>Second level of the signature appearance.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.fieldName">
            <summary>Indicates the field to be signed.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.reuseAppearance">
            <summary>Indicates if we need to reuse the existing appearance as layer 0.</summary>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.#ctor(iText.Kernel.Pdf.PdfDocument,iText.Kernel.Geom.Rectangle,System.Int32)">
            <summary>Creates a PdfSignatureAppearance.</summary>
            <param name="document">PdfDocument</param>
            <param name="pageRect">Rectangle of the appearance</param>
            <param name="pageNumber">Number of the page the appearance should be on</param>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetPageNumber">
            <summary>
            Provides the page number of the signature field which this signature
            appearance is associated with.
            </summary>
            <returns>
            The page number of the signature field which this signature
            appearance is associated with.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetPageNumber(System.Int32)">
            <summary>
            Sets the page number of the signature field which this signature
            appearance is associated with.
            </summary>
            <remarks>
            Sets the page number of the signature field which this signature
            appearance is associated with. Implicitly calls
            <see cref="M:iText.Signatures.PdfSignatureAppearance.SetPageRect(iText.Kernel.Geom.Rectangle)"/>
            which considers page number to process the rectangle correctly.
            </remarks>
            <param name="pageNumber">
            The page number of the signature field which
            this signature appearance is associated with.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetPageRect">
            <summary>
            Provides the rectangle that represent the position and dimension
            of the signature field in the page.
            </summary>
            <returns>
            the rectangle that represent the position and dimension
            of the signature field in the page.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetPageRect(iText.Kernel.Geom.Rectangle)">
            <summary>
            Sets the rectangle that represent the position and dimension of
            the signature field in the page.
            </summary>
            <param name="pageRect">
            The rectangle that represents the position and
            dimension of the signature field in the page.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetLayer0">
            <summary>Get Layer 0 of the appearance.</summary>
            <remarks>
            Get Layer 0 of the appearance.
            <para />
            The size of the layer is determined by the rectangle set via
            <see cref="M:iText.Signatures.PdfSignatureAppearance.SetPageRect(iText.Kernel.Geom.Rectangle)"/>
            </remarks>
            <returns>layer 0.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetLayer2">
            <summary>Get Layer 2 of the appearance.</summary>
            <remarks>
            Get Layer 2 of the appearance.
            <para />
            The size of the layer is determined by the rectangle set via
            <see cref="M:iText.Signatures.PdfSignatureAppearance.SetPageRect(iText.Kernel.Geom.Rectangle)"/>
            </remarks>
            <returns>layer 2.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetRenderingMode">
            <summary>Gets the rendering mode for this signature.</summary>
            <returns>the rendering mode for this signature.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetRenderingMode(iText.Signatures.PdfSignatureAppearance.RenderingMode)">
            <summary>Sets the rendering mode for this signature.</summary>
            <param name="renderingMode">the rendering mode.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetReason">
            <summary>Returns the signing reason.</summary>
            <returns>reason for signing.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetReason(System.String)">
            <summary>Sets the signing reason.</summary>
            <param name="reason">signing reason.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetReasonCaption(System.String)">
            <summary>Sets the caption for the signing reason.</summary>
            <param name="reasonCaption">A new signing reason caption.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetLocation">
            <summary>Returns the signing location.</summary>
            <returns>signing location.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetLocation(System.String)">
            <summary>Sets the signing location.</summary>
            <param name="location">A new signing location.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetLocationCaption(System.String)">
            <summary>Sets the caption for the signing location.</summary>
            <param name="locationCaption">A new signing location caption.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetSignatureCreator">
            <summary>Returns the signature creator.</summary>
            <returns>The signature creator.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetSignatureCreator(System.String)">
            <summary>Sets the name of the application used to create the signature.</summary>
            <param name="signatureCreator">A new name of the application signing a document.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetContact">
            <summary>Returns the signing contact.</summary>
            <returns>The signing contact.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetContact(System.String)">
            <summary>Sets the signing contact.</summary>
            <param name="contact">A new signing contact.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Sets the certificate used to provide the text in the appearance.</summary>
            <remarks>
            Sets the certificate used to provide the text in the appearance.
            This certificate doesn't take part in the actual signing process.
            </remarks>
            <param name="signCertificate">the certificate.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetCertificate">
            <summary>Get the signing certificate.</summary>
            <returns>the signing certificate.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetSignatureGraphic">
            <summary>Gets the Image object to render.</summary>
            <returns>the image.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetSignatureGraphic(iText.IO.Image.ImageData)">
            <summary>Sets the Image object to render when Render is set to RenderingMode.GRAPHIC or RenderingMode.GRAPHIC_AND_DESCRIPTION.
                </summary>
            <param name="signatureGraphic">image rendered. If null the mode is defaulted to RenderingMode.DESCRIPTION</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetReuseAppearance(System.Boolean)">
            <summary>Indicates that the existing appearances needs to be reused as a background layer.</summary>
            <param name="reuseAppearance">is an appearances reusing flag value to set.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetImage">
            <summary>Gets the background image for the layer 2.</summary>
            <returns>the background image for the layer 2.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetImage(iText.IO.Image.ImageData)">
            <summary>Sets the background image for the text in the layer 2.</summary>
            <param name="image">the background image for the layer 2.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetImageScale">
            <summary>Gets the scaling to be applied to the background image.</summary>
            <returns>the scaling to be applied to the background image.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetImageScale(System.Single)">
            <summary>Sets the scaling to be applied to the background image.</summary>
            <remarks>
            Sets the scaling to be applied to the background image. If it's zero the image
            will fully fill the rectangle. If it's less than zero the image will fill the rectangle but
            will keep the proportions. If it's greater than zero that scaling will be applied.
            In any of the cases the image will always be centered. It's zero by default.
            </remarks>
            <param name="imageScale">the scaling to be applied to the background image.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetLayer2Text(System.String)">
            <summary>Sets the signature text identifying the signer.</summary>
            <param name="text">
            the signature text identifying the signer. If null or not set
            a standard description will be used.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetLayer2Text">
            <summary>Gets the signature text identifying the signer if set by setLayer2Text().</summary>
            <returns>the signature text identifying the signer.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetLayer2Font">
            <summary>Gets the n2 and n4 layer font.</summary>
            <returns>the n2 and n4 layer font.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetLayer2Font(iText.Kernel.Font.PdfFont)">
            <summary>Sets the n2 layer font.</summary>
            <remarks>Sets the n2 layer font. If the font size is zero, auto-fit will be used.</remarks>
            <param name="font">the n2 font.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetLayer2FontSize(System.Single)">
            <summary>Sets the n2 and n4 layer font size.</summary>
            <param name="fontSize">font size.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetLayer2FontSize">
            <summary>Gets the n2 and n4 layer font size.</summary>
            <returns>the n2 and n4 layer font size.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetLayer2FontColor(iText.Kernel.Colors.Color)">
            <summary>Sets the n2 and n4 layer font color.</summary>
            <param name="color">font color.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetLayer2FontColor">
            <summary>Gets the n2 layer font color.</summary>
            <returns>the n2 layer font color.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetSignatureAppearance">
            <summary>Gets the signature layout element.</summary>
            <returns>the signature layout element.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetSignatureAppearance(iText.Forms.Form.Element.SignatureFieldAppearance)">
            <summary>Sets the signature layout element.</summary>
            <param name="modelElement">the signature layout element.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetFontProvider(iText.Layout.Font.FontProvider)">
            <summary>
            Sets
            <see cref="T:iText.Layout.Font.FontProvider"/>.
            </summary>
            <remarks>
            Sets
            <see cref="T:iText.Layout.Font.FontProvider"/>
            . Note, font provider is inherited property.
            </remarks>
            <param name="fontProvider">
            the instance of
            <see cref="T:iText.Layout.Font.FontProvider"/>.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetFontFamily(System.String[])">
            <summary>Sets the preferable font families for the signature content.</summary>
            <remarks>
            Sets the preferable font families for the signature content.
            Note that
            <see cref="T:iText.Layout.Font.FontProvider"/>
            shall be set as well.
            </remarks>
            <param name="fontFamilyNames">defines an ordered list of preferable font families for the signature element.
                </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.IsInvisible">
            <summary>Gets the visibility status of the signature.</summary>
            <returns>the visibility status of the signature.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetAppearance">
            <summary>Constructs appearance (top-level) for a signature.</summary>
            <returns>a top-level signature appearance.</returns>
            <seealso><a href="https://www.adobe.com/content/dam/acom/en/devnet/acrobat/pdfs/ppkappearances.pdf">Adobe Pdf Digital
            * Signature Appearances</a></seealso>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetSignDate">
            <summary>Returns the signature date.</summary>
            <returns>the signature date.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetSignDate(System.DateTime)">
            <summary>Sets the signature date.</summary>
            <param name="signDate">A new signature date.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.SetFieldName(System.String)">
            <summary>Set the field name of the appearance.</summary>
            <remarks>
            Set the field name of the appearance. Field name indicates the field to be signed if it is already presented
            in the document (signing existing field). Required for reuseAppearance option.
            </remarks>
            <param name="fieldName">name of the field</param>
            <returns>this instance to support fluent interface</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.IsReuseAppearance">
            <summary>
            Returns reuseAppearance value which indicates that the existing appearances needs to be reused
            as a background layer.
            </summary>
            <returns>an appearances reusing flag value.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.IsReuseAppearanceSet">
            <summary>
            Checks if reuseAppearance value was set using
            <see>this#setReuseAppearance(boolean)</see>.
            </summary>
            <remarks>
            Checks if reuseAppearance value was set using
            <see>this#setReuseAppearance(boolean)</see>.
            Used for backward compatibility.
            </remarks>
            <returns>boolean value.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetBackgroundLayer">
            <summary>Gets the background layer that is present when creating the signature field if it was set.</summary>
            <returns>n0 layer xObject.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSignatureAppearance.GetSignatureAppearanceLayer">
            <summary>Gets the signature appearance layer that contains information about the signature if it was set.</summary>
            <returns>n2 layer xObject.</returns>
        </member>
        <member name="T:iText.Signatures.PdfSignatureAppearance.RenderingMode">
            <summary>Signature rendering modes.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.RenderingMode.DESCRIPTION">
            <summary>The rendering mode is just the description.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.RenderingMode.NAME_AND_DESCRIPTION">
            <summary>The rendering mode is the name of the signer and the description.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.RenderingMode.GRAPHIC_AND_DESCRIPTION">
            <summary>The rendering mode is an image and the description.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSignatureAppearance.RenderingMode.GRAPHIC">
            <summary>The rendering mode is just an image.</summary>
        </member>
        <member name="T:iText.Signatures.PdfSignatureBuildProperties">
            <summary>Dictionary that stores signature build properties.</summary>
        </member>
        <member name="M:iText.Signatures.PdfSignatureBuildProperties.#ctor">
            <summary>Creates new PdfSignatureBuildProperties.</summary>
        </member>
        <member name="M:iText.Signatures.PdfSignatureBuildProperties.#ctor(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Creates new PdfSignatureBuildProperties with preset values.</summary>
            <param name="dict">PdfDictionary containing preset values</param>
        </member>
        <member name="M:iText.Signatures.PdfSignatureBuildProperties.SetSignatureCreator(System.String)">
            <summary>
            Sets the signatureCreator property in the underlying
            <see cref="T:iText.Signatures.PdfSignatureApp"/>
            dictionary.
            </summary>
            <param name="name">the signature creator's name to be set</param>
        </member>
        <member name="M:iText.Signatures.PdfSignatureBuildProperties.GetPdfSignatureAppProperty">
            <summary>
            Gets the
            <see cref="T:iText.Signatures.PdfSignatureApp"/>
            from this dictionary.
            </summary>
            <remarks>
            Gets the
            <see cref="T:iText.Signatures.PdfSignatureApp"/>
            from this dictionary. If it
            does not exist, it adds a new
            <see cref="T:iText.Signatures.PdfSignatureApp"/>
            and
            returns this instance.
            </remarks>
            <returns>
            
            <see cref="T:iText.Signatures.PdfSignatureApp"/>
            </returns>
        </member>
        <member name="T:iText.Signatures.PdfSigner">
            <summary>Takes care of the cryptographic options and appearances that form a signature.</summary>
        </member>
        <member name="T:iText.Signatures.PdfSigner.CryptoStandard">
            <summary>Enum containing the Cryptographic Standards.</summary>
            <remarks>Enum containing the Cryptographic Standards. Possible values are "CMS" and "CADES".</remarks>
        </member>
        <member name="F:iText.Signatures.PdfSigner.CryptoStandard.CMS">
            <summary>Cryptographic Message Syntax.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.CryptoStandard.CADES">
            <summary>CMS Advanced Electronic Signatures.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.NOT_CERTIFIED">
            <summary>Approval signature.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.CERTIFIED_NO_CHANGES_ALLOWED">
            <summary>Author signature, no changes allowed.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.CERTIFIED_FORM_FILLING">
            <summary>Author signature, form filling allowed.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.CERTIFIED_FORM_FILLING_AND_ANNOTATIONS">
            <summary>Author signature, form filling and annotations allowed.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.certificationLevel">
            <summary>The certification level.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.fieldName">
            <summary>The name of the field.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.raf">
            <summary>The file right before the signature is added (can be null).</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.bout">
            <summary>The bytes of the file right before the signature is added (if raf is null).</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.range">
            <summary>Array containing the byte positions of the bytes that need to be hashed.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.document">
            <summary>The PdfDocument.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.cryptoDictionary">
            <summary>The crypto dictionary.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.signatureEvent">
            <summary>Holds value of property signatureEvent.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.originalOS">
            <summary>OutputStream for the bytes of the document.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.temporaryOS">
            <summary>Outputstream that temporarily holds the output in memory.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.tempFile">
            <summary>Tempfile to hold the output temporarily.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.exclusionLocations">
            <summary>Name and content of keys that can only be added in the close() method.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.preClosed">
            <summary>Indicates if the pdf document has already been pre-closed.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.fieldLock">
            <summary>Signature field lock dictionary.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.appearance">
            <summary>The signature appearance.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.signDate">
            <summary>Holds value of property signDate.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.closed">
            <summary>Boolean to check if this PdfSigner instance has been closed already or not.</summary>
        </member>
        <member name="F:iText.Signatures.PdfSigner.acroForm">
            <summary>AcroForm for the PdfDocument.</summary>
        </member>
        <member name="M:iText.Signatures.PdfSigner.#ctor(iText.Kernel.Pdf.PdfReader,System.IO.Stream,iText.Kernel.Pdf.StampingProperties)">
            <summary>Creates a PdfSigner instance.</summary>
            <remarks>
            Creates a PdfSigner instance. Uses a
            <see cref="T:System.IO.MemoryStream"/>
            instead of a temporary file.
            </remarks>
            <param name="reader">PdfReader that reads the PDF file</param>
            <param name="outputStream">OutputStream to write the signed PDF file</param>
            <param name="properties">
            
            <see cref="T:iText.Kernel.Pdf.StampingProperties"/>
            for the signing document. Note that encryption will be
            preserved regardless of what is set in properties.
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.#ctor(iText.Kernel.Pdf.PdfReader,System.IO.Stream,System.String,iText.Kernel.Pdf.StampingProperties,iText.Signatures.SignerProperties)">
            <summary>Creates a PdfSigner instance.</summary>
            <remarks>
            Creates a PdfSigner instance. Uses a
            <see cref="T:System.IO.MemoryStream"/>
            instead of a temporary file.
            </remarks>
            <param name="reader">PdfReader that reads the PDF file</param>
            <param name="outputStream">OutputStream to write the signed PDF file</param>
            <param name="path">File to which the output is temporarily written</param>
            <param name="stampingProperties">
            
            <see cref="T:iText.Kernel.Pdf.StampingProperties"/>
            for the signing document. Note that encryption will be
            preserved regardless of what is set in properties.
            </param>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            bundled properties to be used in signing operations.
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.#ctor(iText.Kernel.Pdf.PdfReader,System.IO.Stream,System.String,iText.Kernel.Pdf.StampingProperties)">
            <summary>Creates a PdfSigner instance.</summary>
            <remarks>
            Creates a PdfSigner instance. Uses a
            <see cref="T:System.IO.MemoryStream"/>
            instead of a temporary file.
            </remarks>
            <param name="reader">PdfReader that reads the PDF file</param>
            <param name="outputStream">OutputStream to write the signed PDF file</param>
            <param name="path">File to which the output is temporarily written</param>
            <param name="properties">
            
            <see cref="T:iText.Kernel.Pdf.StampingProperties"/>
            for the signing document. Note that encryption will be
            preserved regardless of what is set in properties.
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.InitDocument(iText.Kernel.Pdf.PdfReader,iText.Kernel.Pdf.PdfWriter,iText.Kernel.Pdf.StampingProperties)">
            <summary>
            Initialize new
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance by using provided parameters.
            </summary>
            <param name="reader">
            
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            to be used as a reader in the new document
            </param>
            <param name="writer">
            
            <see cref="T:iText.Kernel.Pdf.PdfWriter"/>
            to be used as a writer in the new document
            </param>
            <param name="properties">
            
            <see cref="T:iText.Kernel.Pdf.StampingProperties"/>
            to be provided in the new document
            </param>
            <returns>
            new
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetSignDate">
            <summary>Gets the signature date.</summary>
            <returns>Calendar set to the signature date</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetSignDate(System.DateTime)">
            <summary>Sets the signature date.</summary>
            <param name="signDate">the signature date</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetSignatureAppearance">
            <summary>Provides access to a signature appearance object.</summary>
            <remarks>
            Provides access to a signature appearance object. Use it to
            customize the appearance of the signature.
            <para />
            Be aware:
            <list type="bullet">
            <item><description>If you create new signature field (either use
            <see cref="M:iText.Signatures.PdfSigner.SetFieldName(System.String)"/>
            with
            the name that doesn't exist in the document or don't specify it at all) then
            the signature is invisible by default.
            </description></item>
            <item><description>If you sign already existing field, then the signature appearance object
            is modified to have all the properties (page num., rect etc.) consistent with
            the state of the field (<strong>if you customized the appearance object
            before the
            <see cref="M:iText.Signatures.PdfSigner.SetFieldName(System.String)"/>
            call you'll have to do it again</strong>)
            </description></item>
            </list>
            <para />
            </remarks>
            <returns>
            
            <see cref="T:iText.Signatures.PdfSignatureAppearance"/>
            object.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetSignatureAppearance(iText.Forms.Form.Element.SignatureFieldAppearance)">
            <summary>Sets the signature field layout element to customize the appearance of the signature.</summary>
            <remarks>
            Sets the signature field layout element to customize the appearance of the signature. Signer's sign date will
            be set.
            </remarks>
            <param name="appearance">
            the
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            layout element.
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetCertificationLevel">
            <summary>Returns the document's certification level.</summary>
            <remarks>
            Returns the document's certification level.
            For possible values see
            <see cref="M:iText.Signatures.PdfSigner.SetCertificationLevel(System.Int32)"/>.
            </remarks>
            <returns>The certified status.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetCertificationLevel(System.Int32)">
            <summary>Sets the document's certification level.</summary>
            <remarks>
            Sets the document's certification level.
            This method overrides the value set by
            <see cref="M:iText.Signatures.PdfSigner.SetCertificationLevel(iText.Signatures.AccessPermissions)"/>.
            </remarks>
            <param name="certificationLevel">
            a new certification level for a document.
            Possible values are: <list type="bullet">
            <item><description>
            <see cref="F:iText.Signatures.PdfSigner.NOT_CERTIFIED"/>
            </description></item>
            <item><description>
            <see cref="F:iText.Signatures.PdfSigner.CERTIFIED_NO_CHANGES_ALLOWED"/>
            </description></item>
            <item><description>
            <see cref="F:iText.Signatures.PdfSigner.CERTIFIED_FORM_FILLING"/>
            </description></item>
            <item><description>
            <see cref="F:iText.Signatures.PdfSigner.CERTIFIED_FORM_FILLING_AND_ANNOTATIONS"/>
            </description></item>
            </list>
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetCertificationLevel(iText.Signatures.AccessPermissions)">
            <summary>Sets the document's certification level.</summary>
            <remarks>
            Sets the document's certification level.
            This method overrides the value set by
            <see cref="M:iText.Signatures.PdfSigner.SetCertificationLevel(System.Int32)"/>.
            </remarks>
            <param name="accessPermissions">
            
            <see cref="T:iText.Signatures.AccessPermissions"/>
            enum which specifies which certification level shall be used
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetFieldName">
            <summary>Gets the field name.</summary>
            <returns>the field name</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetSignatureDictionary">
            <summary>Returns the user made signature dictionary.</summary>
            <remarks>
            Returns the user made signature dictionary. This is the dictionary at the /V key
            of the signature field.
            </remarks>
            <returns>The user made signature dictionary.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetSignatureEvent">
            <summary>Getter for property signatureEvent.</summary>
            <returns>Value of property signatureEvent.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetSignatureEvent(iText.Signatures.PdfSigner.ISignatureEvent)">
            <summary>Sets the signature event to allow modification of the signature dictionary.</summary>
            <param name="signatureEvent">the signature event</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetNewSigFieldName">
            <summary>Gets a new signature field name that doesn't clash with any existing name.</summary>
            <returns>A new signature field name.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetFieldName(System.String)">
            <summary>Sets the name indicating the field to be signed.</summary>
            <remarks>
            Sets the name indicating the field to be signed. The field can already be presented in the
            document but shall not be signed. If the field is not presented in the document, it will be created.
            </remarks>
            <param name="fieldName">The name indicating the field to be signed.</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetDocument">
            <summary>Gets the PdfDocument associated with this instance.</summary>
            <returns>the PdfDocument associated with this instance</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetDocument(iText.Kernel.Pdf.PdfDocument)">
            <summary>Sets the PdfDocument.</summary>
            <param name="document">The PdfDocument</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetPageNumber">
            <summary>
            Provides the page number of the signature field which this signature
            appearance is associated with.
            </summary>
            <returns>
            The page number of the signature field which this signature
            appearance is associated with.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetPageNumber(System.Int32)">
            <summary>
            Sets the page number of the signature field which this signature
            appearance is associated with.
            </summary>
            <remarks>
            Sets the page number of the signature field which this signature
            appearance is associated with. Implicitly calls
            <see cref="M:iText.Signatures.PdfSigner.SetPageRect(iText.Kernel.Geom.Rectangle)"/>
            which considers page number to process the rectangle correctly.
            </remarks>
            <param name="pageNumber">
            The page number of the signature field which
            this signature appearance is associated with.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetPageRect">
            <summary>
            Provides the rectangle that represent the position and dimension
            of the signature field in the page.
            </summary>
            <returns>
            the rectangle that represent the position and dimension
            of the signature field in the page
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetPageRect(iText.Kernel.Geom.Rectangle)">
            <summary>
            Sets the rectangle that represent the position and dimension of
            the signature field in the page.
            </summary>
            <param name="pageRect">
            The rectangle that represents the position and
            dimension of the signature field in the page.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetOriginalOutputStream(System.IO.Stream)">
            <summary>Setter for the OutputStream.</summary>
            <param name="originalOS">OutputStream for the bytes of the document</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetFieldLockDict">
            <summary>Getter for the field lock dictionary.</summary>
            <returns>Field lock dictionary.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetFieldLockDict(iText.Forms.PdfSigFieldLock)">
            <summary>Setter for the field lock dictionary.</summary>
            <remarks>
            Setter for the field lock dictionary.
            <para />
            <strong>Be aware:</strong> if a signature is created on an existing signature field,
            then its /Lock dictionary takes the precedence (if it exists).
            </remarks>
            <param name="fieldLock">Field lock dictionary</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetSignatureCreator">
            <summary>Returns the signature creator.</summary>
            <returns>The signature creator.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetSignatureCreator(System.String)">
            <summary>Sets the name of the application used to create the signature.</summary>
            <param name="signatureCreator">A new name of the application signing a document.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetContact">
            <summary>Returns the signing contact.</summary>
            <returns>The signing contact.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetContact(System.String)">
            <summary>Sets the signing contact.</summary>
            <param name="contact">A new signing contact.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetReason">
            <summary>Returns the signing reason.</summary>
            <returns>The signing reason.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetReason(System.String)">
            <summary>Sets the signing reason.</summary>
            <param name="reason">A new signing reason.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetLocation">
            <summary>Returns the signing location.</summary>
            <returns>The signing location.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SetLocation(System.String)">
            <summary>Sets the signing location.</summary>
            <param name="location">A new signing location.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetSignatureField">
            <summary>Gets the signature field to be signed.</summary>
            <remarks>
            Gets the signature field to be signed. The field can already be presented in the document. If the field is
            not presented in the document, it will be created.
            <para />
            This field instance is expected to be used for setting appearance related properties such as
            <see cref="M:iText.Forms.Fields.PdfSignatureFormField.SetReuseAppearance(System.Boolean)"/>
            ,
            <see cref="M:iText.Forms.Fields.PdfSignatureFormField.SetBackgroundLayer(iText.Kernel.Pdf.Xobject.PdfFormXObject)"/>
            and
            <see cref="M:iText.Forms.Fields.PdfSignatureFormField.SetSignatureAppearanceLayer(iText.Kernel.Pdf.Xobject.PdfFormXObject)"/>.
            </remarks>
            <returns>
            the
            <see cref="T:iText.Forms.Fields.PdfSignatureFormField"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SignDetached(iText.Signatures.IExternalDigest,iText.Signatures.IExternalSignature,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.ICollection{iText.Signatures.ICrlClient},iText.Signatures.IOcspClient,iText.Signatures.ITSAClient,System.Int32,iText.Signatures.PdfSigner.CryptoStandard)">
            <summary>Signs the document using the detached mode, CMS or CAdES equivalent.</summary>
            <remarks>
            Signs the document using the detached mode, CMS or CAdES equivalent.
            <br /><br />
            NOTE: This method closes the underlying pdf document. This means, that current instance
            of PdfSigner cannot be used after this method call.
            </remarks>
            <param name="externalSignature">the interface providing the actual signing</param>
            <param name="chain">the certificate chain</param>
            <param name="crlList">the CRL list</param>
            <param name="ocspClient">the OCSP client</param>
            <param name="tsaClient">the Timestamp client</param>
            <param name="externalDigest">an implementation that provides the digest</param>
            <param name="estimatedSize">the reserved size for the signature. It will be estimated if 0</param>
            <param name="sigtype">Either Signature.CMS or Signature.CADES</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SignDetached(iText.Signatures.IExternalSignature,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.ICollection{iText.Signatures.ICrlClient},iText.Signatures.IOcspClient,iText.Signatures.ITSAClient,System.Int32,iText.Signatures.PdfSigner.CryptoStandard)">
            <summary>Signs the document using the detached mode, CMS or CAdES equivalent.</summary>
            <remarks>
            Signs the document using the detached mode, CMS or CAdES equivalent.
            <br /><br />
            NOTE: This method closes the underlying pdf document. This means, that current instance
            of PdfSigner cannot be used after this method call.
            </remarks>
            <param name="externalSignature">the interface providing the actual signing</param>
            <param name="chain">the certificate chain</param>
            <param name="crlList">the CRL list</param>
            <param name="ocspClient">the OCSP client</param>
            <param name="tsaClient">the Timestamp client</param>
            <param name="estimatedSize">the reserved size for the signature. It will be estimated if 0</param>
            <param name="sigtype">Either Signature.CMS or Signature.CADES</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SignDetached(iText.Signatures.IExternalDigest,iText.Signatures.IExternalSignature,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.ICollection{iText.Signatures.ICrlClient},iText.Signatures.IOcspClient,iText.Signatures.ITSAClient,System.Int32,iText.Signatures.PdfSigner.CryptoStandard,iText.Signatures.SignaturePolicyInfo)">
            <summary>Signs the document using the detached mode, CMS or CAdES equivalent.</summary>
            <remarks>
            Signs the document using the detached mode, CMS or CAdES equivalent.
            <br /><br />
            NOTE: This method closes the underlying pdf document. This means, that current instance
            of PdfSigner cannot be used after this method call.
            </remarks>
            <param name="externalSignature">the interface providing the actual signing</param>
            <param name="chain">the certificate chain</param>
            <param name="crlList">the CRL list</param>
            <param name="ocspClient">the OCSP client</param>
            <param name="tsaClient">the Timestamp client</param>
            <param name="externalDigest">an implementation that provides the digest</param>
            <param name="estimatedSize">the reserved size for the signature. It will be estimated if 0</param>
            <param name="sigtype">Either Signature.CMS or Signature.CADES</param>
            <param name="signaturePolicy">the signature policy (for EPES signatures)</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SignDetached(iText.Signatures.IExternalSignature,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.ICollection{iText.Signatures.ICrlClient},iText.Signatures.IOcspClient,iText.Signatures.ITSAClient,System.Int32,iText.Signatures.PdfSigner.CryptoStandard,iText.Signatures.SignaturePolicyInfo)">
            <summary>Signs the document using the detached mode, CMS or CAdES equivalent.</summary>
            <remarks>
            Signs the document using the detached mode, CMS or CAdES equivalent.
            <br /><br />
            NOTE: This method closes the underlying pdf document. This means, that current instance
            of PdfSigner cannot be used after this method call.
            </remarks>
            <param name="externalSignature">the interface providing the actual signing</param>
            <param name="chain">the certificate chain</param>
            <param name="crlList">the CRL list</param>
            <param name="ocspClient">the OCSP client</param>
            <param name="tsaClient">the Timestamp client</param>
            <param name="estimatedSize">the reserved size for the signature. It will be estimated if 0</param>
            <param name="sigtype">Either Signature.CMS or Signature.CADES</param>
            <param name="signaturePolicy">the signature policy (for EPES signatures)</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SignDetached(iText.Signatures.IExternalSignature,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.ICollection{iText.Signatures.ICrlClient},iText.Signatures.IOcspClient,iText.Signatures.ITSAClient,System.Int32,iText.Signatures.PdfSigner.CryptoStandard,iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyIdentifier)">
            <summary>Signs the document using the detached mode, CMS or CAdES equivalent.</summary>
            <remarks>
            Signs the document using the detached mode, CMS or CAdES equivalent.
            <br /><br />
            NOTE: This method closes the underlying pdf document. This means, that current instance
            of PdfSigner cannot be used after this method call.
            </remarks>
            <param name="externalSignature">the interface providing the actual signing</param>
            <param name="chain">the certificate chain</param>
            <param name="crlList">the CRL list</param>
            <param name="ocspClient">the OCSP client</param>
            <param name="tsaClient">the Timestamp client</param>
            <param name="estimatedSize">the reserved size for the signature. It will be estimated if 0</param>
            <param name="sigtype">Either Signature.CMS or Signature.CADES</param>
            <param name="signaturePolicy">the signature policy (for EPES signatures)</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SignDetached(iText.Signatures.IExternalDigest,iText.Signatures.IExternalSignature,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.Collections.Generic.ICollection{iText.Signatures.ICrlClient},iText.Signatures.IOcspClient,iText.Signatures.ITSAClient,System.Int32,iText.Signatures.PdfSigner.CryptoStandard,iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyIdentifier)">
            <summary>Signs the document using the detached mode, CMS or CAdES equivalent.</summary>
            <remarks>
            Signs the document using the detached mode, CMS or CAdES equivalent.
            <br /><br />
            NOTE: This method closes the underlying pdf document. This means, that current instance
            of PdfSigner cannot be used after this method call.
            </remarks>
            <param name="externalSignature">the interface providing the actual signing</param>
            <param name="chain">the certificate chain</param>
            <param name="crlList">the CRL list</param>
            <param name="ocspClient">the OCSP client</param>
            <param name="tsaClient">the Timestamp client</param>
            <param name="externalDigest">an implementation that provides the digest</param>
            <param name="estimatedSize">the reserved size for the signature. It will be estimated if 0</param>
            <param name="sigtype">Either Signature.CMS or Signature.CADES</param>
            <param name="signaturePolicy">the signature policy (for EPES signatures)</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SignExternalContainer(iText.Signatures.IExternalSignatureContainer,System.Int32)">
            <summary>Sign the document using an external container, usually a PKCS7.</summary>
            <remarks>
            Sign the document using an external container, usually a PKCS7. The signature is fully composed
            externally, iText will just put the container inside the document.
            <br /><br />
            NOTE: This method closes the underlying pdf document. This means, that current instance
            of PdfSigner cannot be used after this method call.
            </remarks>
            <param name="externalSignatureContainer">the interface providing the actual signing</param>
            <param name="estimatedSize">the reserved size for the signature</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.Timestamp(iText.Signatures.ITSAClient,System.String)">
            <summary>Signs a document with a PAdES-LTV Timestamp.</summary>
            <remarks>
            Signs a document with a PAdES-LTV Timestamp. The document is closed at the end.
            <br /><br />
            NOTE: This method closes the underlying pdf document. This means, that current instance
            of PdfSigner cannot be used after this method call.
            </remarks>
            <param name="tsa">the timestamp generator</param>
            <param name="signatureName">
            the signature name or null to have a name generated
            automatically
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.SignDeferred(iText.Kernel.Pdf.PdfDocument,System.String,System.IO.Stream,iText.Signatures.IExternalSignatureContainer)">
            <summary>Signs a PDF where space was already reserved.</summary>
            <param name="document">the original PDF</param>
            <param name="fieldName">the field to sign. It must be the last field</param>
            <param name="outs">the output PDF</param>
            <param name="externalSignatureContainer">
            the signature container doing the actual signing. Only the
            method ExternalSignatureContainer.sign is used
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.ProcessCrl(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.Collections.Generic.ICollection{iText.Signatures.ICrlClient})">
            <summary>Processes a CRL list.</summary>
            <param name="cert">a Certificate if one of the CrlList implementations needs to retrieve the CRL URL from it.
                </param>
            <param name="crlList">a list of CrlClient implementations</param>
            <returns>a collection of CRL bytes that can be embedded in a PDF</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.AddDeveloperExtension(iText.Kernel.Pdf.PdfDeveloperExtension)">
            <summary>
            Add developer extension to the current
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            </summary>
            <param name="extension">
            
            <see cref="T:iText.Kernel.Pdf.PdfDeveloperExtension"/>
            to be added
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.IsPreClosed">
            <summary>Checks if the document is in the process of closing.</summary>
            <returns>true if the document is in the process of closing, false otherwise</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.PreClose(System.Collections.Generic.IDictionary{iText.Kernel.Pdf.PdfName,System.Nullable{System.Int32}})">
            <summary>This is the first method to be called when using external signatures.</summary>
            <remarks>
            This is the first method to be called when using external signatures. The general sequence is:
            preClose(), getDocumentBytes() and close().
            <para />
            <c>exclusionSizes</c> must contain at least
            the <c>PdfName.CONTENTS</c> key with the size that it will take in the
            document. Note that due to the hex string coding this size should be byte_size*2+2.
            </remarks>
            <param name="exclusionSizes">
            Map with names and sizes to be excluded in the signature
            calculation. The key is a PdfName and the value an Integer.
            At least the /Contents must be present
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.PopulateExistingSignatureFormField(iText.Forms.PdfAcroForm)">
            <summary>Populates already existing signature form field in the acroForm object.</summary>
            <remarks>
            Populates already existing signature form field in the acroForm object.
            This method is called during the
            <see cref="!:PreClose(System.Collections.Generic.IDictionary&lt;K, V&gt;)"/>
            method if the signature field already exists.
            </remarks>
            <param name="acroForm">
            
            <see cref="T:iText.Forms.PdfAcroForm"/>
            object in which the signature field will be populated
            </param>
            <returns>signature field lock dictionary</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.CreateNewSignatureFormField(iText.Forms.PdfAcroForm,System.String)">
            <summary>Creates new signature form field and adds it to the acroForm object.</summary>
            <remarks>
            Creates new signature form field and adds it to the acroForm object.
            This method is called during the
            <see cref="!:PreClose(System.Collections.Generic.IDictionary&lt;K, V&gt;)"/>
            method if the signature field doesn't exist.
            </remarks>
            <param name="acroForm">
            
            <see cref="T:iText.Forms.PdfAcroForm"/>
            object in which new signature field will be added
            </param>
            <param name="name">the name of the field</param>
            <returns>signature field lock dictionary</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetRangeStream">
            <summary>Gets the document bytes that are hashable when using external signatures.</summary>
            <remarks>
            Gets the document bytes that are hashable when using external signatures.
            The general sequence is:
            <see cref="!:PreClose(System.Collections.Generic.IDictionary&lt;K, V&gt;)"/>
            ,
            <see cref="M:iText.Signatures.PdfSigner.GetRangeStream"/>
            and
            <see cref="M:iText.Signatures.PdfSigner.Close(iText.Kernel.Pdf.PdfDictionary)"/>.
            </remarks>
            <returns>
            The
            <see cref="T:System.IO.Stream"/>
            of bytes to be signed.
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.Close(iText.Kernel.Pdf.PdfDictionary)">
            <summary>This is the last method to be called when using external signatures.</summary>
            <remarks>
            This is the last method to be called when using external signatures. The general sequence is:
            preClose(), getDocumentBytes() and close().
            <para />
            update is a PdfDictionary that must have exactly the
            same keys as the ones provided in
            <see cref="!:PreClose(System.Collections.Generic.IDictionary&lt;K, V&gt;)"/>.
            </remarks>
            <param name="update">
            a PdfDictionary with the key/value that will fill the holes defined
            in
            <see cref="!:PreClose(System.Collections.Generic.IDictionary&lt;K, V&gt;)"/>
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetUnderlyingSource">
            <summary>Returns the underlying source.</summary>
            <returns>the underlying source</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.AddDocMDP(iText.Signatures.PdfSignature)">
            <summary>Adds keys to the signature dictionary that define the certification level and the permissions.</summary>
            <remarks>
            Adds keys to the signature dictionary that define the certification level and the permissions.
            This method is only used for Certifying signatures.
            </remarks>
            <param name="crypto">the signature dictionary</param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.AddFieldMDP(iText.Signatures.PdfSignature,iText.Forms.PdfSigFieldLock)">
            <summary>Adds keys to the signature dictionary that define the field permissions.</summary>
            <remarks>
            Adds keys to the signature dictionary that define the field permissions.
            This method is only used for signatures that lock fields.
            </remarks>
            <param name="crypto">the signature dictionary</param>
            <param name="fieldLock">
            the
            <see cref="T:iText.Forms.PdfSigFieldLock"/>
            instance specified the field lock to be set
            </param>
        </member>
        <member name="M:iText.Signatures.PdfSigner.DocumentContainsCertificationOrApprovalSignatures">
            <summary>Check if current document instance already contains certification or approval signatures.</summary>
            <returns>
            
            <see langword="true"/>
            if document contains certification or approval signatures,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetWidgetRectangle(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation)">
            <summary>Get the rectangle associated to the provided widget.</summary>
            <param name="widget">PdfWidgetAnnotation to extract the rectangle from</param>
            <returns>Rectangle</returns>
        </member>
        <member name="M:iText.Signatures.PdfSigner.GetWidgetPageNumber(iText.Kernel.Pdf.Annot.PdfWidgetAnnotation)">
            <summary>Get the page number associated to the provided widget.</summary>
            <param name="widget">PdfWidgetAnnotation from which to extract the page number</param>
            <returns>page number</returns>
        </member>
        <member name="T:iText.Signatures.PdfSigner.ISignatureEvent">
            <summary>An interface to retrieve the signature dictionary for modification.</summary>
        </member>
        <member name="M:iText.Signatures.PdfSigner.ISignatureEvent.GetSignatureDictionary(iText.Signatures.PdfSignature)">
            <summary>Allows modification of the signature dictionary.</summary>
            <param name="sig">The signature dictionary</param>
        </member>
        <member name="T:iText.Signatures.PdfTwoPhaseSigner">
            <summary>
            Class that prepares document and adds the signature to it while performing signing operation in two steps
            (see
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            for more info).
            </summary>
            <remarks>
            Class that prepares document and adds the signature to it while performing signing operation in two steps
            (see
            <see cref="T:iText.Signatures.PadesTwoPhaseSigningHelper"/>
            for more info).
            <para />
            Firstly, this class allows to prepare the document for signing and calculate the document digest to sign.
            Secondly, it adds an existing signature to a PDF where space was already reserved.
            </remarks>
        </member>
        <member name="M:iText.Signatures.PdfTwoPhaseSigner.#ctor(iText.Kernel.Pdf.PdfReader,System.IO.Stream)">
            <summary>
            Creates new
            <see cref="T:iText.Signatures.PdfTwoPhaseSigner"/>
            instance.
            </summary>
            <param name="reader">
            
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            instance to read the original PDF file
            </param>
            <param name="outputStream">
            
            <see cref="T:System.IO.Stream"/>
            output stream to write the resulting PDF file into
            </param>
        </member>
        <member name="M:iText.Signatures.PdfTwoPhaseSigner.PrepareDocumentForSignature(iText.Signatures.SignerProperties,System.String,iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.PdfName,System.Int32,System.Boolean)">
            <summary>Prepares document for signing, calculates the document digest to sign and closes the document.</summary>
            <param name="signerProperties">
            
            <see cref="T:iText.Signatures.SignerProperties"/>
            properties to be used for main signing operation
            </param>
            <param name="digestAlgorithm">the algorithm to generate the digest with</param>
            <param name="filter">PdfName of the signature handler to use when validating this signature</param>
            <param name="subFilter">PdfName that describes the encoding of the signature</param>
            <param name="estimatedSize">
            the estimated size of the signature, this is the size of the space reserved for
            the Cryptographic Message Container
            </param>
            <param name="includeDate">specifies if the signing date should be set to the signature dictionary</param>
            <returns>the message digest of the prepared document.</returns>
        </member>
        <member name="M:iText.Signatures.PdfTwoPhaseSigner.AddSignatureToPreparedDocument(iText.Kernel.Pdf.PdfDocument,System.String,System.IO.Stream,iText.Signatures.Cms.CMSContainer)">
            <summary>Adds an existing signature to a PDF where space was already reserved.</summary>
            <param name="document">the original PDF</param>
            <param name="fieldName">the field to sign. It must be the last field</param>
            <param name="outs">the output PDF</param>
            <param name="cmsContainer">the finalized CMS container</param>
        </member>
        <member name="M:iText.Signatures.PdfTwoPhaseSigner.AddSignatureToPreparedDocument(iText.Kernel.Pdf.PdfDocument,System.String,System.IO.Stream,System.Byte[])">
            <summary>Adds an existing signature to a PDF where space was already reserved.</summary>
            <param name="document">the original PDF</param>
            <param name="fieldName">the field to sign. It must be the last field</param>
            <param name="outs">the output PDF</param>
            <param name="signedContent">the bytes for the signed data</param>
        </member>
        <member name="M:iText.Signatures.PdfTwoPhaseSigner.SetExternalDigest(iText.Signatures.IExternalDigest)">
            <summary>Use the external digest to inject specific digest implementations</summary>
            <param name="externalDigest">the IExternalDigest instance to use to generate Digests</param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfTwoPhaseSigner"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PdfTwoPhaseSigner.SetStampingProperties(iText.Kernel.Pdf.StampingProperties)">
            <summary>Set stamping properties to be used during main signing operation.</summary>
            <remarks>
            Set stamping properties to be used during main signing operation.
            <para />
            If none is set, stamping properties with append mode enabled will be used
            </remarks>
            <param name="stampingProperties">
            
            <see cref="T:iText.Kernel.Pdf.StampingProperties"/>
            instance to be used during main signing operation
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.PdfTwoPhaseSigner"/>
            </returns>
        </member>
        <member name="T:iText.Signatures.PKCS7ExternalSignatureContainer">
            <summary>
            Implementation class for
            <see cref="T:iText.Signatures.IExternalSignatureContainer"/>.
            </summary>
            <remarks>
            Implementation class for
            <see cref="T:iText.Signatures.IExternalSignatureContainer"/>.
            This external signature container is implemented based on PCS7 standard and
            <see cref="T:iText.Signatures.PdfPKCS7"/>
            class.
            </remarks>
        </member>
        <member name="M:iText.Signatures.PKCS7ExternalSignatureContainer.#ctor(iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.String)">
            <summary>Creates an instance of PKCS7ExternalSignatureContainer</summary>
            <param name="privateKey">The private key to sign with</param>
            <param name="chain">The certificate chain</param>
            <param name="hashAlgorithm">The hash algorithm to use</param>
        </member>
        <member name="M:iText.Signatures.PKCS7ExternalSignatureContainer.Sign(System.IO.Stream)">
            <summary><inheritDoc/></summary>
            <param name="data">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Signatures.PKCS7ExternalSignatureContainer.ModifySigningDictionary(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
            <param name="signDic">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Signatures.PKCS7ExternalSignatureContainer.SetOcspClient(iText.Signatures.IOcspClient)">
            <summary>Set the OcspClient if you want revocation data collected trough Ocsp to be added to the signature
                </summary>
            <param name="ocspClient">the client to be used</param>
        </member>
        <member name="M:iText.Signatures.PKCS7ExternalSignatureContainer.SetCrlClient(iText.Signatures.ICrlClient)">
            <summary>Set the CrlClient if you want revocation data collected trough Crl to be added to the signature</summary>
            <param name="crlClient">the client to be used</param>
        </member>
        <member name="M:iText.Signatures.PKCS7ExternalSignatureContainer.SetTsaClient(iText.Signatures.ITSAClient)">
            <summary>Set the TsaClient if you want a TSA timestamp added to the signature</summary>
            <param name="tsaClient">the client to use</param>
        </member>
        <member name="M:iText.Signatures.PKCS7ExternalSignatureContainer.SetSignaturePolicy(iText.Signatures.SignaturePolicyInfo)">
            <summary>Set the signature policy if you want it to be added to the signature</summary>
            <param name="signaturePolicy">the signature to be set.</param>
        </member>
        <member name="M:iText.Signatures.PKCS7ExternalSignatureContainer.SetSignatureType(iText.Signatures.PdfSigner.CryptoStandard)">
            <summary>
            Set a custom signature type, default value
            <see cref="!:CryptoStandard.CMS"/>
            </summary>
            <param name="sigType">the type  of signature to be created</param>
        </member>
        <member name="T:iText.Signatures.PrivateKeySignature">
            <summary>
            Implementation of the
            <see cref="T:iText.Signatures.IExternalSignature"/>
            interface that
            can be used when you have a
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPrivateKey"/>
            object.
            </summary>
        </member>
        <member name="F:iText.Signatures.PrivateKeySignature.pk">
            <summary>The private key object.</summary>
        </member>
        <member name="F:iText.Signatures.PrivateKeySignature.hashAlgorithm">
            <summary>The hash algorithm.</summary>
        </member>
        <member name="F:iText.Signatures.PrivateKeySignature.signatureAlgorithm">
            <summary>The encryption algorithm (obtained from the private key)</summary>
        </member>
        <member name="F:iText.Signatures.PrivateKeySignature.parameters">
            <summary>The algorithm parameters.</summary>
        </member>
        <member name="M:iText.Signatures.PrivateKeySignature.#ctor(iText.Commons.Bouncycastle.Crypto.IPrivateKey,System.String)">
            <summary>
            Creates a
            <see cref="T:iText.Signatures.PrivateKeySignature"/>
            instance.
            </summary>
            <param name="pk">
            A
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPrivateKey"/>
            object.
            </param>
            <param name="hashAlgorithm">A hash algorithm (e.g. "SHA-1", "SHA-256",...).</param>
            <param name="provider">A security provider (e.g. "BC").</param>
        </member>
        <member name="M:iText.Signatures.PrivateKeySignature.#ctor(iText.Commons.Bouncycastle.Crypto.IPrivateKey,System.String,System.String,iText.Signatures.IApplicableSignatureParams)">
            <summary>
            Creates a
            <see cref="T:iText.Signatures.PrivateKeySignature"/>
            instance.
            </summary>
            <param name="pk">
            A
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPrivateKey"/>
            object.
            </param>
            <param name="hashAlgorithm">A hash algorithm (e.g. "SHA-1", "SHA-256",...).</param>
            <param name="signatureAlgorithm">
            A signiture algorithm (e.g. "RSASSA-PSS", "id-signedData",
            "sha256WithRSAEncryption", ...)
            </param>
            <param name="provider">A security provider (e.g. "BC").</param>
            <param name="params">Parameters for using RSASSA-PSS or other algorithms requiring them.</param>
        </member>
        <member name="M:iText.Signatures.PrivateKeySignature.GetDigestAlgorithmName">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Signatures.PrivateKeySignature.GetSignatureAlgorithmName">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Signatures.PrivateKeySignature.GetSignatureMechanismParameters">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Signatures.PrivateKeySignature.Sign(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Signatures.RootStoreVerifier">
            <summary>
            Verifies a certificate against a <c>KeyStore</c>
            containing trusted anchors.
            </summary>
        </member>
        <member name="F:iText.Signatures.RootStoreVerifier.rootStore">
            <summary>A key store against which certificates can be verified.</summary>
        </member>
        <member name="M:iText.Signatures.RootStoreVerifier.#ctor(iText.Signatures.CertificateVerifier)">
            <summary>Creates a RootStoreVerifier in a chain of verifiers.</summary>
            <param name="verifier">the next verifier in the chain</param>
        </member>
        <member name="M:iText.Signatures.RootStoreVerifier.SetRootStore(System.Collections.Generic.List{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Sets the Key Store against which a certificate can be checked.</summary>
            <param name="keyStore">a root store</param>
        </member>
        <member name="M:iText.Signatures.RootStoreVerifier.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Verifies a single certificate against a key store (if present).</summary>
            <param name="signCert">the certificate to verify</param>
            <param name="issuerCert">the issuer certificate</param>
            <param name="signDate">the date the certificate needs to be valid</param>
            <returns>
            a list of <c>VerificationOK</c> objects.
            The list will be empty if the certificate couldn't be verified.
            </returns>
        </member>
        <member name="T:iText.Signatures.RSASSAPSSMechanismParams">
            <summary>Encode the signer's parameters for producing an RSASSA-PSS signature.</summary>
            <remarks>
            Encode the signer's parameters for producing an RSASSA-PSS signature. Note that this class
            is intended for use in the signing process only, so it does not need to be able to represent all possible
            parameter configurations; only the ones we consider reasonable. For the purposes of this class,
            the mask generation function is always MGF1, and the associated digest function is the same as the digest
            function used in the signing process.
            </remarks>
        </member>
        <member name="F:iText.Signatures.RSASSAPSSMechanismParams.DEFAULT_TRAILER_FIELD">
            <summary>Default value of the trailer field parameter.</summary>
        </member>
        <member name="M:iText.Signatures.RSASSAPSSMechanismParams.#ctor(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Int32,System.Int32)">
            <summary>
            Instantiate RSASSA-PSS parameters with MGF1 for a given digest algorithm OID, salt length
            and trailer field value.
            </summary>
            <param name="digestAlgoOid">the digest algorithm OID that will be used for both the digest and MGF</param>
            <param name="saltLen">the salt length</param>
            <param name="trailerField">the trailer field</param>
        </member>
        <member name="M:iText.Signatures.RSASSAPSSMechanismParams.CreateForDigestAlgorithm(System.String)">
            <summary>Instantiate RSASSA-PSS parameters with MGF1 for the given algorithm name.</summary>
            <param name="digestAlgorithmName">the name of the digest algorithm</param>
        </member>
        <member name="M:iText.Signatures.RSASSAPSSMechanismParams.ToEncodable">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Signatures.RSASSAPSSMechanismParams.Apply(iText.Commons.Bouncycastle.Crypto.ISigner)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Signatures.SecurityIDs">
            <summary>A list of IDs that are used by the security classes</summary>
        </member>
        <member name="T:iText.Signatures.SignatureMechanisms">
            <summary>
            Class that contains OID mappings to extract a signature algorithm name
            from a signature mechanism OID, and conversely, to retrieve the appropriate
            signature mechanism OID given a signature algorithm and a digest function.
            </summary>
        </member>
        <member name="F:iText.Signatures.SignatureMechanisms.algorithmNames">
            <summary>Maps IDs of signature algorithms with its human-readable name.</summary>
        </member>
        <member name="M:iText.Signatures.SignatureMechanisms.GetSignatureMechanismOid(System.String,System.String)">
            <summary>Attempt to look up the most specific OID for a given signature-digest combination.</summary>
            <param name="signatureAlgorithmName">the name of the signature algorithm</param>
            <param name="digestAlgorithmName">the name of the digest algorithm, if any</param>
            <returns>
            an OID string, or
            <see langword="null"/>
            if none was found.
            </returns>
        </member>
        <member name="M:iText.Signatures.SignatureMechanisms.GetAlgorithm(System.String)">
            <summary>Gets the algorithm name for a certain id.</summary>
            <param name="oid">an id (for instance "1.2.840.113549.1.1.1")</param>
            <returns>an algorithm name (for instance "RSA")</returns>
        </member>
        <member name="M:iText.Signatures.SignatureMechanisms.GetMechanism(System.String,System.String)">
            <summary>Get the signing mechanism name for a certain id and digest.</summary>
            <param name="oid">an id of an algorithm</param>
            <param name="digest">digest of an algorithm</param>
            <returns>name of the mechanism</returns>
        </member>
        <member name="T:iText.Signatures.SignaturePermissions">
            <summary>
            A helper class that tells you more about the type of signature
            (certification or approval) and the signature's DMP settings.
            </summary>
        </member>
        <member name="T:iText.Signatures.SignaturePermissions.FieldLock">
            <summary>
            Class that contains a field lock action and
            an array of the fields that are involved.
            </summary>
        </member>
        <member name="F:iText.Signatures.SignaturePermissions.FieldLock.action">
            <summary>Can be /All, /Exclude or /Include</summary>
        </member>
        <member name="F:iText.Signatures.SignaturePermissions.FieldLock.fields">
            <summary>An array of PdfString values with fieldnames</summary>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.FieldLock.#ctor(iText.Signatures.SignaturePermissions,iText.Kernel.Pdf.PdfName,iText.Kernel.Pdf.PdfArray)">
            <summary>Creates a FieldLock instance.</summary>
            <param name="action">indicates the set of fields that should be locked</param>
            <param name="fields">an array of text strings containing field names</param>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.FieldLock.GetAction">
            <summary>Getter for the field lock action.</summary>
            <returns>the action of field lock dictionary</returns>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.FieldLock.GetFields">
            <summary>Getter for the fields involved in the lock action.</summary>
            <returns>the fields of field lock dictionary</returns>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.FieldLock.ToString">
            <summary>toString method</summary>
        </member>
        <member name="F:iText.Signatures.SignaturePermissions.certification">
            <summary>Is the signature a cerification signature (true) or an approval signature (false)?</summary>
        </member>
        <member name="F:iText.Signatures.SignaturePermissions.fillInAllowed">
            <summary>Is form filling allowed by this signature?</summary>
        </member>
        <member name="F:iText.Signatures.SignaturePermissions.annotationsAllowed">
            <summary>Is adding annotations allowed by this signature?</summary>
        </member>
        <member name="F:iText.Signatures.SignaturePermissions.fieldLocks">
            <summary>Does this signature lock specific fields?</summary>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.#ctor(iText.Kernel.Pdf.PdfDictionary,iText.Signatures.SignaturePermissions)">
            <summary>
            Creates an object that can inform you about the type of signature
            in a signature dictionary as well as some of the permissions
            defined by the signature.
            </summary>
            <param name="sigDict">the signature dictionary</param>
            <param name="previous">the signature permissions</param>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.IsCertification">
            <summary>Getter to find out if the signature is a certification signature.</summary>
            <returns>true if the signature is a certification signature, false for an approval signature.</returns>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.IsFillInAllowed">
            <summary>Getter to find out if filling out fields is allowed after signing.</summary>
            <returns>true if filling out fields is allowed</returns>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.IsAnnotationsAllowed">
            <summary>Getter to find out if adding annotations is allowed after signing.</summary>
            <returns>true if adding annotations is allowed</returns>
        </member>
        <member name="M:iText.Signatures.SignaturePermissions.GetFieldLocks">
            <summary>Getter for the field lock actions, and fields that are impacted by the action</summary>
            <returns>an Array with field names</returns>
        </member>
        <member name="T:iText.Signatures.SignaturePolicyInfo">
            <summary>Class that encapsulates the signature policy information</summary>
            <remarks>
            Class that encapsulates the signature policy information
            <para />
            Sample:
            <para />
            SignaturePolicyInfo spi = new SignaturePolicyInfo("2.16.724.1.3.1.1.2.1.9",
            "G7roucf600+f03r/o0bAOQ6WAs0=", "SHA-1", "https://sede.060.gob.es/politica_de_firma_anexo_1.pdf");
            </remarks>
        </member>
        <member name="M:iText.Signatures.SignaturePolicyInfo.#ctor(System.String,System.Byte[],System.String,System.String)">
            <summary>
            Constructs a new
            <see cref="T:iText.Signatures.SignaturePolicyInfo"/>
            instance
            </summary>
            <param name="policyIdentifier">the id of the signature policy</param>
            <param name="policyHash">the hash of the signature policy</param>
            <param name="policyDigestAlgorithm">the digestion algorithm of the signature policy</param>
            <param name="policyUri">the uri of the full policy description</param>
        </member>
        <member name="M:iText.Signatures.SignaturePolicyInfo.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Constructs a new
            <see cref="T:iText.Signatures.SignaturePolicyInfo"/>
            instance
            </summary>
            <param name="policyIdentifier">the id of the signature policy</param>
            <param name="policyHashBase64">the Base64 presentation of the hash of the signature policy</param>
            <param name="policyDigestAlgorithm">the digestion algorithm of the signature policy</param>
            <param name="policyUri">the uri of the full policy description</param>
        </member>
        <member name="M:iText.Signatures.SignaturePolicyInfo.GetPolicyIdentifier">
            <summary>Get the ID of the signature policy.</summary>
            <returns>the ID of the signature policy</returns>
        </member>
        <member name="M:iText.Signatures.SignaturePolicyInfo.GetPolicyHash">
            <summary>Get the hash of the signature policy.</summary>
            <returns>the hash of the signature policy</returns>
        </member>
        <member name="M:iText.Signatures.SignaturePolicyInfo.GetPolicyDigestAlgorithm">
            <summary>Get the digestion algorithm of the signature policy.</summary>
            <returns>the digestion algorithm of the signature policy</returns>
        </member>
        <member name="M:iText.Signatures.SignaturePolicyInfo.GetPolicyUri">
            <summary>Get the uri of the full policy description.</summary>
            <returns>the uri of the full policy description</returns>
        </member>
        <member name="T:iText.Signatures.SignatureUtil">
            <summary>Utility class that provides several convenience methods concerning digital signatures.</summary>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>Creates a SignatureUtil instance.</summary>
            <remarks>
            Creates a SignatureUtil instance. Sets the acroForm field to the acroForm in the PdfDocument.
            iText will create a new AcroForm if the PdfDocument doesn't contain one.
            </remarks>
            <param name="document">PdfDocument to be inspected</param>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.ReadSignatureData(System.String)">
            <summary>
            Prepares an
            <see cref="T:iText.Signatures.PdfPKCS7"/>
            instance for the given signature.
            </summary>
            <remarks>
            Prepares an
            <see cref="T:iText.Signatures.PdfPKCS7"/>
            instance for the given signature.
            This method handles signature parsing and might throw an exception if
            signature is malformed.
            <para />
            The returned
            <see cref="T:iText.Signatures.PdfPKCS7"/>
            can be used to fetch additional info about the signature
            and also to perform integrity check of data signed by the given signature field.
            <para />
            Prepared
            <see cref="T:iText.Signatures.PdfPKCS7"/>
            instance calculates digest based on signature's /ByteRange entry.
            In order to check that /ByteRange is properly defined and given signature indeed covers the current PDF document
            revision please use
            <see cref="M:iText.Signatures.SignatureUtil.SignatureCoversWholeDocument(System.String)"/>
            method.
            </remarks>
            <param name="signatureFieldName">the signature field name</param>
            <returns>
            a
            <see cref="T:iText.Signatures.PdfPKCS7"/>
            instance which can be used to fetch additional info about the signature
            and also to perform integrity check of data signed by the given signature field.
            </returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.GetSignature(System.String)">
            <summary>
            Get
            <see cref="T:iText.Signatures.PdfSignature"/>
            dictionary based on the provided name.
            </summary>
            <param name="name">signature name</param>
            <returns>
            
            <see cref="T:iText.Signatures.PdfSignature"/>
            instance corresponding to the provided name.
            <see langword="null"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.GetSignatureDictionary(System.String)">
            <summary>Gets the signature dictionary, the one keyed by /V.</summary>
            <param name="name">the field name</param>
            <returns>
            the signature dictionary keyed by /V or <c>null</c> if the field is not
            a signature
            </returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.GetSignatureNames">
            <summary>Gets the field names that have signatures and are signed.</summary>
            <returns>List containing the field names that have signatures and are signed</returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.GetBlankSignatureNames">
            <summary>Gets the field names that have blank signatures.</summary>
            <returns>List containing the field names that have blank signatures</returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.GetTotalRevisions">
            <summary>Get the amount of signed document revisions.</summary>
            <returns>
            
            <c>int</c>
            amount of signed document revisions
            </returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.GetRevision(System.String)">
            <summary>Get signed document revision number, which corresponds to the provided signature name.</summary>
            <param name="field">signature name</param>
            <returns>
            
            <c>int</c>
            revision number
            </returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.GetTranslatedFieldName(System.String)">
            <summary>Get field name, translated using XFA, if any present in the document.</summary>
            <param name="name">field name to be translated</param>
            <returns>translated field name if XFA is present, original name otherwise</returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.ExtractRevision(System.String)">
            <summary>Extracts a revision from the document.</summary>
            <param name="field">the signature field name</param>
            <returns>an InputStream covering the revision. Returns null if it's not a signature field</returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.SignatureCoversWholeDocument(System.String)">
            <summary>Checks if the signature covers the entire document (except for signature's Contents) or just a part of it.
                </summary>
            <remarks>
            Checks if the signature covers the entire document (except for signature's Contents) or just a part of it.
            <para />
            If this method does not return
            <see langword="true"/>
            it means that signature in question does not cover the entire
            contents of current
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            . Such signatures cannot be considered as verifying the PDF document,
            because content that is not covered by signature might have been modified since the signature creation.
            <para />
            </remarks>
            <param name="name">the signature field name</param>
            <returns>true if the signature covers the entire document, false if it doesn't</returns>
        </member>
        <member name="M:iText.Signatures.SignatureUtil.DoesSignatureFieldExist(System.String)">
            <summary>Checks whether a name exists as a signature field or not.</summary>
            <remarks>Checks whether a name exists as a signature field or not. It checks both signed fields and blank signatures.
                </remarks>
            <param name="name">name of the field</param>
            <returns>boolean does the signature field exist</returns>
        </member>
        <member name="T:iText.Signatures.SignerProperties">
            <summary>Properties to be used in signing operations.</summary>
        </member>
        <member name="M:iText.Signatures.SignerProperties.#ctor">
            <summary>
            Create instance of
            <see cref="T:iText.Signatures.SignerProperties"/>.
            </summary>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetSignDate">
            <summary>Gets the signature date.</summary>
            <returns>Calendar set to the signature date.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetSignDate(System.DateTime)">
            <summary>Sets the signature date.</summary>
            <param name="signDate">the signature date.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetSignatureAppearance(iText.Forms.Form.Element.SignatureFieldAppearance)">
            <summary>Sets the signature field layout element to customize the appearance of the signature.</summary>
            <remarks>
            Sets the signature field layout element to customize the appearance of the signature. Signer's sign date will
            be set.
            </remarks>
            <param name="appearance">
            the
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            layout element.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetSignatureAppearance">
            <summary>Gets signature field layout element, which customizes the appearance of a signature.</summary>
            <returns>
            
            <see cref="T:iText.Forms.Form.Element.SignatureFieldAppearance"/>
            layout element.
            </returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetCertificationLevel">
            <summary>Returns the document's certification level.</summary>
            <remarks>
            Returns the document's certification level.
            For possible values see
            <see cref="M:iText.Signatures.SignerProperties.SetCertificationLevel(System.Int32)"/>.
            </remarks>
            <returns>The certified status.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetCertificationLevel(System.Int32)">
            <summary>Sets the document's certification level.</summary>
            <param name="certificationLevel">
            a new certification level for a document.
            Possible values are: <list type="bullet">
            <item><description>
            <see cref="F:iText.Signatures.PdfSigner.NOT_CERTIFIED"/>
            </description></item>
            <item><description>
            <see cref="F:iText.Signatures.PdfSigner.CERTIFIED_NO_CHANGES_ALLOWED"/>
            </description></item>
            <item><description>
            <see cref="F:iText.Signatures.PdfSigner.CERTIFIED_FORM_FILLING"/>
            </description></item>
            <item><description>
            <see cref="F:iText.Signatures.PdfSigner.CERTIFIED_FORM_FILLING_AND_ANNOTATIONS"/>
            </description></item>
            </list>
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetFieldName">
            <summary>Gets the field name.</summary>
            <returns>the field name.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetFieldName(System.String)">
            <summary>Sets the name indicating the field to be signed.</summary>
            <remarks>
            Sets the name indicating the field to be signed. The field can already be presented in the
            document but shall not be signed. If the field is not presented in the document, it will be created.
            </remarks>
            <param name="fieldName">The name indicating the field to be signed.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetPageNumber">
            <summary>
            Provides the page number of the signature field which this signature
            appearance is associated with.
            </summary>
            <returns>
            The page number of the signature field which this signature
            appearance is associated with.
            </returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetPageNumber(System.Int32)">
            <summary>
            Sets the page number of the signature field which this signature
            appearance is associated with.
            </summary>
            <remarks>
            Sets the page number of the signature field which this signature
            appearance is associated with. Implicitly calls
            <see cref="M:iText.Signatures.PdfSigner.SetPageRect(iText.Kernel.Geom.Rectangle)"/>
            which considers page number to process the rectangle correctly.
            </remarks>
            <param name="pageNumber">
            The page number of the signature field which
            this signature appearance is associated with.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetPageRect">
            <summary>
            Provides the rectangle that represent the position and dimension
            of the signature field in the page.
            </summary>
            <returns>
            the rectangle that represent the position and dimension
            of the signature field in the page
            </returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetPageRect(iText.Kernel.Geom.Rectangle)">
            <summary>
            Sets the rectangle that represent the position and dimension of
            the signature field in the page.
            </summary>
            <param name="pageRect">
            The rectangle that represents the position and
            dimension of the signature field in the page.
            </param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetFieldLockDict">
            <summary>Getter for the field lock dictionary.</summary>
            <returns>Field lock dictionary.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetFieldLockDict(iText.Forms.PdfSigFieldLock)">
            <summary>Setter for the field lock dictionary.</summary>
            <remarks>
            Setter for the field lock dictionary.
            <para />
            <strong>Be aware:</strong> if a signature is created on an existing signature field,
            then its /Lock dictionary takes the precedence (if it exists).
            </remarks>
            <param name="fieldLock">Field lock dictionary.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetSignatureCreator">
            <summary>Returns the signature creator.</summary>
            <returns>The signature creator.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetSignatureCreator(System.String)">
            <summary>Sets the name of the application used to create the signature.</summary>
            <param name="signatureCreator">A new name of the application signing a document.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetContact">
            <summary>Returns the signing contact.</summary>
            <returns>The signing contact.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetContact(System.String)">
            <summary>Sets the signing contact.</summary>
            <param name="contact">A new signing contact.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetReason">
            <summary>Returns the signing reason.</summary>
            <returns>The signing reason.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetReason(System.String)">
            <summary>Sets the signing reason.</summary>
            <param name="reason">A new signing reason.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.GetLocation">
            <summary>Returns the signing location.</summary>
            <returns>The signing location.</returns>
        </member>
        <member name="M:iText.Signatures.SignerProperties.SetLocation(System.String)">
            <summary>Sets the signing location.</summary>
            <param name="location">A new signing location.</param>
            <returns>this instance to support fluent interface.</returns>
        </member>
        <member name="M:iText.Signatures.SignUtils.ParseCrlFromStream(System.IO.Stream)">
            <summary>
            Parses a CRL from an input Stream.
            </summary>
            <param name="input">The input Stream holding the unparsed CRL.</param>
            <returns>The parsed CRL object.</returns>
        </member>
        <member name="M:iText.Signatures.SignUtils.HasUnsupportedCriticalExtension(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
             <summary>
             This behavior is different in Java and .NET, because in Java we use this two-step check:
             first via #hasUnsupportedCriticalExtension method, and then additionally allowing standard critical extensions;
             in .NET there's only second step. However, removing first step in Java can be a breaking change for some users
             and moreover we don't have any means of providing customization for unsupported extensions check as of right now.
            
             During major release I'd suggest changing java unsupported extensions check logic to the same as in .NET,
             but only if it is possible to customize this logic.
             </summary>
             <param name="cert"></param>
             <returns></returns>
             TODO DEVSIX-2634
        </member>
        <member name="T:iText.Signatures.TimestampConstants">
            <summary>
            Timestamp constants util class for internal usage only.
            </summary>
        </member>
        <member name="F:iText.Signatures.TimestampConstants.UNDEFINED_TIMESTAMP_DATE">
            <summary>The timestamp which is returned in case the signed document doesn't contain timestamp.</summary>
            <remarks>
            The timestamp which is returned in case the signed document doesn't contain timestamp.
            The constant's value is different in Java and .NET.
            </remarks>
        </member>
        <member name="T:iText.Signatures.TSAClientBouncyCastle">
            <summary>
            Time Stamp Authority Client interface implementation using Bouncy Castle
            org.bouncycastle.tsp package.
            </summary>
            <remarks>
            Time Stamp Authority Client interface implementation using Bouncy Castle
            org.bouncycastle.tsp package.
            <para />
            Created by Aiken Sam, 2006-11-15, refactored by Martin Brunecky, 07/15/2007
            for ease of subclassing.
            </remarks>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.DEFAULTHASHALGORITHM">
            <summary>The default value for the hash algorithm</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.DEFAULTTOKENSIZE">
            <summary>The default value for token size estimation.</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.LOGGER">
            <summary>The Logger instance.</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.tsaURL">
            <summary>URL of the Time Stamp Authority</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.tsaUsername">
            <summary>TSA Username</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.tsaPassword">
            <summary>TSA password</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.tsaInfo">
            <summary>An interface that allows you to inspect the timestamp info.</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.tokenSizeEstimate">
            <summary>Estimate of the received time stamp token</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.digestAlgorithm">
            <summary>Hash algorithm</summary>
        </member>
        <member name="F:iText.Signatures.TSAClientBouncyCastle.tsaReqPolicy">
            <summary>TSA request policy</summary>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.#ctor(System.String)">
            <summary>Creates an instance of a TSAClient that will use BouncyCastle.</summary>
            <param name="url">String - Time Stamp Authority URL (i.e. "http://tsatest1.digistamp.com/TSA")</param>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.#ctor(System.String,System.String,System.String)">
            <summary>Creates an instance of a TSAClient that will use BouncyCastle.</summary>
            <param name="url">String - Time Stamp Authority URL (i.e. "http://tsatest1.digistamp.com/TSA")</param>
            <param name="username">String - user(account) name</param>
            <param name="password">String - password</param>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.#ctor(System.String,System.String,System.String,System.Int32,System.String)">
            <summary>Constructor.</summary>
            <remarks>
            Constructor.
            Note the token size estimate is updated by each call, as the token
            size is not likely to change (as long as we call the same TSA using
            the same imprint length).
            </remarks>
            <param name="url">Time Stamp Authority URL (i.e. "http://tsatest1.digistamp.com/TSA")</param>
            <param name="username">user(account) name, optional</param>
            <param name="password">
            password, optional if used in combination with username, the credentials will be used in
            basic authentication. Use only in combination with a https url to ensure encryption
            </param>
            <param name="tokSzEstimate">estimated size of received time stamp token (DER encoded)</param>
            <param name="digestAlgorithm">is a hash algorithm</param>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.SetTSAInfo(iText.Signatures.ITSAInfoBouncyCastle)">
            <param name="tsaInfo">the tsaInfo to set</param>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.GetTokenSizeEstimate">
            <summary>Get the token size estimate.</summary>
            <remarks>
            Get the token size estimate.
            Returned value reflects the result of the last succesfull call, padded
            </remarks>
            <returns>an estimate of the token size</returns>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.GetTSAReqPolicy">
            <summary>Gets the TSA request policy that will be used when retrieving timestamp token.</summary>
            <returns>policy id, or <c>null</c> if not set</returns>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.SetTSAReqPolicy(System.String)">
            <summary>Sets the TSA request policy that will be used when retrieving timestamp token.</summary>
            <param name="tsaReqPolicy">policy id</param>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.GetMessageDigest">
            <summary>Gets the MessageDigest to digest the data imprint</summary>
            <returns>the digest algorithm name</returns>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.GetTimeStampToken(System.Byte[])">
            <summary>Get RFC 3161 timeStampToken.</summary>
            <remarks>
            Get RFC 3161 timeStampToken.
            Method may return null indicating that timestamp should be skipped.
            </remarks>
            <param name="imprint">data imprint to be time-stamped</param>
            <returns>encoded, TSA signed data of the timeStampToken</returns>
        </member>
        <member name="M:iText.Signatures.TSAClientBouncyCastle.GetTSAResponse(System.Byte[])">
            <summary>Get timestamp token - communications layer</summary>
            <param name="requestBytes">is a byte representation of TSA request</param>
            <returns>- byte[] - TSA response, raw bytes (RFC 3161 encoded)</returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.CertificateChainValidator">
            <summary>Validator class, which is expected to be used for certificates chain validation.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.CertificateChainValidator.#ctor(iText.Signatures.Validation.V1.ValidatorChainBuilder)">
            <summary>
            Create new instance of
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>.
            </summary>
            <param name="builder">
            See
            <see cref="T:iText.Signatures.Validation.V1.ValidatorChainBuilder"/>
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.CertificateChainValidator.AddCrlClient(iText.Signatures.ICrlClient)">
            <summary>
            Add
            <see cref="T:iText.Signatures.ICrlClient"/>
            to be used for CRL responses receiving.
            </summary>
            <param name="crlClient">
            
            <see cref="T:iText.Signatures.ICrlClient"/>
            to be used for CRL responses receiving
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.CertificateChainValidator.AddOcspClient(iText.Signatures.IOcspClient)">
            <summary>
            Add
            <see cref="T:iText.Signatures.IOcspClient"/>
            to be used for OCSP responses receiving.
            </summary>
            <param name="ocpsClient">
            
            <see cref="T:iText.Signatures.IOcspClient"/>
            to be used for OCSP responses receiving
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.CertificateChainValidator.ValidateCertificate(iText.Signatures.Validation.V1.Context.ValidationContext,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Validate given certificate using provided validation date and required extensions.</summary>
            <param name="context">the validation context in which to validate the certificate chain</param>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to be validated
            </param>
            <param name="validationDate">
            
            <see cref="T:System.DateTime"/>
            against which certificate is expected to be validated. Usually signing
            date
            </param>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ValidationReport"/>
            which contains detailed validation results.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.CertificateChainValidator.Validate(iText.Signatures.Validation.V1.Report.ValidationReport,iText.Signatures.Validation.V1.Context.ValidationContext,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Validate given certificate using provided validation date and required extensions.</summary>
            <remarks>
            Validate given certificate using provided validation date and required extensions.
            Result is added into provided report.
            </remarks>
            <param name="result">
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ValidationReport"/>
            which is populated with detailed validation results
            </param>
            <param name="context">the context in which to perform the validation</param>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to be validated
            </param>
            <param name="validationDate">
            
            <see cref="T:System.DateTime"/>
            against which certificate is expected to be validated. Usually signing
            date
            </param>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ValidationReport"/>
            which contains both provided and new validation results.
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Context.CertificateSource">
            <summary>This enum lists all possible contexts related to the certificate origin in which a validation may take place
                </summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.CertificateSource.CRL_ISSUER">
            <summary>The context while validating a CRL issuer certificate.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.CertificateSource.OCSP_ISSUER">
            <summary>The context while validating a OCSP issuer certificate that is neither trusted nor CA.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.CertificateSource.CERT_ISSUER">
            <summary>The context while validating a certificate issuer certificate.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.CertificateSource.SIGNER_CERT">
            <summary>The context while validating a signer certificate.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.CertificateSource.TRUSTED">
            <summary>A certificate that is on a trusted list.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.CertificateSource.TIMESTAMP">
            <summary>The context while validating a timestamp issuer certificate.</summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Context.CertificateSources">
            <summary>
            Container class, which contains set of single
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            values.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.CertificateSources.Of(iText.Signatures.Validation.V1.Context.CertificateSource,iText.Signatures.Validation.V1.Context.CertificateSource[])">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSources"/>
            container from several
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            values.
            </summary>
            <param name="first">an element that the set is to contain initially</param>
            <param name="rest">the remaining elements the set is to contain</param>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSources"/>
            container, containing provided elements
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.CertificateSources.All">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSources"/>
            containing all
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            values.
            </summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSources"/>
            container containing all
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            values
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.CertificateSources.ComplementOf(iText.Signatures.Validation.V1.Context.CertificateSources)">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSources"/>
            containing all the elements of this type
            that are not contained in the specified set.
            </summary>
            <param name="other">
            another
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSources"/>
            from whose complement to initialize this container
            </param>
            <returns>
            the complement of the specified
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSources"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.CertificateSources.GetSet">
            <summary>
            Gets encapsulated
            <see cref="T:iText.Commons.Utils.Collections.EnumSet`1"/>
            containing
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            elements.
            </summary>
            <returns>
            encapsulated
            <see cref="T:iText.Commons.Utils.Collections.EnumSet`1"/>
            containing
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            elements
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Context.TimeBasedContext">
            <summary>This enum is used for giving a perspective on a time period at which validation is happening.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.TimeBasedContext.HISTORICAL">
            <summary>The date used lies in the past.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.TimeBasedContext.PRESENT">
            <summary>The date used lies in the present or there is no date.</summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Context.TimeBasedContexts">
            <summary>
            Container class, which contains set of single
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            values.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.TimeBasedContexts.Of(iText.Signatures.Validation.V1.Context.TimeBasedContext,iText.Signatures.Validation.V1.Context.TimeBasedContext[])">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContexts"/>
            container from several
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            values.
            </summary>
            <param name="first">an element that the set is to contain initially</param>
            <param name="rest">the remaining elements the set is to contain</param>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContexts"/>
            container, containing provided elements
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.TimeBasedContexts.All">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContexts"/>
            containing all
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            values.
            </summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContexts"/>
            container containing all
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            values
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.TimeBasedContexts.ComplementOf(iText.Signatures.Validation.V1.Context.TimeBasedContexts)">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContexts"/>
            containing all the elements of this type
            that are not contained in the specified set.
            </summary>
            <param name="other">
            another
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContexts"/>
            from whose complement to initialize this container
            </param>
            <returns>
            the complement of the specified
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContexts"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.TimeBasedContexts.GetSet">
            <summary>
            Gets encapsulated
            <see cref="!:Java.Util.EnumSet&lt;E&gt;"/>
            containing
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            elements.
            </summary>
            <returns>
            encapsulated
            <see cref="!:Java.Util.EnumSet&lt;E&gt;"/>
            containing
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            elements
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Context.ValidationContext">
            <summary>Validation context class, which encapsulates specific context values, related to validation process.
                </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.#ctor(iText.Signatures.Validation.V1.Context.ValidatorContext,iText.Signatures.Validation.V1.Context.CertificateSource,iText.Signatures.Validation.V1.Context.TimeBasedContext)">
            <summary>
            Create
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            instance using provided context values.
            </summary>
            <param name="validatorContext">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            value
            </param>
            <param name="certificateSource">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            value
            </param>
            <param name="timeBasedContext">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            value
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.GetPreviousValidationContext">
            <summary>Get previous validation context instance, from which this instance was created.</summary>
            <returns>
            previous
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.GetCertificateSource">
            <summary>Get specific certificate source context value.</summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            context value
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.SetCertificateSource(iText.Signatures.Validation.V1.Context.CertificateSource)">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            instance with the provided certificate source context value.
            </summary>
            <param name="certificateSource">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            value
            </param>
            <returns>
            new
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.GetTimeBasedContext">
            <summary>Get specific time-based context value.</summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            context value
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.SetTimeBasedContext(iText.Signatures.Validation.V1.Context.TimeBasedContext)">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            instance with the provided certificate source context value.
            </summary>
            <param name="timeBasedContext">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            value
            </param>
            <returns>
            new
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.GetValidatorContext">
            <summary>Get specific validator context value.</summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            context value
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.SetValidatorContext(iText.Signatures.Validation.V1.Context.ValidatorContext)">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            instance with the provided certificate source context value.
            </summary>
            <param name="validatorContext">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            value
            </param>
            <returns>
            new
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.CheckIfContextChainContainsCertificateSource(iText.Signatures.Validation.V1.Context.ValidationContext,iText.Signatures.Validation.V1.Context.CertificateSource)">
            <summary>
            Check if validation contexts chain contains specific
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            value.
            </summary>
            <param name="context">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            instance to start the check from
            </param>
            <param name="source">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/>
            value to check
            </param>
            <returns>
            
            <see langword="true"/>
            if validation contexts chain contains provided certificate source,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.ToString">
            <summary>
            Return string representation of this
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>.
            </summary>
            <remarks>
            Return string representation of this
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>.
            Previous validation context is not a part of this representation.
            </remarks>
            <returns>
            a string representation of the
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.Equals(System.Object)">
            <summary>Check if the provided object is equal to this one.</summary>
            <remarks>
            Check if the provided object is equal to this one.
            Previous validation context field is not taken into account during this comparison.
            </remarks>
            <param name="o">the reference object with which to compare</param>
            <returns>
            
            <see langword="true"/>
            if provided object is equal to this one,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidationContext.GetHashCode">
            <summary>Return a hash code value for this validation context.</summary>
            <remarks>
            Return a hash code value for this validation context.
            Previous validation context field is not taken into account during hash code calculation.
            </remarks>
            <returns>a hash code value for this validation context</returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Context.ValidatorContext">
            <summary>This enum lists all possible contexts related to the validator in which the validation is taking place.
                </summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.ValidatorContext.OCSP_VALIDATOR">
            <summary>
            This value is expected to be used in
            <see cref="T:iText.Signatures.Validation.V1.OCSPValidator"/>
            context.
            </summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.ValidatorContext.CRL_VALIDATOR">
            <summary>
            This value is expected to be used in
            <see cref="T:iText.Signatures.Validation.V1.CRLValidator"/>
            context.
            </summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.ValidatorContext.REVOCATION_DATA_VALIDATOR">
            <summary>
            This value is expected to be used in
            <see cref="T:iText.Signatures.Validation.V1.RevocationDataValidator"/>
            context.
            </summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.ValidatorContext.CERTIFICATE_CHAIN_VALIDATOR">
            <summary>
            This value is expected to be used in
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>
            context.
            </summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.ValidatorContext.SIGNATURE_VALIDATOR">
            <summary>This value is expected to be used in SignatureValidator context.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Context.ValidatorContext.DOCUMENT_REVISIONS_VALIDATOR">
            <summary>
            This value is expected to be used in
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            context.
            </summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Context.ValidatorContexts">
            <summary>
            Container class, which contains set of single
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            values.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidatorContexts.Of(iText.Signatures.Validation.V1.Context.ValidatorContext,iText.Signatures.Validation.V1.Context.ValidatorContext[])">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContexts"/>
            container from several
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            values.
            </summary>
            <param name="first">an element that the set is to contain initially</param>
            <param name="rest">the remaining elements the set is to contain</param>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContexts"/>
            container, containing provided elements
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidatorContexts.All">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContexts"/>
            containing all
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            values.
            </summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContexts"/>
            container containing all
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            values
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidatorContexts.ComplementOf(iText.Signatures.Validation.V1.Context.ValidatorContexts)">
            <summary>
            Creates
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContexts"/>
            containing all the elements of this type
            that are not contained in the specified set.
            </summary>
            <param name="other">
            another
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContexts"/>
            from whose complement to initialize this container
            </param>
            <returns>
            the complement of the specified
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContexts"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Context.ValidatorContexts.GetSet">
            <summary>
            Gets encapsulated
            <see cref="!:Java.Util.EnumSet&lt;E&gt;"/>
            containing
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            elements.
            </summary>
            <returns>
            encapsulated
            <see cref="!:Java.Util.EnumSet&lt;E&gt;"/>
            containing
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidatorContext"/>
            elements
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.CRLValidator">
            <summary>Class that allows you to validate a certificate against a Certificate Revocation List (CRL) Response.
                </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.CRLValidator.#ctor(iText.Signatures.Validation.V1.ValidatorChainBuilder)">
            <summary>
            Creates new
            <see cref="T:iText.Signatures.Validation.V1.CRLValidator"/>
            instance.
            </summary>
            <param name="builder">
            See
            <see cref="T:iText.Signatures.Validation.V1.ValidatorChainBuilder"/>
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.CRLValidator.Validate(iText.Signatures.Validation.V1.Report.ValidationReport,iText.Signatures.Validation.V1.Context.ValidationContext,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Crl,System.DateTime)">
            <summary>Validates a certificate against Certificate Revocation List (CRL) Responses.</summary>
            <param name="report">to store all the chain verification results</param>
            <param name="context">the context in which to perform the validation</param>
            <param name="certificate">the certificate to check against CRL response</param>
            <param name="crl">the crl response to be validated</param>
            <param name="validationDate">validation date to check for</param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.CRLValidator.Validate(iText.Signatures.Validation.V1.Report.ValidationReport,iText.Signatures.Validation.V1.Context.ValidationContext,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Crl,System.DateTime,System.DateTime)">
            <summary>Validates a certificate against Certificate Revocation List (CRL) Responses.</summary>
            <param name="report">to store all the chain verification results</param>
            <param name="context">the context in which to perform the validation</param>
            <param name="certificate">the certificate to check against CRL response</param>
            <param name="crl">the crl response to be validated</param>
            <param name="validationDate">validation date to check for</param>
            <param name="responseGenerationDate">trusted date at which response is generated</param>
        </member>
        <member name="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator">
            <summary>Validator, which is responsible for document revisions validation according to doc-MDP and field-MDP rules.
                </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.DocumentRevisionsValidator.#ctor(iText.Signatures.Validation.V1.ValidatorChainBuilder)">
            <summary>
            Creates new instance of
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>.
            </summary>
            <param name="chainBuilder">
            See
            <see cref="T:iText.Signatures.Validation.V1.ValidatorChainBuilder"/>
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.DocumentRevisionsValidator.SetEventCountingMetaInfo(iText.Commons.Actions.Contexts.IMetaInfo)">
            <summary>
            Sets the
            <see cref="T:iText.Commons.Actions.Contexts.IMetaInfo"/>
            that will be used during new
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            creations.
            </summary>
            <param name="metaInfo">meta info to set</param>
            <returns>
            the same
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.DocumentRevisionsValidator.SetAccessPermissions(iText.Signatures.AccessPermissions)">
            <summary>Set access permissions to be used during docMDP validation.</summary>
            <remarks>
            Set access permissions to be used during docMDP validation.
            If value is provided, access permission related signature parameters will be ignored during the validation.
            </remarks>
            <param name="accessPermissions">
            
            <see cref="T:iText.Signatures.AccessPermissions"/>
            docMDP validation level
            </param>
            <returns>
            the same
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.DocumentRevisionsValidator.SetUnexpectedXrefChangesStatus(iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus)">
            <summary>
            Set the status to be used for the report items produced during docMDP validation in case revision contains
            unexpected changes in the XREF table.
            </summary>
            <remarks>
            Set the status to be used for the report items produced during docMDP validation in case revision contains
            unexpected changes in the XREF table. Default value is
            <see cref="F:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus.INFO"/>.
            </remarks>
            <param name="status">
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus"/>
            to be used in case of unexpected changes in the XREF table
            </param>
            <returns>
            the same
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.DocumentRevisionsValidator.ValidateAllDocumentRevisions(iText.Signatures.Validation.V1.Context.ValidationContext,iText.Kernel.Pdf.PdfDocument)">
            <summary>Validate all document revisions according to docMDP and fieldMDP transform methods.</summary>
            <param name="context">the validation context in which to validate document revisions</param>
            <param name="document">the document to be validated</param>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ValidationReport"/>
            which contains detailed validation results.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.DocumentRevisionsValidator.CompareFields(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary,iText.Signatures.Validation.V1.Report.ValidationReport)">
            <summary>DocMDP level &gt;= 2 allows setting values of the fields and accordingly update the widget appearances of them.
                </summary>
            <remarks>
            DocMDP level &gt;= 2 allows setting values of the fields and accordingly update the widget appearances of them. But
            you cannot change the form structure, so it is not allowed to add, remove or rename fields, change most of their
            properties.
            </remarks>
            <param name="previousField">field from the previous revision to check</param>
            <param name="currentField">field from the current revision to check</param>
            <param name="report">validation report</param>
            <returns>
            
            <see langword="true"/>
            if the changes of the field are allowed,
            <see langword="false"/>
            otherwise.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.DocumentRevisionsValidator.IsAllowedSignatureField(iText.Kernel.Pdf.PdfDictionary,iText.Signatures.Validation.V1.Report.ValidationReport)">
            <summary>
            DocMDP level &lt;=2 allows adding new fields in the following cases:
            docMDP level 1: allows adding only DocTimeStamp signature fields;
            docMDP level 2: same as level 1 and also adding and then signing signature fields,
            so signature dictionary shouldn't be null.
            </summary>
            <param name="field">newly added field entry</param>
            <param name="report">validation report</param>
            <returns>true if newly added field is allowed to be added, false otherwise.</returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Extensions.BasicConstraintsExtension">
            <summary>Class representing "Basic Constraints" certificate extension.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.BasicConstraintsExtension.#ctor(System.Boolean)">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.Extensions.BasicConstraintsExtension"/>
            instance using provided
            <c>boolean</c>
            value.
            </summary>
            <param name="ca">
            
            <c>boolean</c>
            value, which represents if this certificate is a "Certificate Authority"
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.BasicConstraintsExtension.#ctor(System.Int32)">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.Extensions.BasicConstraintsExtension"/>
            instance using provided
            <c>int</c>
            path length.
            </summary>
            <param name="pathLength">
            
            <c>int</c>
            value, which represents acceptable path length for this certificate as a "CA"
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.BasicConstraintsExtension.ExistsInCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if this extension is present in the provided certificate.</summary>
            <remarks>
            Check if this extension is present in the provided certificate. In case of
            <see cref="T:iText.Signatures.Validation.V1.Extensions.BasicConstraintsExtension"/>
            ,
            check if path length for this extension is less or equal to the path length, specified in the certificate.
            </remarks>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            in which this extension shall be present
            </param>
            <returns>
            
            <see langword="true"/>
            if this path length is less or equal to a one from the certificate,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Extensions.CertificateExtension">
            <summary>Class representing certificate extension with all the information required for validation.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.CertificateExtension.#ctor(System.String,iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>
            Create new instance of
            <see cref="T:iText.Signatures.Validation.V1.Extensions.CertificateExtension"/>
            using provided extension OID and value.
            </summary>
            <param name="extensionOid">
            
            <see cref="T:System.String"/>
            , which represents extension OID
            </param>
            <param name="extensionValue">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            , which represents extension value
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.CertificateExtension.GetExtensionValue">
            <summary>Get extension value</summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            , which represents extension value
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.CertificateExtension.GetExtensionOid">
            <summary>Get extension OID</summary>
            <returns>
            
            <see cref="T:System.String"/>
            , which represents extension OID
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.CertificateExtension.ExistsInCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if this extension is present in the provided certificate.</summary>
            <remarks>
            Check if this extension is present in the provided certificate.
            <para />
            This method doesn't always require complete extension value equality,
            instead whenever possible it checks that this extension is present in the certificate.
            </remarks>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            in which this extension shall be present
            </param>
            <returns>
            
            <see langword="true"/>
            if extension if present,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Extensions.DynamicBasicConstraintsExtension">
            <summary>
            Class representing "Basic Constraints" certificate extension,
            which uses provided amount of certificates in chain during the comparison.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.DynamicBasicConstraintsExtension.#ctor">
            <summary>
            Create new instance of
            <see cref="T:iText.Signatures.Validation.V1.Extensions.DynamicBasicConstraintsExtension"/>.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.DynamicBasicConstraintsExtension.ExistsInCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if this extension is present in the provided certificate.</summary>
            <remarks>
            Check if this extension is present in the provided certificate.
            In case of
            <see cref="T:iText.Signatures.Validation.V1.Extensions.DynamicBasicConstraintsExtension"/>
            , check if path length for this extension is less or equal
            to the path length, specified in the certificate.
            </remarks>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            in which this extension shall be present
            </param>
            <returns>
            
            <see langword="true"/>
            if this path length is less or equal to a one from the certificate,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Extensions.DynamicCertificateExtension">
            <summary>Certificate extension which is populated with additional dynamically changing validation related information.
                </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.DynamicCertificateExtension.#ctor(System.String,iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>
            Create new instance of
            <see cref="T:iText.Signatures.Validation.V1.Extensions.CertificateExtension"/>
            using provided extension OID and value.
            </summary>
            <param name="extensionOid">
            
            <see cref="T:System.String"/>
            , which represents extension OID
            </param>
            <param name="extensionValue">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            , which represents extension value
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.DynamicCertificateExtension.WithCertificateChainSize(System.Int32)">
            <summary>Sets amount of certificates currently present in the chain.</summary>
            <param name="certificateChainSize">amount of certificates currently present in the chain</param>
            <returns>
            this
            <see cref="T:iText.Signatures.Validation.V1.Extensions.DynamicCertificateExtension"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.DynamicCertificateExtension.GetCertificateChainSize">
            <summary>Gets amount of certificates currently present in the chain.</summary>
            <returns>amount of certificates currently present in the chain</returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Extensions.ExtendedKeyUsageExtension">
            <summary>Class representing "Extended Key Usage" extension.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Signatures.Validation.V1.Extensions.ExtendedKeyUsageExtension.#ctor(System.Collections.Generic.IList{System.String})" -->
        <member name="M:iText.Signatures.Validation.V1.Extensions.ExtendedKeyUsageExtension.ExistsInCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if this extension is present in the provided certificate.</summary>
            <remarks>
            Check if this extension is present in the provided certificate. In case of
            <see cref="T:iText.Signatures.Validation.V1.Extensions.ExtendedKeyUsageExtension"/>
            ,
            check if this extended key usage OIDs are present. Other values may be present as well.
            </remarks>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            in which this extension shall be present
            </param>
            <returns>
            
            <see langword="true"/>
            if all OIDs are present in certificate extension,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Extensions.KeyUsage">
            <summary>Enum representing possible "Key Usage" extension values.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.DIGITAL_SIGNATURE">
            <summary>"Digital Signature" key usage value</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.NON_REPUDIATION">
            <summary>"Non Repudiation" key usage value</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.KEY_ENCIPHERMENT">
            <summary>"Key Encipherment" key usage value</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.DATA_ENCIPHERMENT">
            <summary>"Data Encipherment" key usage value</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.KEY_AGREEMENT">
            <summary>"Key Agreement" key usage value</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.KEY_CERT_SIGN">
            <summary>"Key Cert Sign" key usage value</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.CRL_SIGN">
            <summary>"CRL Sign" key usage value</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.ENCIPHER_ONLY">
            <summary>"Encipher Only" key usage value</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Extensions.KeyUsage.DECIPHER_ONLY">
            <summary>"Decipher Only" key usage value</summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension">
            <summary>Class representing "Key Usage" extenstion.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension.#ctor(System.Int32)">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension"/>
            instance using provided
            <c>int</c>
            flag.
            </summary>
            <param name="keyUsage">
            
            <c>int</c>
            flag which represents bit values for key usage value
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension.#ctor(System.Collections.Generic.IList{iText.Signatures.Validation.V1.Extensions.KeyUsage})">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension"/>
            instance using provided key usage enum list.
            </summary>
            <param name="keyUsages">
            key usages
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            which represents key usage values
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension.#ctor(iText.Signatures.Validation.V1.Extensions.KeyUsage)">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension"/>
            instance using provided single key usage enum value.
            </summary>
            <param name="keyUsageValue">
            
            <see cref="T:iText.Signatures.Validation.V1.Extensions.KeyUsage"/>
            which represents single key usage enum value
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension.ExistsInCertificate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if this extension is present in the provided certificate.</summary>
            <remarks>
            Check if this extension is present in the provided certificate. In case of
            <see cref="T:iText.Signatures.Validation.V1.Extensions.KeyUsageExtension"/>
            ,
            check if this key usage bit values are present in certificate. Other values may be present as well.
            </remarks>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            in which this extension shall be present
            </param>
            <returns>
            
            <see langword="true"/>
            if this key usage bit values are present in certificate,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.OCSPValidator">
            <summary>Class that allows you to validate a single OCSP response.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.OCSPValidator.#ctor(iText.Signatures.Validation.V1.ValidatorChainBuilder)">
            <summary>
            Creates new
            <see cref="T:iText.Signatures.Validation.V1.OCSPValidator"/>
            instance.
            </summary>
            <param name="builder">
            See
            <see cref="T:iText.Signatures.Validation.V1.ValidatorChainBuilder"/>
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.OCSPValidator.Validate(iText.Signatures.Validation.V1.Report.ValidationReport,iText.Signatures.Validation.V1.Context.ValidationContext,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse,iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,System.DateTime)">
            <summary>Validates a certificate against single OCSP Response.</summary>
            <param name="report">to store all the chain verification results</param>
            <param name="context">the context in which to perform the validation</param>
            <param name="certificate">the certificate to check for</param>
            <param name="singleResp">single response to check</param>
            <param name="ocspResp">basic OCSP response which contains single response to check</param>
            <param name="validationDate">validation date to check for</param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.OCSPValidator.Validate(iText.Signatures.Validation.V1.Report.ValidationReport,iText.Signatures.Validation.V1.Context.ValidationContext,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse,iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,System.DateTime,System.DateTime)">
            <summary>Validates a certificate against single OCSP Response.</summary>
            <param name="report">to store all the chain verification results</param>
            <param name="context">the context in which to perform the validation</param>
            <param name="certificate">the certificate to check for</param>
            <param name="singleResp">single response to check</param>
            <param name="ocspResp">basic OCSP response which contains single response to check</param>
            <param name="validationDate">validation date to check for</param>
            <param name="responseGenerationDate">trusted date at which response is generated</param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.OCSPValidator.VerifyOcspResponder(iText.Signatures.Validation.V1.Report.ValidationReport,iText.Signatures.Validation.V1.Context.ValidationContext,iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Verifies if an OCSP response is genuine.</summary>
            <remarks>
            Verifies if an OCSP response is genuine.
            If it doesn't verify against the issuer certificate and response's certificates, it may verify
            using a trusted anchor or cert.
            </remarks>
            <param name="report">to store all the chain verification results</param>
            <param name="context">the context in which to perform the validation</param>
            <param name="ocspResp">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            the OCSP response wrapper
            </param>
            <param name="issuerCert">the issuer of the certificate for which the OCSP is checked</param>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Report.CertificateReportItem">
            <summary>Report item to be used for single certificate related failure or log message.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.CertificateReportItem.#ctor(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String,System.String,iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus)">
            <summary>
            Create
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem"/>
            instance.
            </summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            processing which report item occurred
            </param>
            <param name="checkName">
            
            <see cref="T:System.String"/>
            , which represents a check name during which report item occurred
            </param>
            <param name="message">
            
            <see cref="T:System.String"/>
            with the exact report item message
            </param>
            <param name="status">
            
            <see cref="!:ReportItemStatus"/>
            report item status that determines validation result
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.CertificateReportItem.#ctor(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String,System.String,System.Exception,iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus)">
            <summary>
            Create
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem"/>
            instance.
            </summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            processing which report item occurred
            </param>
            <param name="checkName">
            
            <see cref="T:System.String"/>
            , which represents a check name during which report item occurred
            </param>
            <param name="message">
            
            <see cref="T:System.String"/>
            with the exact report item message
            </param>
            <param name="cause">
            
            <see cref="T:System.Exception"/>
            , which caused this report item
            </param>
            <param name="status">
            
            <see cref="!:ReportItemStatus"/>
            report item status that determines validation result
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.CertificateReportItem.GetCertificate">
            <summary>Get the certificate related to this report item.</summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            related to this report item.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.CertificateReportItem.ToString">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Report.ReportItem">
            <summary>Report item to be used for single failure or log message.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ReportItem.#ctor(System.String,System.String,iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus)">
            <summary>
            Create
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem"/>
            instance.
            </summary>
            <param name="checkName">
            
            <see cref="T:System.String"/>
            , which represents a check name during which report item occurred
            </param>
            <param name="message">
            
            <see cref="T:System.String"/>
            with the exact report item message
            </param>
            <param name="status">
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus"/>
            report item status that determines validation result
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ReportItem.#ctor(System.String,System.String,System.Exception,iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus)">
            <summary>
            Create
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem"/>
            instance.
            </summary>
            <param name="checkName">
            
            <see cref="T:System.String"/>
            , which represents a check name during which report item occurred
            </param>
            <param name="message">
            
            <see cref="T:System.String"/>
            with the exact report item message
            </param>
            <param name="cause">
            
            <see cref="T:System.Exception"/>
            , which caused this report item
            </param>
            <param name="status">
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus"/>
            report item status that determines validation result
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ReportItem.GetCheckName">
            <summary>Get the check name related to this report item.</summary>
            <returns>
            
            <see cref="T:System.String"/>
            check name related to this report item.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ReportItem.GetMessage">
            <summary>Get the message related to this report item.</summary>
            <returns>
            
            <see cref="T:System.String"/>
            message related to this report item.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ReportItem.GetExceptionCause">
            <summary>Get the exception, which caused this report item.</summary>
            <returns>
            
            <see cref="T:System.Exception"/>
            , which cause this report item.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ReportItem.GetStatus">
            <summary>Get report item status that determines validation result this report item corresponds to.</summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus"/>
            report item status that determines validation result.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ReportItem.SetStatus(iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus)">
            <summary>Set report item status that determines validation result this report item corresponds to.</summary>
            <param name="status">
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus"/>
            report item status that determines validation result
            </param>
            <returns>
            this
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ReportItem.ToString">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus">
            <summary>Enum representing possible report item statuses that determine validation result.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus.INFO">
            <summary>Report item status for info messages.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus.INVALID">
            <summary>Report item status that leads to invalid validation result.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Report.ReportItem.ReportItemStatus.INDETERMINATE">
            <summary>Report item status that leads to indeterminate validation result.</summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Report.ValidationReport">
            <summary>Validation report, which contains detailed validation results.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.#ctor">
            <summary>
            Create new instance of
            <see cref="T:iText.Signatures.Validation.V1.Report.ValidationReport"/>.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.GetValidationResult">
            <summary>Get the result of a validation process.</summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ValidationReport.ValidationResult"/>
            , which represents the result of a validation
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.GetFailures">
            <summary>Get all failures recognized during a validation process.</summary>
            <returns>
            report items
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            , which contains all recognized failures
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.GetCertificateFailures">
            <summary>Get list of failures, which are related to certificate validation.</summary>
            <returns>
            report items
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            , which contains only
            <see cref="T:iText.Signatures.Validation.V1.Report.CertificateReportItem"/>
            failures
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.GetLogs">
            <summary>Get all log messages reported during a validation process.</summary>
            <returns>
            report items
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            , which contains all reported log messages, related to validation
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.GetCertificateLogs">
            <summary>Get list of log messages, which are related to certificate validation.</summary>
            <returns>
            report items
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            , which contains only
            <see cref="T:iText.Signatures.Validation.V1.Report.CertificateReportItem"/>
            log messages
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.AddReportItem(iText.Signatures.Validation.V1.Report.ReportItem)">
            <summary>Add new report item to the overall validation result.</summary>
            <param name="item">
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem"/>
            to be added
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.ToString">
            <summary>
            <inheritDoc/>.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.Report.ValidationReport.Merge(iText.Signatures.Validation.V1.Report.ValidationReport)">
            <summary>
            Merge all
            <see cref="T:iText.Signatures.Validation.V1.Report.ReportItem"/>
            objects from sub report into this one.
            </summary>
            <param name="subReport">report from which items will be merged</param>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ValidationReport"/>
            the same updated validation report instance.
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.Report.ValidationReport.ValidationResult">
            <summary>Enum representing possible validation results.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Report.ValidationReport.ValidationResult.VALID">
            <summary>Valid validation result.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Report.ValidationReport.ValidationResult.INVALID">
            <summary>Invalid validation result.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.Report.ValidationReport.ValidationResult.INDETERMINATE">
            <summary>Indeterminate validation result.</summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.RevocationDataValidator">
            <summary>Class that allows you to fetch and validate revocation data for the certificate.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.RevocationDataValidator.#ctor(iText.Signatures.Validation.V1.ValidatorChainBuilder)">
            <summary>
            Creates new
            <see cref="T:iText.Signatures.Validation.V1.RevocationDataValidator"/>
            instance to validate certificate revocation data.
            </summary>
            <param name="builder">
            See
            <see cref="T:iText.Signatures.Validation.V1.ValidatorChainBuilder"/>
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.RevocationDataValidator.AddCrlClient(iText.Signatures.ICrlClient)">
            <summary>
            Add
            <see cref="T:iText.Signatures.ICrlClient"/>
            to be used for CRL responses receiving.
            </summary>
            <param name="crlClient">
            
            <see cref="T:iText.Signatures.ICrlClient"/>
            to be used for CRL responses receiving
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.Validation.V1.RevocationDataValidator"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.RevocationDataValidator.AddOcspClient(iText.Signatures.IOcspClient)">
            <summary>
            Add
            <see cref="T:iText.Signatures.IOcspClient"/>
            to be used for OCSP responses receiving.
            </summary>
            <param name="ocspClient">
            
            <see cref="T:iText.Signatures.IOcspClient"/>
            to be used for OCSP responses receiving
            </param>
            <returns>
            same instance of
            <see cref="T:iText.Signatures.Validation.V1.RevocationDataValidator"/>.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.RevocationDataValidator.Validate(iText.Signatures.Validation.V1.Report.ValidationReport,iText.Signatures.Validation.V1.Context.ValidationContext,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.DateTime)">
            <summary>Validates revocation data (Certificate Revocation List (CRL) Responses and OCSP Responses) of the certificate.
                </summary>
            <param name="report">to store all the verification results</param>
            <param name="context">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.ValidationContext"/>
            the context
            </param>
            <param name="certificate">the certificate to check revocation data for</param>
            <param name="validationDate">validation date to check for</param>
        </member>
        <member name="T:iText.Signatures.Validation.V1.RevocationDataValidator.OcspResponseValidationInfo">
            <summary>Class which contains validation related information about single OCSP response.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.RevocationDataValidator.OcspResponseValidationInfo.#ctor(iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse,iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,System.DateTime,iText.Signatures.Validation.V1.Context.TimeBasedContext)">
            <summary>Creates validation related information about single OCSP response.</summary>
            <param name="singleResp">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse"/>
            single response to be validated
            </param>
            <param name="basicOCSPResp">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            basic OCSP response which contains this single response
            </param>
            <param name="trustedGenerationDate">
            
            <see cref="T:System.DateTime"/>
            trusted date at which response was generated
            </param>
            <param name="timeBasedContext">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            time based context which corresponds to generation date
            </param>
        </member>
        <member name="T:iText.Signatures.Validation.V1.RevocationDataValidator.CrlValidationInfo">
            <summary>Class which contains validation related information about CRL response.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.RevocationDataValidator.CrlValidationInfo.#ctor(iText.Commons.Bouncycastle.Cert.IX509Crl,System.DateTime,iText.Signatures.Validation.V1.Context.TimeBasedContext)">
            <summary>Creates validation related information about CRL response.</summary>
            <param name="crl">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Crl"/>
            CRL to be validated
            </param>
            <param name="trustedGenerationDate">
            
            <see cref="T:System.DateTime"/>
            trusted date at which response was generated
            </param>
            <param name="timeBasedContext">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            time based context which corresponds to generation date
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SafeCalling.OnExceptionLog(System.Action,iText.Signatures.Validation.V1.Report.ValidationReport,System.Func{System.Exception,iText.Signatures.Validation.V1.Report.ReportItem})">
            <summary>Adds a report item to the report when an exception is thrown in the action.</summary>
            <param name="action">The action to perform</param>
            <param name="report">The report to add the ReportItem to</param>
            <param name="reportItemCreator">A callback to generate a ReportItem</param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SafeCalling.OnExceptionLog``1(System.Func{``0},``0,iText.Signatures.Validation.V1.Report.ValidationReport,System.Func{System.Exception,iText.Signatures.Validation.V1.Report.ReportItem})">
            <summary>Adds a report item to the report when an exception is thrown in the action.</summary>
            <param name="action">The action to perform</param>
            <param name="defaultValue">The value to return when an exception is thrown</param>
            <param name="report">The report to add the ReportItem to</param>
            <param name="reportItemCreator">A callback to generate a ReportItem</param>
            <typeparam name="T"/>
            <returns>The returned value from the action</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SafeCalling.OnRuntimeExceptionLog(System.Action,iText.Signatures.Validation.V1.Report.ValidationReport,System.Func{System.Exception,iText.Signatures.Validation.V1.Report.ReportItem})">
            <summary>Adds a report item to the report when an exception is thrown in the action.</summary>
            <param name="action">The action to perform</param>
            <param name="report">The report to add the ReportItem to</param>
            <param name="reportItemCreator">A callback to generate a ReportItem</param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SafeCalling.OnRuntimeExceptionLog``1(System.Func{``0},``0,iText.Signatures.Validation.V1.Report.ValidationReport,System.Func{System.Exception,iText.Signatures.Validation.V1.Report.ReportItem})">
            <summary>Adds a report item to the report when an exception is thrown in the action.</summary>
            <param name="action">The action to perform</param>
            <param name="defaultValue">The value to return when an exception is thrown</param>
            <param name="report">The report to add the ReportItem to</param>
            <param name="reportItemCreator">A callback to generate a ReportItem</param>
            <typeparam name="T"/>
            <returns>The returned value from the action</returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.SignatureValidationProperties">
            <summary>
            Class which stores properties, which are related to signature validation process.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.#ctor">
            <summary>
            Create <see cref="T:iText.Signatures.Validation.V1.SignatureValidationProperties"/> with default values.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.GetFreshness(iText.Signatures.Validation.V1.Context.ValidationContext)">
            <summary>
            Returns the freshness setting for the provided validation context or the default context
            in milliseconds.
            </summary>
            <param name="validationContext">the validation context for which to retrieve the freshness setting</param>
            <returns>the freshness setting for the provided validation context or the default context in milliseconds</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.SetFreshness(iText.Signatures.Validation.V1.Context.ValidatorContexts,iText.Signatures.Validation.V1.Context.CertificateSources,iText.Signatures.Validation.V1.Context.TimeBasedContexts,System.TimeSpan)">
            <summary>
            Sets the freshness setting for the specified validator,
            time based and certificate source contexts in milliseconds.
            This parameter specifies how old revocation data can be, compared to validation time, in order to be trustworthy.
            </summary>
            <param name="validatorContexts">the validators for which to apply the setting</param>
            <param name="certificateSources">the certificate sources to</param>
            <param name="timeBasedContexts">the date comparison context  for which to apply the setting</param>
            <param name="value">the settings value in milliseconds</param>
            <returns>
            this same
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidationProperties"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.GetContinueAfterFailure(iText.Signatures.Validation.V1.Context.ValidationContext)">
            <summary>Returns the Continue after failure setting for the provided context or the default context.</summary>
            <param name="validationContext">the context for which to retrieve the Continue after failure setting</param>
            <returns>the Continue after failure setting for the provided context or the default context</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.SetContinueAfterFailure(iText.Signatures.Validation.V1.Context.ValidatorContexts,iText.Signatures.Validation.V1.Context.CertificateSources,System.Boolean)">
            <summary>
            Sets the Continue after failure setting for the provided context.
            This parameter specifies if validation is expected to continue after first failure is encountered.
            Only <see cref="!:ValidationResult#INVALID"/> is considered to be a failure.
            </summary>
            <param name="validatorContexts">the validators for which to set the Continue after failure setting</param>
            <param name="certificateSources">the certificateSources for which to set the Continue after failure setting
                </param>
            <param name="value">the Continue after failure setting</param>
            <returns>
            this same
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidationProperties"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.GetRevocationOnlineFetching(iText.Signatures.Validation.V1.Context.ValidationContext)">
            <summary>Sets the onlineFetching property representing possible online fetching permissions.</summary>
            <param name="validationContext">the context for which to retrieve the online fetching setting</param>
            <returns>the online fetching setting.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.SetRevocationOnlineFetching(iText.Signatures.Validation.V1.Context.ValidatorContexts,iText.Signatures.Validation.V1.Context.CertificateSources,iText.Signatures.Validation.V1.Context.TimeBasedContexts,iText.Signatures.Validation.V1.SignatureValidationProperties.OnlineFetching)">
            <summary>Sets the onlineFetching property representing possible online fetching permissions.</summary>
            <param name="validatorContexts">the validators for which to set this value</param>
            <param name="certificateSources">the certificate source for which to set this value</param>
            <param name="timeBasedContexts">time perspective context, at which validation is happening</param>
            <param name="onlineFetching">onlineFetching property value to set</param>
            <returns>
            this same
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidationProperties"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.GetRequiredExtensions(iText.Signatures.Validation.V1.Context.ValidationContext)">
            <summary>Returns required extension for the provided validation context.</summary>
            <param name="validationContext">the validation context for which to retrieve required extensions</param>
            <returns>required extensions for the provided validation context</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.SetRequiredExtensions(iText.Signatures.Validation.V1.Context.CertificateSources,System.Collections.Generic.IList{iText.Signatures.Validation.V1.Extensions.CertificateExtension})">
            <summary>
            Set list of extensions which are required to be set to a certificate depending on certificate source.
            <para />
            By default, required extensions are set to be compliant with common validation norms.
            Changing those can result in falsely positive validation result.
            </summary>
            <param name="certificateSources"><see cref="T:iText.Signatures.Validation.V1.Context.CertificateSource"/> for extensions to be present</param>
            <param name="requiredExtensions">list of required <see cref="T:iText.Signatures.Validation.V1.Extensions.CertificateExtension"/></param>
            <returns>this same <see cref="T:iText.Signatures.Validation.V1.SignatureValidationProperties"/> instance</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.GetCrlClients">
            <summary>
            Gets all ICrlClient instances which will be used to retrieve CRL responses during the validation.
            </summary>
            <returns>
            all ICrlClient instances which will be used to retrieve CRL responses during the validation
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.AddCrlClient(iText.Signatures.ICrlClient)">
            <summary>
            Adds new ICrlClient instance which will be used to retrieve CRL responses during the validation.
            </summary>
            <param name="crlClient">
            ICrlClient instance which will be used to retrieve CRL responses during the validation
            </param>
            <returns>this same SignatureValidationProperties instance</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.GetOcspClients">
            <summary>
            Gets all IOcspClient instances which will be used to retrieve OCSP responses during the validation.
            </summary>
            <returns>
            all IOcspClient instances which will be used to retrieve OCSP responses during the validation
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.AddOcspClient(iText.Signatures.IOcspClient)">
            <summary>
            Adds new IOcspClient instance which will be used to retrieve OCSP response during the validation.
            </summary>
            <param name="ocspClient">
            IOcspClient instance which will be used to retrieve OCSP response during the validation
            </param>
            <returns>this same SignatureValidationProperties instance</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.SetParameterValueFor(iText.Commons.Utils.Collections.EnumSet{iText.Signatures.Validation.V1.Context.ValidatorContext},iText.Commons.Utils.Collections.EnumSet{iText.Signatures.Validation.V1.Context.CertificateSource},iText.Commons.Utils.Collections.EnumSet{iText.Signatures.Validation.V1.Context.TimeBasedContext},System.Action{iText.Signatures.Validation.V1.SignatureValidationProperties.ContextProperties})">
            <summary>This method executes the setter method for every combination of selected validators and certificateSources
                </summary>
            <param name="validatorContexts">the validators to execute the setter on</param>
            <param name="certificateSources">the certificate sources to execute the setter on</param>
            <param name="setter">the setter to execute</param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidationProperties.GetParametersValueFor``1(iText.Signatures.Validation.V1.Context.ValidatorContext,iText.Signatures.Validation.V1.Context.CertificateSource,iText.Signatures.Validation.V1.Context.TimeBasedContext,System.Func{iText.Signatures.Validation.V1.SignatureValidationProperties.ContextProperties,``0})">
            <summary>
            This method executes the getter method to the most granular parameters set down until the getter returns
            a non-null value
            </summary>
            <param name="validatorContext">the validator for which the value is to be retrieved</param>
            <param name="certSource">the certificate source for which the value is to be retrieved</param>
            <param name="getter">the getter to get the value from the parameters set</param>
            <typeparam name="T">the type of the return value of this method and the getter method</typeparam>
            <returns>the first non-null value returned.</returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.SignatureValidationProperties.OnlineFetching">
            <summary>Enum representing possible online fetching permissions.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.SignatureValidationProperties.OnlineFetching.ALWAYS_FETCH">
            <summary>Permission to always fetch revocation data online.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.SignatureValidationProperties.OnlineFetching.FETCH_IF_NO_OTHER_DATA_AVAILABLE">
            <summary>Permission to fetch revocation data online if no other revocation data available.</summary>
        </member>
        <member name="F:iText.Signatures.Validation.V1.SignatureValidationProperties.OnlineFetching.NEVER_FETCH">
            <summary>Forbids fetching revocation data online.</summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.SignatureValidator">
            <summary>Validator class, which is expected to be used for signatures validation.</summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidator.#ctor(iText.Kernel.Pdf.PdfDocument,iText.Signatures.Validation.V1.ValidatorChainBuilder)">
            <summary>
            Creates new instance of
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidator"/>.
            </summary>
            <param name="originalDocument">
            
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance which will be validated
            </param>
            <param name="builder">
            see
            <see cref="T:iText.Signatures.Validation.V1.ValidatorChainBuilder"/>
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidator.SetEventCountingMetaInfo(iText.Commons.Actions.Contexts.IMetaInfo)">
            <summary>
            Sets the
            <see cref="T:iText.Commons.Actions.Contexts.IMetaInfo"/>
            that will be used during new
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            creations.
            </summary>
            <param name="metaInfo">meta info to set</param>
            <returns>
            the same
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidator"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.SignatureValidator.ValidateSignatures">
            <summary>Validate all signatures in the document.</summary>
            <returns>
            
            <see cref="T:iText.Signatures.Validation.V1.Report.ValidationReport"/>
            which contains detailed validation results
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.TrustedCertificatesStore">
            <summary>Trusted certificates storage class to be used to configure trusted certificates in a particular way.
                </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.AddGenerallyTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Add collection of certificates to be trusted for any possible usage.</summary>
            <param name="certificates">
            
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            of
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            instances
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.AddOcspTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Add collection of certificates to be trusted for OCSP response signing.</summary>
            <remarks>
            Add collection of certificates to be trusted for OCSP response signing.
            These certificates are considered to be valid trust anchors for
            arbitrarily long certificate chain responsible for OCSP response generation.
            </remarks>
            <param name="certificates">
            
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            of
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            instances
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.AddCrlTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Add collection of certificates to be trusted for CRL signing.</summary>
            <remarks>
            Add collection of certificates to be trusted for CRL signing.
            These certificates are considered to be valid trust anchors for
            arbitrarily long certificate chain responsible for CRL generation.
            </remarks>
            <param name="certificates">
            
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            of
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            instances
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.AddTimestampTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Add collection of certificates to be trusted for timestamping.</summary>
            <remarks>
            Add collection of certificates to be trusted for timestamping.
            These certificates are considered to be valid trust anchors for
            arbitrarily long certificate chain responsible for timestamp generation.
            </remarks>
            <param name="certificates">
            
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            of
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            instances
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.AddCATrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>Add collection of certificates to be trusted to be CA certificates.</summary>
            <remarks>
            Add collection of certificates to be trusted to be CA certificates.
            These certificates are considered to be valid trust anchors for certificate generation.
            </remarks>
            <param name="certificates">
            
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            of
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            instances
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.IsCertificateGenerallyTrusted(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if provided certificate is configured to be trusted for any purpose.</summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to be checked
            </param>
            <returns>
            
            <see langword="true"/>
            is provided certificate is generally trusted,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.IsCertificateTrustedForOcsp(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if provided certificate is configured to be trusted for OCSP response generation.</summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to be checked
            </param>
            <returns>
            
            <see langword="true"/>
            is provided certificate is trusted for OCSP generation,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.IsCertificateTrustedForCrl(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if provided certificate is configured to be trusted for CRL generation.</summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to be checked
            </param>
            <returns>
            
            <see langword="true"/>
            is provided certificate is trusted for CRL generation,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.IsCertificateTrustedForTimestamp(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if provided certificate is configured to be trusted for timestamp generation.</summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to be checked
            </param>
            <returns>
            
            <see langword="true"/>
            is provided certificate is trusted for timestamp generation,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.IsCertificateTrustedForCA(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>Check if provided certificate is configured to be trusted to be CA.</summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to be checked
            </param>
            <returns>
            
            <see langword="true"/>
            is provided certificate is trusted for certificates generation,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.GetGenerallyTrustedCertificate(System.String)">
            <summary>Get certificate, if any, which is trusted for any usage, which corresponds to the provided certificate name.
                </summary>
            <param name="certificateName">
            
            <see cref="T:System.String"/>
            certificate name
            </param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            which corresponds to the provided certificate name
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.GetCertificateTrustedForOcsp(System.String)">
            <summary>
            Get certificate, if any, which is trusted for OCSP response generation,
            which corresponds to the provided certificate name.
            </summary>
            <param name="certificateName">
            
            <see cref="T:System.String"/>
            certificate name
            </param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            which corresponds to the provided certificate name
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.GetCertificateTrustedForCrl(System.String)">
            <summary>Get certificate, if any, which is trusted for CRL generation, which corresponds to the provided certificate name.
                </summary>
            <param name="certificateName">
            
            <see cref="T:System.String"/>
            certificate name
            </param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            which corresponds to the provided certificate name
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.GetCertificateTrustedForTimestamp(System.String)">
            <summary>
            Get certificate, if any, which is trusted for timestamp generation,
            which corresponds to the provided certificate name.
            </summary>
            <param name="certificateName">
            
            <see cref="T:System.String"/>
            certificate name
            </param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            which corresponds to the provided certificate name
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.GetCertificateTrustedForCA(System.String)">
            <summary>Get certificate, if any, which is trusted to be a CA, which corresponds to the provided certificate name.
                </summary>
            <param name="certificateName">
            
            <see cref="T:System.String"/>
            certificate name
            </param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            which corresponds to the provided certificate name
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.GetKnownCertificate(System.String)">
            <summary>Get certificate, if any, which corresponds to the provided certificate name.</summary>
            <param name="certificateName">
            
            <see cref="T:System.String"/>
            certificate name
            </param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            which corresponds to the provided certificate name
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.TrustedCertificatesStore.GetAllTrustedCertificates">
            <summary>Get all the certificates, which where provided to this storage as trusted certificate.</summary>
            <returns>
            
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            of
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            instances
            </returns>
        </member>
        <member name="T:iText.Signatures.Validation.V1.ValidationCrlClient">
            <summary>CRL client which is expected to be used in case CRL responses shall be linked with generation date.
                </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidationCrlClient.#ctor">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.ValidationCrlClient"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidationCrlClient.AddCrl(iText.Commons.Bouncycastle.Cert.IX509Crl,System.DateTime,iText.Signatures.Validation.V1.Context.TimeBasedContext)">
            <summary>Add CRL response which is linked with generation date.</summary>
            <param name="response">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Crl"/>
            response to be added
            </param>
            <param name="date">
            
            <see cref="T:System.DateTime"/>
            to be linked with the response
            </param>
            <param name="context">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            time based context which corresponds to generation date
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidationCrlClient.GetCrls">
            <summary>Get all the CRL responses linked with generation dates.</summary>
            <returns>all the CRL responses linked with generation dates</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidationCrlClient.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>
            <inheritDoc/>.
            </summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.ValidationOcspClient">
            <summary>OCSP client which is expected to be used in case OCSP responses shall be linked with generation date.
                </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidationOcspClient.#ctor">
            <summary>
            Create new
            <see cref="T:iText.Signatures.Validation.V1.ValidationOcspClient"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidationOcspClient.AddResponse(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse,System.DateTime,iText.Signatures.Validation.V1.Context.TimeBasedContext)">
            <summary>Add OCSP response which is linked with generation date.</summary>
            <param name="response">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            response to be added
            </param>
            <param name="date">
            
            <see cref="T:System.DateTime"/>
            to be linked with the response
            </param>
            <param name="context">
            
            <see cref="T:iText.Signatures.Validation.V1.Context.TimeBasedContext"/>
            time based context which corresponds to generation date
            </param>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidationOcspClient.GetResponses">
            <summary>Get all the OCSP responses linked with generation dates.</summary>
            <returns>all the OCSP responses linked with generation dates</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidationOcspClient.GetEncoded(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>
            <inheritDoc/>.
            </summary>
        </member>
        <member name="T:iText.Signatures.Validation.V1.ValidatorChainBuilder">
            <summary>A builder class to construct all necessary parts of a validation chain.</summary>
            <remarks>
            A builder class to construct all necessary parts of a validation chain.
            The builder can be reused to create multiple instances of a validator.
            </remarks>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.BuildSignatureValidator(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidator"/>
            instance with the current configuration.
            </summary>
            <remarks>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidator"/>
            instance with the current configuration.
            This method can be used to create multiple validators.
            </remarks>
            <param name="document">
            
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance which will be validated
            </param>
            <returns>a new instance of a signature validator.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.BuildDocumentRevisionsValidator">
            <summary>
            Create a bew
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            instance with the current configuration.
            </summary>
            <remarks>
            Create a bew
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            instance with the current configuration.
            This method can be used to create multiple validators.
            </remarks>
            <returns>a new instance of a document revisions validator.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.BuildCertificateChainValidator">
            <summary>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>
            instance.
            </summary>
            <remarks>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>
            instance.
            This method can be used to create multiple validators.
            </remarks>
            <returns>a new instance of a CertificateChainValidator.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.BuildRevocationDataValidator">
            <summary>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.RevocationDataValidator"/>
            instance
            This method can be used to create multiple validators.
            </summary>
            <returns>a new instance of a RevocationDataValidator.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.BuildOCSPValidator">
            <summary>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.OCSPValidator"/>
            instance.
            </summary>
            <remarks>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.OCSPValidator"/>
            instance.
            This method can be used to create multiple validators.
            </remarks>
            <returns>a new instance of a OCSPValidator.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.BuildCRLValidator">
            <summary>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.CRLValidator"/>
            instance.
            </summary>
            <remarks>
            Create a new
            <see cref="T:iText.Signatures.Validation.V1.CRLValidator"/>
            instance.
            This method can be used to create multiple validators.
            </remarks>
            <returns>a new instance of a CRLValidator.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithDocumentRevisionsValidator(iText.Signatures.Validation.V1.DocumentRevisionsValidator)">
            <summary>
            Use this instance of a
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            in the validation chain.
            </summary>
            <param name="documentRevisionsValidator">the document revisions validator instance to use</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithCRLValidator(iText.Signatures.Validation.V1.CRLValidator)">
            <summary>
            Use this instance of a
            <see cref="T:iText.Signatures.Validation.V1.CRLValidator"/>
            in the validation chain.
            </summary>
            <param name="crlValidator">the CRLValidator instance to use</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithOCSPValidator(iText.Signatures.Validation.V1.OCSPValidator)">
            <summary>
            Use this instance of a
            <see cref="T:iText.Signatures.Validation.V1.OCSPValidator"/>
            in the validation chain.
            </summary>
            <param name="ocspValidator">the OCSPValidator instance to use</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithRevocationDataValidator(iText.Signatures.Validation.V1.RevocationDataValidator)">
            <summary>
            Use this instance of a
            <see cref="T:iText.Signatures.Validation.V1.RevocationDataValidator"/>
            in the validation chain.
            </summary>
            <param name="revocationDataValidator">the RevocationDataValidator instance to use</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithCertificateChainValidator(iText.Signatures.Validation.V1.CertificateChainValidator)">
            <summary>
            Use this instance of a
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>
            in the validation chain.
            </summary>
            <param name="certificateChainValidator">the CertificateChainValidator instance to use</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithSignatureValidationProperties(iText.Signatures.Validation.V1.SignatureValidationProperties)">
            <summary>
            Use this instance of a
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidationProperties"/>
            in the validation chain.
            </summary>
            <param name="properties">the SignatureValidationProperties instance to use</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithIssuingCertificateRetriever(iText.Signatures.IssuingCertificateRetriever)">
            <summary>
            Use this instance of a
            <see cref="T:iText.Signatures.IssuingCertificateRetriever"/>
            in the validation chain.
            </summary>
            <param name="certificateRetriever">the IssuingCertificateRetriever instance to use</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithKnownCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>
            Adds known certificates to the
            <see cref="T:iText.Signatures.IssuingCertificateRetriever"/>.
            </summary>
            <param name="knownCertificates">the list of known certificates to add</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.WithTrustedCertificates(System.Collections.Generic.ICollection{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>
            Sets the trusted certificates to the
            <see cref="T:iText.Signatures.IssuingCertificateRetriever"/>.
            </summary>
            <param name="trustedCertificates">the list of trusted certificates to set</param>
            <returns>the current ValidatorChainBuilder.</returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.GetDocumentRevisionsValidator">
            <summary>
            Retrieves the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            instance.
            </summary>
            <returns>
            the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.DocumentRevisionsValidator"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.GetCertificateChainValidator">
            <summary>
            Retrieves the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>
            instance.
            </summary>
            <returns>
            the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.CertificateChainValidator"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.GetRevocationDataValidator">
            <summary>
            Retrieves the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.RevocationDataValidator"/>
            instance.
            </summary>
            <returns>
            the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.RevocationDataValidator"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.GetCRLValidator">
            <summary>
            Retrieves the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.CRLValidator"/>
            instance.
            </summary>
            <returns>
            the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.CRLValidator"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.GetOCSPValidator">
            <summary>
            Retrieves the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.OCSPValidator"/>
            instance.
            </summary>
            <returns>
            the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.OCSPValidator"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.GetCertificateRetriever">
            <summary>
            Retrieves the explicitly added or automatically created
            <see cref="T:iText.Signatures.IssuingCertificateRetriever"/>
            instance.
            </summary>
            <returns>
            the explicitly added or automatically created
            <see cref="T:iText.Signatures.IssuingCertificateRetriever"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Signatures.Validation.V1.ValidatorChainBuilder.GetProperties">
            <summary>
            Retrieves the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidationProperties"/>
            instance.
            </summary>
            <returns>
            the explicitly added or automatically created
            <see cref="T:iText.Signatures.Validation.V1.SignatureValidationProperties"/>
            instance.
            </returns>
        </member>
        <member name="T:iText.Signatures.VerificationException">
            <summary>An exception that is thrown when something is wrong with a certificate.</summary>
        </member>
        <member name="M:iText.Signatures.VerificationException.#ctor(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String)">
            <summary>Creates a VerificationException.</summary>
            <param name="cert">is a failed certificate</param>
            <param name="message">is a reason of failure</param>
        </member>
        <member name="T:iText.Signatures.VerificationOK">
            <summary>
            Class that informs you that the verification of a Certificate
            succeeded using a specific CertificateVerifier and for a specific
            reason.
            </summary>
        </member>
        <member name="F:iText.Signatures.VerificationOK.certificate">
            <summary>The certificate that was verified successfully.</summary>
        </member>
        <member name="F:iText.Signatures.VerificationOK.verifierClass">
            <summary>The CertificateVerifier that was used for verifying.</summary>
        </member>
        <member name="F:iText.Signatures.VerificationOK.message">
            <summary>The reason why the certificate verified successfully.</summary>
        </member>
        <member name="M:iText.Signatures.VerificationOK.#ctor(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.Type,System.String)">
            <summary>Creates a VerificationOK object</summary>
            <param name="certificate">the certificate that was successfully verified</param>
            <param name="verifierClass">the class that was used for verification</param>
            <param name="message">the reason why the certificate could be verified</param>
        </member>
        <member name="M:iText.Signatures.VerificationOK.ToString">
            <summary>Return a single String explaining which certificate was verified, how and why.</summary>
            <seealso cref="M:System.Object.ToString"/>
        </member>
    </members>
</doc>
