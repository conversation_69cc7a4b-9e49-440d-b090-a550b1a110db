<?xml version="1.0"?>
<doc>
    <assembly>
        <name>itext.svg</name>
    </assembly>
    <members>
        <member name="T:iText.Svg.Converter.SvgConverter">
            <summary>
            This is the main container class for static methods that do high-level
            conversion operations from input to PDF, either by drawing on a canvas or by
            returning an XObject, which can then be used by the calling class for further
            processing and drawing operations.
            </summary>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnDocument(System.String,iText.Kernel.Pdf.PdfDocument,System.Int32)">
            <summary>
            Draws a String containing valid SVG to a document, on a given page
            number at the origin of the page.
            </summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="pageNo">the page to draw on</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnDocument(System.String,iText.Kernel.Pdf.PdfDocument,System.Int32,System.Single,System.Single)">
            <summary>
            Draws a String containing valid SVG to a document, on a given page
            number on the provided x and y coordinate.
            </summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="pageNo">the page to draw on</param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnDocument(System.String,iText.Kernel.Pdf.PdfDocument,System.Int32,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Draws a String containing valid SVG to a document, on a given page
            number on the provided x and y coordinate.
            </summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="pageNo">the page to draw on</param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnDocument(System.String,iText.Kernel.Pdf.PdfDocument,System.Int32,System.Single,System.Single,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Draws a String containing valid SVG to a document, on a given page
            number on the provided x and y coordinate.
            </summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="pageNo">the page to draw on</param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnDocument(System.IO.Stream,iText.Kernel.Pdf.PdfDocument,System.Int32)">
            <summary>
            Draws a Stream containing valid SVG to a document, on a given page
            number ate the origni of the page.
            </summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="pageNo">the page to draw on</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnDocument(System.IO.Stream,iText.Kernel.Pdf.PdfDocument,System.Int32,System.Single,System.Single)">
            <summary>
            Draws a Stream containing valid SVG to a document, on a given page
            number on the provided x and y coordinate.
            </summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="pageNo">the page to draw on</param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnDocument(System.IO.Stream,iText.Kernel.Pdf.PdfDocument,System.Int32,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Draws a Stream containing valid SVG to a document, on a given page
            number on the provided x and y coordinate.
            </summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="pageNo">the page to draw on</param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnDocument(System.IO.Stream,iText.Kernel.Pdf.PdfDocument,System.Int32,System.Single,System.Single,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Draws a Stream containing valid SVG to a document, on a given page
            number on the provided x and y coordinate.
            </summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="pageNo">the page to draw on</param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnPage(System.String,iText.Kernel.Pdf.PdfPage)">
            <summary>Draws a String containing valid SVG to a given page at the origin of the page.</summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            instance to draw on
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnPage(System.String,iText.Kernel.Pdf.PdfPage,System.Single,System.Single)">
            <summary>Draws a String containing valid SVG to a given page on the provided x and y coordinate.</summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            instance to draw on
            </param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnPage(System.String,iText.Kernel.Pdf.PdfPage,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Draws a String containing valid SVG to a given page on the provided x and y coordinate.</summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            instance to draw on
            </param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnPage(System.String,iText.Kernel.Pdf.PdfPage,System.Single,System.Single,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Draws a String containing valid SVG to a given page on the provided x and y coordinate.</summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            instance to draw on
            </param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnPage(System.IO.Stream,iText.Kernel.Pdf.PdfPage)">
            <summary>Draws a Stream containing valid SVG to a given page at coordinate 0,0.</summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            object containing valid SVG content
            </param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            instance to draw on
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnPage(System.IO.Stream,iText.Kernel.Pdf.PdfPage,System.Single,System.Single)">
            <summary>Draws a Stream containing valid SVG to a given page, at a given location.</summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            object containing valid SVG content
            </param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            instance to draw on
            </param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnPage(System.IO.Stream,iText.Kernel.Pdf.PdfPage,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Draws a Stream containing valid SVG to a given page at a given location.</summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            object containing valid SVG content
            </param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            instance to draw on
            </param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnPage(System.IO.Stream,iText.Kernel.Pdf.PdfPage,System.Single,System.Single,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Draws a Stream containing valid SVG to a given page at a given location.</summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            object containing valid SVG content
            </param>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfPage"/>
            instance to draw on
            </param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnCanvas(System.String,iText.Kernel.Pdf.Canvas.PdfCanvas)">
            <summary>Draws a String containing valid SVG to a pre-made canvas object.</summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="canvas">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instance to draw on
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnCanvas(System.String,iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single)">
            <summary>Draws a String containing valid SVG to a pre-made canvas object.</summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="canvas">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instance to draw on
            </param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnCanvas(System.String,iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Draws a String containing valid SVG to a pre-made canvas object.</summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="canvas">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instance to draw on
            </param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnCanvas(System.String,iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>draws a String containing valid SVG to a pre-made canvas object, at a specified location.</summary>
            <param name="content">the String value containing valid SVG content</param>
            <param name="canvas">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instance to draw on
            </param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnCanvas(System.IO.Stream,iText.Kernel.Pdf.Canvas.PdfCanvas)">
            <summary>Draws a Stream containing valid SVG to a pre-made canvas object.</summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            object containing valid SVG content
            </param>
            <param name="canvas">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instance to draw on
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnCanvas(System.IO.Stream,iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single)">
            <summary>Draws a Stream containing valid SVG to a pre-made canvas object, to a specified location.</summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            object containing valid SVG content
            </param>
            <param name="canvas">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instance to draw on
            </param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnCanvas(System.IO.Stream,iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Draws a Stream containing valid SVG to a pre-made canvas object.</summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            object containing valid SVG content
            </param>
            <param name="canvas">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instance to draw on
            </param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.DrawOnCanvas(System.IO.Stream,iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Draws a String containing valid SVG to a pre-made canvas object, at a specified position on the canvas.
                </summary>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            object containing valid SVG content
            </param>
            <param name="canvas">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instance to draw on
            </param>
            <param name="x">x-coordinate of the location to draw at</param>
            <param name="y">y-coordinate of the location to draw at</param>
            <param name="props">a container for extra properties that customize the behavior</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.CreatePdf(System.IO.FileInfo,System.IO.FileInfo)">
            <summary>
            Converts SVG stored in a
            <see cref="T:System.IO.FileInfo"/>
            to a PDF
            <see cref="T:System.IO.FileInfo"/>.
            </summary>
            <param name="svgFile">
            the
            <see cref="T:System.IO.FileInfo"/>
            containing the source SVG
            </param>
            <param name="pdfFile">
            the
            <see cref="T:System.IO.FileInfo"/>
            containing the resulting PDF
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.CreatePdf(System.IO.FileInfo,System.IO.FileInfo,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Converts SVG stored in a
            <see cref="T:System.IO.FileInfo"/>
            to a PDF
            <see cref="T:System.IO.FileInfo"/>
            ,
            using specific
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>.
            </summary>
            <param name="svgFile">
            the
            <see cref="T:System.IO.FileInfo"/>
            containing the source SVG
            </param>
            <param name="pdfFile">
            the
            <see cref="T:System.IO.FileInfo"/>
            containing the resulting PDF
            </param>
            <param name="props">
            a
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.CreatePdf(System.IO.FileInfo,System.IO.FileInfo,iText.Kernel.Pdf.WriterProperties)">
            <summary>
            Converts SVG stored in a
            <see cref="T:System.IO.FileInfo"/>
            to a PDF
            <see cref="T:System.IO.FileInfo"/>
            ,
            using
            <see cref="T:iText.Kernel.Pdf.WriterProperties"/>
            </summary>
            <param name="svgFile">
            the
            <see cref="T:System.IO.FileInfo"/>
            containing the source SVG
            </param>
            <param name="pdfFile">
            the
            <see cref="T:System.IO.FileInfo"/>
            containing the resulting PDF
            </param>
            <param name="writerProps">
            the
            <see cref="T:iText.Kernel.Pdf.WriterProperties"/>
            for the pdf document
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.CreatePdf(System.IO.FileInfo,System.IO.FileInfo,iText.Svg.Processors.ISvgConverterProperties,iText.Kernel.Pdf.WriterProperties)">
            <summary>
            Converts SVG stored in a
            <see cref="T:System.IO.FileInfo"/>
            to a PDF
            <see cref="T:System.IO.FileInfo"/>
            ,
            using specific
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            and
            <see cref="T:iText.Kernel.Pdf.WriterProperties"/>.
            </summary>
            <param name="svgFile">
            the
            <see cref="T:System.IO.FileInfo"/>
            containing the source SVG
            </param>
            <param name="pdfFile">
            the
            <see cref="T:System.IO.FileInfo"/>
            containing the resulting PDF
            </param>
            <param name="props">
            a
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <param name="writerProps">
            a
            <see cref="T:iText.Kernel.Pdf.WriterProperties"/>
            for the pdf document
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToSvgConverterProps(iText.Svg.Processors.ISvgConverterProperties,System.String)">
            <summary>Copies properties from custom ISvgConverterProperties into new SvgConverterProperties.</summary>
            <remarks>
            Copies properties from custom ISvgConverterProperties into new SvgConverterProperties.
            Since ISvgConverterProperties itself is immutable we have to do it.
            </remarks>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <param name="baseUri">the directory of new SvgConverterProperties</param>
            <returns>new SvgConverterProperties.</returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.CreatePdf(System.IO.Stream,System.IO.Stream)">
            <summary>Create a single page pdf containing the SVG on its page using the default processing and drawing logic
                </summary>
            <param name="svgStream">
            
            <see cref="T:System.IO.Stream">Stream</see>
            containing the SVG
            </param>
            <param name="pdfDest">PDF destination outputStream</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.CreatePdf(System.IO.Stream,System.IO.Stream,iText.Kernel.Pdf.WriterProperties)">
            <summary>Create a single page pdf containing the SVG on its page using the default processing and drawing logic
                </summary>
            <param name="svgStream">
            
            <see cref="T:System.IO.Stream">Stream</see>
            containing the SVG
            </param>
            <param name="pdfDest">PDF destination outputStream</param>
            <param name="writerProps">writer properties for the pdf document</param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.CreatePdf(System.IO.Stream,System.IO.Stream,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Create a single page pdf containing the SVG on its page using the default processing and drawing logic
                </summary>
            <param name="svgStream">
            
            <see cref="T:System.IO.Stream">Stream</see>
            containing the SVG
            </param>
            <param name="pdfDest">PDF destination outputStream</param>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.CreatePdf(System.IO.Stream,System.IO.Stream,iText.Svg.Processors.ISvgConverterProperties,iText.Kernel.Pdf.WriterProperties)">
            <summary>Create a single page pdf containing the SVG on its page using the default processing and drawing logic
                </summary>
            <param name="svgStream">
            
            <see cref="T:System.IO.Stream">Stream</see>
            containing the SVG
            </param>
            <param name="pdfDest">PDF destination outputStream</param>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <param name="writerProps">
            
            <see cref="T:iText.Kernel.Pdf.WriterProperties"/>
            for the pdf document
            </param>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(System.String,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            </summary>
            <remarks>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            . This method does NOT manipulate the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            in any way.
            <para />
            This method (or its overloads) is the best method to use if you want to
            reuse the same SVG image multiple times on the same
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            <para />
            If you want to reuse this object on other
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instances,
            please either use any of the
            <see cref="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)"/>
            overloads in this same
            class and convert its result to an XObject with
            <see cref="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(iText.Svg.Renderers.ISvgNodeRenderer,iText.Kernel.Pdf.PdfDocument)"/>
            , or look into
            using
            <see cref="M:iText.Kernel.Pdf.PdfObject.CopyTo(iText.Kernel.Pdf.PdfDocument)"/>.
            </remarks>
            <param name="content">the String value containing valid SVG content</param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            containing the PDF instructions
            corresponding to the passed SVG content
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(System.String,iText.Kernel.Pdf.PdfDocument,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            </summary>
            <remarks>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            . This method does NOT manipulate the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            in any way.
            <para />
            This method (or its overloads) is the best method to use if you want to
            reuse the same SVG image multiple times on the same
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            <para />
            If you want to reuse this object on other
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instances,
            please either use any of the
            <see cref="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)"/>
            overloads in this same
            class and convert its result to an XObject with
            <see cref="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(iText.Svg.Renderers.ISvgNodeRenderer,iText.Kernel.Pdf.PdfDocument)"/>
            , or look into
            using
            <see cref="M:iText.Kernel.Pdf.PdfObject.CopyTo(iText.Kernel.Pdf.PdfDocument)"/>.
            </remarks>
            <param name="content">the String value containing valid SVG content</param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            containing the PDF instructions
            corresponding to the passed SVG content
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(System.IO.Stream,iText.Kernel.Pdf.PdfDocument,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            </summary>
            <remarks>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            . This method does NOT manipulate the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            in any way.
            <para />
            This method (or its overloads) is the best method to use if you want to
            reuse the same SVG image multiple times on the same
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            <para />
            If you want to reuse this object on other
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instances,
            please either use any of the
            <see cref="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)"/>
            overloads in this same
            class and convert its result to an XObject with
            <see cref="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(iText.Svg.Renderers.ISvgNodeRenderer,iText.Kernel.Pdf.PdfDocument)"/>
            , or look into
            using
            <see cref="M:iText.Kernel.Pdf.PdfObject.CopyTo(iText.Kernel.Pdf.PdfDocument)"/>.
            </remarks>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            containing the PDF instructions
            corresponding to the passed SVG content
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(System.IO.Stream,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            </summary>
            <remarks>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            . This method does NOT manipulate the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            in any way.
            <para />
            This method (or its overloads) is the best method to use if you want to
            reuse the same SVG image multiple times on the same
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            <para />
            If you want to reuse this object on other
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instances,
            please either use any of the
            <see cref="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)"/>
            overloads in this same
            class and convert its result to an XObject with
            <see cref="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(iText.Svg.Renderers.ISvgNodeRenderer,iText.Kernel.Pdf.PdfDocument)"/>
            , or look into
            using
            <see cref="M:iText.Kernel.Pdf.PdfObject.CopyTo(iText.Kernel.Pdf.PdfDocument)"/>.
            </remarks>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            containing the PDF instructions
            corresponding to the passed SVG content
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToImage(System.IO.Stream,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            </summary>
            <remarks>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            . This method does NOT manipulate the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            in any way.
            <para />
            This method (or its overloads) is the best method to use if you want to
            reuse the same SVG image multiple times on the same
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            <para />
            If you want to reuse this object on other
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instances,
            please either use any of the
            <see cref="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)"/>
            overloads in this same
            class and convert its result to an XObject with
            <see cref="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(iText.Svg.Renderers.ISvgNodeRenderer,iText.Kernel.Pdf.PdfDocument)"/>
            , or look into
            using
            <see cref="M:iText.Kernel.Pdf.PdfObject.CopyTo(iText.Kernel.Pdf.PdfDocument)"/>.
            </remarks>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <returns>
            a
            <see cref="T:iText.Layout.Element.Image">Image</see>
            containing the PDF instructions corresponding to the passed SVG content
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToImage(System.IO.Stream,iText.Kernel.Pdf.PdfDocument,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Layout.Element.Image">image</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            </summary>
            <remarks>
            Converts a String containing valid SVG content to an
            <see cref="T:iText.Layout.Element.Image">image</see>
            that can then be used on the passed
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            . This method does NOT manipulate the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            in any way.
            <para />
            This method (or its overloads) is the best method to use if you want to
            reuse the same SVG image multiple times on the same
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            <para />
            If you want to reuse this object on other
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instances,
            please either use any of the
            <see cref="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)"/>
            overloads in this same
            class and convert its result to an XObject with
            <see cref="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(iText.Svg.Renderers.ISvgNodeRenderer,iText.Kernel.Pdf.PdfDocument)"/>
            , or look into
            using
            <see cref="M:iText.Kernel.Pdf.PdfObject.CopyTo(iText.Kernel.Pdf.PdfDocument)"/>.
            </remarks>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="document">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instance to draw on
            </param>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <returns>
            a
            <see cref="T:iText.Layout.Element.Image">Image</see>
            containing the PDF instructions corresponding to the passed SVG content
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(iText.Svg.Renderers.ISvgNodeRenderer,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            This method draws a NodeRenderer tree to a canvas that is tied to the
            passed document.
            </summary>
            <remarks>
            This method draws a NodeRenderer tree to a canvas that is tied to the
            passed document.
            <para />
            This method (or its overloads) is the best method to use if you want to
            reuse the same SVG image multiple times on the same
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            <para />
            If you want to reuse this object on other
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instances,
            please either use any of the
            <see cref="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)"/>
            overloads in this same
            class and convert its result to an XObject with
            this method, or look into
            using
            <see cref="M:iText.Kernel.Pdf.PdfObject.CopyTo(iText.Kernel.Pdf.PdfDocument)"/>.
            </remarks>
            <param name="topSvgRenderer">
            the
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            instance that contains
            the renderer tree
            </param>
            <param name="document">
            the document that the returned
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            can be drawn on (on any given page
            coordinates)
            </param>
            <returns>
            an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            containing the PDF instructions
            corresponding to the passed node renderer tree.
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ConvertToXObject(iText.Svg.Renderers.ISvgNodeRenderer,iText.Kernel.Pdf.PdfDocument,iText.Svg.Renderers.SvgDrawContext)">
            <summary>
            This method draws a NodeRenderer tree to a canvas that is tied to the
            passed document.
            </summary>
            <remarks>
            This method draws a NodeRenderer tree to a canvas that is tied to the
            passed document.
            <para />
            This method (or its overloads) is the best method to use if you want to
            reuse the same SVG image multiple times on the same
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>.
            <para />
            If you want to reuse this object on other
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            instances,
            please either use any of the
            <see cref="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)"/>
            overloads in this same
            class and convert its result to an XObject with
            this method, or look into
            using
            <see cref="M:iText.Kernel.Pdf.PdfObject.CopyTo(iText.Kernel.Pdf.PdfDocument)"/>.
            </remarks>
            <param name="topSvgRenderer">
            the
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            instance that contains
            the renderer tree
            </param>
            <param name="document">the document that the returned</param>
            <param name="context">the SvgDrawContext</param>
            <returns>
            an
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject">XObject</see>
            containing the PDF instructions
            corresponding to the passed node renderer tree.
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ParseAndProcess(System.IO.Stream)">
            <summary>
            Parse and process an Inputstream containing an SVG, using the default Svg processor (
            <see cref="T:iText.Svg.Processors.Impl.DefaultSvgProcessor"/>
            )
            The parsing of the stream is done using UTF-8 as the default charset.
            </summary>
            <remarks>
            Parse and process an Inputstream containing an SVG, using the default Svg processor (
            <see cref="T:iText.Svg.Processors.Impl.DefaultSvgProcessor"/>
            )
            The parsing of the stream is done using UTF-8 as the default charset.
            The properties used by the processor are the
            <see cref="T:iText.Svg.Processors.Impl.SvgConverterProperties"/>
            </remarks>
            <param name="svgStream">
            
            <see cref="T:System.IO.Stream">Stream</see>
            containing the SVG to parse and process
            </param>
            <returns>
            
            <see cref="T:iText.Svg.Processors.ISvgProcessorResult"/>
            containing the root renderer and metadata of the svg
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ParseAndProcess(System.IO.Stream,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Parse and process an Inputstream containing an SVG, using the default Svg processor (
            <see cref="T:iText.Svg.Processors.Impl.DefaultSvgProcessor"/>
            )
            </summary>
            <param name="svgStream">
            
            <see cref="T:System.IO.Stream">Stream</see>
            containing the SVG to parse and process
            </param>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <returns>
            
            <see cref="T:iText.Svg.Processors.ISvgProcessorResult"/>
            containing the root renderer and metadata of the svg
            </returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Use the default implementation of
            <see cref="T:iText.Svg.Processors.ISvgProcessor"/>
            to convert an XML
            DOM tree to a node renderer tree.
            </summary>
            <remarks>
            Use the default implementation of
            <see cref="T:iText.Svg.Processors.ISvgProcessor"/>
            to convert an XML
            DOM tree to a node renderer tree. The passed properties can modify the default behaviour
            </remarks>
            <param name="root">the XML DOM tree</param>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <returns>a node renderer tree corresponding to the passed XML DOM tree</returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.Parse(System.String)">
            <summary>
            Parse a String containing valid SVG into an XML DOM node, using the
            default JSoup XML parser.
            </summary>
            <param name="content">the String value containing valid SVG content</param>
            <returns>an XML DOM tree corresponding to the passed String input</returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.Parse(System.IO.Stream)">
            <summary>
            Parse a Stream containing valid SVG into an XML DOM node, using the
            default JSoup XML parser.
            </summary>
            <remarks>
            Parse a Stream containing valid SVG into an XML DOM node, using the
            default JSoup XML parser. This method will assume that the encoding of
            the Stream is
            <c>UTF-8</c>.
            </remarks>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <returns>an XML DOM tree corresponding to the passed String input</returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.Parse(System.IO.Stream,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Parse a Stream containing valid SVG into an XML DOM node, using the
            default JSoup XML parser.
            </summary>
            <remarks>
            Parse a Stream containing valid SVG into an XML DOM node, using the
            default JSoup XML parser. This method will assume that the encoding of
            the Stream is
            <c>UTF-8</c>
            , unless specified otherwise by the method
            <see cref="M:iText.Svg.Processors.ISvgConverterProperties.GetCharset"/>
            of the
            <paramref name="props"/>
            parameter.
            </remarks>
            <param name="stream">
            the
            <see cref="T:System.IO.Stream">Stream</see>
            containing valid SVG content
            </param>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <returns>an XML DOM tree corresponding to the passed String input</returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.ExtractWidthAndHeight(iText.Svg.Renderers.ISvgNodeRenderer)">
            <summary>
            Extract width and height of the passed SVGNodeRenderer,
            defaulting to respective viewbox values if either one is not present or
            to browser default if viewbox is missing as well
            </summary>
            <param name="topSvgRenderer">
            the
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            instance that contains
            the renderer tree
            </param>
            <returns>float[2], width is in position 0, height in position 1</returns>
        </member>
        <member name="M:iText.Svg.Converter.SvgConverter.TryToExtractCharset(iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Tries to extract charset from
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>.
            </summary>
            <param name="props">
            
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            an instance for extra properties to customize the behavior
            </param>
            <returns>charset | null</returns>
        </member>
        <member name="T:iText.Svg.Css.Impl.SvgAttributeInheritance">
            <summary>Helper class that allows you to check if a property is inheritable.</summary>
        </member>
        <member name="F:iText.Svg.Css.Impl.SvgAttributeInheritance.inheritableProperties">
            <summary>
            Set of inheritable SVG style attributes
            in accordance with "http://www.w3schools.com/cssref/"
            and "https://developer.mozilla.org/en-US/docs/Web/CSS/Reference"
            </summary>
        </member>
        <member name="T:iText.Svg.Css.Impl.SvgNodeRendererInheritanceResolver">
            <summary>
            Style and attribute inheritance resolver for
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            objects.
            </summary>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgNodeRendererInheritanceResolver.ApplyInheritanceToSubTree(iText.Svg.Renderers.ISvgNodeRenderer,iText.Svg.Renderers.ISvgNodeRenderer,iText.Svg.Css.SvgCssContext)">
            <summary>Apply style and attribute inheritance to the tree formed by the root and the subTree.</summary>
            <param name="root">the renderer to consider as the root of the subtree</param>
            <param name="subTree">
            the tree of
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            </param>
            <param name="cssContext">the current SVG CSS context</param>
        </member>
        <member name="T:iText.Svg.Css.Impl.SvgStyleResolver">
            <summary>Default implementation of SVG`s styles and attribute resolver .</summary>
        </member>
        <member name="F:iText.Svg.Css.Impl.SvgStyleResolver.deviceDescription">
            <summary>The device description.</summary>
        </member>
        <member name="F:iText.Svg.Css.Impl.SvgStyleResolver.fonts">
            <summary>The list of fonts.</summary>
        </member>
        <member name="F:iText.Svg.Css.Impl.SvgStyleResolver.resourceResolver">
            <summary>The resource resolver</summary>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.#ctor(System.IO.Stream,iText.Svg.Processors.Impl.SvgProcessorContext)">
            <summary>
            Creates a
            <see cref="T:iText.Svg.Css.Impl.SvgStyleResolver"/>
            with a given default CSS.
            </summary>
            <param name="defaultCssStream">the default CSS</param>
            <param name="context">the processor context</param>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.#ctor(iText.Svg.Processors.Impl.SvgProcessorContext)">
            <summary>
            Creates a
            <see cref="T:iText.Svg.Css.Impl.SvgStyleResolver"/>.
            </summary>
            <param name="context">the processor context</param>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.#ctor(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.Impl.SvgProcessorContext)">
            <summary>
            Creates a
            <see cref="T:iText.Svg.Css.Impl.SvgStyleResolver"/>.
            </summary>
            <remarks>
            Creates a
            <see cref="T:iText.Svg.Css.Impl.SvgStyleResolver"/>
            . This constructor will instantiate its internal
            style sheet and it will collect the css declarations from the provided node.
            </remarks>
            <param name="rootNode">node to collect css from</param>
            <param name="context">the processor context</param>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.ResolveNativeStyles(iText.StyledXmlParser.Node.INode,iText.StyledXmlParser.Css.Resolve.AbstractCssContext)">
            <summary>Resolves node styles without inheritance of parent element styles.</summary>
            <param name="node">the node</param>
            <param name="cssContext">the CSS context (RootFontSize, etc.)</param>
            <returns>the map containing the resolved styles that are defined in the body of the element</returns>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.ProcessXLink(iText.StyledXmlParser.Node.IAttribute,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Resolves the full path of link href attribute,
            thanks to the resource resolver.
            </summary>
            <param name="attr">the attribute to process</param>
            <param name="attributesMap">the element styles map</param>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.IsStartedWithHash(System.String)">
            <summary>Checks if string starts with #.</summary>
            <param name="s">the test string</param>
            <returns>true if the string starts with #, otherwise false</returns>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.GetFonts">
            <summary>Gets the list of fonts.</summary>
            <returns>
            the list of
            <see cref="T:iText.StyledXmlParser.Css.CssFontFaceRule"/>
            instances
            </returns>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.CollectFonts">
            <summary>Collects fonts from the style sheet.</summary>
        </member>
        <member name="M:iText.Svg.Css.Impl.SvgStyleResolver.CollectFonts(iText.StyledXmlParser.Css.CssStatement)">
            <summary>
            Collects fonts from a
            <see cref="T:iText.StyledXmlParser.Css.CssStatement"/>.
            </summary>
            <param name="cssStatement">the CSS statement</param>
        </member>
        <member name="T:iText.Svg.Css.SvgCssContext">
            <summary>
            Context necessary for evaluating certain Css statements whose final values depends on other statements
            e.g. relative font-size statements.
            </summary>
        </member>
        <member name="F:iText.Svg.Css.SvgCssContext.rootFontSize">
            <summary>The root font size value in pt.</summary>
        </member>
        <member name="M:iText.Svg.Css.SvgCssContext.GetRootFontSize">
            <summary>Gets the root font size.</summary>
            <returns>the root font size in pt</returns>
        </member>
        <member name="M:iText.Svg.Css.SvgCssContext.SetRootFontSize(System.String)">
            <summary>Sets the root font size.</summary>
            <param name="fontSizeStr">the new root font size</param>
        </member>
        <member name="T:iText.Svg.Css.SvgStrokeParameterConverter">
            <summary>This class converts stroke related SVG parameters and attributes into those from PDF specification.
                </summary>
        </member>
        <member name="M:iText.Svg.Css.SvgStrokeParameterConverter.ConvertStrokeDashParameters(System.String,System.String,System.Single,iText.Svg.Renderers.SvgDrawContext)">
            <summary>Convert stroke related SVG parameters and attributes into PDF line dash parameters.</summary>
            <param name="strokeDashArray">'stroke-dasharray' css property value.</param>
            <param name="strokeDashOffset">'stroke-dashoffset' css property value.</param>
            <param name="fontSize">font size of the current element.</param>
            <param name="context">the svg draw context.</param>
            <returns>
            PDF line dash parameters represented by
            <see cref="T:iText.Svg.Css.SvgStrokeParameterConverter.PdfLineDashParameters"/>.
            </returns>
        </member>
        <member name="T:iText.Svg.Css.SvgStrokeParameterConverter.PdfLineDashParameters">
            <summary>This class represents PDF dash parameters.</summary>
        </member>
        <member name="M:iText.Svg.Css.SvgStrokeParameterConverter.PdfLineDashParameters.#ctor(System.Single[],System.Single)">
            <summary>Construct PDF dash parameters.</summary>
            <param name="dashArray">
            Numbers that specify the lengths of alternating dashes and gaps;
            the numbers shall be nonnegative and not all zero.
            </param>
            <param name="dashPhase">A number that specifies the distance into the dash pattern at which to start the dash.
                </param>
        </member>
        <member name="M:iText.Svg.Css.SvgStrokeParameterConverter.PdfLineDashParameters.GetDashArray">
            <summary>Return dash array.</summary>
            <returns>dash array.</returns>
        </member>
        <member name="M:iText.Svg.Css.SvgStrokeParameterConverter.PdfLineDashParameters.GetDashPhase">
            <summary>Return dash phase.</summary>
            <returns>dash phase.</returns>
        </member>
        <member name="M:iText.Svg.Css.SvgStrokeParameterConverter.PdfLineDashParameters.Equals(System.Object)">
            <summary>Check if some object is equal to the given object.</summary>
        </member>
        <member name="M:iText.Svg.Css.SvgStrokeParameterConverter.PdfLineDashParameters.GetHashCode">
            <summary>Generate a hash code for this object.</summary>
            <returns>hash code.</returns>
        </member>
        <member name="T:iText.Svg.Element.SvgImage">
            <summary>A layout element that represents SVG image for inclusion in the document model.</summary>
        </member>
        <member name="M:iText.Svg.Element.SvgImage.#ctor(iText.Svg.Xobject.SvgImageXObject)">
            <summary>
            Creates an
            <see cref="T:iText.Svg.Element.SvgImage"/>
            from the
            <see cref="T:iText.Svg.Xobject.SvgImageXObject"/>
            which represents Form XObject and contains
            processor result with the SVG information and resource resolver for the SVG image.
            </summary>
            <param name="xObject">
            an internal
            <see cref="T:iText.Svg.Xobject.SvgImageXObject"/>.
            </param>
        </member>
        <member name="M:iText.Svg.Element.SvgImage.Generate(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Draws SVG image to a canvas-like object maintained in the
            <see cref="T:iText.Svg.Renderers.SvgDrawContext"/>.
            </summary>
            <param name="document">pdf that shall contain the SVG image.</param>
        </member>
        <member name="M:iText.Svg.Element.SvgImage.MakeNewRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Svg.Exceptions.SvgExceptionMessageConstant">
            <summary>Class that bundles all the error message templates as constants.</summary>
        </member>
        <member name="T:iText.Svg.Exceptions.SvgProcessingException">
            <summary>
            Exception thrown by
            <see cref="T:iText.Svg.Processors.ISvgProcessor"/>
            when it cannot process an SVG
            </summary>
        </member>
        <member name="M:iText.Svg.Exceptions.SvgProcessingException.#ctor(System.String)">
            <summary>Creates a new SvgProcessingException instance.</summary>
            <param name="message">the message</param>
        </member>
        <member name="M:iText.Svg.Exceptions.SvgProcessingException.#ctor(System.String,System.Exception)">
            <summary>Creates a new SvgProcessingException instance.</summary>
            <param name="message">the message</param>
            <param name="cause">the nested exception</param>
        </member>
        <member name="T:iText.Svg.Logs.SvgLogMessageConstant">
            <summary>Class that holds the logging and exception messages.</summary>
        </member>
        <member name="T:iText.Svg.MarkerVertexType">
            <summary>
            Defines a property of markable elements (&lt;path&gt;, &lt;line&gt;, &lt;polyline&gt; or
            &lt;polygon&gt;) which is used to determine at which verticies a marker should be drawn.
            </summary>
        </member>
        <member name="F:iText.Svg.MarkerVertexType.MARKER_START">
            <summary>Specifies that marker will be drawn only at the first vertex of element.</summary>
        </member>
        <member name="F:iText.Svg.MarkerVertexType.MARKER_MID">
            <summary>Specifies that marker will be drawn at every vertex except the first and last.</summary>
        </member>
        <member name="F:iText.Svg.MarkerVertexType.MARKER_END">
            <summary>Specifies that marker will be drawn only at the last vertex of element.</summary>
        </member>
        <member name="T:iText.Svg.Processors.Impl.DefaultSvgProcessor">
            <summary>
            Default implementation of
            <see cref="T:iText.Svg.Processors.ISvgProcessor"/>.
            </summary>
            <remarks>
            Default implementation of
            <see cref="T:iText.Svg.Processors.ISvgProcessor"/>.
            This implementation traverses the
            <see cref="T:iText.StyledXmlParser.Node.INode"/>
            tree depth-first,
            using a stack to recreate a tree of
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            with the same structure.
            </remarks>
        </member>
        <member name="M:iText.Svg.Processors.Impl.DefaultSvgProcessor.#ctor">
            <summary>Instantiates a DefaultSvgProcessor object.</summary>
        </member>
        <member name="M:iText.Svg.Processors.Impl.DefaultSvgProcessor.PerformSetup(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>Load in configuration, set initial processorState and create/fill-in context of the processor</summary>
            <param name="converterProps">that contains configuration properties and operations</param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.DefaultSvgProcessor.ExecuteDepthFirstTraversal(iText.StyledXmlParser.Node.INode)">
            <summary>Start the depth-first traversal of the INode tree, pushing the results on the stack</summary>
            <param name="startingNode">node to start on</param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.DefaultSvgProcessor.CreateResultAndClean">
            <summary>Extract result from internal processorState and clean up afterwards</summary>
            <returns>Root renderer of the processed SVG</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.DefaultSvgProcessor.Visit(iText.StyledXmlParser.Node.INode)">
            <summary>Recursive visit of the object tree, depth-first, processing the visited node and calling visit on its children.
                </summary>
            <remarks>
            Recursive visit of the object tree, depth-first, processing the visited node and calling visit on its children.
            Visit responsibilities for element nodes:
            - Assign styles(CSS and attributes) to element
            - Create Renderer based on element
            - push and pop renderer to stack
            Visit responsibilities for text nodes
            - add text to parent object
            </remarks>
            <param name="node">INode to visit</param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.DefaultSvgProcessor.ProcessAsText(iText.StyledXmlParser.Node.INode)">
            <summary>Check if this node is a text node that needs to be processed by the parent</summary>
            <param name="node">node to check</param>
            <returns>true if the node should be processed as text, false otherwise</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.DefaultSvgProcessor.ProcessText(iText.StyledXmlParser.Node.ITextNode)">
            <summary>Process the text contained in the text-node</summary>
            <param name="textNode">node containing text to process</param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.DefaultSvgProcessor.FindFirstElement(iText.StyledXmlParser.Node.INode,System.String)">
            <summary>Find the first element in the node-tree that corresponds with the passed tag-name.</summary>
            <remarks>Find the first element in the node-tree that corresponds with the passed tag-name. Search is performed depth-first
                </remarks>
            <param name="node">root-node to start with</param>
            <param name="tagName">name of the tag that needs to be fonund</param>
            <returns>IElementNode</returns>
        </member>
        <member name="T:iText.Svg.Processors.Impl.Font.SvgFontProcessor">
            <summary>Class that processes and add resolved css fonts to the FontProvider</summary>
        </member>
        <member name="M:iText.Svg.Processors.Impl.Font.SvgFontProcessor.AddFontFaceFonts(iText.StyledXmlParser.Css.ICssResolver)">
            <summary>Adds @font-face fonts to the FontProvider.</summary>
            <param name="cssResolver">the css styles resolver</param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.Font.SvgFontProcessor.CreateFont(System.String,iText.StyledXmlParser.Css.Font.CssFontFace.CssFontFaceSrc,iText.Layout.Font.Range)">
            <summary>Creates a font and adds it to the context.</summary>
            <param name="fontFamily">the font family</param>
            <param name="src">the source of the font</param>
            <returns>true, if successful</returns>
        </member>
        <member name="T:iText.Svg.Processors.Impl.ProcessorState">
            <summary>
            Internal ProcessorState representation for
            <see cref="T:iText.Svg.Processors.Impl.DefaultSvgProcessor"/>
            </summary>
        </member>
        <member name="M:iText.Svg.Processors.Impl.ProcessorState.#ctor">
            <summary>Instantiates the processor state.</summary>
        </member>
        <member name="M:iText.Svg.Processors.Impl.ProcessorState.Size">
            <summary>Returns the amount of ISvgNodeRenderers being processed.</summary>
            <returns>amount of ISvgNodeRenderers</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.ProcessorState.Push(iText.Svg.Renderers.ISvgNodeRenderer)">
            <summary>Adds an ISvgNodeRenderer to the processor's state.</summary>
            <param name="svgNodeRenderer">renderer to be added to the state</param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.ProcessorState.Pop">
            <summary>Removes and returns the first renderer of the processor state.</summary>
            <returns>the removed ISvgNodeRenderer object</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.ProcessorState.Top">
            <summary>Returns the first ISvgNodeRenderer object without removing it.</summary>
            <returns>the first ISvgNodeRenderer</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.ProcessorState.Empty">
            <summary>Returns true when the processorstate is empty, false when there is at least one ISvgNodeRenderer in the state.
                </summary>
            <returns>true if empty, false if not empty</returns>
        </member>
        <member name="T:iText.Svg.Processors.Impl.SvgConverterProperties">
            <summary>
            Default and fallback implementation of
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            for
            <see cref="T:iText.Svg.Processors.Impl.DefaultSvgProcessor"/>.
            </summary>
        </member>
        <member name="F:iText.Svg.Processors.Impl.SvgConverterProperties.mediaDeviceDescription">
            <summary>The media device description.</summary>
        </member>
        <member name="F:iText.Svg.Processors.Impl.SvgConverterProperties.fontProvider">
            <summary>The font provider.</summary>
        </member>
        <member name="F:iText.Svg.Processors.Impl.SvgConverterProperties.baseUri">
            <summary>The base URI.</summary>
        </member>
        <member name="F:iText.Svg.Processors.Impl.SvgConverterProperties.resourceRetriever">
            <summary>The resource retriever.</summary>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgConverterProperties.#ctor">
            <summary>
            Creates a new
            <see cref="T:iText.Svg.Processors.Impl.SvgConverterProperties"/>
            instance.
            </summary>
            <remarks>
            Creates a new
            <see cref="T:iText.Svg.Processors.Impl.SvgConverterProperties"/>
            instance.
            Instantiates its members, IResourceRetriever and ISvgNodeRendererFactory, to its default implementations.
            </remarks>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgConverterProperties.GetBaseUri">
            <summary>Gets the base URI.</summary>
            <returns>the base URI</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgConverterProperties.GetFontProvider">
            <summary>Gets the font provider.</summary>
            <returns>the font provider</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgConverterProperties.GetMediaDeviceDescription">
            <summary>Gets the media device description.</summary>
            <returns>the media device description</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgConverterProperties.SetMediaDeviceDescription(iText.StyledXmlParser.Css.Media.MediaDeviceDescription)">
            <summary>Sets the media device description.</summary>
            <param name="mediaDeviceDescription">the media device description</param>
            <returns>the ConverterProperties instance</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgConverterProperties.SetBaseUri(System.String)">
            <summary>Sets the base URI.</summary>
            <param name="baseUri">the base URI</param>
            <returns>the ConverterProperties instance</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgConverterProperties.SetResourceRetriever(iText.StyledXmlParser.Resolver.Resource.IResourceRetriever)">
            <summary>Sets the resource retriever.</summary>
            <remarks>
            Sets the resource retriever.
            The resourceRetriever is used to retrieve data from resources by URL.
            </remarks>
            <param name="resourceRetriever">the resource retriever</param>
            <returns>
            the
            <see cref="T:iText.Svg.Processors.Impl.SvgConverterProperties"/>
            instance
            </returns>
        </member>
        <member name="T:iText.Svg.Processors.Impl.SvgProcessorContext">
            <summary>Context class with accessors to properties/objects used in processing Svg documents</summary>
        </member>
        <member name="F:iText.Svg.Processors.Impl.SvgProcessorContext.fontProvider">
            <summary>The font provider.</summary>
        </member>
        <member name="F:iText.Svg.Processors.Impl.SvgProcessorContext.tempFonts">
            <summary>Temporary set of fonts used in the PDF.</summary>
        </member>
        <member name="F:iText.Svg.Processors.Impl.SvgProcessorContext.deviceDescription">
            <summary>The device description.</summary>
        </member>
        <member name="F:iText.Svg.Processors.Impl.SvgProcessorContext.cssContext">
            <summary>The SVG CSS context.</summary>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.#ctor(iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Instantiates a new
            <see cref="T:iText.Svg.Processors.Impl.SvgProcessorContext"/>
            instance.
            </summary>
            <param name="converterProperties">
            a
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>
            instance
            </param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.GetFontProvider">
            <summary>Gets the font provider.</summary>
            <returns>the font provider</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.GetResourceResolver">
            <summary>Gets the resource resolver.</summary>
            <returns>the resource resolver</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.GetDeviceDescription">
            <summary>Gets the device description.</summary>
            <returns>the device description</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.GetTempFonts">
            <summary>Gets the temporary set of fonts.</summary>
            <returns>the set of fonts</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.GetCssContext">
            <summary>Gets the SVG CSS context.</summary>
            <returns>the SVG CSS context</returns>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.AddTemporaryFont(iText.IO.Font.FontProgram,System.String,System.String,iText.Layout.Font.Range)">
            <summary>Add temporary font from @font-face.</summary>
            <param name="fontProgram">the font program</param>
            <param name="encoding">the encoding</param>
            <param name="alias">the alias</param>
            <param name="unicodeRange">the specific range of characters to be used from the font</param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.AddTemporaryFont(iText.IO.Font.FontProgram,System.String,System.String)">
            <summary>Add temporary font from @font-face.</summary>
            <param name="fontProgram">the font program</param>
            <param name="encoding">the encoding</param>
            <param name="alias">the alias</param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorContext.AddTemporaryFont(iText.Layout.Font.FontInfo,System.String)">
            <summary>Add temporary font from @font-face.</summary>
            <param name="fontInfo">the font info</param>
            <param name="alias">the alias</param>
        </member>
        <member name="T:iText.Svg.Processors.Impl.SvgProcessorResult">
            <summary>
            A wrapper class that encapsulates processing results of
            <see cref="T:iText.Svg.Processors.ISvgProcessor"/>
            objects.
            </summary>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorResult.#ctor(System.Collections.Generic.IDictionary{System.String,iText.Svg.Renderers.ISvgNodeRenderer},iText.Svg.Renderers.ISvgNodeRenderer,iText.Svg.Processors.Impl.SvgProcessorContext)">
            <summary>
            Creates new
            <see cref="T:iText.Svg.Processors.Impl.SvgProcessorResult"/>
            entity.
            </summary>
            <param name="namedObjects">
            a map of named-objects with their id's as
            <see cref="T:System.String"/>
            keys and
            the
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            objects as values.
            </param>
            <param name="root">
            a wrapped
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            root renderer.
            </param>
            <param name="context">
            a
            <see cref="T:iText.Svg.Processors.Impl.SvgProcessorContext"/>
            instance.
            </param>
        </member>
        <member name="M:iText.Svg.Processors.Impl.SvgProcessorResult.GetContext">
            <summary>
            Gets processor context, containing
            <see cref="T:iText.Layout.Font.FontProvider"/>
            and
            <see cref="T:iText.Layout.Font.FontSet"/>
            of temporary fonts inside.
            </summary>
            <returns>
            
            <see cref="T:iText.Svg.Processors.Impl.SvgProcessorContext"/>
            instance
            </returns>
        </member>
        <member name="T:iText.Svg.Processors.ISvgConverterProperties">
            <summary>
            Interface for the configuration classes used by
            <see cref="T:iText.Svg.Processors.ISvgProcessor"/>
            </summary>
        </member>
        <member name="M:iText.Svg.Processors.ISvgConverterProperties.GetRendererFactory">
            <summary>
            Retrieve the factory responsible for creating
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            </summary>
            <returns>
            A
            <see cref="T:iText.Svg.Renderers.Factories.ISvgNodeRendererFactory"/>
            implementation
            </returns>
        </member>
        <member name="M:iText.Svg.Processors.ISvgConverterProperties.GetFontProvider">
            <summary>Gets the font provider.</summary>
            <returns>the font provider</returns>
        </member>
        <member name="M:iText.Svg.Processors.ISvgConverterProperties.GetCharset">
            <summary>Get the name of the Charset to be used when decoding an InputStream.</summary>
            <remarks>
            Get the name of the Charset to be used when decoding an InputStream. This
            method is allowed to return null, in which case
            <c>UTF-8</c>
            will
            be used (by JSoup).
            <para />
            Please be aware that this method is NOT used when handling a
            <c>String</c>
            variable in the
            <see cref="T:iText.Svg.Converter.SvgConverter"/>.
            </remarks>
            <returns>
            the String name of the
            <see cref="T:System.Text.Encoding"/>
            used for decoding
            </returns>
        </member>
        <member name="M:iText.Svg.Processors.ISvgConverterProperties.GetBaseUri">
            <summary>Gets the base URI.</summary>
            <returns>the base URI</returns>
        </member>
        <member name="M:iText.Svg.Processors.ISvgConverterProperties.GetMediaDeviceDescription">
            <summary>Gets the media device description.</summary>
            <returns>the media device description</returns>
        </member>
        <member name="M:iText.Svg.Processors.ISvgConverterProperties.GetResourceRetriever">
            <summary>Gets the resource retriever.</summary>
            <remarks>
            Gets the resource retriever.
            The resourceRetriever is used to retrieve data from resources by URL.
            </remarks>
            <returns>the resource retriever</returns>
        </member>
        <member name="T:iText.Svg.Processors.ISvgProcessor">
            <summary>Interface for SVG processors.</summary>
            <remarks>
            Interface for SVG processors.
            Processors take the root
            <see cref="T:iText.StyledXmlParser.Node.INode"/>
            that corresponds to a Svg element
            and return a
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            that serves as the root for the same SVG
            </remarks>
        </member>
        <member name="M:iText.Svg.Processors.ISvgProcessor.Process(iText.StyledXmlParser.Node.INode,iText.Svg.Processors.ISvgConverterProperties)">
            <summary>
            Process an SVG, returning the root of a renderer-tree and a list
            of named objects wrapped in a processor result object
            </summary>
            <param name="root">Root of the INode representation of the SVG</param>
            <param name="converterProps">configuration properties</param>
            <returns>
            root of the renderer-tree representing the SVG wrapped in {link
            <see cref="T:iText.Svg.Processors.ISvgProcessorResult"/>
            }
            </returns>
        </member>
        <member name="T:iText.Svg.Processors.ISvgProcessorResult">
            <summary>Interface for SVG processors results.</summary>
        </member>
        <member name="M:iText.Svg.Processors.ISvgProcessorResult.GetNamedObjects">
            <summary>Obtains a map of named-objects with their id's as keys and the objects as values</summary>
            <returns>
            Map of Strings as keys and
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            as values
            </returns>
        </member>
        <member name="M:iText.Svg.Processors.ISvgProcessorResult.GetRootRenderer">
            <summary>
            Obtains the wrapped
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            root renderer.
            </summary>
            <returns>ISvgNodeRenderer</returns>
        </member>
        <member name="M:iText.Svg.Processors.ISvgProcessorResult.GetFontProvider">
            <summary>
            Obtains the
            <see cref="T:iText.Layout.Font.FontProvider"/>.
            </summary>
            <returns>FontProvider</returns>
        </member>
        <member name="M:iText.Svg.Processors.ISvgProcessorResult.GetTempFonts">
            <summary>Obtains the list of temporary fonts</summary>
            <returns>FontSet</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Factories.DefaultSvgNodeRendererFactory">
            <summary>
            The default implementation of
            <see cref="T:iText.Svg.Renderers.Factories.ISvgNodeRendererFactory"/>
            that will be
            used by default by the entry points defined by this project.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Factories.DefaultSvgNodeRendererFactory.#ctor">
            <summary>
            Default constructor with default
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            creation logic.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Factories.DefaultSvgNodeRendererMapper">
            <summary>
            Contains the mapping of the default implementations, provided by this project for the standard SVG
            tags as defined in the SVG Specification.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Factories.DefaultSvgNodeRendererMapper.#ctor">
            <summary>
            Creates a new
            <see cref="T:iText.Svg.Renderers.Factories.DefaultSvgNodeRendererMapper"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Factories.DefaultSvgNodeRendererMapper.GetMapping">
            <summary>Gets the default SVG tags mapping.</summary>
            <returns>the default SVG tags mapping</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Factories.DefaultSvgNodeRendererMapper.GetIgnoredTags">
            <summary>Gets the default ignored SVG tags.</summary>
            <returns>default ignored SVG tags</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Factories.ISvgNodeRendererFactory">
            <summary>
            Interface for the factory used by
            <see cref="T:iText.Svg.Processors.Impl.DefaultSvgProcessor"/>.
            </summary>
            <remarks>
            Interface for the factory used by
            <see cref="T:iText.Svg.Processors.Impl.DefaultSvgProcessor"/>.
            Pass along using
            <see cref="T:iText.Svg.Processors.ISvgConverterProperties"/>.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.Factories.ISvgNodeRendererFactory.CreateSvgNodeRendererForTag(iText.StyledXmlParser.Node.IElementNode,iText.Svg.Renderers.ISvgNodeRenderer)">
            <summary>Create a configured renderer based on the passed Svg tag and set its parent.</summary>
            <param name="tag">Representation of the Svg tag, with all style attributes set</param>
            <param name="parent">renderer of the parent tag</param>
            <returns>Configured ISvgNodeRenderer</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Factories.ISvgNodeRendererFactory.IsTagIgnored(iText.StyledXmlParser.Node.IElementNode)">
            <summary>Checks whether the provided tag is an ignored tag of this factory or not.</summary>
            <remarks>Checks whether the provided tag is an ignored tag of this factory or not. If ignored, the factory won't process this IElementNode into an ISvgNodeRenderer.
                </remarks>
            <param name="tag">the IElementNode</param>
            <returns>true if ignored</returns>
        </member>
        <member name="T:iText.Svg.Renderers.IBranchSvgNodeRenderer">
            <summary>Interface that defines branches in the NodeRenderer structure.</summary>
            <remarks>
            Interface that defines branches in the NodeRenderer structure. Differs from a leaf renderer
            in that a branch has children and as such methods that can add or retrieve those children.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.IBranchSvgNodeRenderer.AddChild(iText.Svg.Renderers.ISvgNodeRenderer)">
            <summary>Adds a renderer as the last element of the list of children.</summary>
            <param name="child">any renderer</param>
        </member>
        <member name="M:iText.Svg.Renderers.IBranchSvgNodeRenderer.GetChildren">
            <summary>Gets all child renderers of this object.</summary>
            <returns>the list of child renderers (in the order that they were added)</returns>
        </member>
        <member name="T:iText.Svg.Renderers.IMarkerCapable">
            <summary>Interface implemented by elements that support marker drawing.</summary>
            <remarks>
            Interface implemented by elements that support marker drawing.
            Defines methods for working with markers.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.IMarkerCapable.DrawMarker(iText.Svg.Renderers.SvgDrawContext,iText.Svg.MarkerVertexType)">
            <summary>Draws a marker in the specified context.</summary>
            <remarks>
            Draws a marker in the specified context.
            The marker is drawn on the vertices defined according to the given marker type.
            </remarks>
            <param name="context">the object that knows the place to draw this element and maintains its state</param>
            <param name="markerVertexType">
            type of marker that determine on which vertices of the given element
            marker should  be drawn
            </param>
        </member>
        <member name="M:iText.Svg.Renderers.IMarkerCapable.GetAutoOrientAngle(iText.Svg.Renderers.Impl.MarkerSvgNodeRenderer,System.Boolean)">
            <summary>
            Calculates marker orientation angle if
            <c>orient</c>
            attribute is set to
            <c>auto</c>
            </summary>
            <param name="marker">marker for which the rotation angle should be calculated</param>
            <param name="reverse">indicates that the resulting angle should be rotated 180 degrees</param>
            <returns>
            marker orientation angle so that its positive x-axis is pointing in the direction of the path at the
            point it is placed
            </returns>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer">
            <summary>
            Abstract class that will be the superclass for any element that can function
            as a parent.
            </summary>
        </member>
        <member name="F:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer.VIEWBOX_VALUES_NUMBER">
            <summary>The number of viewBox values.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer.DoDraw(iText.Svg.Renderers.SvgDrawContext)">
            <summary>
            Method that will set properties to be inherited by this branch renderer's
            children and will iterate over all children in order to draw them.
            </summary>
            <param name="context">
            the object that knows the place to draw this element and
            maintains its state
            </param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer.ApplyViewBox(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Applies a transformation based on a viewBox for a given branch node.</summary>
            <param name="context">current svg draw context</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer.ApplyViewportClip(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Applies a clipping operation based on the view port.</summary>
            <param name="context">the svg draw context</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer.ProcessAspectRatioPosition(iText.Svg.Renderers.SvgDrawContext,System.Single[],System.String,System.Single,System.Single)">
            <summary>If present, process the preserveAspectRatio position.</summary>
            <param name="context">the svg draw context</param>
            <param name="viewBoxValues">the four values depicting the viewbox [min-x min-y width height]</param>
            <param name="align">alignment method to use</param>
            <param name="scaleWidth">the multiplier for scaling width</param>
            <param name="scaleHeight">the multiplier for scaling height</param>
            <returns>the transformation based on the preserveAspectRatio value</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer.CleanUp(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Cleans up the SvgDrawContext by removing the current viewport and by popping the current canvas.</summary>
            <param name="context">context to clean</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer.DeepCopyChildren(iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer)">
            <summary>
            Create a deep copy of every child renderer and add them to the passed
            <see cref="T:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer"/>
            </summary>
            <param name="deepCopy">renderer to add copies of children to</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractBranchSvgNodeRenderer.WriteBBoxAccordingToVisibleOverflow(iText.Svg.Renderers.SvgDrawContext,iText.Kernel.Pdf.PdfStream)">
            <summary>
            When in the svg element
            <c>overflow</c>
            is
            <c>visible</c>
            the corresponding formXObject
            should have a BBox (form XObject’s bounding box; see PDF 32000-1:2008 - 8.10.2 Form Dictionaries)
            that should cover the entire svg space (page in pdf) in order to be able to show parts of the element which are outside the current element viewPort.
            </summary>
            <remarks>
            When in the svg element
            <c>overflow</c>
            is
            <c>visible</c>
            the corresponding formXObject
            should have a BBox (form XObject’s bounding box; see PDF 32000-1:2008 - 8.10.2 Form Dictionaries)
            that should cover the entire svg space (page in pdf) in order to be able to show parts of the element which are outside the current element viewPort.
            To do this, we get the inverse matrix of all the current transformation matrix changes and apply it to the root viewPort.
            This allows you to get the root rectangle in the final coordinate system.
            </remarks>
            <param name="context">current context to get canvases and view ports</param>
            <param name="stream">stream to write a BBox</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractContainerSvgNodeRenderer.CalculateViewPort(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Calculate the viewport based on the context.</summary>
            <param name="context">the SVG draw context</param>
            <returns>the viewport that applies to this renderer</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.AbstractGradientSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            abstract implementation for gradient tags
            (&lt;linearGradient&gt;, &lt;radialGradient&gt;).
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractGradientSvgNodeRenderer.IsObjectBoundingBoxUnits">
            <summary>Checks whether the gradient units values are on user space on use or object bounding box</summary>
            <returns>
            
            <see langword="false"/>
            if the 'gradientUnits' value of the gradient tag equals
            to 'userSpaceOnUse', otherwise
            <see langword="true"/>
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractGradientSvgNodeRenderer.GetGradientTransform">
            <summary>Evaluates the 'gradientTransform' transformations</summary>
            <returns>
            an
            <see cref="T:iText.Kernel.Geom.AffineTransform"/>
            object representing the specified gradient transformation
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractGradientSvgNodeRenderer.GetChildStopRenderers">
            <summary>Construct a list of child stop renderers</summary>
            <returns>
            a list of
            <see cref="T:iText.Svg.Renderers.Impl.StopSvgNodeRenderer"/>
            elements that represents the child stop values
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractGradientSvgNodeRenderer.ParseSpreadMethod">
            <summary>Parses the gradient spread method</summary>
            <returns>
            the parsed
            <see cref="T:iText.Kernel.Colors.Gradients.GradientSpreadMethod"/>
            specified in the gradient
            </returns>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            abstract implementation.
            </summary>
        </member>
        <member name="F:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.attributesAndStyles">
            <summary>Map that contains attributes and styles used for drawing operations.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.GetAttributeOrDefault(System.String,System.String)">
            <summary>
            Retrieves the property value for a given key name or default if the property value is
            <see langword="null"/>
            or missing.
            </summary>
            <param name="key">the name of the property to search for</param>
            <param name="defaultValue">
            the default value to be returned if the property is
            <see langword="null"/>
            or missing
            </param>
            <returns>
            the value for this key, or
            <paramref name="defaultValue"/>
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.Draw(iText.Svg.Renderers.SvgDrawContext)">
            <summary>
            Applies transformations set to this object, if any, and delegates the drawing of this element and its children
            to the
            <see cref="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.DoDraw(iText.Svg.Renderers.SvgDrawContext)">doDraw</see>
            method.
            </summary>
            <param name="context">the object that knows the place to draw this element and maintains its state</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.CanElementFill">
            <summary>Method to see if a certain renderer can use fill.</summary>
            <returns>true if the renderer can use fill</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.CanConstructViewPort">
            <summary>Method to see if the renderer can create a viewport</summary>
            <returns>true if the renderer can construct a viewport</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.GetCurrentFontSize">
            <summary>Return font-size of the current element</summary>
            <returns>absolute value of font-size</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.DeepCopyAttributesAndStyles(iText.Svg.Renderers.ISvgNodeRenderer)">
            <summary>
            Make a deep copy of the styles and attributes of this renderer
            Helper method for deep copying logic
            </summary>
            <param name="deepCopy">renderer to insert the deep copied attributes into</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.DoDraw(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Draws this element to a canvas-like object maintained in the context.</summary>
            <param name="context">the object that knows the place to draw this element and maintains its state</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.CalculateViewPortTranslation(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Calculate the transformation for the viewport based on the context.</summary>
            <remarks>
            Calculate the transformation for the viewport based on the context. Only used by elements that can create
            viewports
            </remarks>
            <param name="context">the SVG draw context</param>
            <returns>the transformation that needs to be applied to this renderer</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.PostDraw(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Operations to be performed after drawing the element.</summary>
            <remarks>
            Operations to be performed after drawing the element.
            This includes filling, stroking.
            </remarks>
            <param name="context">the svg draw context</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.PreDraw(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Operations to perform before drawing an element.</summary>
            <remarks>
            Operations to perform before drawing an element.
            This includes setting stroke color and width, fill color.
            </remarks>
            <param name="context">the svg draw context</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.ParseAbsoluteLength(System.String,System.Single,System.Single,iText.Svg.Renderers.SvgDrawContext)">
            <summary>Parse length attributes.</summary>
            <param name="length">
            
            <see cref="T:System.String"/>
            for parsing
            </param>
            <param name="percentBaseValue">the value on which percent length is based on</param>
            <param name="defaultValue">default value if length is not recognized</param>
            <param name="context">
            current
            <see cref="T:iText.Svg.Renderers.SvgDrawContext"/>
            </param>
            <returns>absolute value in points</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.CircleSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;circle&gt; tag.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.ClipPathSvgNodeRenderer">
            <summary>This renderer represents a collection of elements (simple shapes and paths).</summary>
            <remarks>
            This renderer represents a collection of elements (simple shapes and paths).
            The elements are not drawn visibly, but the union of their shapes will be used
            to only show the parts of the drawn objects that fall within the clipping path.
            In PDF, the clipping path operators use the intersection of all its elements, not the union (as in SVG);
            thus, we need to draw the clipped elements multiple times if the clipping path consists of multiple elements.
            </remarks>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.EllipseSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;circle&gt; tag.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.EllipseSvgNodeRenderer.SetParameters">
            <summary>
            Fetches a map of String values by calling getAttribute(String s) method
            and maps it's values to arc parameter cx, cy , rx, ry respectively
            </summary>
            <returns>boolean values to indicate whether all values exit or not</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.GroupSvgNodeRenderer">
            <summary>This renderer represents a branch in an SVG tree.</summary>
            <remarks>This renderer represents a branch in an SVG tree. It doesn't do anything aside from calling the superclass doDraw.
                </remarks>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.ImageSvgNodeRenderer">
            <summary>Responsible for drawing Images to the canvas.</summary>
            <remarks>
            Responsible for drawing Images to the canvas.
            Referenced SVG images aren't supported yet. TODO DEVSIX-2277
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.ISvgTextNodeRenderer.GetTextRectangle(iText.Svg.Renderers.SvgDrawContext,iText.Kernel.Geom.Point)">
            <summary>Return the bounding rectangle of the text element.</summary>
            <param name="context">
            current
            <see cref="T:iText.Svg.Renderers.SvgDrawContext"/>
            </param>
            <param name="basePoint">end point of previous text element</param>
            <returns>
            created instance of
            <see cref="T:iText.Svg.Utils.TextRectangle"/>
            </returns>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.LinearGradientSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;linearGradient&gt; tag.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.LineSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;line&gt; tag.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.MarkerSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;marker&gt; tag.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.PathSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;path&gt; tag.
            </summary>
        </member>
        <member name="F:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.INVALID_OPERATOR_REGEX">
            <summary>
            The regular expression to find invalid operators in the <a href="https://www.w3.org/tr/svg/paths.html#pathdata">PathData
            attribute of the &lt;path&gt; element</a>
            </summary>
            <remarks>
            The regular expression to find invalid operators in the <a href="https://www.w3.org/tr/svg/paths.html#pathdata">PathData
            attribute of the &lt;path&gt; element</a>
            <para />
            Find any occurrence of a letter that is not an operator
            </remarks>
        </member>
        <member name="F:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.SPLIT_PATTERN">
            <summary>
            The regular expression to split the <a href="https://www.w3.org/tr/svg/paths.html#pathdata">PathData attribute of
            the &lt;path&gt; element</a>
            </summary>
            <remarks>
            The regular expression to split the <a href="https://www.w3.org/tr/svg/paths.html#pathdata">PathData attribute of
            the &lt;path&gt; element</a>
            <para />
            Since
            <see cref="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.ContainsInvalidAttributes(System.String)"/>
            is called before the use of this expression
            in
            <see cref="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.ParsePathOperations"/>
            the attribute to be split is valid.
            <para />
            SVG defines 6 types of path commands, for a total of 20 commands:
            <para />
            MoveTo: M, m
            LineTo: L, l, H, h, V, v
            Cubic Bezier Curve: C, c, S, s
            Quadratic Bezier Curve: Q, q, T, t
            Elliptical Arc Curve: A, a
            ClosePath: Z, z
            </remarks>
        </member>
        <member name="F:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.zOperator">
            <summary>
            The
            <see cref="T:iText.Svg.Renderers.Path.Impl.ClosePath"/>
            shape keeping track of the initial point set by a
            <see cref="T:iText.Svg.Renderers.Path.Impl.MoveTo"/>
            operation.
            </summary>
            <remarks>
            The
            <see cref="T:iText.Svg.Renderers.Path.Impl.ClosePath"/>
            shape keeping track of the initial point set by a
            <see cref="T:iText.Svg.Renderers.Path.Impl.MoveTo"/>
            operation.
            The original value is
            <see langword="null"/>
            , and must be set via a
            <see cref="T:iText.Svg.Renderers.Path.Impl.MoveTo"/>
            operation before it may be drawn.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.GetShapeCoordinates(iText.Svg.Renderers.Path.IPathShape,iText.Svg.Renderers.Path.IPathShape,System.String[])">
            <summary>
            Gets the coordinates that shall be passed to
            <see cref="M:iText.Svg.Renderers.Path.IPathShape.SetCoordinates(System.String[],iText.Kernel.Geom.Point)"/>
            for the current shape.
            </summary>
            <param name="shape">The current shape.</param>
            <param name="previousShape">The previous shape which can affect the coordinates of the current shape.</param>
            <param name="pathProperties">
            The operator and all arguments as an array of
            <see cref="T:System.String">String</see>
            s
            </param>
            <returns>
            a
            <see cref="T:System.String"/>
            array of coordinates that shall be passed to
            <see cref="M:iText.Svg.Renderers.Path.IPathShape.SetCoordinates(System.String[],iText.Kernel.Geom.Point)"/>
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.ProcessPathOperator(System.String[],iText.Svg.Renderers.Path.IPathShape)">
            <summary>
            Processes an individual pathing operator and all of its arguments, converting into one or more
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>
            objects.
            </summary>
            <param name="pathProperties">
            The property operator and all arguments as an array of
            <see cref="T:System.String"/>
            s
            </param>
            <param name="previousShape">
            The previous shape which can affect the positioning of the current shape. If no previous
            shape exists
            <see langword="null"/>
            is passed.
            </param>
            <returns>
            a
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            of each
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>
            that should be drawn to represent the operator.
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.GetShapes">
            <summary>
            Processes the
            <see cref="F:iText.Svg.SvgConstants.Attributes.D"/>
            
            <see cref="F:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.attributesAndStyles"/>
            and converts them
            into one or more
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>
            objects to be drawn on the canvas.
            </summary>
            <remarks>
            Processes the
            <see cref="F:iText.Svg.SvgConstants.Attributes.D"/>
            
            <see cref="F:iText.Svg.Renderers.Impl.AbstractSvgNodeRenderer.attributesAndStyles"/>
            and converts them
            into one or more
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>
            objects to be drawn on the canvas.
            <para />
            Each individual operator is passed to
            <see cref="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.ProcessPathOperator(System.String[],iText.Svg.Renderers.Path.IPathShape)"/>
            to be
            processed individually.
            </remarks>
            <returns>
            a
            <see cref="!:System.Collections.ICollection&lt;E&gt;"/>
            of each
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>
            that should be drawn to represent the path.
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.SeparateDecimalPoints(System.String)">
            <summary>Iterate over the input string and separate numbers from each other with space chars</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.SplitPathStringIntoOperators(System.String)">
            <summary>
            Gets an array of strings representing operators with their arguments, e.g. {"M 100 100", "L 300 100", "L200,
            300", "z"}
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.PatternSvgNodeRenderer">
            <summary>Implementation for the svg &lt;pattern&gt; tag.</summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.PdfRootSvgNodeRenderer">
            <summary>Root renderer responsible for applying the initial axis-flipping transform</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PdfRootSvgNodeRenderer.#ctor(iText.Svg.Renderers.ISvgNodeRenderer)">
            <summary>
            Creates a
            <see cref="T:iText.Svg.Renderers.Impl.PdfRootSvgNodeRenderer"/>
            instance.
            </summary>
            <param name="subTreeRoot">root of the subtree</param>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.PolygonSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;polygon&gt; tag.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PolygonSvgNodeRenderer.SetPoints(System.String)">
            <summary>
            Calls setPoints(String) to set
            <see cref="F:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer.points"/>
            Then calls
            <see cref="M:iText.Svg.Renderers.Impl.PolygonSvgNodeRenderer.ConnectPoints"/>
            to create a path between the first and last point if it doesn't already exist
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PolygonSvgNodeRenderer.ConnectPoints">
            <summary>
            Appends the starting point to the end of
            <see cref="F:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer.points"/>
            if it is not already there.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;polyline&gt; tag.
            </summary>
        </member>
        <member name="F:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer.points">
            <summary>
            A List of
            <see cref="T:iText.Kernel.Geom.Point"/>
            objects representing the path to be drawn by the polyline tag
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer.SetPoints(System.String)">
            <summary>
            Parses a string of space separated x,y pairs into individual
            <see cref="T:iText.Kernel.Geom.Point"/>
            objects and appends them to
            <see cref="F:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer.points"/>.
            </summary>
            <remarks>
            Parses a string of space separated x,y pairs into individual
            <see cref="T:iText.Kernel.Geom.Point"/>
            objects and appends them to
            <see cref="F:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer.points"/>.
            Throws an
            <see cref="T:iText.Svg.Exceptions.SvgProcessingException"/>
            if pointsAttribute does not have a valid list of numerical x,y pairs.
            </remarks>
            <param name="pointsAttribute">A string of space separated x,y value pairs</param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer.DoDraw(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Draws this element to a canvas-like object maintained in the context.</summary>
            <param name="context">the object that knows the place to draw this element and maintains its state</param>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.RectangleSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;rect&gt; tag.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.RectangleSvgNodeRenderer.#ctor">
            <summary>Constructs a RectangleSvgNodeRenderer.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.RectangleSvgNodeRenderer.CheckRadius(System.Single,System.Single)">
            <summary>
            a radius must be positive, and cannot be more than half the distance in
            the dimension it is for.
            </summary>
            <remarks>
            a radius must be positive, and cannot be more than half the distance in
            the dimension it is for.
            e.g. rx &lt;= width / 2
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.RectangleSvgNodeRenderer.FindCircularRadius(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            In case of a circular radius, the calculation in
            <see cref="M:iText.Svg.Renderers.Impl.RectangleSvgNodeRenderer.CheckRadius(System.Single,System.Single)"/>
            isn't enough: the radius cannot be more than half of the <b>smallest</b>
            dimension.
            </summary>
            <remarks>
            In case of a circular radius, the calculation in
            <see cref="M:iText.Svg.Renderers.Impl.RectangleSvgNodeRenderer.CheckRadius(System.Single,System.Single)"/>
            isn't enough: the radius cannot be more than half of the <b>smallest</b>
            dimension.
            This method assumes that
            <see cref="M:iText.Svg.Renderers.Impl.RectangleSvgNodeRenderer.CheckRadius(System.Single,System.Single)"/>
            has already run, and it is
            silently assumed (though not necessary for this method) that either
            <paramref name="rx"/>
            or
            <paramref name="ry"/>
            is zero.
            </remarks>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.StopSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the gradient &lt;stop&gt; tag.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.StopSvgNodeRenderer.GetOffset">
            <summary>Evaluates the stop color offset value.</summary>
            <returns>the stop color offset value in [0, 1] range</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.StopSvgNodeRenderer.GetStopColor">
            <summary>Evaluates the rgba array of the specified stop color.</summary>
            <returns>
            the array of 4 floats which contains the rgba value corresponding
            to the specified stop color
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.StopSvgNodeRenderer.GetStopOpacity">
            <summary>Evaluates the stop opacity of the specified stop color.</summary>
            <returns>the stop opacity value specified in the stop color</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.SvgTagSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;svg&gt; tag.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.TextLeafSvgNodeRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for drawing text to a canvas.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.TextSvgBranchRenderer">
            <summary>
            <see cref="T:iText.Svg.Renderers.ISvgNodeRenderer"/>
            implementation for the &lt;text&gt; and &lt;tspan&gt; tag.
            </summary>
        </member>
        <member name="F:iText.Svg.Renderers.Impl.TextSvgBranchRenderer.TEXTFLIP">
            <summary>Top level transformation to flip the y-axis results in the character glyphs being mirrored, this tf corrects for this behaviour
                </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.TextSvgBranchRenderer.DoDraw(iText.Svg.Renderers.SvgDrawContext)">
            <summary>
            Method that will set properties to be inherited by this branch renderer's
            children and will iterate over all children in order to draw them.
            </summary>
            <param name="context">
            the object that knows the place to draw this element and
            maintains its state
            </param>
        </member>
        <member name="M:iText.Svg.Renderers.Impl.TextSvgBranchRenderer.GetFont">
            <summary>Return the font used in this text element.</summary>
            <remarks>
            Return the font used in this text element.
            Note that font should already be resolved with
            <see cref="M:iText.Svg.Renderers.Impl.TextSvgBranchRenderer.ResolveFont(iText.Svg.Renderers.SvgDrawContext)"/>.
            </remarks>
            <returns>font of the current text element</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Impl.UseSvgNodeRenderer">
            <summary>Renderer implementing the use tag.</summary>
            <remarks>Renderer implementing the use tag. This tag allows you to reuse previously defined elements.</remarks>
        </member>
        <member name="T:iText.Svg.Renderers.INoDrawSvgNodeRenderer">
            <summary>Interface that defines branches in the NodeRenderer structure that will not draw on its own.</summary>
        </member>
        <member name="T:iText.Svg.Renderers.ISvgNodeRenderer">
            <summary>
            Interface for SvgNodeRenderer, the renderer draws the SVG to its Pdf-canvas
            passed in
            <see cref="T:iText.Svg.Renderers.SvgDrawContext"/>
            , applying styling
            (CSS and attributes).
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.SetParent(iText.Svg.Renderers.ISvgNodeRenderer)">
            <summary>Sets the parent of this renderer.</summary>
            <remarks>
            Sets the parent of this renderer. The parent may be the source of
            inherited properties and default values.
            </remarks>
            <param name="parent">the parent renderer</param>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.GetParent">
            <summary>Gets the parent of this renderer.</summary>
            <remarks>
            Gets the parent of this renderer. The parent may be the source of
            inherited properties and default values.
            </remarks>
            <returns>the parent renderer; null in case of a root node</returns>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.Draw(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Draws this element to a canvas-like object maintained in the context.</summary>
            <param name="context">
            the object that knows the place to draw this element and
            maintains its state
            </param>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.SetAttributesAndStyles(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Sets the map of XML node attributes and CSS style properties that this
            renderer needs.
            </summary>
            <param name="attributesAndStyles">the mapping from key names to values</param>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.GetAttribute(System.String)">
            <summary>Retrieves the property value for a given key name.</summary>
            <param name="key">the name of the property to search for</param>
            <returns>
            the value for this key, or
            <see langword="null"/>
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.SetAttribute(System.String,System.String)">
            <summary>Sets a property key and value pairs for a given attribute</summary>
            <param name="key">the name of the attribute</param>
            <param name="value">the value of the attribute</param>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.GetAttributeMapCopy">
            <summary>Get a modifiable copy of the style and attribute map</summary>
            <returns>copy of the attributes and styles-map</returns>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.CreateDeepCopy">
            <summary>Creates a deep copy of this renderer, including it's subtree of children</summary>
            <returns>deep copy of this renderer</returns>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgNodeRenderer.GetObjectBoundingBox(iText.Svg.Renderers.SvgDrawContext)">
            <summary>Calculates the current object bounding box.</summary>
            <param name="context">
            the current context, for instance it contains current viewport and available
            font data
            </param>
            <returns>
            the
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            representing the current object's bounding box, or null
            if bounding box is undefined
            </returns>
        </member>
        <member name="T:iText.Svg.Renderers.ISvgPaintServer">
            <summary>Interface for working with paint servers.</summary>
            <remarks>
            Interface for working with paint servers. These are the elements that are referenced from the fill or stroke of an
            object.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.ISvgPaintServer.CreateColor(iText.Svg.Renderers.SvgDrawContext,iText.Kernel.Geom.Rectangle,System.Single,System.Single)">
            <summary>
            Creates the
            <see cref="T:iText.Kernel.Colors.Color"/>
            that represents the corresponding paint server for specified object box.
            </summary>
            <param name="context">the current svg draw context</param>
            <param name="objectBoundingBox">
            the coloring object bounding box without any adjustments
            (additional stroke width or others)
            </param>
            <param name="objectBoundingBoxMargin">
            the objectBoundingBoxMargin of the object bounding box
            to be colored (for example - the part of stroke width
            that exceeds the object bounding box, i.e. the half of stroke
            width value)
            </param>
            <param name="parentOpacity">current parent opacity modifier</param>
            <returns>the created color</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.AbstractPathShape">
            <summary>This class handles common behaviour in IPathShape implementations</summary>
        </member>
        <member name="F:iText.Svg.Renderers.Path.Impl.AbstractPathShape.properties">
            <summary>The properties of this shape.</summary>
        </member>
        <member name="F:iText.Svg.Renderers.Path.Impl.AbstractPathShape.relative">
            <summary>Whether this is a relative operator or not.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.AbstractPathShape.GetPathShapeRectangle(iText.Kernel.Geom.Point)">
            <summary>Get bounding rectangle of the current path shape.</summary>
            <param name="lastPoint">start point for this shape</param>
            <returns>calculated rectangle</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.ClosePath">
            <summary>Implements closePath(Z) attribute of SVG's path element</summary>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.CurveTo">
            <summary>Implements curveTo(C) attribute of SVG's path element</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.CurveTo.GetBezierMinMaxPoints(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initial function of cubic bezier is f(t) = (t-1)^3*P0 + 3*(1-t)^2*t*P1 + 3*(1-t)*t^2*P2 + t^3*P3, where 0 &lt;= t &lt;= 1
            After opening brackets it can be reduced to f(t) = a*t^3 + b*t^2 + c*t + d, where
            a = P3-3*P2+3*P1-P0
            b = 3*P2-6*P1+3*P0
            c = 3*P1-3*P0
            d = P0
            First we must find the values of t at which the function reaches its extreme points.
            </summary>
            <remarks>
            Initial function of cubic bezier is f(t) = (t-1)^3*P0 + 3*(1-t)^2*t*P1 + 3*(1-t)*t^2*P2 + t^3*P3, where 0 &lt;= t &lt;= 1
            After opening brackets it can be reduced to f(t) = a*t^3 + b*t^2 + c*t + d, where
            a = P3-3*P2+3*P1-P0
            b = 3*P2-6*P1+3*P0
            c = 3*P1-3*P0
            d = P0
            First we must find the values of t at which the function reaches its extreme points.
            This happens in the method
            <see cref="M:iText.Svg.Renderers.Path.Impl.CurveTo.GetTValuesInExtremePoints(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)"/>.
            Next we get x and y values in extremes and compare it with the start and ending points coordinates to get the borders of the bounding box.
            </remarks>
            <param name="x0">x coordinate of the starting point</param>
            <param name="y0">y coordinate of the starting point</param>
            <param name="x1">x coordinate of the first control point</param>
            <param name="y1">y coordinate of the first control point</param>
            <param name="x2">x coordinate of the second control point</param>
            <param name="y2">y coordinate of the second control point</param>
            <param name="x3">x coordinate of the ending point</param>
            <param name="y3">y coordinate of the ending point</param>
            <returns>array of {xMin, yMin, xMax, yMax} values</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.CurveTo.GetTValuesInExtremePoints(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>Calculate values of t at which the function reaches its extreme points.</summary>
            <remarks>
            Calculate values of t at which the function reaches its extreme points. To do this, we get the derivative of the function and equate it to 0:
            f'(t) = 3a*t^2 + 2b*t + c. This is parabola and for finding we calculate the discriminant. t can only be in the range [0, 1] and it discarded otherwise.
            </remarks>
            <param name="x0">x coordinate of the starting point</param>
            <param name="y0">y coordinate of the starting point</param>
            <param name="x1">x coordinate of the first control point</param>
            <param name="y1">y coordinate of the first control point</param>
            <param name="x2">x coordinate of the second control point</param>
            <param name="y2">y coordinate of the second control point</param>
            <param name="x3">x coordinate of the ending point</param>
            <param name="y3">y coordinate of the ending point</param>
            <returns>array of theta values corresponding to extreme points</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.CurveTo.CalculateTValues(System.Double,System.Double,System.Double,System.Double)">
            <summary>Calculate the quadratic function 3a*t^2 + 2b*t + c = 0 to obtain the values of t</summary>
            <param name="p0">coordinate of the starting point</param>
            <param name="p1">coordinate of the first control point</param>
            <param name="p2">coordinate of the second control point</param>
            <param name="p3">coordinate of the ending point</param>
            <returns>list of t values. t should be in range [0, 1]</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.CurveTo.AddTValueToList(System.Double,System.Collections.Generic.IList{System.Double})">
            <summary>Check that t is in the range [0, 1] and add it to list</summary>
            <param name="t">value of t</param>
            <param name="tValuesList">list storing t values</param>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo">
            <summary>Implements elliptical curveTo (A) segment of SVG's path element.</summary>
            <remarks>
            Implements elliptical curveTo (A) segment of SVG's path element. Implemented in PDF as Bézier curves.
            Edge cases &amp; value correction below always refer to https://www.w3.org/TR/SVG11/implnote.html#ArcImplementationNotes
            For some calculations we need double precision floating point math, so we have forced all calculations to use double.
            However, float comparison is used instead of double comparison, because close coordinates can be considered equal.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.#ctor">
            <summary>Creates an absolute Elliptical curveTo.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.#ctor(System.Boolean)">
            <summary>Creates a Elliptical curveTo.</summary>
            <remarks>Creates a Elliptical curveTo. Set argument to true to create a relative EllipticalCurveTo.</remarks>
            <param name="relative">whether this is a relative EllipticalCurveTo or not</param>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.Rotate(iText.Kernel.Geom.Point[][],System.Double,iText.Kernel.Geom.Point)">
            <summary>This convenience method rotates a given set of points around a given point</summary>
            <param name="list">the input list</param>
            <param name="rotation">the rotation angle, in radians</param>
            <param name="rotator">the point to rotate around</param>
            <returns>the list of rotated points</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.EllipseArc">
            <summary>
            Converts between two types of definitions of an arc:
            The input is an arc defined by two points and the two semi-axes of the ellipse.
            </summary>
            <remarks>
            Converts between two types of definitions of an arc:
            The input is an arc defined by two points and the two semi-axes of the ellipse.
            The output is an arc defined by a bounding rectangle, the starting angle,
            and the angular extent of the ellipse that is to be drawn.
            The output is an intermediate step to calculating the Bézier curve(s) that approximate(s) the elliptical arc,
            which happens in
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.GetEllipticalArcMinMaxPoints(System.Double,System.Double,System.Double,System.Double,System.Double,System.Boolean,System.Boolean,System.Double,System.Double)">
            <summary>
            Algorithm to find elliptical arc bounding box:
            1.
            </summary>
            <remarks>
            Algorithm to find elliptical arc bounding box:
            1. Compute extremes using parametric description of the whole ellipse
            We use parametric description of ellipse:
            x(theta) = cx + rx*cos(theta)*cos(phi) - ry*sin(theta)*sin(phi)
            y(theta) = cy + rx*cos(theta)*sin(phi) + ry*sin(theta)*cos(phi)
            After obtaining the derivative and equating it to zero, we get two solutions for x:
            theta = -atan(ry*tan(phi)/rx) and theta = M_PI -atan(ry*tan(phi)/rx)
            and two solutions for y:
            theta = atan(ry/(tan(phi)*rx)) and theta = M_PI + atan(ry/(tan(phi)*rx))
            Then to get theta values we need to know cx and cy - the coordinates of the center of the ellipse.
            2. Compute the center of the ellipse
            Method
            <see cref="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.GetEllipseCenterCoordinates(System.Double,System.Double,System.Double,System.Double,System.Double,System.Boolean,System.Boolean,System.Double,System.Double)"/>
            3. Determine the bounding box of the whole ellipse
            When we know cx and cy values we can get the bounding box of whole ellipse. That done in the method
            <see cref="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.GetEllipseCenterCoordinates(System.Double,System.Double,System.Double,System.Double,System.Double,System.Boolean,System.Boolean,System.Double,System.Double)"/>.
            4. Find tightest possible bounding box
            Check that given points is on the arc using polar coordinates of points. Method
            <see cref="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.IsPointOnTheArc(System.Double,System.Double,System.Double,System.Boolean)"/>.
            </remarks>
            <param name="x1">x coordinate of the starting point</param>
            <param name="y1">y coordinate of the starting point</param>
            <param name="rx">x radius</param>
            <param name="ry">y radius</param>
            <param name="phi">x-axis rotation</param>
            <param name="largeArc">large arc flag. If this is true, then one of the two larger arc sweeps will be chosen (greater than or equal to 180 degrees)
                </param>
            <param name="sweep">sweep flag. If sweep flag is true, then the arc will be drawn in a "positive-angle" direction and if false - in a "negative-angle" direction
                </param>
            <param name="x2">x coordinate of ending point</param>
            <param name="y2">y coordinate of ending point</param>
            <returns>array of {xMin, yMin, xMax, yMax} values</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.GetEllipseCenterCoordinates(System.Double,System.Double,System.Double,System.Double,System.Double,System.Boolean,System.Boolean,System.Double,System.Double)">
            <summary>Calculate the center coordinates of the whole ellipse.</summary>
            <remarks>
            Calculate the center coordinates of the whole ellipse.
            Also return rx, ry values since they can be changed in this method.
            Algorithm for calculation centre coordinates: https://www.w3.org/TR/SVG/implnote.html#ArcConversionEndpointToCenter
            </remarks>
            <param name="x1">x coordinate of the starting point</param>
            <param name="y1">y coordinate of the starting point</param>
            <param name="rx">x radius</param>
            <param name="ry">y radius</param>
            <param name="phi">x-axis rotation</param>
            <param name="largeArc">large arc flag</param>
            <param name="sweep">sweep flag</param>
            <param name="x2">x coordinate of ending point</param>
            <param name="y2">y coordinate of ending point</param>
            <returns>the array of {cx, cy, rx, ry} values</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.GetExtremeCoordinatesAndAngles(System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>Calculate extremes of the ellipse function and corresponding angles.</summary>
            <remarks>
            Calculate extremes of the ellipse function and corresponding angles.
            Angles are calculated relative to the center of the ellipse.
            </remarks>
            <param name="rx">x radius</param>
            <param name="ry">y radius</param>
            <param name="phi">x-axis rotation</param>
            <param name="cx">x coordinate of ellipse center</param>
            <param name="cy">y coordinate of ellipse center</param>
            <returns>array of extreme coordinate and array of angles corresponding to these coordinates.</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.IsPointOnTheArc(System.Double,System.Double,System.Double,System.Boolean)">
            <summary>Check that angle corresponding to extreme points is on the current arc.</summary>
            <remarks>
            Check that angle corresponding to extreme points is on the current arc.
            For this we check that this angle is between the angles of starting and ending points.
            </remarks>
            <param name="pointAngle">angle to check</param>
            <param name="angle1">angle of the first extreme point if ellipse(starting or ending)</param>
            <param name="angle2">angle of the second extreme point if ellipse(starting or ending)</param>
            <param name="otherArc">if we should check that point is placed on the other arc of the current ellipse</param>
            <returns>true if point is on the arc</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.EllipticalCurveTo.GetAngleBetweenVectors(System.Double,System.Double)">
            <summary>
            Return the angle between the vector (1, 0) and the line specified by points (0, 0) and (bx, by) in range [ 0,
            Pi/2 ] U [ 3*Pi/2, 2*Pi).
            </summary>
            <remarks>
            Return the angle between the vector (1, 0) and the line specified by points (0, 0) and (bx, by) in range [ 0,
            Pi/2 ] U [ 3*Pi/2, 2*Pi).
            As the angle between vectors should cover the whole circle, i.e. [0, 2* Pi).
            General formula to find angle between two vectors is formula F.6.5.4 on the https://www.w3.org/TR/SVG/implnote.html#ArcConversionEndpointToCenter.
            </remarks>
            <param name="bx">x coordinate of the vector ending point</param>
            <param name="by">y coordinate of the vector ending point</param>
            <returns>calculated angle between vectors</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.HorizontalLineTo">
            <summary>Implements lineTo(H) attribute of SVG's path element</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.HorizontalLineTo.#ctor">
            <summary>Creates an absolute Horizontal LineTo.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.HorizontalLineTo.#ctor(System.Boolean)">
            <summary>Creates a Horizontal LineTo.</summary>
            <remarks>Creates a Horizontal LineTo. Set argument to true to create a relative HorizontalLineTo.</remarks>
            <param name="relative">whether this is a relative HorizontalLineTo or not</param>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.IControlPointCurve">
            <summary>Interface that describes a Path object which is defined by control points.</summary>
            <remarks>
            Interface that describes a Path object which is defined by control points. In practice this only means Bézier curves,
            both quadratic (one control point) and cubic (two control points). This interface is relevant in the context of
            Smooth (Shorthand) Bézier curves, which omit a control point from their arguments list because it can be calculated
            from the last control point of the previous curve. Therefore, the last control point of a curve must be exposed to
            the
            <see cref="T:iText.Svg.Renderers.Impl.PathSvgNodeRenderer"/>
            algorithm.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.IControlPointCurve.GetLastControlPoint">
            <summary>
            Returns coordinates of the last control point (the one closest to the ending point)
            in the Bezier curve, in SVG space coordinates
            </summary>
            <returns>coordinates of the last control point in SVG space coordinates</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.IOperatorConverter">
            <summary>
            A locally used strategy for converting relative coordinates to absolute coordinates (in the current SVG coordinate
            space).
            </summary>
            <remarks>
            A locally used strategy for converting relative coordinates to absolute coordinates (in the current SVG coordinate
            space). Its implementation differs between Smooth (Shorthand) Bézier curves and all other path commands.
            </remarks>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.IOperatorConverter.MakeCoordinatesAbsolute(System.String[],System.Double[])">
            <summary>Convert an array of relative coordinates to an array with the same size containing absolute coordinates.
                </summary>
            <param name="relativeCoordinates">the initial set of coordinates</param>
            <param name="initialPoint">an array representing the point relative to which the relativeCoordinates are defined
                </param>
            <returns>a String array of absolute coordinates, with the same length as the input array</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.SmoothOperatorConverter">
            <summary>
            Implementation of
            <see cref="T:iText.Svg.Renderers.Path.Impl.IOperatorConverter"/>
            specifically for smooth curves.
            </summary>
            <remarks>
            Implementation of
            <see cref="T:iText.Svg.Renderers.Path.Impl.IOperatorConverter"/>
            specifically for smooth curves. It will convert all operators from
            relative to absolute coordinates except the first coordinate pair.
            This implementation is used by the Smooth (Shorthand) Bézier curve commands, because the conversion of the first
            coordinate pair is calculated in
            <see cref="M:iText.Svg.Renderers.Impl.PathSvgNodeRenderer.GetShapeCoordinates(iText.Svg.Renderers.Path.IPathShape,iText.Svg.Renderers.Path.IPathShape,System.String[])"/>.
            </remarks>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.DefaultOperatorConverter">
            <summary>
            Default implementation of
            <see cref="T:iText.Svg.Renderers.Path.Impl.IOperatorConverter"/>
            used by the regular (not-smooth) curves and other path commands.
            </summary>
            <remarks>
            Default implementation of
            <see cref="T:iText.Svg.Renderers.Path.Impl.IOperatorConverter"/>
            used by the regular (not-smooth) curves and other path commands.
            It will convert all operators from relative to absolute coordinates.
            </remarks>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.LineTo">
            <summary>Implements lineTo(L) attribute of SVG's path element</summary>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.MoveTo">
            <summary>Implements moveTo(M) attribute of SVG's path element</summary>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.PathShapeMapper">
            <summary>
            The implementation of
            <see cref="T:iText.Svg.Renderers.Path.IPathShapeMapper"/>
            that will be used by
            sub classes of
            <see cref="T:iText.Svg.Renderers.Impl.PathSvgNodeRenderer"/>
            To map the path-data
            instructions(moveto, lineto, corveto ...) to their respective implementations.
            </summary>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.QuadraticCurveTo">
            <summary>Implements quadratic Bezier curveTo(Q) attribute of SVG's path element</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.QuadraticCurveTo.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas)">
            <summary>Draws a quadratic Bezier curve from the current point to (x,y) using (x1,y1) as the control point
                </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.QuadraticCurveTo.GetBezierMinMaxPoints(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            The algorithm is similar to cubic curve in the method CurveTo#getBezierMinMaxPoints,
            but the initial function is f(t) = (1 - t)^2*P0 + 2*(1-t)*t*P1 + t^2*P2, 0 &lt;= t &lt;= 1
            </summary>
            <param name="x0">x coordinate of the starting point</param>
            <param name="y0">y coordinate of the starting point</param>
            <param name="x1">x coordinate of the control point</param>
            <param name="y1">y coordinate of the control point</param>
            <param name="x2">x coordinate of the ending point</param>
            <param name="y2">y coordinate of the ending point</param>
            <returns>array of {xMin, yMin, xMax, yMax} values</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.QuadraticCurveTo.GetExtremeTValues(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>Calculate values of t at which the function reaches its extreme points.</summary>
            <remarks>
            Calculate values of t at which the function reaches its extreme points. To do this, we get the derivative of the
            function and equate it to 0:
            f'(t) = 2a*t + b. t can only be in the range [0, 1] and it discarded otherwise.
            </remarks>
            <param name="x0">x coordinate of the starting point</param>
            <param name="y0">y coordinate of the starting point</param>
            <param name="x1">x coordinate of the control point</param>
            <param name="y1">y coordinate of the control point</param>
            <param name="x2">x coordinate of the ending point</param>
            <param name="y2">y coordinate of the ending point</param>
            <returns>array of theta values corresponding to extreme points</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.QuadraticCurveTo.AddTValueToList(System.Double,System.Collections.Generic.IList{System.Double})">
            <summary>Check that t is in the range [0, 1] and add it to list</summary>
            <param name="t">value of t</param>
            <param name="tValuesList">list storing t values</param>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.QuadraticSmoothCurveTo">
            <summary>Implements shorthand/smooth quadraticCurveTo (T) attribute of SVG's path element</summary>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.SmoothSCurveTo">
            <summary>Implements shorthand/smooth curveTo (S) attribute of SVG's path element</summary>
        </member>
        <member name="T:iText.Svg.Renderers.Path.Impl.VerticalLineTo">
            <summary>Implements lineTo(V) attribute of SVG's path element</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.VerticalLineTo.#ctor">
            <summary>Creates an absolute Vertical LineTo.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.Impl.VerticalLineTo.#ctor(System.Boolean)">
            <summary>Creates a Vertical LineTo.</summary>
            <remarks>Creates a Vertical LineTo. Set argument to true to create a relative VerticalLineTo.</remarks>
            <param name="relative">whether this is a relative VerticalLineTo or not</param>
        </member>
        <member name="T:iText.Svg.Renderers.Path.IPathShape">
            <summary>Interface for IPathShape, which draws the Path-data's d element instructions.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.IPathShape.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas)">
            <summary>Draws this instruction to a canvas object.</summary>
            <param name="canvas">to which this instruction is drawn</param>
        </member>
        <member name="M:iText.Svg.Renderers.Path.IPathShape.SetCoordinates(System.String[],iText.Kernel.Geom.Point)">
            <summary>
            This method sets the coordinates for the path painting operator and does internal
            preprocessing, if necessary
            </summary>
            <param name="inputCoordinates">an array containing point values for path coordinates</param>
            <param name="startPoint">
            the ending point of the previous operator, or, in broader terms,
            the point that the coordinates should be absolutized against, for relative operators
            </param>
        </member>
        <member name="M:iText.Svg.Renderers.Path.IPathShape.GetEndingPoint">
            <summary>
            Gets the ending point on the canvas after the path shape has been drawn
            via the
            <see cref="M:iText.Svg.Renderers.Path.IPathShape.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas)"/>
            method, in SVG space coordinates.
            </summary>
            <returns>
            The
            <see cref="T:iText.Kernel.Geom.Point"/>
            representing the final point in the drawn path.
            If the point does not exist or does not change
            <see langword="null"/>
            may be returned.
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.IPathShape.IsRelative">
            <summary>Returns true when this shape is a relative operator.</summary>
            <remarks>Returns true when this shape is a relative operator. False if it is an absolute operator.</remarks>
            <returns>true if relative, false if absolute</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.IPathShape.GetPathShapeRectangle(iText.Kernel.Geom.Point)">
            <summary>Get bounding rectangle of the current path shape.</summary>
            <param name="lastPoint">start point for this shape</param>
            <returns>calculated rectangle</returns>
        </member>
        <member name="T:iText.Svg.Renderers.Path.IPathShapeMapper">
            <summary>
            Interface that will provide a mapping from path element-data  instruction names to
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.IPathShapeMapper.GetMapping">
            <summary>Provides a mapping of Path-data instructions' names to path shape classes.</summary>
            <returns>
            a
            <see cref="!:System.Collections.IDictionary&lt;K, V&gt;"/>
            with Strings as keys and {link @
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>
            implementations as values
            </returns>
        </member>
        <member name="T:iText.Svg.Renderers.Path.SvgPathShapeFactory">
            <summary>
            A factory for creating
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>
            objects.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.Path.SvgPathShapeFactory.CreatePathShape(System.String)">
            <summary>
            Creates a configured
            <see cref="T:iText.Svg.Renderers.Path.IPathShape"/>
            object based on the passed Svg path data instruction tag.
            </summary>
            <param name="name">svg path element's path-data instruction name.</param>
            <returns>IPathShape implementation</returns>
        </member>
        <member name="M:iText.Svg.Renderers.Path.SvgPathShapeFactory.GetArgumentCount(System.String)">
            <summary>Finds the appropriate number of arguments for a path command, based on the passed Svg path data instruction tag.
                </summary>
            <param name="name">svg path element's path-data instruction name.</param>
            <returns>an integer value with the required number of arguments or null if there is no mapping for the given value
                </returns>
        </member>
        <member name="T:iText.Svg.Renderers.SvgDrawContext">
            <summary>
            The SvgDrawContext keeps a stack of
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            instances, which
            represent all levels of XObjects that are added to the root canvas.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.#ctor(iText.StyledXmlParser.Resolver.Resource.ResourceResolver,iText.Layout.Font.FontProvider)">
            <summary>Create an instance of the context that is used to store information when converting SVG.</summary>
            <param name="resourceResolver">
            instance of
            <see cref="T:iText.StyledXmlParser.Resolver.Resource.ResourceResolver"/>
            </param>
            <param name="fontProvider">
            instance of
            <see cref="T:iText.Layout.Font.FontProvider"/>
            </param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetCurrentCanvas">
            <summary>Retrieves the current top of the stack, without modifying the stack.</summary>
            <returns>the current canvas that can be used for drawing operations.</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.PopCanvas">
            <summary>
            Retrieves the current top of the stack, thereby taking the current item
            off the stack.
            </summary>
            <returns>the current canvas that can be used for drawing operations.</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.PushCanvas(iText.Kernel.Pdf.Canvas.PdfCanvas)">
            <summary>
            Adds a
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            to the stack (by definition its top), for use in
            drawing operations.
            </summary>
            <param name="canvas">the new top of the stack</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.Size">
            <summary>
            Get the current size of the stack, signifying the nesting level of the
            XObjects.
            </summary>
            <returns>the current size of the stack.</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.AddViewPort(iText.Kernel.Geom.Rectangle)">
            <summary>Adds a viewbox to the context.</summary>
            <param name="viewPort">rectangle representing the current viewbox</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetCurrentViewPort">
            <summary>Get the current viewbox.</summary>
            <returns>the viewbox as it is currently set</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetRootViewPort">
            <summary>Get the viewbox which is the root viewport for the current document.</summary>
            <returns>root viewbox.</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.RemoveCurrentViewPort">
            <summary>Remove the currently set view box.</summary>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.AddNamedObject(System.String,iText.Svg.Renderers.ISvgNodeRenderer)">
            <summary>Adds a named object to the draw context.</summary>
            <remarks>Adds a named object to the draw context. These objects can then be referenced from a different tag.
                </remarks>
            <param name="name">name of the object</param>
            <param name="namedObject">object to be referenced</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetNamedObject(System.String)">
            <summary>Get a named object based on its name.</summary>
            <remarks>Get a named object based on its name. If the name isn't listed, this method will return null.</remarks>
            <param name="name">name of the object you want to reference</param>
            <returns>the referenced object</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetResourceResolver">
            <summary>Gets the ResourceResolver to be used during the drawing operations.</summary>
            <returns>resource resolver instance</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.AddNamedObjects(System.Collections.Generic.IDictionary{System.String,iText.Svg.Renderers.ISvgNodeRenderer})">
            <summary>* Adds a number of named object to the draw context.</summary>
            <remarks>* Adds a number of named object to the draw context. These objects can then be referenced from a different tag.
                </remarks>
            <param name="namedObjects">Map containing the named objects keyed to their ID strings</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetFontProvider">
            <summary>Gets the FontProvider to be used during the drawing operations.</summary>
            <returns>font provider instance</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetTempFonts">
            <summary>Gets list of temporary fonts from @font-face.</summary>
            <returns>font set instance</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.SetTempFonts(iText.Layout.Font.FontSet)">
            <summary>Sets the FontSet.</summary>
            <param name="tempFonts">font set to be used during drawing operations</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.IsIdUsedByUseTagBefore(System.String)">
            <summary>Returns true when this id has been used before</summary>
            <param name="elementId">element id to check</param>
            <returns>true if id has been encountered before through a use element</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.AddUsedId(System.String)">
            <summary>Adds an ID that has been referenced by a use element.</summary>
            <param name="elementId">referenced element ID</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.RemoveUsedId(System.String)">
            <summary>Removes an ID that has been referenced by a use element.</summary>
            <param name="elementId">referenced element ID</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetLastTextTransform">
            <summary>Get the text transformation that was last applied</summary>
            <returns>
            
            <see cref="T:iText.Kernel.Geom.AffineTransform"/>
            representing the last text transformation
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.SetLastTextTransform(iText.Kernel.Geom.AffineTransform)">
            <summary>Set the last text transformation</summary>
            <param name="newTransform">last text transformation</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetTextMove">
            <summary>Get the stored current text move</summary>
            <returns>[horizontal text move, vertical text move]</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.ResetTextMove">
            <summary>Reset the stored text move to [0f,0f]</summary>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.AddTextMove(System.Single,System.Single)">
            <summary>Increment the stored text move</summary>
            <param name="additionalMoveX">horizontal value to add</param>
            <param name="additionalMoveY">vertical value to add</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetCurrentCanvasTransform">
            <summary>Get the current canvas transformation</summary>
            <returns>
            the
            <see cref="T:iText.Kernel.Geom.AffineTransform"/>
            representing the current canvas transformation
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.GetCssContext">
            <summary>Gets the SVG CSS context.</summary>
            <returns>the SVG CSS context</returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.SetCssContext(iText.Svg.Css.SvgCssContext)">
            <summary>Sets the SVG CSS context.</summary>
            <param name="cssContext">the SVG CSS context</param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.PushPatternId(System.String)">
            <summary>Add pattern id to stack.</summary>
            <remarks>
            Add pattern id to stack. Check if the id is already in the stack.
            If it is, then return
            <see langword="false"/>
            and not add, if it is not - add and return
            <see langword="true"/>.
            </remarks>
            <param name="patternId">pattern id</param>
            <returns>
            
            <see langword="true"/>
            if pattern id was not on the stack and was pushed;
            <see langword="false"/>
            if it is on the stack
            </returns>
        </member>
        <member name="M:iText.Svg.Renderers.SvgDrawContext.PopPatternId">
            <summary>Pops the last template id from the stack.</summary>
        </member>
        <member name="T:iText.Svg.Renderers.SvgImageRenderer">
            <summary>
            Represents a renderer for the
            <see cref="T:iText.Svg.Element.SvgImage"/>
            layout element.
            </summary>
        </member>
        <member name="M:iText.Svg.Renderers.SvgImageRenderer.#ctor(iText.Svg.Element.SvgImage)">
            <summary>Creates an SvgImageRenderer from its corresponding layout object.</summary>
            <param name="image">
            the
            <see cref="T:iText.Svg.Element.SvgImage"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Svg.Renderers.SvgImageRenderer.Draw(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Svg.SvgConstants">
            <summary>
            A class containing constant values signifying the proeprty names of tags, attribute, CSS-style
            and certain values in SVG XML.
            </summary>
        </member>
        <member name="T:iText.Svg.SvgConstants.Tags">
            <summary>Class containing the constant property names for the tags in the SVG spec</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.A">
            <summary>Tag defining a Hyperlink.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.ALT_GLYPH">
            <summary>Alternate glyphs to be used instead of regular grlyphs, e.g. ligatures, Asian scripts, ...</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.ALT_GLYPH_DEF">
            <summary>Defines a set of glyph substitions.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.ALT_GLYPH_ITEM">
            <summary>Defines a candidate set of glyph substitutions.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.ANIMATE">
            <summary>Not supported in PDF.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.ANIMATE_MOTION">
            <summary>Not supported in PDF.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.ANIMATE_COLOR">
            <summary>Not supported in PDF.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.ANIMATE_TRANSFORM">
            <summary>Not supported in PDF.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.CIRCLE">
            <summary>
            Tag defining a
            <see cref="T:iText.Svg.Renderers.Impl.CircleSvgNodeRenderer">circle</see>.
            </summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.CLIP_PATH">
            <summary>Tag defining a clipping path.</summary>
            <remarks>Tag defining a clipping path. A clipping path defines the region where can be drawn. Anything outside the path won't be drawn.
                </remarks>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.COLOR_PROFILE">
            <summary>Tag defining the color profile to be used.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.CURSOR">
            <summary>Not supported in PDF</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.DEFS">
            <summary>Tag defining objects that can be reused from another context</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.DESC">
            <summary>Tag defining the description of its parent element</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.ELLIPSE">
            <summary>
            Tag defining an
            <see cref="T:iText.Svg.Renderers.Impl.EllipseSvgNodeRenderer">ellipse</see>.
            </summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_BLEND">
            <summary>Tag defining how to blend two objects together.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_COLOR_MATRIX">
            <summary>Tag defining the color matrix transformations that can be performed.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_COMPONENT_TRANSFER">
            <summary>Tag defining color component remapping.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_COMPOSITE">
            <summary>Tag defining the combination of two input images.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_COMVOLVE_MATRIX">
            <summary>Tag defining a matrix convolution filter</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_DIFFUSE_LIGHTING">
            <summary>Tag defining the lighting map.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_DISPLACEMENT_MAP">
            <summary>Tag defining the values to displace an image.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_DISTANT_LIGHT">
            <summary>Tag defining a distant light source.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_FLOOD">
            <summary>Tag defining the fill of a subregion.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_FUNC_A">
            <summary>Tag defining the transfer function for the Alpha component.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_FUNC_B">
            <summary>Tag defining the transfer function for the Blue component.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_FUNC_G">
            <summary>Tag defining the transfer function for the Green component.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_FUNC_R">
            <summary>Tag defining the transfer function for the Red component.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_GAUSSIAN_BLUR">
            <summary>Tag defining the blur values.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_IMAGE">
            <summary>Tag defining a image data from a source.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_MERGE">
            <summary>Tag defining that filters will be applied concurrently instead of sequentially.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_MERGE_NODE">
            <summary>Tag defining a node in a merge.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_MORPHOLOGY">
            <summary>Tag defining the erosion or dilation of an image.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_OFFSET">
            <summary>Tag defining the offset of an image.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_POINT_LIGHT">
            <summary>Tag defining a point light effect.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_SPECULAR_LIGHTING">
            <summary>Tag defining a lighting map.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_SPOTLIGHT">
            <summary>Tag defining a spotlight.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_TILE">
            <summary>Tag defining a fill that can be repeated.</summary>
            <remarks>Tag defining a fill that can be repeated. Similar to PATTERN.</remarks>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FE_TURBULENCE">
            <summary>Tag defining values for the perlin turbulence function.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FILTER">
            <summary>Tag defining a collection of filter operations.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FONT">
            <summary>Tag defining a font.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FONT_FACE">
            <summary>Tag defining a font-face.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FONT_FACE_FORMAT">
            <summary>Tag defining the formats of the font.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FONT_FACE_NAME">
            <summary>Tag defining the name of the font.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FONT_FACE_SRC">
            <summary>Tag defining the source file of the font.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FONT_FACE_URI">
            <summary>Tag defining the URI of a font.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.FOREIGN_OBJECT">
            <summary>Tag definign a foreign XML standard to be inserted.</summary>
            <remarks>Tag definign a foreign XML standard to be inserted. E.g. MathML</remarks>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.G">
            <summary>Tag defining a group of elements.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.GLYPH">
            <summary>Tag defining a single glyph.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.GLYPH_REF">
            <summary>Tag defining a sigle glyph for altGlyph.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.HKERN">
            <summary>Tag defining the horizontal kerning values in between two glyphs.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.IMAGE">
            <summary>Tag defining an image.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.LINE">
            <summary>
            Tag defining a
            <see cref="T:iText.Svg.Renderers.Impl.LineSvgNodeRenderer">line</see>.
            </summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.LINEAR_GRADIENT">
            <summary>
            Tag defining a
            <see cref="T:iText.Svg.Renderers.Impl.LinearGradientSvgNodeRenderer">linear gradient</see>.
            </summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.LINK">
            <summary>Tag defining a link</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.MARKER">
            <summary>Tag defining the graphics (arrowheads or polymarkers) to be drawn at the end of paths, lines, etc.
                </summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.MASK">
            <summary>Tag defining a mask.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.METADATA">
            <summary>Tag defining metadata.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.MISSING_GLYPH">
            <summary>Tag defining content to be rendered if a glyph is missing from the font.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.MPATH">
            <summary>Not supported in PDF</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.PATH">
            <summary>
            Tag defining a
            <see cref="T:iText.Svg.Renderers.Impl.PathSvgNodeRenderer">path</see>.
            </summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.PATTERN">
            <summary>Tag defining a graphical object that can be repeated.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.POLYGON">
            <summary>
            Tag defining a
            <see cref="T:iText.Svg.Renderers.Impl.PolygonSvgNodeRenderer">polygon</see>
            shape.
            </summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.POLYLINE">
            <summary>
            Tag defining a
            <see cref="T:iText.Svg.Renderers.Impl.PolylineSvgNodeRenderer">polyline</see>
            shape.
            </summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.RADIAL_GRADIENT">
            <summary>Tag defining a radial gradient</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.RECT">
            <summary>
            Tag defining a
            <see cref="T:iText.Svg.Renderers.Impl.RectangleSvgNodeRenderer">rectangle</see>.
            </summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.SCRIPT">
            <summary>Not supported in PDF.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.SET">
            <summary>Not supported in PDF.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.STOP">
            <summary>Tag defining the ramp of colors in a gradient.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.STOP_COLOR">
            <summary>Tag defining the color in stop point of a gradient.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.STOP_OPACITY">
            <summary>Tag defining the opacity in stop point of a gradient.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.STYLE">
            <summary>Tag defining the style to be.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.SVG">
            <summary>
            Tag defining an
            <see cref="T:iText.Svg.Renderers.Impl.SvgTagSvgNodeRenderer">SVG</see>
            element.
            </summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.SWITCH">
            <summary>Tag defining a switch element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.SYMBOL">
            <summary>Tag defining graphical templates that can be reused by the use tag.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.TEXT">
            <summary>Tag defining text to be drawn on a page/screen.</summary>
            <since>7.1.2</since>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.TEXT_LEAF">
            <summary>Phantom tag for text leaf.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.TEXT_PATH">
            <summary>Tag defining a path on which text can be drawn.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.TITLE">
            <summary>Tag defining the description of an element.</summary>
            <remarks>Tag defining the description of an element. Is not rendered.</remarks>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.TSPAN">
            <summary>Tag defining a span within a text element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.USE">
            <summary>Tag defining the use of a named object.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.VIEW">
            <summary>Tag defining how to view the image.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Tags.VKERN">
            <summary>Tag defining the vertical kerning values in between two glyphs.</summary>
        </member>
        <member name="T:iText.Svg.SvgConstants.Attributes">
            <summary>Class containing the constant property names for the attributes of tags in the SVG spec</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.CLIP_PATH">
            <summary>Attribute defining the clipping path to be applied to a specific shape or group of shapes.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.CLIP_RULE">
            <summary>Attribute defining the clipping rule in a clipping path (or element thereof).</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.CX">
            <summary>Attribute defining the x value of the center of a circle or ellipse.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.CY">
            <summary>Attribute defining the y value of the center of a circle or ellipse.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.D">
            <summary>Attribute defining the outline of a shape.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.DX">
            <summary>Attribute defining the relative x-translation of a text-element</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.DY">
            <summary>Attribute defining the relative y-translation of a text-element</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.FILL">
            <summary>Attribute defining the fill color.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.FILL_OPACITY">
            <summary>Attribute defining the fill opacity.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.FILL_RULE">
            <summary>Attribute defining the fill rule.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.FONT_FAMILY">
            <summary>Attribute defining the font family.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.FONT_WEIGHT">
            <summary>Attribute defining the font weight.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.FONT_STYLE">
            <summary>Attribute defining the font style.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.FONT_SIZE">
            <summary>Attribute defining the font size.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.ITALIC">
            <summary>The Constant ITALIC.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.BOLD">
            <summary>The Constant BOLD.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.GRADIENT_UNITS">
            <summary>Attribute defining the units relation for a color gradient.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.GRADIENT_TRANSFORM">
            <summary>Attribute defining the transformations for a color gradient.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.HEIGHT">
            <summary>Attribute defining the height.</summary>
            <remarks>Attribute defining the height. Used in several elements.</remarks>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.HREF">
            <summary>Attribute defining the href value.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.ID">
            <summary>Attribute defining the unique id of an element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.MARKER_END">
            <summary>Attribute defining the marker to use at the end of a path, line, polygon or polyline</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.MARKER_HEIGHT">
            <summary>Attribute defining the height of the viewport in which the marker is to be fitted</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.MARKER_MID">
            <summary>Attribute defining the marker drawn at every other vertex but the start and end of a path, line, polygon or polyline
                </summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.MARKER_START">
            <summary>Attribute defining the marker to use at the start of a path, line, polygon or polyline</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.MARKER_WIDTH">
            <summary>Attribute defining the width of the viewport in which the marker is to be fitted</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.MARKER_UNITS">
            <summary>Attribute defining the coordinate system for attributes ‘markerWidth’, ‘markerHeight’ and the contents of the ‘marker’.
                </summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.OFFSET">
            <summary>Attribute defining the offset of a stop color for gradients.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.OPACITY">
            <summary>Attribute defining the opacity of a group or graphic element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.ORIENT">
            <summary>Attribute defining the orientation of a marker</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_CLOSE_PATH">
            <summary>Close Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_CURVE_TO">
            <summary>CurveTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_CURVE_TO">
            <summary>Relative CurveTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_ELLIPTICAL_ARC_A">
            <summary>Attribute defining Elliptical arc path operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_ELLIPTICAL_ARC_A">
            <summary>Attribute defining Elliptical arc path operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_CURVE_TO_S">
            <summary>Smooth CurveTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_CURVE_TO_S">
            <summary>Relative Smooth CurveTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_LINE_TO">
            <summary>Absolute LineTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_LINE_TO_H">
            <summary>Absolute hrizontal LineTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_LINE_TO_H">
            <summary>Relative horizontal LineTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_LINE_TO_V">
            <summary>Absolute vertical LineTo Path operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_LINE_TO_V">
            <summary>Relative vertical LineTo Path operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_LINE_TO">
            <summary>Relative LineTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_MOVE_TO">
            <summary>MoveTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_MOVE_TO">
            <summary>Relative MoveTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_SHORTHAND_CURVE_TO">
            <summary>Shorthand/smooth quadratic Bézier curveto.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_SHORTHAND_CURVE_TO">
            <summary>Relative Shorthand/smooth quadratic Bézier curveto.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_CATMULL_CURVE">
            <summary>Catmull-Rom curve command.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_CATMULL_CURVE">
            <summary>Relative Catmull-Rom curve command.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_BEARING">
            <summary>Bearing command.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_BEARING">
            <summary>Relative Bearing command.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_QUAD_CURVE_TO">
            <summary>Quadratic CurveTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATH_DATA_REL_QUAD_CURVE_TO">
            <summary>Relative Quadratic CurveTo Path Operator.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATTERN_CONTENT_UNITS">
            <summary>Attribute defining the coordinate system for the pattern content.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATTERN_TRANSFORM">
            <summary>Attribute defining list of transform definitions for the pattern element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PATTERN_UNITS">
            <summary>Attribute defining the coordinate system for attributes x, y, width , and height in pattern.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.POINTS">
            <summary>Attribute defining the points of a polyline or polygon.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.PRESERVE_ASPECT_RATIO">
            <summary>Attribute defining how to preserve the aspect ratio when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.R">
            <summary>Attribute defining the radius of a circle.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.REFX">
            <summary>Attribute defining the x-axis coordinate of the reference point which is to be aligned exactly at the marker position.
                </summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.REFY">
            <summary>Attribute defining the y-axis coordinate of the reference point which is to be aligned exactly at the marker position.
                </summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.RX">
            <summary>Attribute defining the x-axis of an ellipse or the x-axis radius of rounded rectangles.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.RY">
            <summary>Attribute defining the y-axis of an ellipse or the y-axis radius of rounded rectangles.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.SPREAD_METHOD">
            <summary>Attribute defining the spread method for a color gradient.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.STROKE">
            <summary>Attribute defining the stroke color.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.STROKE_DASHARRAY">
            <summary>Attribute defining the stroke dash offset.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.STROKE_DASHOFFSET">
            <summary>Attribute defining the stroke dash offset.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.STROKE_LINECAP">
            <summary>Attribute defining the stroke linecap.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.STROKE_MITERLIMIT">
            <summary>Attribute defining the stroke miterlimit.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.STROKE_OPACITY">
            <summary>Attribute defingin the stroke opacity.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.STROKE_WIDTH">
            <summary>Attribute defining the stroke width.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.STYLE">
            <summary>Attribute defining the style of an element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.TEXT_CONTENT">
            <summary>Attribute defining the text content of a text node.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.TEXT_ANCHOR">
            <summary>Attribute defining the text anchor used by the text</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.TRANSFORM">
            <summary>Attribute defining a transformation that needs to be applied.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.VIEWBOX">
            <summary>Attribute defining the viewbox of an element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.WIDTH">
            <summary>Attribute defining the width of an element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.X">
            <summary>Attribute defining the x value of an element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.X1">
            <summary>Attribute defining the first x coordinate value of a line.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.X2">
            <summary>Attribute defining the second x coordinate value of a line.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.XLINK_HREF">
            <summary>Attribute defining image source.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.XMLNS">
            <summary>Attribute defining XML namespace</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.Y">
            <summary>Attribute defining the y value of an element.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.Y1">
            <summary>Attribute defining the first y coordinate value of a line.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.Y2">
            <summary>Attribute defining the second y coordinate value of a line.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Attributes.VERSION">
            <summary>Attribute defining version</summary>
        </member>
        <member name="T:iText.Svg.SvgConstants.Values">
            <summary>Class containing the constants for values appearing in SVG tags and attributes</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.AUTO">
            <summary>Value representing automatic orientation for the marker attribute orient.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.AUTO_START_REVERSE">
            <summary>Value representing reverse automatic orientation for the start marker.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.BUTT">
            <summary>Value representing the default value for the stroke linecap.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.DEFAULT_ASPECT_RATIO">
            <summary>Value representing the default aspect ratio: xmidymid.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.DEFER">
            <summary>Value representing how to preserve the aspect ratio when dealing with images.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.FILL_RULE_EVEN_ODD">
            <summary>Value representing the fill rule "even odd".</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.FILL_RULE_NONZERO">
            <summary>Value representing the fill rule "nonzero".</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.MEET">
            <summary>Value representing the meet for preserve aspect ratio calculations.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.NONE">
            <summary>Value representing the "none" value".</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.OBJECT_BOUNDING_BOX">
            <summary>Value representing the units relation "objectBoundingBox".</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.SLICE">
            <summary>The value representing slice for the preserve aspect ratio calculations;</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.TEXT_ANCHOR_END">
            <summary>Value representing the text-alignment end for text objects</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.TEXT_ANCHOR_MIDDLE">
            <summary>Value representing the text-alignment middle for text objects</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.TEXT_ANCHOR_START">
            <summary>Value representing the text-alignment start for text objects</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.SPREAD_METHOD_PAD">
            <summary>Value representing the gradient spread method "pad".</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.SPREAD_METHOD_REPEAT">
            <summary>Value representing the gradient spread method "repeat".</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.SPREAD_METHOD_REFLECT">
            <summary>Value representing the gradient spread method "reflect".</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.STROKEWIDTH">
            <summary>The value for markerUnits that represent values in a coordinate system which has a single unit equal the size in user units of the current stroke width.
                </summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.USER_SPACE_ON_USE">
            <summary>Value representing the units relation "userSpaceOnUse".</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMIN_YMIN">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMIN_YMID">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMIN_YMAX">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMID_YMID">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMID_YMIN">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMID_YMAX">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMAX_YMIN">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMAX_YMID">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="F:iText.Svg.SvgConstants.Values.XMAX_YMAX">
            <summary>Value representing how to align when scaling.</summary>
        </member>
        <member name="T:iText.Svg.Utils.DrawUtils">
            <summary>
            Utility class for drowing shapes on
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            </summary>
        </member>
        <member name="M:iText.Svg.Utils.DrawUtils.Arc(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double,iText.Kernel.Pdf.Canvas.PdfCanvas)">
            <summary>
            Draw an arc on the passed canvas,
            enclosed by the rectangle for which two opposite corners are specified.
            </summary>
            <remarks>
            Draw an arc on the passed canvas,
            enclosed by the rectangle for which two opposite corners are specified.
            The arc starts at the passed starting angle and extends to the starting angle + extent
            </remarks>
            <param name="x1">corner-coordinate of the enclosing rectangle, first corner</param>
            <param name="y1">corner-coordinate of the enclosing rectangle, first corner</param>
            <param name="x2">corner-coordinate of the enclosing rectangle, second corner</param>
            <param name="y2">corner-coordinate of the enclosing rectangle, second corner</param>
            <param name="startAng">starting angle in degrees</param>
            <param name="extent">extent of the arc</param>
            <param name="cv">canvas to paint on</param>
        </member>
        <member name="M:iText.Svg.Utils.SvgCoordinateUtils.MakeRelativeOperatorCoordinatesAbsolute(System.String[],System.Double[])">
            <summary>Converts relative coordinates to absolute ones.</summary>
            <remarks>
            Converts relative coordinates to absolute ones. Assumes that relative coordinates are represented by
            an array of coordinates with length proportional to the length of current coordinates array,
            so that current coordinates array is applied in segments to the relative coordinates array
            </remarks>
            <param name="relativeCoordinates">the initial set of coordinates</param>
            <param name="currentCoordinates">an array representing the point relative to which the relativeCoordinates are defined
                </param>
            <returns>a String array of absolute coordinates, with the same length as the input array</returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgCoordinateUtils.CalculateAngleBetweenTwoVectors(iText.Kernel.Geom.Vector,iText.Kernel.Geom.Vector)">
            <summary>Calculate the angle between two vectors</summary>
            <param name="vectorA">first vector</param>
            <param name="vectorB">second vector</param>
            <returns>angle between vectors in radians units</returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgCoordinateUtils.GetCoordinateForUserSpaceOnUse(System.String,System.Double,System.Double,System.Double,System.Single,System.Single)">
            <summary>Returns absolute value for attribute in userSpaceOnUse coordinate system.</summary>
            <param name="attributeValue">value of attribute.</param>
            <param name="defaultValue">default value.</param>
            <param name="start">start border for calculating percent value.</param>
            <param name="length">length for calculating percent value.</param>
            <param name="em">em value.</param>
            <param name="rem">rem value.</param>
            <returns>absolute value in the userSpaceOnUse coordinate system.</returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgCoordinateUtils.GetCoordinateForObjectBoundingBox(System.String,System.Double)">
            <summary>Returns a value relative to the object bounding box.</summary>
            <remarks>
            Returns a value relative to the object bounding box.
            We should only call this method for attributes with coordinates relative to the object bounding rectangle.
            </remarks>
            <param name="attributeValue">attribute value to parse</param>
            <param name="defaultValue">this value will be returned if an error occurs while parsing the attribute value
                </param>
            <returns>
            if
            <paramref name="attributeValue"/>
            is a percentage value, the given percentage of 1 will be returned.
            And if it's a valid value with a number, the number will be extracted from that value.
            </returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgCoordinateUtils.ApplyViewBox(iText.Kernel.Geom.Rectangle,iText.Kernel.Geom.Rectangle,System.String,System.String)">
            <summary>Returns the viewBox received after scaling and displacement given preserveAspectRatio.</summary>
            <param name="viewBox">
            parsed viewBox rectangle. It should be a valid
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            </param>
            <param name="currentViewPort">
            current element view port. It should be a valid
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            </param>
            <param name="align">
            the alignment value that indicates whether to force uniform scaling
            and, if so, the alignment method to use in case the aspect ratio of
            the viewBox doesn't match the aspect ratio of the viewport. If align
            is
            <see langword="null"/>
            or align is invalid (i.e. not in the predefined list),
            then the default logic with align = "xMidYMid", and meetOrSlice = "meet" would be used
            </param>
            <param name="meetOrSlice">
            the way to scale the viewBox. If meetOrSlice is not
            <see langword="null"/>
            and invalid,
            then the default logic with align = "xMidYMid"
            and meetOrSlice = "meet" would be used, if meetOrSlice is
            <see langword="null"/>
            then default "meet" value would be used with the specified align
            </param>
            <returns>
            the applied viewBox
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            </returns>
        </member>
        <member name="T:iText.Svg.Utils.SvgCssUtils">
            <summary>Utility class that facilitates parsing values from CSS.</summary>
        </member>
        <member name="M:iText.Svg.Utils.SvgCssUtils.SplitValueList(System.String)">
            <summary>Splits a given String into a list of substrings.</summary>
            <remarks>
            Splits a given String into a list of substrings.
            The string is split up by commas and whitespace characters (\t, \n, \r, \f).
            </remarks>
            <param name="value">the string to be split</param>
            <returns>a list containing the split strings, an empty list if the value is null or empty</returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgCssUtils.ConvertFloatToString(System.Single)">
            <summary>Converts a float to a String.</summary>
            <param name="value">to be converted float value</param>
            <returns>the value in a String representation</returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgCssUtils.ConvertDoubleToString(System.Double)">
            <summary>Converts a double to a String.</summary>
            <param name="value">to be converted double value</param>
            <returns>the value in a String representation</returns>
        </member>
        <member name="T:iText.Svg.Utils.SvgTextUtil">
            <summary>Class containing utility methods for text operations in the context of SVG processing</summary>
        </member>
        <member name="M:iText.Svg.Utils.SvgTextUtil.TrimLeadingWhitespace(System.String)">
            <summary>Trim all the leading whitespace characters from the passed string</summary>
            <param name="toTrim">string to trim</param>
            <returns>string with all leading whitespace characters removed</returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgTextUtil.TrimTrailingWhitespace(System.String)">
            <summary>Trim all the trailing whitespace characters from the passed string</summary>
            <param name="toTrim">string to trim</param>
            <returns>string with al trailing whitespace characters removed</returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgTextUtil.ProcessWhiteSpace(iText.Svg.Renderers.Impl.TextSvgBranchRenderer,System.Boolean)">
            <summary>Process the whitespace inside the Text Tree.</summary>
            <remarks>
            Process the whitespace inside the Text Tree.
            Whitespace is collapsed and new lines are handled
            A leading element in each subtree is handled different: the preceding whitespace is trimmed instead of kept
            </remarks>
            <param name="root">root of the text-renderer subtree</param>
            <param name="isLeadingElement">true if this element is a leading element(either the first child or the first element after an absolute position change)
                </param>
        </member>
        <member name="M:iText.Svg.Utils.SvgTextUtil.IsOnlyWhiteSpace(System.String)">
            <summary>Check if the String is only composed of whitespace characters</summary>
            <param name="s">string to check</param>
            <returns>true if the string only contains whitespace characters, false otherwise</returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgTextUtil.ResolveFontSize(iText.Svg.Renderers.Impl.ISvgTextNodeRenderer,System.Single)">
            <summary>Resolve the font size stored inside the passed renderer</summary>
            <param name="renderer">renderer containing the font size declaration</param>
            <param name="parentFontSize">parent font size to fall back on if the renderer does not contain a font size declarations or if the stored declaration is invalid
                </param>
            <returns>float containing the font-size, or the parent font size if the renderer's declaration cannot be resolved
                </returns>
        </member>
        <member name="M:iText.Svg.Utils.SvgTextUtil.FilterReferenceValue(System.String)">
            <summary>The reference value may contain a hashtag character or 'url' designation and this method will filter them.
                </summary>
            <param name="name">value to be filtered</param>
            <returns>filtered value</returns>
        </member>
        <member name="T:iText.Svg.Utils.TextRectangle">
            <summary>A rectangle adapted for working with text elements.</summary>
        </member>
        <member name="F:iText.Svg.Utils.TextRectangle.textBaseLineYCoordinate">
            <summary>The y coordinate of the line on which the text is located.</summary>
        </member>
        <member name="M:iText.Svg.Utils.TextRectangle.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>Create new instance of text rectangle.</summary>
            <param name="x">the x coordinate of lower left point</param>
            <param name="y">the y coordinate of lower left point</param>
            <param name="width">the width value</param>
            <param name="height">the height value</param>
            <param name="textBaseLineYCoordinate">the y coordinate of the line on which the text is located.</param>
        </member>
        <member name="M:iText.Svg.Utils.TextRectangle.GetTextBaseLineRightPoint">
            <summary>Return the far right point of the rectangle with y on the baseline.</summary>
            <returns>the far right baseline point</returns>
        </member>
        <member name="T:iText.Svg.Utils.TransformUtils">
            <summary>
            Utility class responsible for converting Strings containing transformation declarations
            into AffineTransform objects.
            </summary>
            <remarks>
            Utility class responsible for converting Strings containing transformation declarations
            into AffineTransform objects.
            <para />
            This class only supports the transformations as described in the SVG specification:
            - matrix
            - rotate
            - scale
            - skewX
            - skewY
            - translate
            </remarks>
        </member>
        <member name="F:iText.Svg.Utils.TransformUtils.MATRIX">
            <summary>Keyword for matrix transformations.</summary>
            <remarks>
            Keyword for matrix transformations. Accepts 6 values.
            <para />
            matrix(0 1 2 3 4 5)
            </remarks>
        </member>
        <member name="F:iText.Svg.Utils.TransformUtils.ROTATE">
            <summary>Keyword for rotation transformation.</summary>
            <remarks>
            Keyword for rotation transformation. Accepts either 1 or 3 values.
            In the case of 1 value, x and y are assumed to be the origin of the user space.
            <para />
            rotate(angle x y)
            rotate(angle)
            </remarks>
        </member>
        <member name="F:iText.Svg.Utils.TransformUtils.SCALE">
            <summary>Keyword for scale transformation.</summary>
            <remarks>
            Keyword for scale transformation. Accepts either 1 or 2 values.
            In the case of 1 value, the second value is assumed to be the same as the first one.
            <para />
            scale(x y)
            scale(x)
            </remarks>
        </member>
        <member name="F:iText.Svg.Utils.TransformUtils.SKEWX">
            <summary>Keyword for skewX transformation.</summary>
            <remarks>
            Keyword for skewX transformation. Accepts 1 value.
            <para />
            skewX(angle)
            </remarks>
        </member>
        <member name="F:iText.Svg.Utils.TransformUtils.SKEWY">
            <summary>Keyword for skewY transformation.</summary>
            <remarks>
            Keyword for skewY transformation. Accepts 1 value.
            <para />
            skewY(angle)
            </remarks>
        </member>
        <member name="F:iText.Svg.Utils.TransformUtils.TRANSLATE">
            <summary>Keyword for translate transformation.</summary>
            <remarks>
            Keyword for translate transformation. Accepts either 1 or 2 values.
            In the case of 1 value, the y value is assumed to be 0.
            <para />
            translate(x y)
            translate(x)
            </remarks>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.ParseTransform(System.String)">
            <summary>Converts a string containing a transform declaration into an AffineTransform object.</summary>
            <remarks>
            Converts a string containing a transform declaration into an AffineTransform object.
            This class only supports the transformations as described in the SVG specification:
            - matrix
            - translate
            - skewx
            - skewy
            - rotate
            - scale
            </remarks>
            <param name="transform">value to be parsed</param>
            <returns>the AffineTransform object</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.SplitString(System.String)">
            <summary>A transformation attribute can encompass multiple transformation operation (e.g. "translate(10,20) scale(30,40)".
                </summary>
            <remarks>
            A transformation attribute can encompass multiple transformation operation (e.g. "translate(10,20) scale(30,40)".
            This method splits the original transformation string into multiple strings so that they can be handled separately.
            </remarks>
            <param name="transform">the transformation value</param>
            <returns>a list containing strings describing a single transformation operation</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.TransformationStringToMatrix(System.String)">
            <summary>This method decides which transformation operation the given transformation strings maps onto.</summary>
            <param name="transformation">string containing a transformation operation</param>
            <returns>the mapped AffineTransform object</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.CreateSkewYTransformation(System.Collections.Generic.IList{System.String})">
            <summary>Creates a skewY transformation.</summary>
            <param name="values">values of the transformation</param>
            <returns>AffineTransform for the skew operation</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.CreateSkewXTransformation(System.Collections.Generic.IList{System.String})">
            <summary>Creates a skewX transformation.</summary>
            <param name="values">values of the transformation</param>
            <returns>AffineTransform for the skew operation</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.CreateRotationTransformation(System.Collections.Generic.IList{System.String})">
            <summary>Creates a rotate transformation.</summary>
            <param name="values">values of the transformation</param>
            <returns>AffineTransform for the rotate operation</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.CreateScaleTransformation(System.Collections.Generic.IList{System.String})">
            <summary>Creates a scale transformation.</summary>
            <param name="values">values of the transformation</param>
            <returns>AffineTransform for the scale operation</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.CreateTranslateTransformation(System.Collections.Generic.IList{System.String})">
            <summary>Creates a translate transformation.</summary>
            <param name="values">values of the transformation</param>
            <returns>AffineTransform for the translate operation</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.CreateMatrixTransformation(System.Collections.Generic.IList{System.String})">
            <summary>Creates a matrix transformation.</summary>
            <param name="values">values of the transformation</param>
            <returns>AffineTransform for the matrix keyword</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.GetNameFromString(System.String)">
            <summary>This method extracts the transformation name given a transformation.</summary>
            <param name="transformation">the transformation</param>
            <returns>the name of the transformation</returns>
        </member>
        <member name="M:iText.Svg.Utils.TransformUtils.GetValuesFromTransformationString(System.String)">
            <summary>This method extracts the values from a transformation.</summary>
            <param name="transformation">the transformation</param>
            <returns>values of the transformation</returns>
        </member>
        <member name="T:iText.Svg.Xobject.SvgImageXObject">
            <summary>A wrapper for Form XObject for SVG images.</summary>
        </member>
        <member name="M:iText.Svg.Xobject.SvgImageXObject.#ctor(iText.Kernel.Geom.Rectangle,iText.Svg.Processors.ISvgProcessorResult,iText.StyledXmlParser.Resolver.Resource.ResourceResolver)">
            <summary>Creates a new instance of Form XObject for the SVG image.</summary>
            <param name="bBox">the form XObject’s bounding box.</param>
            <param name="result">processor result containing the SVG information.</param>
            <param name="resourceResolver">
            
            <see cref="T:iText.StyledXmlParser.Resolver.Resource.ResourceResolver"/>
            for the SVG image.
            </param>
        </member>
        <member name="M:iText.Svg.Xobject.SvgImageXObject.GetResult">
            <summary>Returns processor result containing the SVG information.</summary>
            <returns>{ISvgProcessorResult} processor result.</returns>
        </member>
        <member name="M:iText.Svg.Xobject.SvgImageXObject.GetResourceResolver">
            <summary>Returns resource resolver for the SVG image.</summary>
            <returns>
            
            <see cref="T:iText.StyledXmlParser.Resolver.Resource.ResourceResolver"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Svg.Xobject.SvgImageXObject.Generate(iText.Kernel.Pdf.PdfDocument)">
            <summary>Processes xObject before first image generation to avoid drawing it twice or more.</summary>
            <remarks>
            Processes xObject before first image generation to avoid drawing it twice or more. It allows to reuse the same
            Form XObject multiple times.
            </remarks>
            <param name="document">pdf that shall contain the SVG image.</param>
        </member>
    </members>
</doc>
