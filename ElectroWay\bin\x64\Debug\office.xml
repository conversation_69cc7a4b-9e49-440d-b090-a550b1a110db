﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
	<assembly>
		<name>office</name>
	</assembly>
	<members>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.accDoDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">hiddenmemberparam</param>
			<param name="yTop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">hiddenmemberparam</param>
			<param name="pcyHeight">hiddenmemberparam</param>
			<param name="pxLeft">hiddenmemberparam</param>
			<param name="pyTop">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.accNavigate(System.Int32,System.Object)">
			<param name="navDir">hiddenmemberparam</param>
			<param name="varStart">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.Copy(System.Object,System.Object)">
			<param name="Before">hiddenmemberparam</param>
			<param name="Bar">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.Delete(System.Object)">
			<param name="Temporary">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.Move(System.Object,System.Object)">
			<param name="Before">hiddenmemberparam</param>
			<param name="Bar">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarActiveX.SetInnerObjectFactory(System.Object)">
			<param name="pUnk">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accChild(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accDescription(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accHelp(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accKeyboardShortcut(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accName(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accRole(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accState(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.accValue(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarActiveX.QueryControlInterface(System.String)">
			<param name="bstrIid">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBarButton">
			<summary>Represents a button control on a command bar.</summary>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButton.accDoDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButton.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">hiddenmemberparam</param>
			<param name="yTop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButton.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">hiddenmemberparam</param>
			<param name="pcyHeight">hiddenmemberparam</param>
			<param name="pxLeft">hiddenmemberparam</param>
			<param name="pyTop">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButton.accNavigate(System.Int32,System.Object)">
			<param name="navDir">hiddenmemberparam</param>
			<param name="varStart">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButton.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButton.Copy(System.Object,System.Object)">
			<summary>Copies a command bar control to an existing command bar.</summary>
			<param name="Before">Optional Object. A <see cref="T:Microsoft.Office.Core.CommandBar"></see> object that represents the destination command bar. If this argument is omitted, the control is copied to the command bar where the control already exists.</param>
			<param name="Bar">Optional Object. A number that indicates the position for the new control on the command bar. The new control will be inserted before the control at this position. If this argument is omitted, the control is copied to the end of the command bar.</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButton.Delete(System.Object)">
			<summary>Deletes the specified object from its collection.</summary>
			<param name="Temporary">Optional Object. True to delete the control for the current session. The application will display the control again in the next session.</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButton.Move(System.Object,System.Object)">
			<summary>Moves the specified command bar control to an existing command bar.</summary>
			<param name="Before">A <see cref="T:Microsoft.Office.Core.CommandBar"></see> object that represents the destination command bar for the control. If this argument is omitted, the control is moved to the end of the command bar where the control currently resides.</param>
			<param name="Bar">A number that indicates the position for the control. The control is inserted before the control currently occupying this position. If this argument is omitted, the control is inserted on the same command bar.</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accChild(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accDescription(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accHelp(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accKeyboardShortcut(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accName(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accRole(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accState(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarButton.accValue(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarButtonEvents.Click(Microsoft.Office.Core.CommandBarButton,System.Boolean@)">
			<param name="CancelDefault">internaluseonlyparam</param>
			<param name="Ctrl">internaluseonlyparam</param>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBarButtonEvents_ClickEventHandler">
			<summary>A Delegate type used to add an event handler for the <see cref="E:Microsoft.Office.Core._CommandBarButtonEvents_Event.Click"></see> event. The Click event occurs when the user clicks a <see cref="T:Microsoft.Office.Core.CommandBarButton"></see> object.</summary>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBarButtonEvents_Event">
			<summary>Events interface for Microsoft Office <see cref="T:Microsoft.Office.Core.CommandBarButton"></see> object events.</summary>
		</member>
		<member name="E:Microsoft.Office.Core._CommandBarButtonEvents_Event.Click">
			<summary>Occurs when the user clicks a <see cref="T:Microsoft.Office.Core.CommandBarButton"></see> object.</summary>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBarComboBox">
			<summary>Represents a combo box control on a command bar.</summary>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.accDoDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">hiddenmemberparam</param>
			<param name="yTop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">hiddenmemberparam</param>
			<param name="pcyHeight">hiddenmemberparam</param>
			<param name="pxLeft">hiddenmemberparam</param>
			<param name="pyTop">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.accNavigate(System.Int32,System.Object)">
			<param name="navDir">hiddenmemberparam</param>
			<param name="varStart">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.AddItem(System.String,System.Object)">
			<summary>Adds a list item to the specified command bar combo box control.</summary>
			<param name="Index">Optional Object. The position of the item in the list. If this argument is omitted, the item is added to the end of the list.</param>
			<param name="Text">Required String. The text added to the control.</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.Copy(System.Object,System.Object)">
			<summary>Copies a command bar control to an existing command bar.</summary>
			<param name="Before">Optional Object. A number that indicates the position for the new control on the command bar. The new control will be inserted before the control at this position. If this argument is omitted, the control is copied to the end of the command bar.</param>
			<param name="Bar">Optional Object. A <see cref="T:Microsoft.Office.Core.CommandBar"></see>  object that represents the destination command bar. If this argument is omitted, the control is copied to the command bar where the control already exists.</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.Delete(System.Object)">
			<summary>Deletes the specified object from its collection.</summary>
			<param name="Temporary">Optional Object. True to delete the control for the current session. The application will display the control again in the next session.</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.Move(System.Object,System.Object)">
			<summary>Moves the specified command bar control to an existing command bar.</summary>
			<param name="Before">Optional Object. A number that indicates the position for the control. The control is inserted before the control currently occupying this position. If this argument is omitted, the control is inserted on the same command bar.</param>
			<param name="Bar">Optional Object. A <see cref="T:Microsoft.Office.Core.CommandBar"></see> object that represents the destination command bar for the control. If this argument is omitted, the control is moved to the end of the command bar where the control currently resides.</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBox.RemoveItem(System.Int32)">
			<summary>Removes an item from a command bar combo box control.</summary>
			<param name="Index">Required Integer. The item to be removed from the list.</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accChild(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accDescription(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accHelp(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accKeyboardShortcut(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accName(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accRole(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accState(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.accValue(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBarComboBox.List(System.Int32)">
			<summary>Returns or sets an item in the command bar combo box control.</summary>
			<param name="Index">Required Integer. The list item to be set.</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBarComboBoxEvents.Change(Microsoft.Office.Core.CommandBarComboBox)">
			<param name="Ctrl">internaluseonlyparam</param>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBarComboBoxEvents_ChangeEventHandler">
			<summary>A Delegate type used to add an event handler for the <see cref="E:Microsoft.Office.Core._CommandBarComboBoxEvents_Event.Change"></see> event. The Change event occurs when the end user changes the selection in a command bar combo box.</summary>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBarComboBoxEvents_Event">
			<summary>Events interface for Microsoft Office <see cref="T:Microsoft.Office.Core.CommandBarComboBox"></see> object events.</summary>
		</member>
		<member name="E:Microsoft.Office.Core._CommandBarComboBoxEvents_Event.Change">
			<summary>Occurs when the end user changes the selection in a command bar combo box.</summary>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBars">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.CommandBar"></see> objects that represent the command bars in the container application.</summary>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBars.Add(System.Object,System.Object,System.Object,System.Object)">
			<summary>Creates a new command bar and adds it to the collection of command bars.</summary>
			<param name="Position">Optional Object. The position or type of the new command bar. Can be one of the <see cref="T:Microsoft.Office.Core.MsoBarPosition"></see> constants listed in the following table.ConstantDescriptionmsoBarLeft, msoBarTop, msoBarRight, msoBarBottomIndicates the left, top, right, and bottom coordinates of the new command barmsoBarFloatingIndicates that the new command bar won't be dockedmsoBarPopupIndicates that the new command bar will be a shortcut menumsoBarMenuBarMacintosh only</param>
			<param name="Temporary">Optional Object. True to make the new command bar temporary. Temporary command bars are deleted when the container application is closed. The default value is False.</param>
			<param name="MenuBar">Optional Object. True to replace the active menu bar with the new command bar. The default value is False.</param>
			<param name="Name">Optional Object. The name of the new command bar. If this argument is omitted, a default name is assigned to the command bar (such as Custom 1).</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBars.AddEx(System.Object,System.Object,System.Object,System.Object,System.Object)">
			<param name="Position">hiddenmemberparam</param>
			<param name="Temporary">hiddenmemberparam</param>
			<param name="MenuBar">hiddenmemberparam</param>
			<param name="TbtrProtection">hiddenmemberparam</param>
			<param name="TbidOrName">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBars.FindControl(System.Object,System.Object,System.Object,System.Object)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.CommandBarControl"></see> object that fits the specified criteria.</summary>
			<param name="Id">Optional Object. The identifier of the control.</param>
			<param name="Type">Optional <see cref="T:Microsoft.Office.Core.MsoControlType"></see>. The type of control.</param>
			<param name="Tag">Optional Object. The tag value of the control.</param>
			<param name="Visible">Optional Object. True to include only visible command bar controls in the search. The default value is False. Visible command bars include all visible toolbars and any menus that are open at the time the FindControl method is executed.</param>
		</member>
		<member name="M:Microsoft.Office.Core._CommandBars.FindControls(System.Object,System.Object,System.Object,System.Object)">
			<summary>Returns the <see cref="T:Microsoft.Office.Core.CommandBarControls"></see> collection that fits the specified criteria.</summary>
			<param name="Id">Optional Object. The control’s identifier.</param>
			<param name="Type">Optional <see cref="T:Microsoft.Office.Core.MsoControlType"></see>. The type of control.</param>
			<param name="Tag">Optional Object. The control’s tag value.</param>
			<param name="Visible">Optional Object. True to include only visible command bar controls in the search. The default value is False.</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBars.IdsString(System.Int32,System.String@)">
			<param name="pbstrName">hiddenmemberparam</param>
			<param name="ids">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBars.Item(System.Object)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.CommandBar"></see> object from the <see cref="T:Microsoft.Office.Core.CommandBars"></see> collection.</summary>
			<param name="Index">Required Object. The name or index number of the object to be returned.</param>
		</member>
		<member name="P:Microsoft.Office.Core._CommandBars.TmcGetName(System.Int32,System.String@)">
			<param name="pbstrName">hiddenmemberparam</param>
			<param name="tmc">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBarsEvents_Event">
			<summary>Events interface for Microsoft Office <see cref="T:Microsoft.Office.Core.CommandBars"></see> object events.</summary>
		</member>
		<member name="E:Microsoft.Office.Core._CommandBarsEvents_Event.OnUpdate">
			<summary>Occurs when any change is made to a command bar.</summary>
		</member>
		<member name="T:Microsoft.Office.Core._CommandBarsEvents_OnUpdateEventHandler">
			<summary>A Delegate type used to add an event handler for the <see cref="E:Microsoft.Office.Core._CommandBarsEvents_Event.OnUpdate"></see> event. The OnUpdate event occurs when any change is made to a command bar.</summary>
		</member>
		<member name="M:Microsoft.Office.Core._IMsoOleAccDispObj.accDoDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._IMsoOleAccDispObj.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">hiddenmemberparam</param>
			<param name="yTop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._IMsoOleAccDispObj.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">hiddenmemberparam</param>
			<param name="pcyHeight">hiddenmemberparam</param>
			<param name="pxLeft">hiddenmemberparam</param>
			<param name="pyTop">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._IMsoOleAccDispObj.accNavigate(System.Int32,System.Object)">
			<param name="navDir">hiddenmemberparam</param>
			<param name="varStart">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core._IMsoOleAccDispObj.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accChild(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accDescription(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accHelp(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accKeyboardShortcut(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accName(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accRole(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accState(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core._IMsoOleAccDispObj.accValue(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.Adjustments.Item(System.Int32)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.AnswerWizard">
			<summary>Represents the Answer Wizard in a Microsoft Office application. </summary>
		</member>
		<member name="M:Microsoft.Office.Core.AnswerWizard.ClearFileList">
			<summary>Clears the list of files for the current AnswerWizard, including the default list of files for the Microsoft Office host application.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.AnswerWizard.ResetFileList">
			<summary>Resets the list of files for the current AnswerWizard to the default list of files for the Microsoft Office host application.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.AnswerWizardFiles">
			<summary>The AnswerWizardFiles collection contains all of the Answer Wizard files (with the file name extension .AW) available to the active Microsoft Office application.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.AnswerWizardFiles.Add(System.String)">
			<summary>Creates a new reference to an Answer Wizard file and adds it to the <see cref="T:Microsoft.Office.Core.AnswerWizardFiles"></see> collection.</summary>
			<param name="FileName">Required String. The fully qualified path to the specified Answer Wizard file.</param>
		</member>
		<member name="M:Microsoft.Office.Core.AnswerWizardFiles.Delete(System.String)">
			<summary>Deletes the specified object from its collection.</summary>
			<param name="FileName">Required String. The name of the file to be deleted, including the fully qualified path, file name, and extension.</param>
		</member>
		<member name="P:Microsoft.Office.Core.AnswerWizardFiles.Item(System.Int32)">
			<summary>Returns a file name string from an <see cref="T:Microsoft.Office.Core.AnswerWizardFiles"></see> collection.</summary>
			<param name="Index">Required Integer. The index number of the Answer Wizard file name string, or the file name, to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.Assistant">
			<summary>Represents the Microsoft Office Assistant.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.Assistant.ActivateWizard(System.Int32,Microsoft.Office.Core.MsoWizardActType,System.Object)">
			<summary>Resumes or suspends Office Assistant Help during a custom wizard.</summary>
			<param name="act">Specifies the change to the Office Assistant Help session.</param>
			<param name="Animation">The animation the Office Assistant performs when it is suspended or resumed.</param>
			<param name="WizardID">The number returned by the <see cref="M:Microsoft.Office.Core.Assistant.StartWizard(System.Boolean,System.String,System.Int32,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)"></see> method.</param>
		</member>
		<member name="M:Microsoft.Office.Core.Assistant.DoAlert(System.String,System.String,Microsoft.Office.Core.MsoAlertButtonType,Microsoft.Office.Core.MsoAlertIconType,Microsoft.Office.Core.MsoAlertDefaultType,Microsoft.Office.Core.MsoAlertCancelType,System.Boolean)">
			<summary>Displays an alert and returns an Integer that indicates which button the user pressed.</summary>
			<param name="bstrAlertText">Sets the text of the alert.</param>
			<param name="varfSysAlert">True if the alert is displayed in a message box or False if the alert is displayed through the Office Assistant.</param>
			<param name="alq">Always set this to msoAlertCancelDefault. Any other setting may return an error.</param>
			<param name="bstrAlertTitle">Sets the title of the alert.</param>
			<param name="alc">Determines the icon that is displayed on the alert.</param>
			<param name="alb">Determines which buttons are displayed on the alert.</param>
			<param name="ald">Determines which button is set as the default button of the alert. If this argument is set to a value greater than the number of buttons, an error is returned.</param>
		</member>
		<member name="M:Microsoft.Office.Core.Assistant.EndWizard(System.Int32,System.Boolean,System.Object)">
			<summary>Releases the variable returned by the <see cref="M:Microsoft.Office.Core.Assistant.StartWizard(System.Boolean,System.String,System.Int32,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)"></see> method.</summary>
			<param name="Animation">The animation the Office Assistant performs if varfSuccess is set to True. The default value is msoAnimationCharacterSuccessMajor.</param>
			<param name="WizardID">The number returned by the StartWizard method.</param>
			<param name="varfSuccess">True to indicate that the user completed the wizard successfully.</param>
		</member>
		<member name="M:Microsoft.Office.Core.Assistant.Help">
			<summary>Displays the Office Assistant and the built-in "What would you like to do?" Assistant balloon for standard Office online Help.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.Assistant.Move(System.Int32,System.Int32)">
			<summary>Moves the Office Assistant to the specified location.</summary>
			<param name="xLeft">The left position of the Office Assistant window, in points.</param>
			<param name="yTop">The top position of the Office Assistant window, in points.</param>
		</member>
		<member name="M:Microsoft.Office.Core.Assistant.ResetTips">
			<summary>Resets the application tips that appear in the Office Assistant balloon.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.Assistant.StartWizard(System.Boolean,System.String,System.Int32,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)">
			<summary>Starts the Office Assistant and returns an Integer value that identifies the session.</summary>
			<param name="On">True to display the Office decision balloon. The Office decision balloon asks the user whether he or she wants help with the active custom wizard. It isn't necessary to use the <see cref="P:Microsoft.Office.Core.Assistant.Visible"></see> property to display the Office Assistant if you specify True for this argument.</param>
			<param name="Callback">The name of the callback procedure run by the Office decision balloon and the branch balloon. The branch balloon allows the user to choose between custom Help you've provided for the wizard and standard Office Help.</param>
			<param name="Left">The position of the corners (in points and relative to the screen) of the custom wizard form the Office Assistant will avoid when the Office Assistant appears.</param>
			<param name="CustomTeaser">False to display the Office decision balloon.</param>
			<param name="Right">The position of the corners (in points and relative to the screen) of the custom wizard form the Office Assistant will avoid when the Office Assistant appears.</param>
			<param name="Bottom">The position of the corners (in points and relative to the screen) of the custom wizard form the Office Assistant will avoid when the Office Assistant appears.</param>
			<param name="Animation">The animation the Office Assistant performs when this method is used. The default value is msoAnimationGetWizardy.</param>
			<param name="PrivateX">A number that identifies the balloon that initiated the callback procedure.</param>
			<param name="Top">The position of the corners (in points and relative to the screen) of the custom wizard form the Office Assistant will avoid when the Office Assistant appears.</param>
		</member>
		<member name="T:Microsoft.Office.Core.Balloon">
			<summary>Represents the balloon where the Office Assistant displays information.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.Balloon.SetAvoidRectangle(System.Int32,System.Int32,System.Int32,System.Int32)">
			<summary>Prevents the Office Assistant balloon from being displayed in a specified area of the screen.</summary>
			<param name="Left">Required Integer. The coordinates (in points and relative to the screen) of the area of the screen that the Office Assistant balloon will avoid when it's displayed.</param>
			<param name="Right">Required Integer. The coordinates (in points and relative to the screen) of the area of the screen that the Office Assistant balloon will avoid when it's displayed.</param>
			<param name="Bottom">Required Integer. The coordinates (in points and relative to the screen) of the area of the screen that the Office Assistant balloon will avoid when it's displayed.</param>
			<param name="Top">Required Integer. The coordinates (in points and relative to the screen) of the area of the screen that the Office Assistant balloon will avoid when it's displayed.</param>
		</member>
		<member name="T:Microsoft.Office.Core.BalloonCheckbox">
			<summary>Represents a checkbox in the Office Assistant balloon.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.BalloonCheckboxes">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.BalloonCheckbox"></see> objects that represent all the check boxes in the Office Assistant balloon.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.BalloonCheckboxes.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.BalloonCheckbox"></see> object.</summary>
			<param name="Index">Required Item. The index number of the check box or label to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.BalloonLabel">
			<summary>Represents a label in the Office Assistant balloon.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.BalloonLabels">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.BalloonLabel"></see> objects that represent all the labels in the Office Assistant balloon.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.BalloonLabels.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.BalloonLabel"></see> object.</summary>
			<param name="Index">Required Integer. The index number of the check box or label to be returned.</param>
		</member>
		<member name="M:Microsoft.Office.Core.CalloutFormat.CustomDrop(System.Single)">
			<param name="Drop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CalloutFormat.CustomLength(System.Single)">
			<param name="Length">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CalloutFormat.PresetDrop(Microsoft.Office.Core.MsoCalloutDropType)">
			<param name="DropType">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddCallout(Microsoft.Office.Core.MsoCalloutType,System.Single,System.Single,System.Single,System.Single)">
			<param name="Type">hiddenmemberparam</param>
			<param name="Height">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddConnector(Microsoft.Office.Core.MsoConnectorType,System.Single,System.Single,System.Single,System.Single)">
			<param name="BeginX">hiddenmemberparam</param>
			<param name="Type">hiddenmemberparam</param>
			<param name="EndY">hiddenmemberparam</param>
			<param name="EndX">hiddenmemberparam</param>
			<param name="BeginY">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddCurve(System.Object)">
			<param name="SafeArrayOfPoints">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddLabel(Microsoft.Office.Core.MsoTextOrientation,System.Single,System.Single,System.Single,System.Single)">
			<param name="Height">hiddenmemberparam</param>
			<param name="Orientation">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddLine(System.Single,System.Single,System.Single,System.Single)">
			<param name="BeginX">hiddenmemberparam</param>
			<param name="EndY">hiddenmemberparam</param>
			<param name="EndX">hiddenmemberparam</param>
			<param name="BeginY">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddPicture(System.String,Microsoft.Office.Core.MsoTriState,Microsoft.Office.Core.MsoTriState,System.Single,System.Single,System.Single,System.Single)">
			<param name="Width">hiddenmemberparam</param>
			<param name="FileName">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Height">hiddenmemberparam</param>
			<param name="LinkToFile">hiddenmemberparam</param>
			<param name="SaveWithDocument">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddPolyline(System.Object)">
			<param name="SafeArrayOfPoints">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddShape(Microsoft.Office.Core.MsoAutoShapeType,System.Single,System.Single,System.Single,System.Single)">
			<param name="Type">hiddenmemberparam</param>
			<param name="Height">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddTextbox(Microsoft.Office.Core.MsoTextOrientation,System.Single,System.Single,System.Single,System.Single)">
			<param name="Height">hiddenmemberparam</param>
			<param name="Orientation">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.AddTextEffect(Microsoft.Office.Core.MsoPresetTextEffect,System.String,System.String,System.Single,Microsoft.Office.Core.MsoTriState,Microsoft.Office.Core.MsoTriState,System.Single,System.Single)">
			<param name="PresetTextEffect">hiddenmemberparam</param>
			<param name="Text">hiddenmemberparam</param>
			<param name="FontSize">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="FontItalic">hiddenmemberparam</param>
			<param name="FontBold">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
			<param name="FontName">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.BuildFreeform(Microsoft.Office.Core.MsoEditingType,System.Single,System.Single)">
			<param name="EditingType">hiddenmemberparam</param>
			<param name="X1">hiddenmemberparam</param>
			<param name="Y1">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.Item(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CanvasShapes.Range(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.COMAddIn">
			<summary>Represents a COM add-in in the Microsoft Office host application.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.COMAddIns">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.COMAddIns"></see> objects that provide information about a COM add-in registered in the Windows Registry.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.COMAddIns.Item(System.Object@)">
			<summary>Returns a member of the specified <see cref="T:Microsoft.Office.Core.COMAddIns"></see> collection.</summary>
			<param name="Index">Required Object. Either an ordinal value that returns the COM add-in at that position in the COMAddIns collection, or a String value that represents the ProgID of the specified COM add-in.</param>
		</member>
		<member name="M:Microsoft.Office.Core.COMAddIns.SetAppModal(System.Boolean)">
			<param name="varfModal">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.CommandBar">
			<summary>Represents a command bar in the container application.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBar.accDoDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBar.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">hiddenmemberparam</param>
			<param name="yTop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBar.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">hiddenmemberparam</param>
			<param name="pcyHeight">hiddenmemberparam</param>
			<param name="pyTop">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
			<param name="pxLeft">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBar.accNavigate(System.Int32,System.Object)">
			<param name="varStart">hiddenmemberparam</param>
			<param name="navDir">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBar.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBar.FindControl(System.Object,System.Object,System.Object,System.Object,System.Object)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.CommandBarControl"></see> object that fits the specified criteria.</summary>
			<param name="Visible">Optional Object. True to include only visible command bar controls in the search. The default value is False. Visible command bars include all visible toolbars and any menus that are open at the time the FindControl method is executed.</param>
			<param name="Type">Optional <see cref="T:Microsoft.Office.Core.MsoControlType"></see>. The type of control.</param>
			<param name="Recursive">Optional Boolean. True to include the command bar and all of its pop-up subtoolbars in the search. The default value is False.</param>
			<param name="Tag">Optional Object. The tag value of the control.</param>
			<param name="Id">Optional Object. The identifier of the control.</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBar.ShowPopup(System.Object,System.Object)">
			<summary>Displays a command bar as a shortcut menu at the specified coordinates or at the current pointer coordinates.</summary>
			<param name="y">Optional Object. The y-coordinate for the location of the shortcut menu. If this argument is omitted, the current y-coordinate of the pointer is used.</param>
			<param name="x">Optional Object. The x-coordinate for the location of the shortcut menu. If this argument is omitted, the current x-coordinate of the pointer is used.</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accChild(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accDescription(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accHelp(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accKeyboardShortcut(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accName(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accRole(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accState(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.accValue(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBar.Enabled">
			<summary>Determines if the specified command bar or is enabled.</summary>
			<returns>This property returns True if the specified command bar is enabled; False if not enabled.Setting this property to True causes the name of the command bar to appear in the list of available command bars.</returns>
		</member>
		<member name="T:Microsoft.Office.Core.CommandBarButton">
			<summary>Represents a button control on a command bar.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.accDoDefaultAction(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">classclassparam</param>
			<param name="yTop">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">classclassparam</param>
			<param name="pcyHeight">classclassparam</param>
			<param name="pyTop">classclassparam</param>
			<param name="varChild">classclassparam</param>
			<param name="pxLeft">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.accNavigate(System.Int32,System.Object)">
			<param name="varStart">classclassparam</param>
			<param name="navDir">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">classclassparam</param>
			<param name="varChild">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.add_Click(Microsoft.Office.Core._CommandBarButtonEvents_ClickEventHandler)">
			<param name="__unnamed0">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.Copy(System.Object,System.Object)">
			<param name="Before">classclassparam</param>
			<param name="Bar">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.Delete(System.Object)">
			<param name="Temporary">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.Move(System.Object,System.Object)">
			<param name="Before">classclassparam</param>
			<param name="Bar">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarButtonClass.remove_Click(Microsoft.Office.Core._CommandBarButtonEvents_ClickEventHandler)">
			<param name="__unnamed0">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accChild(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accDefaultAction(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accDescription(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accHelp(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">classclassparam</param>
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accKeyboardShortcut(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accName(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accRole(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accState(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarButtonClass.accValue(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.CommandBarComboBox">
			<summary>Represents a combo box control on a command bar.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.accDoDefaultAction(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">classclassparam</param>
			<param name="yTop">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">classclassparam</param>
			<param name="pcyHeight">classclassparam</param>
			<param name="pyTop">classclassparam</param>
			<param name="varChild">classclassparam</param>
			<param name="pxLeft">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.accNavigate(System.Int32,System.Object)">
			<param name="varStart">classclassparam</param>
			<param name="navDir">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">classclassparam</param>
			<param name="varChild">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.add_Change(Microsoft.Office.Core._CommandBarComboBoxEvents_ChangeEventHandler)">
			<param name="__unnamed0">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.AddItem(System.String,System.Object)">
			<param name="Text">classclassparam</param>
			<param name="Index">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.Copy(System.Object,System.Object)">
			<param name="Before">classclassparam</param>
			<param name="Bar">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.Delete(System.Object)">
			<param name="Temporary">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.Move(System.Object,System.Object)">
			<param name="Before">classclassparam</param>
			<param name="Bar">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.remove_Change(Microsoft.Office.Core._CommandBarComboBoxEvents_ChangeEventHandler)">
			<param name="__unnamed0">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarComboBoxClass.RemoveItem(System.Int32)">
			<param name="Index">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accChild(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accDefaultAction(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accDescription(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accHelp(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">classclassparam</param>
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accKeyboardShortcut(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accName(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accRole(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accState(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.accValue(System.Object)">
			<param name="varChild">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarComboBoxClass.List(System.Int32)">
			<param name="Index">classclassparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.CommandBarControl">
			<summary>Represents a command bar control.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControl.accDoDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControl.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">hiddenmemberparam</param>
			<param name="yTop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControl.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">hiddenmemberparam</param>
			<param name="pcyHeight">hiddenmemberparam</param>
			<param name="pyTop">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
			<param name="pxLeft">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControl.accNavigate(System.Int32,System.Object)">
			<param name="varStart">hiddenmemberparam</param>
			<param name="navDir">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControl.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControl.Copy(System.Object,System.Object)">
			<summary>Copies a command bar control to an existing command bar.</summary>
			<param name="Before">Optional Object. A number that indicates the position for the new control on the command bar. The new control will be inserted before the control at this position. If this argument is omitted, the control is copied to the end of the command bar.</param>
			<param name="Bar">Optional Object. A <see cref="T:Microsoft.Office.Core.CommandBar"></see> object that represents the destination command bar. If this argument is omitted, the control is copied to the command bar where the control already exists.</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControl.Delete(System.Object)">
			<summary>Deletes the specified object from its collection.</summary>
			<param name="Temporary">Optional Object. Set to True to delete the control for the current session. The application will display the control again in the next session.</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControl.Move(System.Object,System.Object)">
			<summary>Moves the specified command bar control to an existing command bar.</summary>
			<param name="Before">Optional Object. A number that indicates the position for the control. The control is inserted before the control currently occupying this position. If this argument is omitted, the control is inserted on the same command bar.</param>
			<param name="Bar">Optional Object. A <see cref="T:Microsoft.Office.Core.CommandBar"></see> object that represents the destination command bar for the control. If this argument is omitted, the control is moved to the end of the command bar where the control currently resides.</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accChild(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accDescription(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accHelp(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accKeyboardShortcut(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accName(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accRole(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accState(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControl.accValue(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.CommandBarControls">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.CommandBarControl"></see> objects that represent the command bar controls on a command bar.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarControls.Add(System.Object,System.Object,System.Object,System.Object,System.Object)">
			<summary>Creates a new <see cref="T:Microsoft.Office.Core.CommandBarControl"></see> object and adds it to the collection of controls on the specified command bar.</summary>
			<param name="Type">Optional Object. The type of control to be added to the specified command bar. Can be one of the following <see cref="T:Microsoft.Office.Core.MsoControlType"></see> constants: msoControlButton, msoControlEdit, msoControlDropdown, msoControlComboBox, or msoControlPopup.</param>
			<param name="Before">Optional Object. A number that indicates the position of the new control on the command bar. The new control will be inserted before the control at this position. If this argument is omitted, the control is added at the end of the specified command bar.</param>
			<param name="Temporary">Optional Object. True to make the new control temporary. Temporary controls are automatically deleted when the container application is closed. The default value is False.</param>
			<param name="Parameter">Optional Object. For built-in controls, this argument is used by the container application to run the command. For custom controls, you can use this argument to send information to procedures, or you can use it to store information about the control (similar to a second <see cref="P:Microsoft.Office.Core.CommandBarControl.Tag"></see> property value).</param>
			<param name="Id">Optional Object. An integer that specifies a built-in control. If the value of this argument is 1, or if this argument is omitted, a blank custom control of the specified type will be added to the command bar.</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarControls.Item(System.Object)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.CommandBarControl"></see> object from the <see cref="T:Microsoft.Office.Core.CommandBarControls"></see> collection.</summary>
			<param name="Index">Required Object. The name or index number of the object to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.CommandBarPopup">
			<summary>Represents a pop-up control on a command bar.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarPopup.accDoDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarPopup.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">hiddenmemberparam</param>
			<param name="yTop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarPopup.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">hiddenmemberparam</param>
			<param name="pcyHeight">hiddenmemberparam</param>
			<param name="pyTop">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
			<param name="pxLeft">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarPopup.accNavigate(System.Int32,System.Object)">
			<param name="varStart">hiddenmemberparam</param>
			<param name="navDir">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarPopup.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarPopup.Copy(System.Object,System.Object)">
			<summary>Copies a command bar control to an existing command bar.</summary>
			<param name="Before">Optional Object. A number that indicates the position for the new control on the command bar. The new control will be inserted before the control at this position. If this argument is omitted, the control is copied to the end of the command bar.</param>
			<param name="Bar">Optional Object. A <see cref="T:Microsoft.Office.Core.CommandBar"></see> object that represents the destination command bar. If this argument is omitted, the control is copied to the command bar where the control already exists.</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarPopup.Delete(System.Object)">
			<summary>Deletes the specified object from its collection.</summary>
			<param name="Temporary">Optional Object. True to delete the control for the current session. The application will display the control again in the next session.</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarPopup.Move(System.Object,System.Object)">
			<summary>Moves the specified command bar control to an existing command bar.</summary>
			<param name="Before">Optional Object. A number that indicates the position for the control. The control is inserted before the control currently occupying this position. If this argument is omitted, the control is inserted on the same command bar.</param>
			<param name="Bar">Optional Object. A <see cref="T:Microsoft.Office.Core.CommandBar"></see> object that represents the destination command bar for the control. If this argument is omitted, the control is moved to the end of the command bar where the control currently resides.</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accChild(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accDescription(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accHelp(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accKeyboardShortcut(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accName(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accRole(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accState(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarPopup.accValue(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.CommandBars">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.CommandBar"></see> objects that represent the command bars in the container application.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarsClass.Add(System.Object,System.Object,System.Object,System.Object)">
			<param name="Temporary">classclassparam</param>
			<param name="Position">classclassparam</param>
			<param name="Name">classclassparam</param>
			<param name="MenuBar">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarsClass.add_OnUpdate(Microsoft.Office.Core._CommandBarsEvents_OnUpdateEventHandler)">
			<param name="__unnamed0">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarsClass.AddEx(System.Object,System.Object,System.Object,System.Object,System.Object)">
			<param name="Temporary">classclassparam</param>
			<param name="TbidOrName">classclassparam</param>
			<param name="TbtrProtection">classclassparam</param>
			<param name="Position">classclassparam</param>
			<param name="MenuBar">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarsClass.FindControl(System.Object,System.Object,System.Object,System.Object)">
			<param name="Visible">classclassparam</param>
			<param name="Type">classclassparam</param>
			<param name="Tag">classclassparam</param>
			<param name="Id">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarsClass.FindControls(System.Object,System.Object,System.Object,System.Object)">
			<param name="Visible">classclassparam</param>
			<param name="Type">classclassparam</param>
			<param name="Tag">classclassparam</param>
			<param name="Id">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.CommandBarsClass.remove_OnUpdate(Microsoft.Office.Core._CommandBarsEvents_OnUpdateEventHandler)">
			<param name="__unnamed0">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarsClass.IdsString(System.Int32,System.String@)">
			<param name="ids">classclassparam</param>
			<param name="pbstrName">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarsClass.Item(System.Object)">
			<param name="Index">classclassparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.CommandBarsClass.TmcGetName(System.Int32,System.String@)">
			<param name="pbstrName">classclassparam</param>
			<param name="tmc">classclassparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ConnectorFormat.BeginConnect(Microsoft.Office.Core.Shape,System.Int32)">
			<param name="ConnectedShape">hiddenmemberparam</param>
			<param name="ConnectionSite">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ConnectorFormat.EndConnect(Microsoft.Office.Core.Shape,System.Int32)">
			<param name="ConnectedShape">hiddenmemberparam</param>
			<param name="ConnectionSite">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNode.AddNode(Microsoft.Office.Core.MsoRelativeNodePosition,Microsoft.Office.Core.MsoDiagramNodeType)">
			<param name="Pos">hiddenmemberparam</param>
			<param name="NodeType">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNode.CloneNode(System.Boolean,Microsoft.Office.Core.DiagramNode,Microsoft.Office.Core.MsoRelativeNodePosition)">
			<param name="Pos">hiddenmemberparam</param>
			<param name="CopyChildren">hiddenmemberparam</param>
			<param name="TargetNode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNode.MoveNode(Microsoft.Office.Core.DiagramNode,Microsoft.Office.Core.MsoRelativeNodePosition)">
			<param name="Pos">hiddenmemberparam</param>
			<param name="TargetNode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNode.ReplaceNode(Microsoft.Office.Core.DiagramNode)">
			<param name="TargetNode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNode.SwapNode(Microsoft.Office.Core.DiagramNode,System.Boolean)">
			<param name="SwapChildren">hiddenmemberparam</param>
			<param name="TargetNode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNode.TransferChildren(Microsoft.Office.Core.DiagramNode)">
			<param name="ReceivingNode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNodeChildren.AddNode(System.Object,Microsoft.Office.Core.MsoDiagramNodeType)">
			<param name="NodeType">hiddenmemberparam</param>
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNodeChildren.Item(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.DiagramNodes.Item(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="F:Microsoft.Office.Core.DocProperties.offPropertyTypeBoolean">
			<summary>hiddenmemberparam</summary>
		</member>
		<member name="F:Microsoft.Office.Core.DocProperties.offPropertyTypeDate">
			<summary>hiddenmemberparam</summary>
		</member>
		<member name="F:Microsoft.Office.Core.DocProperties.offPropertyTypeFloat">
			<summary>hiddenmemberparam</summary>
		</member>
		<member name="F:Microsoft.Office.Core.DocProperties.offPropertyTypeNumber">
			<summary>hiddenmemberparam</summary>
		</member>
		<member name="F:Microsoft.Office.Core.DocProperties.offPropertyTypeString">
			<summary>hiddenmemberparam</summary>
		</member>
		<member name="T:Microsoft.Office.Core.DocumentLibraryVersion">
			<summary>The DocumentLibraryVersion object represents a single saved version of a shared document that has versioning enabled and that is stored in a document library on the server.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.DocumentLibraryVersions">
			<summary>The DocumentLibraryVersions object represents a collection of <see cref="T:Microsoft.Office.Core.DocumentLibraryVersion"></see> objects.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.DocumentLibraryVersions.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.DocumentLibraryVersion"></see> object from the <see cref="T:Microsoft.Office.Core.DocumentLibraryVersions"></see> collection.</summary>
			<param name="lIndex">Required Integer. The index number of the DocumentLibraryVersion returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.DocumentProperties">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.DocumentProperty"></see> objects.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.DocumentProperties.Add(System.String,System.Boolean,System.Object,System.Object,System.Object)">
			<summary>Creates a new custom document property.</summary>
			<param name="Type">Optional Object. The data type of the property. Can be one of the following <see cref="T:Microsoft.Office.Core.MsoDocProperties"></see> constants: msoPropertyTypeBoolean, msoPropertyTypeDate, msoPropertyTypeFloat, msoPropertyTypeNumber, or msoPropertyTypeString.</param>
			<param name="LinkToContent">Required Boolean. Specifies whether the property is linked to the contents of the container document. If this argument is True, the LinkSource argument is required; if it's False, the value argument is required.</param>
			<param name="LinkSource">Optional Object. Ignored if LinkToContent is False. The source of the linked property. The container application determines what types of source linking you can use.</param>
			<param name="Name">Required String. The name of the property.</param>
			<param name="Value">Optional Object. The value of the property, if it's not linked to the contents of the container document. The value is converted to match the data type specified by the type argument. If it can't be converted, an error occurs. If LinkToContent is True, the Value argument is ignored and the new document property is assigned a default value until the linked property values are updated by the container application (usually when the document is saved).</param>
		</member>
		<member name="P:Microsoft.Office.Core.DocumentProperties.Item(System.Object)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.DocumentProperty"></see> object from the <see cref="T:Microsoft.Office.Core.DocumentProperties"></see> collection.</summary>
			<param name="Index">Required Object. The name or index number of the document property returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.DocumentProperty">
			<summary>Represents a custom or built-in document property of a container document.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.FileDialog">
			<summary>Provides file dialog box functionality similar to the functionality of the standard Open and Save dialog boxes found in Microsoft Office applications.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.FileDialogFilter">
			<summary>Represents a file filter in a file dialog box displayed through the <see cref="T:Microsoft.Office.Core.FileDialog"></see> object.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.FileDialogFilters">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.FileDialogFilter"></see> objects that represent the types of files that can be selected in a file dialog box that is displayed using the <see cref="T:Microsoft.Office.Core.FileDialog"></see> object.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.FileDialogFilters.Add(System.String,System.String,System.Object)">
			<summary>Adds a new file filter to the list of filters in the Files of type list box in the File dialog box, and returns a FileDialogFilter object that represents the newly added file filter.</summary>
			<param name="Extensions">Required String. The text representing the file extension you want to add to the list of filters. More than one extension may be specified and each must be separated by a semi-colon (;). For example, the Extensions argument can be assigned to the string: "*.txt; *.htm".Note Parentheses do not need to be added around the extensions. Office will automatically add parentheses around the extensions string when the description and extensions strings are concatenated into one file filter item.</param>
			<param name="Description">Required String. The text representing the description of the file extension you want to add to the list of filters.</param>
			<param name="Position">Optional Object. A number that indicates the position of the new control in the filter list. The new filter will be inserted before the filter at this position. If this argument is omitted, the filter is added at the end of the list.</param>
		</member>
		<member name="M:Microsoft.Office.Core.FileDialogFilters.Clear">
			<summary>Removes all list items from a command bar combo box control (drop-down list box or combo box) and clears the text box (edit box or combo box).</summary>
		</member>
		<member name="M:Microsoft.Office.Core.FileDialogFilters.Delete(System.Object)">
			<summary>Removes a file dialog filter.</summary>
			<param name="filter">Optional Object. The filter to be removed.</param>
		</member>
		<member name="M:Microsoft.Office.Core.FileDialogFilters.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.FileDialogFilter"></see> object that is a member of the specified <see cref="T:Microsoft.Office.Core.FileDialogFilters"></see> collection.</summary>
			<param name="Index">Required Integer. The index number of the FileDialogFilter object to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.FileDialogSelectedItems">
			<summary>A collection of String values that correspond to the paths of the files or folders that a user has selected from a file dialog box displayed through the <see cref="T:Microsoft.Office.Core.FileDialog"></see> object.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.FileDialogSelectedItems.Item(System.Int32)">
			<summary>Returns a String that corresponds to the path of one of the files that the user selected from a file dialog box that was displayed using the <see cref="M:Microsoft.Office.Core.FileDialog.Show"></see> method of the <see cref="T:Microsoft.Office.Core.FileDialog"></see> object.</summary>
			<param name="Index">Required Integer. The index number of the string to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.FileSearch">
			<summary>Represents the functionality of the Open dialog box accessible by the File menu.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.FileSearch.Execute(Microsoft.Office.Core.MsoSortBy,Microsoft.Office.Core.MsoSortOrder,System.Boolean)">
			<summary>Begins the search for the specified file(s).</summary>
			<param name="SortOrder">Optional <see cref="T:Microsoft.Office.Core.MsoSortOrder"></see>. The order in which the returned file(s) are sorted.</param>
			<param name="SortBy">Optional <see cref="T:Microsoft.Office.Core.MsoSortBy"></see>. The method used to sort the returned file(s).</param>
			<param name="AlwaysAccurate">Optional Boolean. True to include files that have been added, modified, or deleted since the file index was last updated as part of the file search. The default value is True.</param>
		</member>
		<member name="P:Microsoft.Office.Core.FileSearch.LastModified">
			<summary>Returns or sets a constant that represents the amount of time since the specified file was last modified and saved.</summary>
			<returns>The default value is msoLastModifiedAnyTime.</returns>
		</member>
		<member name="T:Microsoft.Office.Core.FileTypes">
			<summary>A collection of values of the type <see cref="T:Microsoft.Office.Core.MsoFileType"></see> that determine which types of files are returned by the <see cref="M:Microsoft.Office.Core.FileSearch.Execute(Microsoft.Office.Core.MsoSortBy,Microsoft.Office.Core.MsoSortOrder,System.Boolean)"></see> method of the <see cref="T:Microsoft.Office.Core.FileSearch"></see> object.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.FileTypes.Add(Microsoft.Office.Core.MsoFileType)">
			<summary>Adds a new file type to a file search.</summary>
			<param name="FileType">Required <see cref="T:Microsoft.Office.Core.MsoFileType"></see>. Specifies the type of file for which to search.</param>
		</member>
		<member name="M:Microsoft.Office.Core.FileTypes.Remove(System.Int32)">
			<summary>Removes the specified object from the collection.</summary>
			<param name="Index">Required Integer. The index number of the property test to be removed.</param>
		</member>
		<member name="P:Microsoft.Office.Core.FileTypes.Item(System.Int32)">
			<summary>Returns a value that indicates which file type will be searched for by the <see cref="M:Microsoft.Office.Core.FileSearch.Execute(Microsoft.Office.Core.MsoSortBy,Microsoft.Office.Core.MsoSortOrder,System.Boolean)"></see> method of the <see cref="T:Microsoft.Office.Core.FileSearch"></see> object.</summary>
			<param name="Index">Optional Integer. The index number of the object to be returned.</param>
		</member>
		<member name="M:Microsoft.Office.Core.FillFormat.OneColorGradient(Microsoft.Office.Core.MsoGradientStyle,System.Int32,System.Single)">
			<param name="Variant">hiddenmemberparam</param>
			<param name="Degree">hiddenmemberparam</param>
			<param name="Style">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.FillFormat.Patterned(Microsoft.Office.Core.MsoPatternType)">
			<param name="Pattern">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.FillFormat.PresetGradient(Microsoft.Office.Core.MsoGradientStyle,System.Int32,Microsoft.Office.Core.MsoPresetGradientType)">
			<param name="Variant">hiddenmemberparam</param>
			<param name="PresetGradientType">hiddenmemberparam</param>
			<param name="Style">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.FillFormat.PresetTextured(Microsoft.Office.Core.MsoPresetTexture)">
			<param name="PresetTexture">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.FillFormat.TwoColorGradient(Microsoft.Office.Core.MsoGradientStyle,System.Int32)">
			<param name="Variant">hiddenmemberparam</param>
			<param name="Style">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.FillFormat.UserPicture(System.String)">
			<param name="PictureFile">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.FillFormat.UserTextured(System.String)">
			<param name="TextureFile">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.FoundFiles">
			<summary>Represents the list of files returned from a file search.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.FoundFiles.Item(System.Int32)">
			<summary>Returns a file name from the list of file names represented by the <see cref="T:Microsoft.Office.Core.FoundFiles"></see> object.</summary>
			<param name="Index">Required Integer. The index number of the Answer Wizard file name string or the file name to be returned.</param>
		</member>
		<member name="M:Microsoft.Office.Core.FreeformBuilder.AddNodes(Microsoft.Office.Core.MsoSegmentType,Microsoft.Office.Core.MsoEditingType,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
			<param name="EditingType">hiddenmemberparam</param>
			<param name="X2">hiddenmemberparam</param>
			<param name="X3">hiddenmemberparam</param>
			<param name="X1">hiddenmemberparam</param>
			<param name="Y3">hiddenmemberparam</param>
			<param name="Y2">hiddenmemberparam</param>
			<param name="Y1">hiddenmemberparam</param>
			<param name="SegmentType">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.GroupShapes.Item(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.GroupShapes.Range(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.HTMLProject">
			<summary>Represents a top-level project branch, as in the Project Explorer in the Microsoft Script Editor.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.HTMLProject.Open(Microsoft.Office.Core.MsoHTMLProjectOpen)">
			<summary>Opens the specified HTML project or HTML project item in the Microsoft Script Editor in one of the views specified by the optional <see cref="T:Microsoft.Office.Core.MsoHTMLProjectOpen"></see> constants.</summary>
			<param name="OpenKind">Optional <see cref="T:Microsoft.Office.Core.MsoHTMLProjectOpen"></see>. The view in which the specified project or project item is opened.</param>
		</member>
		<member name="M:Microsoft.Office.Core.HTMLProject.RefreshDocument(System.Boolean)">
			<summary>Refreshes the specified HTML project in the Microsoft Office host application.</summary>
			<param name="Refresh">Optional Boolean. True if all changes are to be saved; False if all changes are to be ignored.</param>
		</member>
		<member name="M:Microsoft.Office.Core.HTMLProject.RefreshProject(System.Boolean)">
			<summary>Refreshes the specified HTML project in the Microsoft Script Editor.</summary>
			<param name="Refresh">Optional Boolean. True if the document will be refreshed; False if the document will not be refreshed.</param>
		</member>
		<member name="T:Microsoft.Office.Core.HTMLProjectItem">
			<summary>Represents an individual project item that’s a project item branch in the Project Explorer in the Microsoft Script Editor.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.HTMLProjectItem.LoadFromFile(System.String)">
			<summary>Updates the text in the Microsoft Script Editor with text from the specified file (on disk).</summary>
			<param name="FileName">Required String. The fully qualified path of the text file that contains the text to be loaded.</param>
		</member>
		<member name="M:Microsoft.Office.Core.HTMLProjectItem.Open(Microsoft.Office.Core.MsoHTMLProjectOpen)">
			<summary>Opens the specified HTML project or HTML project item in the Microsoft Script Editor in one of the views specified by the optional <see cref="T:Microsoft.Office.Core.MsoHTMLProjectOpen"></see> constants.</summary>
			<param name="OpenKind">Optional <see cref="T:Microsoft.Office.Core.MsoHTMLProjectOpen"></see>. The view in which the specified project or project item is opened.</param>
		</member>
		<member name="M:Microsoft.Office.Core.HTMLProjectItem.SaveCopyAs(System.String)">
			<summary>Saves the specified HTML project item using a new file name.</summary>
			<param name="FileName">Required String. The fully qualified path of the file to which you want to save the HTML project item.</param>
		</member>
		<member name="T:Microsoft.Office.Core.HTMLProjectItems">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.HTMLProjectItem"></see> objects that represent the HTML project items contained in the <see cref="T:Microsoft.Office.Core.HTMLProject"></see> object.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.HTMLProjectItems.Item(System.Object@)">
			<summary>Returns the <see cref="T:Microsoft.Office.Core.HTMLProjectItem"></see> object that represents a particular project in the Microsoft Script Editor.</summary>
			<param name="Index">Required Object. The name or index number of the HTML project item to be returned.</param>
		</member>
		<member name="M:Microsoft.Office.Core.IAccessible.accDoDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IAccessible.accHitTest(System.Int32,System.Int32)">
			<param name="xLeft">hiddenmemberparam</param>
			<param name="yTop">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IAccessible.accLocation(System.Int32@,System.Int32@,System.Int32@,System.Int32@,System.Object)">
			<param name="pcxWidth">hiddenmemberparam</param>
			<param name="pcyHeight">hiddenmemberparam</param>
			<param name="pyTop">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
			<param name="pxLeft">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IAccessible.accNavigate(System.Int32,System.Object)">
			<param name="varStart">hiddenmemberparam</param>
			<param name="navDir">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IAccessible.accSelect(System.Int32,System.Object)">
			<param name="flagsSelect">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accChild(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accDefaultAction(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accDescription(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accHelp(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accHelpTopic(System.String@,System.Object)">
			<param name="pszHelpFile">hiddenmemberparam</param>
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accKeyboardShortcut(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accName(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accRole(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accState(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="P:Microsoft.Office.Core.IAccessible.accValue(System.Object)">
			<param name="varChild">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ICommandBarButtonEvents.Click(Microsoft.Office.Core.CommandBarButton,System.Boolean@)">
			<param name="Ctrl">hiddenmemberparam</param>
			<param name="CancelDefault">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ICommandBarComboBoxEvents.Change(Microsoft.Office.Core.CommandBarComboBox)">
			<param name="Ctrl">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.IFind">
			<summary>maconlytype</summary>
		</member>
		<member name="M:Microsoft.Office.Core.IFind.Delete(System.String)">
			<summary>maconlymember</summary>
			<param name="bstrQueryName">maconlyparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IFind.Execute">
			<summary>maconlymember</summary>
		</member>
		<member name="M:Microsoft.Office.Core.IFind.Load(System.String)">
			<summary>maconlymember</summary>
			<param name="bstrQueryName">maconlyparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IFind.Save(System.String)">
			<summary>maconlymember</summary>
			<param name="bstrQueryName">maconlyparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.IFoundFiles">
			<summary>maconlytype</summary>
		</member>
		<member name="P:Microsoft.Office.Core.IFoundFiles.Item(System.Int32)">
			<summary>maconlymember</summary>
			<param name="Index">maconlyparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.AsyncProcessHandshakeRequest(System.Int32)">
			<param name="bReviseCustInfo">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.CancelAsyncProcessRequest(System.Int32)">
			<param name="bIsLicenseRequest">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.DepositConfirmationId(System.String)">
			<param name="bstrVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.GetCreditCardCode(System.UInt32)">
			<param name="dwIndex">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.GetCreditCardName(System.UInt32)">
			<param name="dwIndex">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.GetCurrencyDescription(System.UInt32)">
			<param name="dwCurrencyIndex">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.GetPriceItemLabel(System.UInt32)">
			<param name="dwIndex">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.GetPriceItemValue(System.UInt32,System.UInt32)">
			<param name="dwCurrencyIndex">hiddenmemberparam</param>
			<param name="dwIndex">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.GetVATLabel(System.String)">
			<param name="bstrCountryCode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.Initialize(System.UInt32,System.UInt32,System.String)">
			<param name="dwBPC">hiddenmemberparam</param>
			<param name="bstrLicSource">hiddenmemberparam</param>
			<param name="dwMode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.IsCCRenewalCountry(System.String)">
			<param name="bstrCountryCode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SaveBillingInfo(System.Int32)">
			<param name="bSave">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetAddress1(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetAddress2(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingAddress1(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingAddress2(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingCity(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingCountryCode(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingFirstName(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingLastName(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingPhone(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingState(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetBillingZip(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetCity(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetCountryCode(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetCountryDesc(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetCreditCardExpiryMonth(System.UInt32)">
			<param name="dwCCMonth">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetCreditCardExpiryYear(System.UInt32)">
			<param name="dwCCYear">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetCreditCardNumber(System.String)">
			<param name="bstrCCNumber">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetCreditCardType(System.String)">
			<param name="bstrCCCode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetCurrencyOption(System.UInt32)">
			<param name="dwCurrencyOption">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetDisconnectOption(System.Int32)">
			<param name="bNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetEmail(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetFirstName(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetIsoLanguage(System.UInt32)">
			<param name="dwNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetLastName(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetMSOffer(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetMSUpdate(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetOrgName(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetOtherOffer(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetPhone(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetState(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetVATNumber(System.String)">
			<param name="bstrVATNumber">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.SetZip(System.String)">
			<param name="bstrNewVal">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.VerifyCheckDigits(System.String)">
			<param name="bstrCIDIID">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicAgent.WantUpgrade(System.Int32)">
			<param name="bWantUpgrade">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.DepositPidKey(System.String,System.Int32)">
			<param name="fMORW">hiddenmemberparam</param>
			<param name="bstrKey">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.DisableVORWReminder(System.Int32)">
			<param name="BPC">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.FormatDate(System.DateTime,System.String)">
			<param name="date">hiddenmemberparam</param>
			<param name="pFormat">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.MsoAlert(System.String,System.String,System.String)">
			<param name="bstrIcon">hiddenmemberparam</param>
			<param name="bstrButtons">hiddenmemberparam</param>
			<param name="bstrText">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.OpenInDefaultBrowser(System.String)">
			<param name="bstrUrl">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.PrintHtmlDocument(System.Object)">
			<param name="punkHtmlDoc">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.ResignDpc(System.String)">
			<param name="bstrProductCode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.SaveReceipt(System.String)">
			<param name="bstrReceipt">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.SetDialogSize(System.Int32,System.Int32)">
			<param name="dx">hiddenmemberparam</param>
			<param name="dy">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.ShowHelp(System.Object@)">
			<param name="pvarId">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.SortSelectOptions(System.Object)">
			<param name="pdispSelect">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.VerifyClock(System.Int32)">
			<param name="lMode">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ILicWizExternal.WriteLog(System.String)">
			<param name="bstrMessage">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IMsoDiagram.Convert(Microsoft.Office.Core.MsoDiagramType)">
			<param name="Type">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IMsoDispCagNotifySink.InsertClip(System.Object,System.Object)">
			<param name="pClipMoniker">hiddenmemberparam</param>
			<param name="pItemMoniker">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.IMsoEnvelopeVB">
			<summary>Provides access to functionality that lets you send documents as emails directly from Microsoft Office applications.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.IMsoEnvelopeVBEvents_EnvelopeHideEventHandler">
			<summary>A Delegate type used to add an event handler for the <see cref="E:Microsoft.Office.Core.IMsoEnvelopeVBEvents_Event.EnvelopeHide"></see> event. The EnvelopeHide event occurs when the user interface (UI) that corresponds to the <see cref="T:Microsoft.Office.Core.MsoEnvelope"></see> object is hidden.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.IMsoEnvelopeVBEvents_EnvelopeShowEventHandler">
			<summary>A Delegate type used to add an event handler for the <see cref="E:Microsoft.Office.Core.IMsoEnvelopeVBEvents_Event.EnvelopeShow"></see> event. The EnvelopeShow event occurs when the user interface (UI) that corresponds to the <see cref="T:Microsoft.Office.Core.MsoEnvelope"></see> object is displayed.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.IMsoEnvelopeVBEvents_Event">
			<summary>Events interface for Microsoft Office <see cref="T:Microsoft.Office.Core.MsoEnvelope"></see> object events.</summary>
		</member>
		<member name="E:Microsoft.Office.Core.IMsoEnvelopeVBEvents_Event.EnvelopeHide">
			<summary>Occurs when the user interface (UI) that corresponds to the <see cref="T:Microsoft.Office.Core.MsoEnvelope"></see> object is hidden.</summary>
		</member>
		<member name="E:Microsoft.Office.Core.IMsoEnvelopeVBEvents_Event.EnvelopeShow">
			<summary>Occurs when the user interface (UI) that corresponds to the <see cref="T:Microsoft.Office.Core.MsoEnvelope"></see> object is displayed.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.IMsoEServicesDialog.AddTrustedDomain(System.String)">
			<param name="Domain">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.IMsoEServicesDialog.Close(System.Boolean)">
			<param name="ApplyWebComponentChanges">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.LanguageSettings">
			<summary>Returns information about the language settings in a Microsoft Office application.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.LanguageSettings.LanguageID(Microsoft.Office.Core.MsoAppLanguageID)">
			<summary>Returns the locale identifier (LCID) for the install language, the user interface language, or the Help language.</summary>
			<param name="Id">Required <see cref="T:Microsoft.Office.Core.MsoAppLanguageID"></see>.</param>
		</member>
		<member name="P:Microsoft.Office.Core.LanguageSettings.LanguagePreferredForEditing(Microsoft.Office.Core.MsoLanguageID)">
			<summary>Determines if the value for the <see cref="T:Microsoft.Office.Core.MsoLanguageID"></see> constant has been identified in the Windows Registry as a preferred language for editing.</summary>
			<param name="lid">Required <see cref="T:Microsoft.Office.Core.MsoLanguageID"></see>.</param>
		</member>
		<member name="T:Microsoft.Office.Core.MailFormat">
			<summary>Specifies the format for an e-mail message. These formats correspond to the formats supported by Microsoft Outlook for e-mail messages.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MailFormat.mfHTML">
			<summary>Hypertext Markup Language (HTML) formatting.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MailFormat.mfPlainText">
			<summary>Plain text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MailFormat.mfRTF">
			<summary>Rich Text Format (RTF) formatting.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAlertButtonType">
			<summary>Specifies the buttons to be displayed when issuing an alert to the user with the DoAlert method of the Assistant object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertButtonType.msoAlertButtonAbortRetryIgnore">
			<summary>Abort, Retry, and Ignore buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertButtonType.msoAlertButtonOK">
			<summary>OK button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertButtonType.msoAlertButtonOKCancel">
			<summary>OK and Cancel buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertButtonType.msoAlertButtonRetryCancel">
			<summary>Retry and Cancel buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertButtonType.msoAlertButtonYesAllNoCancel">
			<summary>Yes, Yes to All, No, and Cancel buttons. Can only be used if the varSysAlert argument of the DoAlert method is set to False.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertButtonType.msoAlertButtonYesNo">
			<summary>Yes and No buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertButtonType.msoAlertButtonYesNoCancel">
			<summary>Yes, No, and Cancel buttons.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAlertCancelType">
			<summary>Specifies behavior when the user cancels an alert. Only <see cref="F:Microsoft.Office.Core.MsoAlertCancelType.msoAlertCancelDefault"></see> is currently supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertCancelType.msoAlertCancelDefault">
			<summary>Default behavior for canceling an alert.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertCancelType.msoAlertCancelFifth">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertCancelType.msoAlertCancelFirst">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertCancelType.msoAlertCancelFourth">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertCancelType.msoAlertCancelSecond">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertCancelType.msoAlertCancelThird">
			<summary>Not supported.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAlertDefaultType">
			<summary>Specifies which button is set as the default when calling the DoAlert method of the Assistant object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertDefaultType.msoAlertDefaultFifth">
			<summary>Default to fifth button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertDefaultType.msoAlertDefaultFirst">
			<summary>Default to first button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertDefaultType.msoAlertDefaultFourth">
			<summary>Default to fourth button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertDefaultType.msoAlertDefaultSecond">
			<summary>Default to second button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertDefaultType.msoAlertDefaultThird">
			<summary>Default to third button.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAlertIconType">
			<summary>Specifies which icon, if any, to display with an alert. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertIconType.msoAlertIconCritical">
			<summary>Displays the Critical icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertIconType.msoAlertIconInfo">
			<summary>Displays the Info icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertIconType.msoAlertIconNoIcon">
			<summary>Displays no icon with the alert message.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertIconType.msoAlertIconQuery">
			<summary>Displays the Query icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlertIconType.msoAlertIconWarning">
			<summary>Displays the Warning icon.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAlignCmd">
			<summary>Defines how to align specified objects relative to one another.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlignCmd.msoAlignBottoms">
			<summary>Align bottoms of specified objects.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlignCmd.msoAlignCenters">
			<summary>Align centers of specified objects.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlignCmd.msoAlignLefts">
			<summary>Align left sides of specified objects.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlignCmd.msoAlignMiddles">
			<summary>Align middles of specified objects.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlignCmd.msoAlignRights">
			<summary>Align right sides of specified objects.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAlignCmd.msoAlignTops">
			<summary>Align tops of specified objects.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAnimationType">
			<summary>Specifies the animation action for the Office Assistant.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationAppear">
			<summary>"Appear" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationBeginSpeaking">
			<summary>"Begin speaking" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationCharacterSuccessMajor">
			<summary>"Major success" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationCheckingSomething">
			<summary>"Checking something" animation action. Repeats until Assistant is dismissed or Animation property is reset with another animation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationDisappear">
			<summary>"Disappear" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationEmptyTrash">
			<summary>"Empty trash" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGestureDown">
			<summary>"Gesture down" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGestureLeft">
			<summary>"Gesture left" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGestureRight">
			<summary>"Gesture right" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGestureUp">
			<summary>"Gesture up" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGetArtsy">
			<summary>"Get artsy" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGetAttentionMajor">
			<summary>Major "Get attention" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGetAttentionMinor">
			<summary>Minor "Get attention" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGetTechy">
			<summary>"Get techy" animation action. Repeats until Assistant is dismissed or Animation property is reset with another animation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGetWizardy">
			<summary>"Get wizardy" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGoodbye">
			<summary>"Goodbye" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationGreeting">
			<summary>"Greeting" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationIdle">
			<summary>"Idle" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationListensToComputer">
			<summary>"Listens to computer" animation action. Repeats until Assistant is dismissed or Animation property is reset with another animation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationLookDown">
			<summary>"Look down" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationLookDownLeft">
			<summary>"Look down and left" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationLookDownRight">
			<summary>"Look down and right" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationLookLeft">
			<summary>"Look left" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationLookRight">
			<summary>"Look right" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationLookUp">
			<summary>"Look up" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationLookUpLeft">
			<summary>"Look up and left" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationLookUpRight">
			<summary>"Look up and right" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationPrinting">
			<summary>"Printing" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationRestPose">
			<summary>"Rest pose" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationSaving">
			<summary>"Saying" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationSearching">
			<summary>"Searching" animation action. Repeats until Assistant is dismissed or Animation property is reset with another animation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationSendingMail">
			<summary>"Sending mail" animation action.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationThinking">
			<summary>"Thinking" animation action. Repeats until Assistant is dismissed or Animation property is reset with another animation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationWorkingAtSomething">
			<summary>"Working at something" animation action. Repeats until Assistant is dismissed or Animation property is reset with another animation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAnimationType.msoAnimationWritingNotingSomething">
			<summary>"Noting something" animation action. Repeats until Assistant is dismissed or Animation property is reset with another animation.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAppLanguageID">
			<summary>Specifies a language setting in a Microsoft Office application. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAppLanguageID.msoLanguageIDExeMode">
			<summary>Execution mode language.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAppLanguageID.msoLanguageIDHelp">
			<summary>Help language.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAppLanguageID.msoLanguageIDInstall">
			<summary>Install language.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAppLanguageID.msoLanguageIDUI">
			<summary>User interface language.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAppLanguageID.msoLanguageIDUIPrevious">
			<summary>User interface language used prior to the current user interface language.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoArrowheadLength">
			<summary>Specifies the length of the arrowhead at the end of a line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadLength.msoArrowheadLengthMedium">
			<summary>Medium.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadLength.msoArrowheadLengthMixed">
			<summary>Return value only; indicates a combination of the other states in the specified shape range.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadLength.msoArrowheadLong">
			<summary>Long.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadLength.msoArrowheadShort">
			<summary>Short.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoArrowheadStyle">
			<summary>Specifies the style of the arrowhead at the end of a line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadStyle.msoArrowheadDiamond">
			<summary>Diamond-shaped.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadStyle.msoArrowheadNone">
			<summary>No arrowhead.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadStyle.msoArrowheadOpen">
			<summary>Open.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadStyle.msoArrowheadOval">
			<summary>Oval-shaped.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadStyle.msoArrowheadStealth">
			<summary>Stealth-shaped.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadStyle.msoArrowheadStyleMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadStyle.msoArrowheadTriangle">
			<summary>Triangular.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoArrowheadWidth">
			<summary>Specifies the width of the arrowhead at the end of a line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadWidth.msoArrowheadNarrow">
			<summary>Narrow.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadWidth.msoArrowheadWide">
			<summary>Wide.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadWidth.msoArrowheadWidthMedium">
			<summary>Medium.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoArrowheadWidth.msoArrowheadWidthMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAutomationSecurity">
			<summary>Specifies the security mode an application uses when programmatically opening files.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutomationSecurity.msoAutomationSecurityByUI">
			<summary>Uses the security setting specified in the Security dialog box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutomationSecurity.msoAutomationSecurityForceDisable">
			<summary>Disables all macros in all files opened programmatically, without showing any security alerts.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutomationSecurity.msoAutomationSecurityLow">
			<summary>Enables all macros. This is the default value when the application is started.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoAutoShapeType">
			<summary>Specifies the shape type for an AutoShape object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShape16pointStar">
			<summary>16-point star.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShape24pointStar">
			<summary>24-point star.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShape32pointStar">
			<summary>32-point star.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShape4pointStar">
			<summary>4-point star.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShape5pointStar">
			<summary>5-point star.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShape8pointStar">
			<summary>8-point star.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonBackorPrevious">
			<summary>Back or Previous button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonBeginning">
			<summary>Beginning button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonCustom">
			<summary>Button with no default picture or text. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonDocument">
			<summary>Document button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonEnd">
			<summary>End button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonForwardorNext">
			<summary>Forward or Next button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonHelp">
			<summary>Help button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonHome">
			<summary>Home button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonInformation">
			<summary>Information button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonMovie">
			<summary>Movie button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonReturn">
			<summary>Return button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeActionButtonSound">
			<summary>Sound button. Supports mouse-click and mouse-over actions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeArc">
			<summary>Arc.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeBalloon">
			<summary>Balloon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeBentArrow">
			<summary>Block arrow that follows a curved 90-degree angle. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeBentUpArrow">
			<summary>Block arrow that follows a sharp 90-degree angle. Points up by default.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeBevel">
			<summary>Bevel.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeBlockArc">
			<summary>Block arc.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCan">
			<summary>Can.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeChevron">
			<summary>Chevron.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCircularArrow">
			<summary>Block arrow that follows a curved 180-degree angle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCloudCallout">
			<summary>Cloud callout.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCross">
			<summary>Cross.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCube">
			<summary>Cube.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCurvedDownArrow">
			<summary>Block arrow that curves down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCurvedDownRibbon">
			<summary>Ribbon banner that curves down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCurvedLeftArrow">
			<summary>Block arrow that curves left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCurvedRightArrow">
			<summary>Block arrow that curves right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCurvedUpArrow">
			<summary>Block arrow that curves up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeCurvedUpRibbon">
			<summary>Ribbon banner that curves up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeDiamond">
			<summary>Diamond.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeDonut">
			<summary>Donut.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeDoubleBrace">
			<summary>Double brace.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeDoubleBracket">
			<summary>Double bracket.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeDoubleWave">
			<summary>Double wave.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeDownArrow">
			<summary>Block arrow that points down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeDownArrowCallout">
			<summary>Callout with arrow that points down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeDownRibbon">
			<summary>Ribbon banner with center area below ribbon ends.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeExplosion1">
			<summary>Explosion.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeExplosion2">
			<summary>Explosion.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartAlternateProcess">
			<summary>Alternate process flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartCard">
			<summary>Card flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartCollate">
			<summary>Collate flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartConnector">
			<summary>Connector flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartData">
			<summary>Data flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartDecision">
			<summary>Decision flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartDelay">
			<summary>Delay flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartDirectAccessStorage">
			<summary>Direct access storage flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartDisplay">
			<summary>Display flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartDocument">
			<summary>Document flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartExtract">
			<summary>Extract flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartInternalStorage">
			<summary>Internal storage flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartMagneticDisk">
			<summary>Magnetic disk flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartManualInput">
			<summary>Manual input flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartManualOperation">
			<summary>Manual operation flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartMerge">
			<summary>Merge flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartMultidocument">
			<summary>Multi-document flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartOffpageConnector">
			<summary>Off-page connector flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartOr">
			<summary>"Or" flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartPredefinedProcess">
			<summary>Predefined process flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartPreparation">
			<summary>Preparation flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartProcess">
			<summary>Process flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartPunchedTape">
			<summary>Punched tape flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartSequentialAccessStorage">
			<summary>Sequential access storage flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartSort">
			<summary>Sort flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartStoredData">
			<summary>Stored data flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartSummingJunction">
			<summary>Summing junction flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFlowchartTerminator">
			<summary>Terminator flowchart symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeFoldedCorner">
			<summary>Folded corner.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeHeart">
			<summary>Heart.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeHexagon">
			<summary>Hexagon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeHorizontalScroll">
			<summary>Horizontal scroll.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeIsoscelesTriangle">
			<summary>Isosceles triangle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLeftArrow">
			<summary>Block arrow that points left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLeftArrowCallout">
			<summary>Callout with arrow that points left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLeftBrace">
			<summary>Left brace.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLeftBracket">
			<summary>Left bracket.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLeftRightArrow">
			<summary>Block arrow with arrowheads that point both left and right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLeftRightArrowCallout">
			<summary>Callout with arrowheads that point both left and right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLeftRightUpArrow">
			<summary>Block arrow with arrowheads that point left, right, and up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLeftUpArrow">
			<summary>Block arrow with arrowheads that point left and up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLightningBolt">
			<summary>Lightning bolt.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout1">
			<summary>Callout with border and horizontal callout line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout1AccentBar">
			<summary>Callout with horizontal accent bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout1BorderandAccentBar">
			<summary>Callout with border and horizontal accent bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout1NoBorder">
			<summary>Callout with horizontal line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout2">
			<summary>Callout with diagonal straight line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout2AccentBar">
			<summary>Callout with diagonal callout line and accent bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout2BorderandAccentBar">
			<summary>Callout with border, diagonal straight line, and accent bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout2NoBorder">
			<summary>Callout with no border and diagonal callout line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout3">
			<summary>Callout with angled line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout3AccentBar">
			<summary>Callout with angled callout line and accent bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout3BorderandAccentBar">
			<summary>Callout with border, angled callout line, and accent bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout3NoBorder">
			<summary>Callout with no border and angled callout line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout4">
			<summary>Callout with callout line segments forming a U-shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout4AccentBar">
			<summary>Callout with accent bar and callout line segments forming a U-shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout4BorderandAccentBar">
			<summary>Callout with border, accent bar, and callout line segments forming a U-shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeLineCallout4NoBorder">
			<summary>Callout with no border and callout line segments forming a U-shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeMoon">
			<summary>Moon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeNoSymbol">
			<summary>"No" symbol.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeNotchedRightArrow">
			<summary>Notched block arrow that points right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeNotPrimitive">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeOctagon">
			<summary>Octagon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeOval">
			<summary>Oval.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeOvalCallout">
			<summary>Oval-shaped callout.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeParallelogram">
			<summary>Parallelogram.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapePentagon">
			<summary>Pentagon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapePlaque">
			<summary>Plaque.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeQuadArrow">
			<summary>Block arrows that point up, down, left, and right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeQuadArrowCallout">
			<summary>Callout with arrows that point up, down, left, and right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRectangle">
			<summary>Rectangle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRectangularCallout">
			<summary>Rectangular callout.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRegularPentagon">
			<summary>Pentagon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRightArrow">
			<summary>Block arrow that points right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRightArrowCallout">
			<summary>Callout with arrow that points right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRightBrace">
			<summary>Right brace.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRightBracket">
			<summary>Right bracket.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRightTriangle">
			<summary>Right triangle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRoundedRectangle">
			<summary>Rounded rectangle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeRoundedRectangularCallout">
			<summary>Rounded rectangle-shaped callout.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeSmileyFace">
			<summary>Smiley face.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeStripedRightArrow">
			<summary>Block arrow that points right with stripes at the tail.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeSun">
			<summary>Sun.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeTrapezoid">
			<summary>Trapezoid.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeUpArrow">
			<summary>Block arrow that points up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeUpArrowCallout">
			<summary>Callout with arrow that points up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeUpDownArrow">
			<summary>Block arrow that points up and down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeUpDownArrowCallout">
			<summary>Callout with arrows that point up and down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeUpRibbon">
			<summary>Ribbon banner with center area above ribbon ends.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeUTurnArrow">
			<summary>Block arrow forming a U shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeVerticalScroll">
			<summary>Vertical scroll.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoAutoShapeType.msoShapeWave">
			<summary>Wave.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoBalloonButtonType">
			<summary>Indicates which button the user clicked in a balloon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonAbort">
			<summary>Abort button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonBack">
			<summary>Back button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonCancel">
			<summary>Cancel button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonClose">
			<summary>Close button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonIgnore">
			<summary>Ignore button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonNext">
			<summary>Next button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonNo">
			<summary>No button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonNull">
			<summary>Null button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonOK">
			<summary>OK button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonOptions">
			<summary>Options button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonRetry">
			<summary>Retry button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonSearch">
			<summary>Search button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonSnooze">
			<summary>Snooze button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonTips">
			<summary>Tips button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonYes">
			<summary>Yes button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonButtonType.msoBalloonButtonYesToAll">
			<summary>Yes to all button.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoBalloonErrorType">
			<summary>Specifies what error occurred in a balloon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorBadCharacter">
			<summary>Balloon contains an ASCII control character other than CR or LF and less than 32.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorBadPictureRef">
			<summary>Balloon contains a graphic that couldn't be displayed because the file doesn't exist or because the graphic isn't a valid .BMP or .WMF file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorBadReference">
			<summary>Balloon contains an unrecognized or unsupported reference.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorButtonlessModal">
			<summary>The balloon you attempted to display is modal, but it contains no buttons. The balloon won't be shown because it can't be dismissed.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorButtonModeless">
			<summary>The balloon you attempted to display is modeless, contains buttons, and has no procedure assigned to the Callback property. The balloon won't be shown because a callback procedure is required for modeless balloons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorCharNotTopmostForModal">
			<summary>Modal balloon was requested by an application that isn't the active application. Microsoft Office renders balloons for the active (topmost) application only.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorCOMFailure">
			<summary>Balloon could not be displayed because of a COM failure.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorNone">
			<summary>No error was encountered.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorOther">
			<summary>Balloon won't appear because some other error occurred, such as another modal balloon is already active.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorOutOfMemory">
			<summary>Balloon won't appear because there is insufficient memory.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorTooBig">
			<summary>Balloon is too big to appear on the screen.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonErrorType.msoBalloonErrorTooManyControls">
			<summary>Balloon contains more than twenty controls (check boxes or labels).</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoBalloonType">
			<summary>Specifies the type of label used in a balloon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonType.msoBalloonTypeBullets">
			<summary>Bulleted labels.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonType.msoBalloonTypeButtons">
			<summary>Labeled buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBalloonType.msoBalloonTypeNumbers">
			<summary>Numbered labels.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoBarPosition">
			<summary>Specifies the position or behavior of a command bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarPosition.msoBarBottom">
			<summary>Command bar is docked at the bottom of the application window.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarPosition.msoBarFloating">
			<summary>Command bar floats on top of the application window.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarPosition.msoBarLeft">
			<summary>Command bar is docked on the left side of the application window.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarPosition.msoBarMenuBar">
			<summary>Command bar will be a menu bar (Macintosh only).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarPosition.msoBarPopup">
			<summary>Command bar will be a shortcut menu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarPosition.msoBarRight">
			<summary>Command bar is docked on the right side of the application window.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarPosition.msoBarTop">
			<summary>Command bar is docked at the top of the application window.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoBarProtection">
			<summary>Specifies how a command bar is protected from user customization.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarProtection.msoBarNoChangeDock">
			<summary>Docking setting cannot be changed.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarProtection.msoBarNoChangeVisible">
			<summary>Command bar cannot be hidden.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarProtection.msoBarNoCustomize">
			<summary>Command bar cannot be customized.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarProtection.msoBarNoHorizontalDock">
			<summary>Command bar cannot be docked to the top or bottom.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarProtection.msoBarNoMove">
			<summary>Command bar cannot be moved.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarProtection.msoBarNoProtection">
			<summary>All aspects of command bar can be customized by user.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarProtection.msoBarNoResize">
			<summary>Command bar cannot be resized.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarProtection.msoBarNoVerticalDock">
			<summary>Command bar cannot be docked to the left or right.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoBarRow">
			<summary>Specifies whether a command bar is in the first row or last row relative to other command bars in the same docking area.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarRow.msoBarRowFirst">
			<summary>First row of docking area.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarRow.msoBarRowLast">
			<summary>Last row of docking area.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoBarType">
			<summary>Specifies the type of the command bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarType.msoBarTypeMenuBar">
			<summary>Menu bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarType.msoBarTypeNormal">
			<summary>Default command bar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBarType.msoBarTypePopup">
			<summary>Shortcut menu.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoBlackWhiteMode">
			<summary>Specifies how a shape appears when viewed in black-and-white mode.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteAutomatic">
			<summary>Default behavior.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteBlack">
			<summary>Black.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteBlackTextAndLine">
			<summary>White with grayscale fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteDontShow">
			<summary>Not shown.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteGrayOutline">
			<summary>Gray with white fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteGrayScale">
			<summary>Grayscale.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteHighContrast">
			<summary>Black with white fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteInverseGrayScale">
			<summary>Inverse grayscale.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteLightGrayScale">
			<summary>Light grayscale.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoBlackWhiteMode.msoBlackWhiteWhite">
			<summary>White.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoButtonSetType">
			<summary>Specifies the type of button to be displayed at the bottom of an Office Assistant balloon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetAbortRetryIgnore">
			<summary>Abort, Retry, and Ignore buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetBackClose">
			<summary>Back and Close buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetBackNextClose">
			<summary>Back, Next, and Close buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetBackNextSnooze">
			<summary>Back, Next, and Snooze buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetCancel">
			<summary>Cancel button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetNextClose">
			<summary>Next and Close buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetNone">
			<summary>No buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetOK">
			<summary>OK button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetOkCancel">
			<summary>OK and Cancel buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetRetryCancel">
			<summary>Retry and Cancel buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetSearchClose">
			<summary>Search and Close buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetTipsOptionsClose">
			<summary>Tips, Options, and Close buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetYesAllNoCancel">
			<summary>Yes to All, No, and Cancel buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetYesNo">
			<summary>Yes and No buttons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonSetType.msoButtonSetYesNoCancel">
			<summary>Yes, No, and Cancel buttons.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoButtonState">
			<summary>Specifies the appearance of a command bar button control.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonState.msoButtonDown">
			<summary>Button is pressed down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonState.msoButtonMixed">
			<summary>Button is pressed down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonState.msoButtonUp">
			<summary>Button is not pressed down.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoButtonStyle">
			<summary>Specifies the style of a command bar button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyle.msoButtonAutomatic">
			<summary>Default behavior.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyle.msoButtonCaption">
			<summary>Text only.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyle.msoButtonIcon">
			<summary>Image only.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyle.msoButtonIconAndCaption">
			<summary>Image and text, with text to the right of image.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyle.msoButtonIconAndCaptionBelow">
			<summary>Image with text below.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyle.msoButtonIconAndWrapCaption">
			<summary>Image with text wrapped and to the right of the image.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyle.msoButtonIconAndWrapCaptionBelow">
			<summary>Image with text wrapped below image.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyle.msoButtonWrapCaption">
			<summary>Text only, centered and wrapped.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyleHidden.msoButtonTextBelow">
			<summary>hiddenconstant</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoButtonStyleHidden.msoButtonWrapText">
			<summary>hiddenconstant</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoCalloutAngleType">
			<summary>Specifies the size of the angle between the callout line and the side of the callout text box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutAngleType.msoCalloutAngle30">
			<summary>30˚ angle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutAngleType.msoCalloutAngle45">
			<summary>45˚ angle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutAngleType.msoCalloutAngle60">
			<summary>60˚ angle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutAngleType.msoCalloutAngle90">
			<summary>90˚ angle.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutAngleType.msoCalloutAngleAutomatic">
			<summary>Default angle. Angle can be changed as you drag the object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutAngleType.msoCalloutAngleMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoCalloutDropType">
			<summary>Specifies starting position of the callout line relative to the text bounding box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutDropType.msoCalloutDropBottom">
			<summary>Bottom.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutDropType.msoCalloutDropCenter">
			<summary>Center.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutDropType.msoCalloutDropCustom">
			<summary>Custom. If this value is used as the value for the PresetDrop property, the Drop and AutoAttach properties of the CalloutFormat object are used to determine where the callout line attaches to the text box. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutDropType.msoCalloutDropMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutDropType.msoCalloutDropTop">
			<summary>Top.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoCalloutType">
			<summary>Specifies the type of callout line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutType.msoCalloutFour">
			<summary>Callout line made up of two line segments. Callout line is attached on right side of text bounding box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutType.msoCalloutMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutType.msoCalloutOne">
			<summary>Single, horizontal callout line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutType.msoCalloutThree">
			<summary>Callout line made up of two line segments. Callout line is attached on left side of text bounding box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCalloutType.msoCalloutTwo">
			<summary>Single, angled callout line.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoCharacterSet">
			<summary>Specifies the character set to be used when rendering text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetArabic">
			<summary>Arabic character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetCyrillic">
			<summary>Cyrillic character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetEnglishWesternEuropeanOtherLatinScript">
			<summary>English, Western European, and other Latin script character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetGreek">
			<summary>Greek character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetHebrew">
			<summary>Hebrew character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetJapanese">
			<summary>Japanese character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetKorean">
			<summary>Korean character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetMultilingualUnicode">
			<summary>Multilingual Unicode character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetSimplifiedChinese">
			<summary>Simplified Chinese character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetThai">
			<summary>Thai character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetTraditionalChinese">
			<summary>Traditional Chinese character set.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCharacterSet.msoCharacterSetVietnamese">
			<summary>Vietnamese character set.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoColorType">
			<summary>Specifies the color type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoColorType.msoColorTypeCMS">
			<summary>Color Management System color type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoColorType.msoColorTypeCMYK">
			<summary>Color is determined by values of cyan, magenta, yellow, and black.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoColorType.msoColorTypeInk">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoColorType.msoColorTypeMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoColorType.msoColorTypeRGB">
			<summary>Color is determined by values of red, green, and blue.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoColorType.msoColorTypeScheme">
			<summary>Color is defined by an application-specific scheme.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoComboStyle">
			<summary>Specifies whether the command bar combo box includes a label or not.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoComboStyle.msoComboLabel">
			<summary>Combo box includes a label, specified by the Caption property of the combo box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoComboStyle.msoComboNormal">
			<summary>Combo box does not include a label.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoCommandBarButtonHyperlinkType">
			<summary>Specifies whether the command bar button is a hyperlink. If the command bar button is a hyperlink, further specifies whether the hyperlink should launch another application such as the browser or insert a picture at the active selection point.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCommandBarButtonHyperlinkType.msoCommandBarButtonHyperlinkInsertPicture">
			<summary>Clicking the command bar button inserts a picture at the active selection point.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCommandBarButtonHyperlinkType.msoCommandBarButtonHyperlinkNone">
			<summary>The command bar button is not a hyperlink.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCommandBarButtonHyperlinkType.msoCommandBarButtonHyperlinkOpen">
			<summary>Clicking the command bar button opens the link specified in the command bar button's TooltipText property.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoCondition">
			<summary>Defines the condition for comparison between a file and a specified property in a file search.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionAnyNumberBetween">
			<summary>Any number between values specified with the Value and SecondValue properties of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionAnytime">
			<summary>Date specified in the Name property of the PropertyTest object can be any time.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionAnytimeBetween">
			<summary>Date specified in the Name property of the PropertyTest object is between the dates specified with the Value and SecondValue properties of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionAtLeast">
			<summary>Value of the file property specified in Name property of the PropertyTest object is at least the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionAtMost">
			<summary>Value of the file property specified in Name property of the PropertyTest object is at most the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionBeginsWith">
			<summary>Value of the file property specified in Name property of the PropertyTest object begins with the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionDoesNotEqual">
			<summary>Value of the file property specified in Name property of the PropertyTest object does not equal the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEndsWith">
			<summary>Value of the file property specified in Name property of the PropertyTest object ends with the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEquals">
			<summary>Value of the file property specified in Name property of the PropertyTest object equals the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEqualsCompleted">
			<summary>Status equals "Completed". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEqualsDeferred">
			<summary>Status equals "Deferred". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEqualsHigh">
			<summary>Priority equals "High". Value of the Name property must be Priority.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEqualsInProgress">
			<summary>Status equals "In Progress". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEqualsLow">
			<summary>Priority equals "Low". Value of the Name property must be Priority.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEqualsNormal">
			<summary>Priority equals "Normal". Value of the Name property must be Priority.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEqualsNotStarted">
			<summary>Status equals "Not Started". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionEqualsWaitingForSomeoneElse">
			<summary>Status equals "Waiting for Someone Else". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeAllFiles">
			<summary>File can be any type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeBinders">
			<summary>Binder file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeCalendarItem">
			<summary>Calendar item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeContactItem">
			<summary>Contact item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeDatabases">
			<summary>Database.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeDataConnectionFiles">
			<summary>Data connection file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeDesignerFiles">
			<summary>Designer file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeDocumentImagingFiles">
			<summary>Document imaging file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeExcelWorkbooks">
			<summary>Excel workbook.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeJournalItem">
			<summary>Journal item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeMailItem">
			<summary>Mail item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeNoteItem">
			<summary>Note item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeOfficeFiles">
			<summary>File can be any Office file type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeOutlookItems">
			<summary>Outlook item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypePhotoDrawFiles">
			<summary>PhotoDraw file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypePowerPointPresentations">
			<summary>PowerPoint presentation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeProjectFiles">
			<summary>Project file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypePublisherFiles">
			<summary>Publisher file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeTaskItem">
			<summary>Task item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeTemplates">
			<summary>Template.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeVisioFiles">
			<summary>Visio file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeWebPages">
			<summary>Web page.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFileTypeWordDocuments">
			<summary>Word document.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionFreeText">
			<summary>Value of the file property specified in the Name property of the PropertyTest object matches the value specified in the Value property of the PropertyTest object when a FreeText search is used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionIncludes">
			<summary>Value of the file property specified in Name property of the PropertyTest object includes the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionIncludesFormsOf">
			<summary>Value of the file property specified in Name property of the PropertyTest object includes forms of the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionIncludesNearEachOther">
			<summary>Value of the file property specified in Name property of the PropertyTest object and the value specified in the Value property of the PropertyTest object are near each other.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionIncludesPhrase">
			<summary>Value of the file property specified in Name property of the PropertyTest object includes the phrase specified in the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionInTheLast">
			<summary>Date specified in the Name property of the PropertyTest object is within the last time interval specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionInTheNext">
			<summary>Date specified in the Name property of the PropertyTest object is within the next time interval specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionIsExactly">
			<summary>Value of the file property specified in Name property of the PropertyTest object is exactly the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionIsNo">
			<summary>Value of the file property specified in Name property of the PropertyTest object is "False".</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionIsNot">
			<summary>Value of the file property specified in Name property of the PropertyTest object is not the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionIsYes">
			<summary>Value of the file property specified in Name property of the PropertyTest object is "True".</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionLastMonth">
			<summary>Date specified in the Name property of the PropertyTest object is within the last month.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionLastWeek">
			<summary>Date specified in the Name property of the PropertyTest object is within the last week.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionLessThan">
			<summary>Value of the file property specified in Name property of the PropertyTest object is less than the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionMoreThan">
			<summary>Value of the file property specified in Name property of the PropertyTest object is more than the value specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNextMonth">
			<summary>Date specified in the Name property of the PropertyTest object is next month.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNextWeek">
			<summary>Date specified in the Name property of the PropertyTest object is next week.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNotEqualToCompleted">
			<summary>Status does not equal "Completed". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNotEqualToDeferred">
			<summary>Status does not equal "Deferred". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNotEqualToHigh">
			<summary>Value of file property specified in the Name property of the PropertyTest object does not equal "High". Value of the Name property must be Priority or Importance.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNotEqualToInProgress">
			<summary>Status does not equal "In Progress". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNotEqualToLow">
			<summary>Value of file property specified in the Name property of the PropertyTest object does not equal "Low". Value of the Name property must be Priority or Importance.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNotEqualToNormal">
			<summary>Value of file property specified in the Name property of the PropertyTest object does not equal "Normal". Value of the Name property must be Priority or Importance.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNotEqualToNotStarted">
			<summary>Status does not equal "Not Started". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionNotEqualToWaitingForSomeoneElse">
			<summary>Status does not equal "Waiting for Someone Else". Value of the Name property must be Status.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionOn">
			<summary>Date specified in the Name property of the PropertyTest object is the same as the date specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionOnOrAfter">
			<summary>Date specified in the Name property of the PropertyTest object is on or after the date specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionOnOrBefore">
			<summary>Date specified in the Name property of the PropertyTest object is on or before the date specified in the Value property of the PropertyTest object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionThisMonth">
			<summary>Date specified in the Name property of the PropertyTest object is this month.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionThisWeek">
			<summary>Date specified in the Name property of the PropertyTest object is this week.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionToday">
			<summary>Date specified in the Name property of the PropertyTest object is today.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionTomorrow">
			<summary>Date specified in the Name property of the PropertyTest object is tomorrow.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoCondition.msoConditionYesterday">
			<summary>Date specified in the Name property of the PropertyTest object is yesterday.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoConnector">
			<summary>Specifies the connector between two similar property test values.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoConnector.msoConnectorAnd">
			<summary>Combine property test values to form one property test.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoConnector.msoConnectorOr">
			<summary>Treat property test values as separate criteria.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoConnectorType">
			<summary>Specifies a type of connector.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoConnectorType.msoConnectorCurve">
			<summary>Curved connector.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoConnectorType.msoConnectorElbow">
			<summary>Elbow connector.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoConnectorType.msoConnectorStraight">
			<summary>Straight line connector.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoConnectorType.msoConnectorTypeMixed">
			<summary>Return value only; indicates a combination of the other states.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoControlOLEUsage">
			<summary>Specifies the OLE client and OLE server roles in which a command bar control is used when two Microsoft Office applications are merged.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlOLEUsage.msoControlOLEUsageBoth">
			<summary>Control runs on both client and server.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlOLEUsage.msoControlOLEUsageClient">
			<summary>Client-only control.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlOLEUsage.msoControlOLEUsageNeither">
			<summary>Control runs on neither client nor server.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlOLEUsage.msoControlOLEUsageServer">
			<summary>Server-only control.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoControlType">
			<summary>Specifies the type of the command bar control.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlActiveX">
			<summary>ActiveX control.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlAutoCompleteCombo">
			<summary>Combo box in which the first matching choice is automatically filled in as the user types. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlButton">
			<summary>Command button.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlButtonDropdown">
			<summary>Drop-down button. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlButtonPopup">
			<summary>Pop-up button. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlComboBox">
			<summary>Combo box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlCustom">
			<summary>Custom control. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlDropdown">
			<summary>Drop-down list.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlEdit">
			<summary>Text box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlExpandingGrid">
			<summary>Expanding grid. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlGauge">
			<summary>Gauge control. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlGenericDropdown">
			<summary>Generic drop-down list. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlGraphicCombo">
			<summary>Graphic combo box. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlGraphicDropdown">
			<summary>Graphic drop-down list. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlGraphicPopup">
			<summary>Graphic pop-up menu. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlGrid">
			<summary>Grid. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlLabel">
			<summary>Label. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlLabelEx">
			<summary>Extended label. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlOCXDropdown">
			<summary>OCX drop-down list. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlPane">
			<summary>Pane. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlPopup">
			<summary>Pop-up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlSpinner">
			<summary>Spinner. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlSplitButtonMRUPopup">
			<summary>Most Recently Used (MRU) pop-up. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlSplitButtonPopup">
			<summary>Split button pop-up. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlSplitDropdown">
			<summary>Split drop-down list. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlSplitExpandingGrid">
			<summary>Split expanding grid. Cannot be created through the object model.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoControlType.msoControlWorkPane">
			<summary>Work pane. Cannot be created through the object model.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoDiagramNodeType">
			<summary>Specifies type of diagram node.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramNodeType.msoDiagramAssistant">
			<summary>Diagram node is an assistant to its parent.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramNodeType.msoDiagramNode">
			<summary>Diagram node is a subordinate of its parent.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoDiagramType">
			<summary>Specifies the type of diagram.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramType.msoDiagramCycle">
			<summary>Cycle diagram.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramType.msoDiagramMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramType.msoDiagramOrgChart">
			<summary>Organization chart diagram.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramType.msoDiagramPyramid">
			<summary>Pyramid diagram.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramType.msoDiagramRadial">
			<summary>Radial diagram.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramType.msoDiagramTarget">
			<summary>Target diagram.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDiagramType.msoDiagramVenn">
			<summary>Venn diagram.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoDistributeCmd">
			<summary>Specifies how to evenly distribute a collection of shapes.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDistributeCmd.msoDistributeHorizontally">
			<summary>Distribute horizontally.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDistributeCmd.msoDistributeVertically">
			<summary>Distribute vertically.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoDocProperties">
			<summary>Specifies the data type for a document property.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDocProperties.msoPropertyTypeBoolean">
			<summary>Boolean value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDocProperties.msoPropertyTypeDate">
			<summary>Date value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDocProperties.msoPropertyTypeFloat">
			<summary>Floating point value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDocProperties.msoPropertyTypeNumber">
			<summary>Integer value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoDocProperties.msoPropertyTypeString">
			<summary>String value.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoEditingType">
			<summary>Specifies the editing type of a node.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEditingType.msoEditingAuto">
			<summary>Editing type is appropriate to the segments being connected.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEditingType.msoEditingCorner">
			<summary>Corner node.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEditingType.msoEditingSmooth">
			<summary>Smooth node.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEditingType.msoEditingSymmetric">
			<summary>Symmetric node.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoEncoding">
			<summary>Specifies the document encoding (code page or character set) for the Web browser to use when a user views a saved document.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingArabic">
			<summary>Arabic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingArabicASMO">
			<summary>Arabic ASMO.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingArabicAutoDetect">
			<summary>Web browser auto-detects type of Arabic encoding to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingArabicTransparentASMO">
			<summary>Transparent Arabic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingAutoDetect">
			<summary>Web browser auto-detects type of encoding to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingBaltic">
			<summary>Baltic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingCentralEuropean">
			<summary>Central European.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingCyrillic">
			<summary>Cyrillic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingCyrillicAutoDetect">
			<summary>Web browser auto-detects type of Cyrillic encoding to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICArabic">
			<summary>Extended Binary Coded Decimal Interchange Code (EBCDIC) Arabic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICDenmarkNorway">
			<summary>EBCDIC as used in Denmark and Norway.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICFinlandSweden">
			<summary>EBCDIC as used in Finland and Sweden.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICFrance">
			<summary>EBCDIC as used in France.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICGermany">
			<summary>EBCDIC as used in Germany.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICGreek">
			<summary>EBCDIC as used in the Greek language.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICGreekModern">
			<summary>EBCDIC as used in the Modern Greek language.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICHebrew">
			<summary>EBCDIC as used in the Hebrew language.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICIcelandic">
			<summary>EBCDIC as used in Iceland.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICInternational">
			<summary>International EBCDIC.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICItaly">
			<summary>EBCDIC as used in Italy.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICJapaneseKatakanaExtended">
			<summary>EBCDIC as used with Japanese Katakana (extended).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICJapaneseKatakanaExtendedAndJapanese">
			<summary>EBCDIC as used with Japanese Katakana (extended) and Japanese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICJapaneseLatinExtendedAndJapanese">
			<summary>EBCDIC as used with Japanese Latin (extended) and Japanese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICKoreanExtended">
			<summary>EBCDIC as used with Korean (extended).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICKoreanExtendedAndKorean">
			<summary>EBCDIC as used with Korean (extended) and Korean.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICLatinAmericaSpain">
			<summary>EBCDIC as used in Latin America and Spain.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICMultilingualROECELatin2">
			<summary>EBCDIC Multilingual ROECE (Latin 2).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICRussian">
			<summary>EBCDIC as used with Russian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICSerbianBulgarian">
			<summary>EBCDIC as used with Serbian and Bulgarian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICSimplifiedChineseExtendedAndSimplifiedChinese">
			<summary>EBCDIC as used with Simplified Chinese (extended) and Simplified Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICThai">
			<summary>EBCDIC as used with Thai.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICTurkish">
			<summary>EBCDIC as used with Turkish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICTurkishLatin5">
			<summary>EBCDIC as used with Turkish (Latin 5).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICUnitedKingdom">
			<summary>EBCDIC as used in the United Kingdom.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICUSCanada">
			<summary>EBCDIC as used in the United States and Canada.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICUSCanadaAndJapanese">
			<summary>EBCDIC as used in the United States and Canada, and with Japanese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEBCDICUSCanadaAndTraditionalChinese">
			<summary>EBCDIC as used in the United States and Canada, and with Traditional Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEUCChineseSimplifiedChinese">
			<summary>Extended Unix Code (EUC) as used with Chinese and Simplified Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEUCJapanese">
			<summary>EUC as used with Japanese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEUCKorean">
			<summary>EUC as used with Korean.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEUCTaiwaneseTraditionalChinese">
			<summary>EUC as used with Taiwanese and Traditional Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingEuropa3">
			<summary>Europa.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingExtAlphaLowercase">
			<summary>Extended Alpha lowercase.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingGreek">
			<summary>Greek.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingGreekAutoDetect">
			<summary>Web browser auto-detects type of Greek encoding to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingHebrew">
			<summary>Hebrew.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingHZGBSimplifiedChinese">
			<summary>Simplified Chinese (HZGB).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingIA5German">
			<summary>German (International Alphabet No. 5, or IA5).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingIA5IRV">
			<summary>IA5, International Reference Version (IRV).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingIA5Norwegian">
			<summary>IA5 as used with Norwegian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingIA5Swedish">
			<summary>IA5 as used with Swedish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIIAssamese">
			<summary>Indian Script Code for Information Interchange (ISCII) as used with Assamese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIIBengali">
			<summary>ISCII as used with Bengali.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIIDevanagari">
			<summary>ISCII as used with Devanagari.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIIGujarati">
			<summary>ISCII as used with Gujarati.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIIKannada">
			<summary>ISCII as used with Kannada.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIIMalayalam">
			<summary>ISCII as used with Malayalam.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIIOriya">
			<summary>ISCII as used with Oriya.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIIPunjabi">
			<summary>ISCII as used with Punjabi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIITamil">
			<summary>ISCII as used with Tamil.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISCIITelugu">
			<summary>ISCII as used with Telugu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO2022CNSimplifiedChinese">
			<summary>ISO 2022-CN encoding as used with Simplified Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO2022CNTraditionalChinese">
			<summary>ISO 2022-CN encoding as used with Traditional Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO2022JPJISX02011989">
			<summary>ISO 2022-JP </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO2022JPJISX02021984">
			<summary>ISO 2022-JP</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO2022JPNoHalfwidthKatakana">
			<summary>ISO 2022-JP with no half-width Katakana.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO2022KR">
			<summary>ISO 2022-KR.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO6937NonSpacingAccent">
			<summary>ISO 6937 Non-Spacing Accent.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO885915Latin9">
			<summary>ISO 8859-15 with Latin 9.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88591Latin1">
			<summary>ISO 8859-1 Latin 1.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88592CentralEurope">
			<summary>ISO 8859-2 Central Europe.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88593Latin3">
			<summary>ISO 8859-3 Latin 3.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88594Baltic">
			<summary>ISO 8859-4 Baltic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88595Cyrillic">
			<summary>ISO 8859-5 Cyrillic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88596Arabic">
			<summary>ISA 8859-6 Arabic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88597Greek">
			<summary>ISO 8859-7 Greek.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88598Hebrew">
			<summary>ISO 8859-8 Hebrew.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88598HebrewLogical">
			<summary>ISO 8859-8 Hebrew (Logical).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingISO88599Turkish">
			<summary>ISO 8859-9 Turkish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingJapaneseAutoDetect">
			<summary>Web browser auto-detects type of Japanese encoding to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingJapaneseShiftJIS">
			<summary>Japanese (Shift-JIS).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingKOI8R">
			<summary>KOI8-R.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingKOI8U">
			<summary>K0I8-U.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingKorean">
			<summary>Korean.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingKoreanAutoDetect">
			<summary>Web browser auto-detects type of Korean encoding to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingKoreanJohab">
			<summary>Korean (Johab).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacArabic">
			<summary>Macintosh Arabic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacCroatia">
			<summary>Macintosh Croatian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacCyrillic">
			<summary>Macintosh Cyrillic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacGreek1">
			<summary>Macintosh Greek.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacHebrew">
			<summary>Macintosh Hebrew.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacIcelandic">
			<summary>Macintosh Icelandic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacJapanese">
			<summary>Macintosh Japanese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacKorean">
			<summary>Macintosh Korean.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacLatin2">
			<summary>Macintosh Latin 2.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacRoman">
			<summary>Macintosh Roman.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacRomania">
			<summary>Macintosh Romanian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacSimplifiedChineseGB2312">
			<summary>Macintosh Simplified Chinese (GB 2312).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacTraditionalChineseBig5">
			<summary>Macintosh Traditional Chinese (Big 5).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacTurkish">
			<summary>Macintosh Turkish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingMacUkraine">
			<summary>Macintosh Ukrainian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMArabic">
			<summary>OEM as used with Arabic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMBaltic">
			<summary>OEM as used with Baltic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMCanadianFrench">
			<summary>OEM as used with Canadian French.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMCyrillic">
			<summary>OEM as used with Cyrillic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMCyrillicII">
			<summary>OEM as used with Cyrillic II.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMGreek437G">
			<summary>OEM as used with Greek 437G.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMHebrew">
			<summary>OEM as used with Hebrew.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMIcelandic">
			<summary>OEM as used with Icelandic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMModernGreek">
			<summary>OEM as used with Modern Greek.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMMultilingualLatinI">
			<summary>OEM as used with multi-lingual Latin I.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMMultilingualLatinII">
			<summary>OEM as used with multi-lingual Latin II.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMNordic">
			<summary>OEM as used with Nordic languages.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMPortuguese">
			<summary>OEM as used with Portuguese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMTurkish">
			<summary>OEM as used with Turkish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingOEMUnitedStates">
			<summary>OEM as used in the United States.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingSimplifiedChineseAutoDetect">
			<summary>Web browser auto-detects type of Simplified Chinese encoding to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingSimplifiedChineseGB18030">
			<summary>Simplified Chinese GB 18030.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingSimplifiedChineseGBK">
			<summary>Simplified Chinese GBK.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingT61">
			<summary>T61.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTaiwanCNS">
			<summary>Taiwan CNS.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTaiwanEten">
			<summary>Taiwan Eten.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTaiwanIBM5550">
			<summary>Taiwan IBM 5550.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTaiwanTCA">
			<summary>Taiwan TCA.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTaiwanTeleText">
			<summary>Taiwan Teletext.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTaiwanWang">
			<summary>Taiwan Wang.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingThai">
			<summary>Thai.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTraditionalChineseAutoDetect">
			<summary>Web browser auto-detects type of Traditional Chinese encoding to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTraditionalChineseBig5">
			<summary>Traditional Chinese Big 5.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingTurkish">
			<summary>Turkish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingUnicodeBigEndian">
			<summary>Unicode big endian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingUnicodeLittleEndian">
			<summary>Unicode little endian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingUSASCII">
			<summary>United States ASCII.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingUTF7">
			<summary>UTF-7 encoding.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingUTF8">
			<summary>UTF-8 encoding.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingVietnamese">
			<summary>Vietnamese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoEncoding.msoEncodingWestern">
			<summary>Western.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoEnvelope">
			<summary>Provides access to functionality that lets you send documents as emails directly from Microsoft Office applications.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoExtraInfoMethod">
			<summary>Specifies how to use the value specified in the ExtraInfo property of the FollowHyperlink method.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoExtraInfoMethod.msoMethodGet">
			<summary>The value specified in the ExtraInfo property is a string that is appended to the address.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoExtraInfoMethod.msoMethodPost">
			<summary>The value specified in the ExtraInfo property is posted as a string or byte array.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoExtrusionColorType">
			<summary>Specifies whether the extrusion color is based on the extruded shape's fill (the front face of the extrusion) and automatically changes when the shape's fill changes, or whether the extrusion color is independent of the shape's fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoExtrusionColorType.msoExtrusionColorAutomatic">
			<summary>Extrusion color is based on shape fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoExtrusionColorType.msoExtrusionColorCustom">
			<summary>Extrusion color is independent of shape fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoExtrusionColorType.msoExtrusionColorTypeMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFarEastLineBreakLanguageID">
			<summary>Specifies the language to use to determine which line break level is used when the line break control option is turned on.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFarEastLineBreakLanguageID.MsoFarEastLineBreakLanguageJapanese">
			<summary>Japanese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFarEastLineBreakLanguageID.MsoFarEastLineBreakLanguageKorean">
			<summary>Korean.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFarEastLineBreakLanguageID.MsoFarEastLineBreakLanguageSimplifiedChinese">
			<summary>Simplified Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFarEastLineBreakLanguageID.MsoFarEastLineBreakLanguageTraditionalChinese">
			<summary>Traditional Chinese.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFeatureInstall">
			<summary>Specifies how the application handles calls to methods and properties that require features not yet installed.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFeatureInstall.msoFeatureInstallNone">
			<summary>Generates a generic automation error at run time when uninstalled features are called.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFeatureInstall.msoFeatureInstallOnDemand">
			<summary>Prompts the user to install new features.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFeatureInstall.msoFeatureInstallOnDemandWithUI">
			<summary>Displays a progress meter during installation; does not prompt the user to install new features.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileDialogType">
			<summary>Specifies the type of a FileDialog object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogType.msoFileDialogFilePicker">
			<summary>File picker dialog box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogType.msoFileDialogFolderPicker">
			<summary>Folder picker dialog box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogType.msoFileDialogOpen">
			<summary>Open dialog box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogType.msoFileDialogSaveAs">
			<summary>Save As dialog box.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileDialogView">
			<summary>Specifies the view presented to the user in a file dialog box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewDetails">
			<summary>Files displayed in a list with detail information.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewLargeIcons">
			<summary>Files displayed as large icons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewList">
			<summary>Files displayed in a list without details.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewPreview">
			<summary>Files displayed in a list with a preview pane showing the selected file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewProperties">
			<summary>Files displayed in a list with a pane showing the selected file's properties.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewSmallIcons">
			<summary>Files displayed as small icons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewThumbnail">
			<summary>Files displayed as thumbnails.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewTiles">
			<summary>Files displayed as tiled icons.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileDialogView.msoFileDialogViewWebView">
			<summary>Files displayed in Web view.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileFindListBy">
			<summary>This enumeration applies to the Macintosh only and should not be used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindListBy.msoListbyName">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindListBy.msoListbyTitle">
			<summary>maconlymember</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileFindOptions">
			<summary>This enumeration applies to the Macintosh only and should not be used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindOptions.msoOptionsAdd">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindOptions.msoOptionsNew">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindOptions.msoOptionsWithin">
			<summary>maconlymember</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileFindSortBy">
			<summary>This enumeration applies to the Macintosh only and should not be used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindSortBy.msoFileFindSortbyAuthor">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindSortBy.msoFileFindSortbyDateCreated">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindSortBy.msoFileFindSortbyDateSaved">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindSortBy.msoFileFindSortbyFileName">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindSortBy.msoFileFindSortbyLastSavedBy">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindSortBy.msoFileFindSortbySize">
			<summary>maconlymember</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindSortBy.msoFileFindSortbyTitle">
			<summary>maconlymember</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileFindView">
			<summary>Specifies view to use for a file find process.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindView.msoViewFileInfo">
			<summary>View file information.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindView.msoViewPreview">
			<summary>View preview of file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileFindView.msoViewSummaryInfo">
			<summary>View summary information.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileNewAction">
			<summary>Specifies action to take when a user clicks an item in the task pane.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileNewAction.msoCreateNewFile">
			<summary>Create a new file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileNewAction.msoEditFile">
			<summary>Edit file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileNewAction.msoOpenFile">
			<summary>Open file.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileNewSection">
			<summary>Specifies the task pane section to which to add a file or where the file reference exists.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileNewSection.msoBottomSection">
			<summary>Bottom section.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileNewSection.msoNew">
			<summary>New section.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileNewSection.msoNewfromExistingFile">
			<summary>New from Existing File section.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileNewSection.msoNewfromTemplate">
			<summary>New from Template section.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileNewSection.msoOpenDocument">
			<summary>Open Document section.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFileType">
			<summary>Specifies a type of file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeAllFiles">
			<summary>All files.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeBinders">
			<summary>Microsoft Binder file (*.obd).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeCalendarItem">
			<summary>Calendar item file (*.ics or *.vsc).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeContactItem">
			<summary>Contact item file (*.vcf).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeDatabases">
			<summary>Database file (*.mdb).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeDataConnectionFiles">
			<summary>Data connection file (*.mdf).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeDesignerFiles">
			<summary>Visual Basic Active Designer file (*.dsr).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeDocumentImagingFiles">
			<summary>Microsoft Document Imaging file (*.mdi).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeExcelWorkbooks">
			<summary>Microsoft Excel workbook (*.wbk).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeJournalItem">
			<summary>Microsoft Outlook Journal item</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeMailItem">
			<summary>Mail item file (*.msg).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeNoteItem">
			<summary>Microsoft Outlook Note item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeOfficeFiles">
			<summary>Files with any of the following extensions: *.doc, *.xls, *.ppt, *.pps, *.obd, *.mdb, *.mpd, *.dot, *.xlt, *.pot, *.obt, *.htm, or *.html.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeOutlookItems">
			<summary>Any Microsoft Outlook item file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypePhotoDrawFiles">
			<summary>PhotoDraw item file (*.mix).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypePowerPointPresentations">
			<summary>PowerPoint presentation file (*.ppt), PowerPoint template file (*.pot), or PowerPoint slide show file (*.pps).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeProjectFiles">
			<summary>Project file (*.mpd).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypePublisherFiles">
			<summary>Microsoft Publisher file (*.pub)</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeTaskItem">
			<summary>Microsoft Outlook task item.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeTemplates">
			<summary>Microsoft PowerPoint template (*.pot), Word template (*.dot), Excel template (*.xlt).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeVisioFiles">
			<summary>Microsoft Visio file (*.vsd).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeWebPages">
			<summary>HTML file (*.htm or *.html).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFileType.msoFileTypeWordDocuments">
			<summary>Microsoft Word document file (*.doc).</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFillType">
			<summary>Specifies a shape's fill type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFillType.msoFillBackground">
			<summary>Fill is the same as the background.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFillType.msoFillGradient">
			<summary>Gradient fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFillType.msoFillMixed">
			<summary>Mixed fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFillType.msoFillPatterned">
			<summary>Patterned fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFillType.msoFillPicture">
			<summary>Picture fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFillType.msoFillSolid">
			<summary>Solid fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFillType.msoFillTextured">
			<summary>Textured fill.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFilterComparison">
			<summary>Specifies how the Column and CompareTo properties are compared for an ODSOFilter object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonContains">
			<summary>Column matches CompareTo if any part of the CompareTo string is contained in the Column value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonEqual">
			<summary>Column matches CompareTo if the CompareTo value is the same as the Column value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonGreaterThan">
			<summary>Column matches CompareTo if the Column value is greater than the CompareTo value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonGreaterThanEqual">
			<summary>Column matches CompareTo if the Column value is greater than or equal to the CompareTo value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonIsBlank">
			<summary>Column passes filter if Column is blank.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonIsNotBlank">
			<summary>Column passes filter if Column is blank.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonLessThan">
			<summary>Column matches CompareTo if the Column value is less than the CompareTo value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonLessThanEqual">
			<summary>Column matches CompareTo if the Column value is less than or equal to the CompareTo value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonNotContains">
			<summary>Column matches CompareTo if any part of the CompareTo string is not contained in the Column value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterComparison.msoFilterComparisonNotEqual">
			<summary>Column matches CompareTo if the CompareTo value is not equal to the Column value.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFilterConjunction">
			<summary>Specifies how a filter criterion relates to other filter criteria.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterConjunction.msoFilterConjunctionAnd">
			<summary>And conjunction.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFilterConjunction.msoFilterConjunctionOr">
			<summary>Or conjunction.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoFlipCmd">
			<summary>Specifies whether a shape should be flipped horizontally or vertically.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFlipCmd.msoFlipHorizontal">
			<summary>Flip horizontally.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoFlipCmd.msoFlipVertical">
			<summary>Flip vertically.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoGradientColorType">
			<summary>Specifies the type of gradient used in a shape's fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientColorType.msoGradientColorMixed">
			<summary>Mixed gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientColorType.msoGradientOneColor">
			<summary>One-color gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientColorType.msoGradientPresetColors">
			<summary>Gradient colors set according to a built-in gradient of the set defined by the msoPresetGradientType constant.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientColorType.msoGradientTwoColors">
			<summary>Two-color gradient.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoGradientStyle">
			<summary>Specifies the style for a gradient fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientStyle.msoGradientDiagonalDown">
			<summary>Diagonal gradient moving from a top corner down to the opposite corner.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientStyle.msoGradientDiagonalUp">
			<summary>Diagonal gradient moving from a bottom corner up to the opposite corner.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientStyle.msoGradientFromCenter">
			<summary>Gradient running from the center out to the corners.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientStyle.msoGradientFromCorner">
			<summary>Gradient running from a corner to the other three corners.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientStyle.msoGradientFromTitle">
			<summary>Gradient running from the title outward.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientStyle.msoGradientHorizontal">
			<summary>Gradient running horizontally across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientStyle.msoGradientMixed">
			<summary>Gradient is mixed.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoGradientStyle.msoGradientVertical">
			<summary>Gradient running vertically down the shape.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoHorizontalAnchor">
			<summary>Specifies the horizontal alignment of text in a text frame.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHorizontalAnchor.msoAnchorCenter">
			<summary>Text is centered horizontally.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHorizontalAnchor.msoAnchorNone">
			<summary>No alignment.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHorizontalAnchor.msoHorizontalAnchorMixed">
			<summary>Return value only; indicates a combination of the other states.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoHTMLProjectOpen">
			<summary>Specifies the view in which an HTML project or project item is opened.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHTMLProjectOpen.msoHTMLProjectOpenSourceView">
			<summary>Open project in source view.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHTMLProjectOpen.msoHTMLProjectOpenTextView">
			<summary>Open project in text view.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoHTMLProjectState">
			<summary>Specifies the current state of an HTMLProject object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHTMLProjectState.msoHTMLProjectStateDocumentLocked">
			<summary>Document is locked. In a Microsoft Office host application or Microsoft Script Editor, indicates that the Refresh toolbar is displayed in the host application.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHTMLProjectState.msoHTMLProjectStateDocumentProjectUnlocked">
			<summary>Document is unlocked. In a Microsoft Office host application or Microsoft Script Editor, indicates that the Refresh toolbar is not displayed at all.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHTMLProjectState.msoHTMLProjectStateProjectLocked">
			<summary>Project is locked. In the Microsoft Script Editor, indicates that the Refresh toolbar is displayed.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoHyperlinkType">
			<summary>Specifies the type of hyperlink.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHyperlinkType.msoHyperlinkInlineShape">
			<summary>Hyperlink applies to an inline shape. Used only with Microsoft Word.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHyperlinkType.msoHyperlinkRange">
			<summary>Hyperlink applies to a Range object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoHyperlinkType.msoHyperlinkShape">
			<summary>Hyperlink applies to a Shape object.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoIconType">
			<summary>Specifies an icon type to show in a Balloon object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoIconType.msoIconAlert">
			<summary>Alert icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoIconType.msoIconAlertCritical">
			<summary>Critical alert icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoIconType.msoIconAlertInfo">
			<summary>Information alert icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoIconType.msoIconAlertQuery">
			<summary>Query alert icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoIconType.msoIconAlertWarning">
			<summary>Warning alert icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoIconType.msoIconNone">
			<summary>No icon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoIconType.msoIconTip">
			<summary>Tip icon.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoLanguageID">
			<summary>Specifies which language to use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDAfrikaans">
			<summary>Afrikaans.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDAlbanian">
			<summary>Albanian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDAmharic">
			<summary>Amharic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabic">
			<summary>Arabic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicAlgeria">
			<summary>Arabic as spoken in Algeria.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicBahrain">
			<summary>Arabic as spoken in Bahrain.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicEgypt">
			<summary>Arabic as spoken in Egypt.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicIraq">
			<summary>Arabic as spoken in Iraq.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicJordan">
			<summary>Arabic as spoken in Jordan.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicKuwait">
			<summary>Arabic as spoken in Kuwait.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicLebanon">
			<summary>Arabic as spoken in Lebanon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicLibya">
			<summary>Arabic as spoken in Libya.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicMorocco">
			<summary>Arabic as spoken in Morocco.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicOman">
			<summary>Arabic as spoken in Oman.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicQatar">
			<summary>Arabic as spoken in Qatar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicSyria">
			<summary>Arabic as spoken in Syria.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicTunisia">
			<summary>Arabic as spoken in Tunisia.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicUAE">
			<summary>Arabic as spoken in the United Arab Emirates.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArabicYemen">
			<summary>Arabic as spoken in Yemen.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDArmenian">
			<summary>Armenian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDAssamese">
			<summary>Assamese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDAzeriCyrillic">
			<summary>Azeri-Cyrillic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDAzeriLatin">
			<summary>Azeri-Latin.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDBasque">
			<summary>Basque.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDBelgianDutch">
			<summary>Belgian Dutch.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDBelgianFrench">
			<summary>Belgian French.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDBengali">
			<summary>Bengali.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDBosnian">
			<summary>Bosnian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDBrazilianPortuguese">
			<summary>Brazilian Portuguese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDBulgarian">
			<summary>Bulgarian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDBurmese">
			<summary>Burmese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDByelorussian">
			<summary>Byelorussian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDCatalan">
			<summary>Catalan.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDCherokee">
			<summary>Cherokee.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDChineseHongKongSAR">
			<summary>Chinese as spoken in Hong Kong SAR.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDChineseMacaoSAR">
			<summary>Chinese as spoken in Macao SAR.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDChineseSingapore">
			<summary>Chinese as spoken in Singapore.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDCroatian">
			<summary>Croatian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDCzech">
			<summary>Czech.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDDanish">
			<summary>Danish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDDivehi">
			<summary>Divehi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDDutch">
			<summary>Dutch.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDDzongkhaBhutan">
			<summary>Dzongkha as spoken in Bhutan.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEdo">
			<summary>Edo.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishAUS">
			<summary>English as spoken in Australia.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishBelize">
			<summary>English as spoken in Belize.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishCanadian">
			<summary>English as spoken in Canada.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishCaribbean">
			<summary>English as spoken in the Caribbean.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishIndonesia">
			<summary>English as spoken in Indonesia.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishIreland">
			<summary>English as spoken in Ireland.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishJamaica">
			<summary>English as spoken in Jamaica.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishNewZealand">
			<summary>English as spoken in New Zealand.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishPhilippines">
			<summary>English as spoken in the Philippines.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishSouthAfrica">
			<summary>English as spoken in South Africa.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishTrinidadTobago">
			<summary>English as spoken in Trinidad and Tobago.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishUK">
			<summary>English as spoken in the United Kingdom.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishUS">
			<summary>English as spoken in the United States.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEnglishZimbabwe">
			<summary>English as spoken in Zimbabwe.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDEstonian">
			<summary>Estonian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFaeroese">
			<summary>Faeroese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFarsi">
			<summary>Farsi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFilipino">
			<summary>Filipina.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFinnish">
			<summary>Finnish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrench">
			<summary>French.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchCameroon">
			<summary>French as spoken in Cameroon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchCanadian">
			<summary>French as spoken in Canada.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchCotedIvoire">
			<summary>French as spoken in Cote d'Ivoire.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchHaiti">
			<summary>French as spoken in Haiti.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchLuxembourg">
			<summary>French as spoken in Luxembourg.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchMali">
			<summary>French as spoken in Mali.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchMonaco">
			<summary>French as spoken in Monaco.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchMorocco">
			<summary>French as spoken in Morocco.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchReunion">
			<summary>French as spoken in French Reunion Island.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchSenegal">
			<summary>French as spoken in Senegal.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchWestIndies">
			<summary>French as spoken in the West Indies.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrenchZaire">
			<summary>French as spoken in Zaire.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFrisianNetherlands">
			<summary>French as spoken in the Netherlands.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDFulfulde">
			<summary>Fulfulde.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGaelicIreland">
			<summary>Gaelic as spoken in Ireland.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGaelicScotland">
			<summary>Gaelic as spoken in Scotland.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGalician">
			<summary>Galician.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGeorgian">
			<summary>Georgian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGerman">
			<summary>German.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGermanAustria">
			<summary>German as spoken in Austria.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGermanLiechtenstein">
			<summary>German as spoken in Liechtenstein.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGermanLuxembourg">
			<summary>German as spoken in Luxembourg.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGreek">
			<summary>Greek.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGuarani">
			<summary>Guarani.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDGujarati">
			<summary>Gujarati.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDHausa">
			<summary>Hausa.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDHawaiian">
			<summary>Hawaiian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDHebrew">
			<summary>Hebrew.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDHindi">
			<summary>Hindi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDHungarian">
			<summary>Hungarian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDIbibio">
			<summary>Ibibio.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDIcelandic">
			<summary>Icelandic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDIgbo">
			<summary>Igbo.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDIndonesian">
			<summary>Indonesian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDInuktitut">
			<summary>Inuktitut.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDItalian">
			<summary>Italian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDJapanese">
			<summary>Japanese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKannada">
			<summary>Kannada.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKanuri">
			<summary>Kanuri.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKashmiri">
			<summary>Kashmiri.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKashmiriDevanagari">
			<summary>Kashmiri in Devanagari script.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKazakh">
			<summary>Kazakh.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKhmer">
			<summary>Khmer.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKirghiz">
			<summary>Kirghiz.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKonkani">
			<summary>Konkani.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKorean">
			<summary>Korean.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDKyrgyz">
			<summary>Kyrgyz.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDLao">
			<summary>Lao.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDLatin">
			<summary>Latin.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDLatvian">
			<summary>Latvian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDLithuanian">
			<summary>Lithuanian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMacedonian">
			<summary>Macedonian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMalayalam">
			<summary>Malayalam.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMalayBruneiDarussalam">
			<summary>Malay as spoken in Brunei Darussalam.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMalaysian">
			<summary>Malaysian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMaltese">
			<summary>Maltese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDManipuri">
			<summary>Manipuri.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMaori">
			<summary>Maori.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMarathi">
			<summary>Marathi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMexicanSpanish">
			<summary>Spanish as spoken in Mexico.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMixed">
			<summary>Mixed languages.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDMongolian">
			<summary>Mongolian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDNepali">
			<summary>Nepali.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDNone">
			<summary>No language specified.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDNoProofing">
			<summary>No proofing requested.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDNorwegianBokmol">
			<summary>Bokmol as spoken in Norway.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDNorwegianNynorsk">
			<summary>Nynorsk as spoken in Norway.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDOriya">
			<summary>Oriya.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDOromo">
			<summary>Oromo.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDPashto">
			<summary>Pashto.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDPolish">
			<summary>Polish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDPortuguese">
			<summary>Portuguese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDPunjabi">
			<summary>Punjabi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDQuechuaBolivia">
			<summary>Quechua as spoken in Bolivia.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDQuechuaEcuador">
			<summary>Quechua as spoken in Ecuador.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDQuechuaPeru">
			<summary>Quechua as spoken in Peru.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDRhaetoRomanic">
			<summary>Rhaeto-Romanic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDRomanian">
			<summary>Romanian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDRomanianMoldova">
			<summary>Romanian as spoken in Moldova.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDRussian">
			<summary>Russian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDRussianMoldova">
			<summary>Russian as spoken in Moldova.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSamiLappish">
			<summary>Sami/Lappish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSanskrit">
			<summary>Sanskrit.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSepedi">
			<summary>Sepedi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSerbianCyrillic">
			<summary>Serbian/Cyrillic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSerbianLatin">
			<summary>Serbian/Latin.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSesotho">
			<summary>Sesotho.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSimplifiedChinese">
			<summary>Simplified Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSindhi">
			<summary>Sindhi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSindhiPakistan">
			<summary>Sindhi as spoken in Pakistan.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSinhalese">
			<summary>Sinhalese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSlovak">
			<summary>Slovak.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSlovenian">
			<summary>Slovenian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSomali">
			<summary>Somali.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSorbian">
			<summary>Sorbian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanish">
			<summary>Spanish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishArgentina">
			<summary>Spanish as spoken in Argentina.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishBolivia">
			<summary>Spanish as spoken in Bolivia.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishChile">
			<summary>Spanish as spoken in Chile.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishColombia">
			<summary>Spanish as spoken in Colombia.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishCostaRica">
			<summary>Spanish as spoken in Costa Rica.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishDominicanRepublic">
			<summary>Spanish as spoken in the Dominican Republic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishEcuador">
			<summary>Spanish as spoken in Ecuador.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishElSalvador">
			<summary>Spanish as spoken in El Salvador.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishGuatemala">
			<summary>Spanish as spoken in Guatemala.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishHonduras">
			<summary>Spanish as spoken in Honduras.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishModernSort">
			<summary>Spanish (Modern Sort).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishNicaragua">
			<summary>Spanish as spoken in Nicaragua.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishPanama">
			<summary>Spanish as spoken in Panama.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishParaguay">
			<summary>Spanish as spoken in Paraguay.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishPeru">
			<summary>Spanish as spoken in Peru.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishPuertoRico">
			<summary>Spanish as spoken in Puerto Rico.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishUruguay">
			<summary>Spanish as spoken in Uruguay.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSpanishVenezuela">
			<summary>Spanish as spoken in Venezuela.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSutu">
			<summary>Sutu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSwahili">
			<summary>Swahili.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSwedish">
			<summary>Swedish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSwedishFinland">
			<summary>Swedish as spoken in Finland.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSwissFrench">
			<summary>French as spoken in Switzerland.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSwissGerman">
			<summary>German as spoken in Switzerland.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSwissItalian">
			<summary>Italian as spoken in Switzerland.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDSyriac">
			<summary>Syriac.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTajik">
			<summary>Tajik.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTamazight">
			<summary>Tamazight.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTamazightLatin">
			<summary>Tamazight (Latin).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTamil">
			<summary>Tamil.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTatar">
			<summary>Tatar.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTelugu">
			<summary>Telugu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDThai">
			<summary>Thai.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTibetan">
			<summary>Tibetan.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTigrignaEritrea">
			<summary>Tigrigna as spoken in Eritrea.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTigrignaEthiopic">
			<summary>Tigrigna as spoken in Ethiopia.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTraditionalChinese">
			<summary>Traditional Chinese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTsonga">
			<summary>Tsonga.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTswana">
			<summary>Tswana.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTurkish">
			<summary>Turkish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDTurkmen">
			<summary>Turkmen.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDUkrainian">
			<summary>Ukrainian.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDUrdu">
			<summary>Urdu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDUzbekCyrillic">
			<summary>Uzbek (Cyrillic).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDUzbekLatin">
			<summary>Uzbek (Latin).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDVenda">
			<summary>Venda.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDVietnamese">
			<summary>Vietnamese.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDWelsh">
			<summary>Welsh.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDXhosa">
			<summary>Xhosa.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDYi">
			<summary>Yi.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDYiddish">
			<summary>Yiddish.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDYoruba">
			<summary>Yoruba.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageID.msoLanguageIDZulu">
			<summary>Zulu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageIDHidden.msoLanguageIDChineseHongKong">
			<summary>hiddenconstant</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageIDHidden.msoLanguageIDChineseMacao">
			<summary>hiddenconstant</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLanguageIDHidden.msoLanguageIDEnglishTrinidad">
			<summary>hiddenconstant</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoLastModified">
			<summary>Specifies the period of time to filter files by the date last modified. Used with the LastModified property of the FileSearch object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLastModified.msoLastModifiedAnyTime">
			<summary>File last modified any time. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLastModified.msoLastModifiedLastMonth">
			<summary>File last modified last month.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLastModified.msoLastModifiedLastWeek">
			<summary>File last modified last week.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLastModified.msoLastModifiedThisMonth">
			<summary>File last modified this month.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLastModified.msoLastModifiedThisWeek">
			<summary>File last modified this week.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLastModified.msoLastModifiedToday">
			<summary>File last modified today.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLastModified.msoLastModifiedYesterday">
			<summary>File last modified yesterday.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoLineDashStyle">
			<summary>Specifies the dash style for a line. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineDash">
			<summary>Line consists of dashes only.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineDashDot">
			<summary>Line is a dash-dot pattern.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineDashDotDot">
			<summary>Line is a dash-dot-dot pattern.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineDashStyleMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineLongDash">
			<summary>Line consists of long dashes.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineLongDashDot">
			<summary>Line is a long dash-dot pattern.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineRoundDot">
			<summary>Line is made up of round dots.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineSolid">
			<summary>Line is solid.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineDashStyle.msoLineSquareDot">
			<summary>Line is made up of square dots.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoLineStyle">
			<summary>Specifies the style for a line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineStyle.msoLineSingle">
			<summary>Single line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineStyle.msoLineStyleMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineStyle.msoLineThickBetweenThin">
			<summary>Thick line with a thin line on each side.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineStyle.msoLineThickThin">
			<summary>Thick line next to thin line. For horizontal lines, thick line is above thin line. For vertical lines, thick line is to the left of the thin line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineStyle.msoLineThinThick">
			<summary>Thick line next to thin line. For horizontal lines, thick line is below thin line. For vertical lines, thick line is to the right of the thin line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoLineStyle.msoLineThinThin">
			<summary>Two thin lines.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoMenuAnimation">
			<summary>Specifies animation style for Microsoft Office command bars.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMenuAnimation.msoMenuAnimationNone">
			<summary>No animation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMenuAnimation.msoMenuAnimationRandom">
			<summary>Random animation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMenuAnimation.msoMenuAnimationSlide">
			<summary>Menus slide into view.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMenuAnimation.msoMenuAnimationUnfold">
			<summary>Menus unfold into view.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoMixedType">
			<summary>This enumeration has been deprecated and should not be used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMixedType.msoIntegerMixed">
			<summary>Internal use only.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMixedType.msoSingleMixed">
			<summary>Internal use only.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoModeType">
			<summary>Specifies the mode type for a Balloon object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoModeType.msoModeAutoDown">
			<summary>Auto-down. Balloon is dismissed when user clicks anywhere on the screen.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoModeType.msoModeModal">
			<summary>Modal. User must dismiss balloon before continuing work.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoModeType.msoModeModeless">
			<summary>Modeless. User can work in application while balloon is displayed.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoMoveRow">
			<summary>This enumeration has been deprecated and should not be used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMoveRow.msoMoveRowFirst">
			<summary>Internal use only.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMoveRow.msoMoveRowNbr">
			<summary>Internal use only.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMoveRow.msoMoveRowNext">
			<summary>Internal use only.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoMoveRow.msoMoveRowPrev">
			<summary>Internal use only.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoOLEMenuGroup">
			<summary>Specifies the menu group that a command bar pop-up control belongs to when the menu groups of the OLE server are merged with the menu groups of an OLE client (that is, when an object of the container application type is embedded in another application).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOLEMenuGroup.msoOLEMenuGroupContainer">
			<summary>Container menu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOLEMenuGroup.msoOLEMenuGroupEdit">
			<summary>Edit menu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOLEMenuGroup.msoOLEMenuGroupFile">
			<summary>File menu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOLEMenuGroup.msoOLEMenuGroupHelp">
			<summary>Help menu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOLEMenuGroup.msoOLEMenuGroupNone">
			<summary>Pop-up control is not merged.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOLEMenuGroup.msoOLEMenuGroupObject">
			<summary>Object menu.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOLEMenuGroup.msoOLEMenuGroupWindow">
			<summary>Window menu.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoOrgChartLayoutType">
			<summary>Indicates how to format the child nodes in an organization chart.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrgChartLayoutType.msoOrgChartLayoutBothHanging">
			<summary>Places child nodes vertically below the parent node on both the left and the right side.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrgChartLayoutType.msoOrgChartLayoutLeftHanging">
			<summary>Places child nodes vertically below the parent node on the left side.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrgChartLayoutType.msoOrgChartLayoutMixed">
			<summary>Return value for a parent node that has children formatted using more than one MsoOrgChartLayoutType.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrgChartLayoutType.msoOrgChartLayoutRightHanging">
			<summary>Places child nodes vertically below the parent node on the right side.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrgChartLayoutType.msoOrgChartLayoutStandard">
			<summary>Places child nodes horizontally below the parent node.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoOrgChartOrientation">
			<summary>Specifies orientation of an organization chart.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrgChartOrientation.msoOrgChartOrientationMixed">
			<summary>Mixed orientation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrgChartOrientation.msoOrgChartOrientationVertical">
			<summary>Vertical orientation.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoOrientation">
			<summary>Specifies orientation of an object when it is displayed or printed.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrientation.msoOrientationHorizontal">
			<summary>Horizontal (landscape) orientation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrientation.msoOrientationMixed">
			<summary>Mixed orientation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoOrientation.msoOrientationVertical">
			<summary>Vertical (portrait) orientation.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPatternType">
			<summary>Specifies the fill pattern used in a shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern10Percent">
			<summary>10% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern20Percent">
			<summary>20% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern25Percent">
			<summary>25% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern30Percent">
			<summary>30% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern40Percent">
			<summary>40% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern50Percent">
			<summary>50% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern5Percent">
			<summary>5% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern60Percent">
			<summary>60% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern70Percent">
			<summary>70% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern75Percent">
			<summary>75% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern80Percent">
			<summary>80% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPattern90Percent">
			<summary>90% of the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDarkDownwardDiagonal">
			<summary>Thick lines in the foreground color running from the top to the right-hand side of the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDarkHorizontal">
			<summary>Thick horizontal lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDarkUpwardDiagonal">
			<summary>Thick lines in the foreground color running from the top to the left-hand side of the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDarkVertical">
			<summary>Thick vertical lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDashedDownwardDiagonal">
			<summary>Dashed lines in the foreground color running from the top to the right-hand side of the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDashedHorizontal">
			<summary>Dashed horizontal lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDashedUpwardDiagonal">
			<summary>Dashed lines in the foreground color running from the top to the left-hand side of the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDashedVertical">
			<summary>Dashed vertical lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDiagonalBrick">
			<summary>Rectangular brick pattern running diagonally across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDivot">
			<summary>Small angled shapes in the foreground color running in alternating rows down the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDottedDiamond">
			<summary>Dotted perpendicular lines in the foreground color running diagonally to form diamonds across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternDottedGrid">
			<summary>Dotted perpendicular lines in the foreground color running horizontally and vertically to form grid lines across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternHorizontalBrick">
			<summary>Rectangular brick pattern running horizontally across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternLargeCheckerBoard">
			<summary>Squares in alternating foreground/background colors.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternLargeConfetti">
			<summary>Large dots in the foreground color scattered across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternLargeGrid">
			<summary>Solid, widely spaced perpendicular lines in the foreground color running horizontally and vertically to form grid lines across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternLightDownwardDiagonal">
			<summary>Thin lines in the foreground color running from the top to the right-hand side of the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternLightHorizontal">
			<summary>Thin horizontal lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternLightUpwardDiagonal">
			<summary>Thin lines in the foreground color running from the top to the left-hand side of the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternLightVertical">
			<summary>Thin vertical lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternNarrowHorizontal">
			<summary>Narrowly spaced horizontal lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternNarrowVertical">
			<summary>Narrowly spaced vertical lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternOutlinedDiamond">
			<summary>Solid perpendicular lines in the foreground color running diagonally to form diamonds across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternPlaid">
			<summary>Very thick solid lines in the foreground color running vertically, coupled with very thick lines and 40% of the foreground color running horizontally.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternShingle">
			<summary>Overlapping curved rectangles running diagonally across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternSmallCheckerBoard">
			<summary>Small squares in alternating foreground/background colors.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternSmallConfetti">
			<summary>Small dots in the foreground color scattered across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternSmallGrid">
			<summary>Solid, closely spaced perpendicular lines in the foreground color running horizontally and vertically to form grid lines across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternSolidDiamond">
			<summary>Diamond shapes in alternating foreground/background colors.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternSphere">
			<summary>Circles that use foreground and background colors to make them appear three-dimensional, oriented in rows across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternTrellis">
			<summary>Trellis pattern in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternWave">
			<summary>Wavy lines in the foreground color.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternWeave">
			<summary>Weave pattern in the foreground color running diagonally across the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternWideDownwardDiagonal">
			<summary>Widely spaced lines in the foreground color running from the top to the right-hand side of the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternWideUpwardDiagonal">
			<summary>Widely spaced lines in the foreground color running from the top to the left-hand side of the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPatternType.msoPatternZigZag">
			<summary>Zigzag lines running horizontally across the shape.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPermission">
			<summary>Specifies an Information Rights Management (IRM) permission type for a document.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionChange">
			<summary>Permission to change.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionEdit">
			<summary>Permission to edit.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionExtract">
			<summary>Permission to extract.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionFullControl">
			<summary>Full control permissions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionObjModel">
			<summary>Permission to access the object model programmatically.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionPrint">
			<summary>Permission to print.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionRead">
			<summary>Permission to read.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionSave">
			<summary>Permission to save.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPermission.msoPermissionView">
			<summary>Permission to view.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPictureColorType">
			<summary>Specifies the color transformation applied to a picture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPictureColorType.msoPictureAutomatic">
			<summary>Default color transformation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPictureColorType.msoPictureBlackAndWhite">
			<summary>Black-and-white transformation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPictureColorType.msoPictureGrayscale">
			<summary>Grayscale transformation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPictureColorType.msoPictureMixed">
			<summary>Mixed transformation.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPictureColorType.msoPictureWatermark">
			<summary>Watermark transformation.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetExtrusionDirection">
			<summary>Specifies the direction that the extrusion's sweep path takes away from the extruded shape (the front face of the extrusion).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionBottom">
			<summary>Bottom.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionBottomLeft">
			<summary>Bottom left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionBottomRight">
			<summary>Bottom right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionLeft">
			<summary>Left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionNone">
			<summary>No extrusion.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionRight">
			<summary>Right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionTop">
			<summary>Top.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionTopLeft">
			<summary>Top left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoExtrusionTopRight">
			<summary>Top right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetExtrusionDirection.msoPresetExtrusionDirectionMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetGradientType">
			<summary>Specifies which predefined gradient to use to fill a shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientBrass">
			<summary>Brass gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientCalmWater">
			<summary>Calm Water gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientChrome">
			<summary>Chrome gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientChromeII">
			<summary>Chrome II gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientDaybreak">
			<summary>Daybreak gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientDesert">
			<summary>Desert gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientEarlySunset">
			<summary>Early Sunset gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientFire">
			<summary>Fire gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientFog">
			<summary>Fog gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientGold">
			<summary>Gold gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientGoldII">
			<summary>Gold II gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientHorizon">
			<summary>Horizon gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientLateSunset">
			<summary>Late Sunset gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientMahogany">
			<summary>Mahogany gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientMoss">
			<summary>Moss gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientNightfall">
			<summary>Nightfall gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientOcean">
			<summary>Ocean gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientParchment">
			<summary>Parchment gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientPeacock">
			<summary>Peacock gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientRainbow">
			<summary>Rainbow gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientRainbowII">
			<summary>Rainbow II gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientSapphire">
			<summary>Sapphire gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientSilver">
			<summary>Silver gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoGradientWheat">
			<summary>Wheat gradient.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetGradientType.msoPresetGradientMixed">
			<summary>Mixed gradient.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetLightingDirection">
			<summary>Specifies the location of lighting on an extruded (three-dimensional) shape relative to the shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingBottom">
			<summary>Lighting comes from the bottom.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingBottomLeft">
			<summary>Lighting comes from the bottom left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingBottomRight">
			<summary>Lighting comes from the bottom right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingLeft">
			<summary>Lighting comes from the left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingNone">
			<summary>No lighting.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingRight">
			<summary>Lighting comes from the right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingTop">
			<summary>Lighting comes from the top.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingTopLeft">
			<summary>Lighting comes from the top left.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoLightingTopRight">
			<summary>Lighting comes from the top right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingDirection.msoPresetLightingDirectionMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetLightingSoftness">
			<summary>Specifies the intensity of light used on a shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingSoftness.msoLightingBright">
			<summary>Bright light.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingSoftness.msoLightingDim">
			<summary>Dim light.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingSoftness.msoLightingNormal">
			<summary>Normal light.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetLightingSoftness.msoPresetLightingSoftnessMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetMaterial">
			<summary>Specifies the extrusion surface material.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetMaterial.msoMaterialMatte">
			<summary>Matte.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetMaterial.msoMaterialMetal">
			<summary>Metal.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetMaterial.msoMaterialPlastic">
			<summary>Plastic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetMaterial.msoMaterialWireFrame">
			<summary>Wire frame.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetMaterial.msoPresetMaterialMixed">
			<summary>Return value only; indicates a combination of the other states.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetTextEffect">
			<summary>Specifies what text effect to use on a WordArt object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect1">
			<summary>First text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect10">
			<summary>Tenth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect11">
			<summary>Eleventh text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect12">
			<summary>Twelfth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect13">
			<summary>Thirteenth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect14">
			<summary>Fourteenth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect15">
			<summary>Fifteenth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect16">
			<summary>Sixteenth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect17">
			<summary>Seventeenth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect18">
			<summary>Eighteenth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect19">
			<summary>Nineteenth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect2">
			<summary>Second text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect20">
			<summary>Twentieth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect21">
			<summary>Twenty-first text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect22">
			<summary>Twenty-second text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect23">
			<summary>Twenty-third text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect24">
			<summary>Twenty-fourth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect25">
			<summary>Twenty-fifth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect26">
			<summary>Twenty-sixth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect27">
			<summary>Twenty-seventh text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect28">
			<summary>Twenty-eighth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect29">
			<summary>Twenty-ninth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect3">
			<summary>Third text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect30">
			<summary>Thirtieth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect4">
			<summary>Fourth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect5">
			<summary>Fifth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect6">
			<summary>Sixth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect7">
			<summary>Seventh text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect8">
			<summary>Eighth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffect9">
			<summary>Ninth text effect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffect.msoTextEffectMixed">
			<summary>Not used.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetTextEffectShape">
			<summary>Specifies shape of WordArt text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeArchDownCurve">
			<summary>Text is an arch that curves down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeArchDownPour">
			<summary>Text is a 3-D arch that curves down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeArchUpCurve">
			<summary>Text is an arch that curves up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeArchUpPour">
			<summary>Text is a 3-D arch that curves up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeButtonCurve">
			<summary>Text is curved around a center "button."</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeButtonPour">
			<summary>Text is seen in 3-D, curved around a center "button."</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeCanDown">
			<summary>Text is stretched to fill the height of the shape, with only a slight curve down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeCanUp">
			<summary>Text is stretched to fill the height of the shape, with only a slight curve up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeCascadeDown">
			<summary>Text slants up and to the right as font size decreases.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeCascadeUp">
			<summary>Text slants down and to the right as font size increases.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeChevronDown">
			<summary>Text slants up to its center point and then slants down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeChevronUp">
			<summary>Text slants down to its center point and then slants up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeCircleCurve">
			<summary>Text follows a circle, reading clockwise.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeCirclePour">
			<summary>Text has a 3-D effect and follows a circle, reading clockwise.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeCurveDown">
			<summary>Text curves down and to the right as font size decreases.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeCurveUp">
			<summary>Text curves down and to the right as font size increases.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeDeflate">
			<summary>Font size decreases to the text's midpoint, then increases to the starting size.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeDeflateBottom">
			<summary>Font size decreases to the text's midpoint, then increases to the starting size, while keeping the top of the text along the same curve.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeDeflateInflate">
			<summary>Font size increases to the text's midpoint, then decreases to the starting size.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeDeflateInflateDeflate">
			<summary>Font size decreases, increases, and decreases again across the text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeDeflateTop">
			<summary>Font size decreases to the text's midpoint, then increases to the starting size, while keeping the bottom of the text along the same curve.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeDoubleWave1">
			<summary>Text follows a line that curves up, then down, then up and down again.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeDoubleWave2">
			<summary>Text follows a line that curves down, then up, then down and up again.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeFadeDown">
			<summary>Top of the text appears to be closer to the viewer than bottom of the text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeFadeLeft">
			<summary>Left side of text appears to be closer to the viewer than right side.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeFadeRight">
			<summary>Right side of text appears to be closer to the viewer than left side.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeFadeUp">
			<summary>Bottom of text appears to be closer to the viewer than top.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeInflate">
			<summary>Font size of text increases to its center point, then decreases. Center point of each letter is on the same straight line. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeInflateBottom">
			<summary>Font size of text increases to its center point, then decreases. Center point of each letter follows an arch that curves downward.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeInflateTop">
			<summary>Font size of text increases to its center point, then decreases. Center point of each letter follows an arch that curves upward.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeMixed">
			<summary>Not used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapePlainText">
			<summary>No shape applied.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeRingInside">
			<summary>Text appears to be written on the inside of a 3-D ring.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeRingOutside">
			<summary>Text appears to be written on the outside of a 3-D ring.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeSlantDown">
			<summary>Text slants down and to the right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeSlantUp">
			<summary>Text slants up and to the right.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeStop">
			<summary>Text follows the shape of a stop sign.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeTriangleDown">
			<summary>Text slants up, then down.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeTriangleUp">
			<summary>Text slants down, then up.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeWave1">
			<summary>Text follows a wave up, then down and up again.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTextEffectShape.msoTextEffectShapeWave2">
			<summary>Text follows a wave down, then up and down again.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetTexture">
			<summary>Specifies texture to be used to fill a shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoPresetTextureMixed">
			<summary>Not used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureBlueTissuePaper">
			<summary>Blue tissue paper texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureBouquet">
			<summary>Bouquet texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureBrownMarble">
			<summary>Brown marble texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureCanvas">
			<summary>Canvas texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureCork">
			<summary>Cork texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureDenim">
			<summary>Denim texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureFishFossil">
			<summary>Fish fossil texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureGranite">
			<summary>Granite texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureGreenMarble">
			<summary>Green marble texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureMediumWood">
			<summary>Medium wood texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureNewsprint">
			<summary>Newsprint texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureOak">
			<summary>Oak texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTexturePaperBag">
			<summary>Paper bag texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTexturePapyrus">
			<summary>Papyrus texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureParchment">
			<summary>Parchment texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTexturePinkTissuePaper">
			<summary>Pink tissue paper texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTexturePurpleMesh">
			<summary>Purple mesh texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureRecycledPaper">
			<summary>Recycled paper texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureSand">
			<summary>Sand texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureStationery">
			<summary>Stationery texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureWalnut">
			<summary>Walnut texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureWaterDroplets">
			<summary>Water droplets texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureWhiteMarble">
			<summary>White marble texture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetTexture.msoTextureWovenMat">
			<summary>Woven mat texture.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoPresetThreeDFormat">
			<summary>Specifies an extrusion (three-dimensional) format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoPresetThreeDFormatMixed">
			<summary>Not used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD1">
			<summary>First 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD10">
			<summary>Tenth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD11">
			<summary>Eleventh 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD12">
			<summary>Twelfth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD13">
			<summary>Thirteenth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD14">
			<summary>Fourteenth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD15">
			<summary>Fifteenth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD16">
			<summary>Sixteenth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD17">
			<summary>Seventeenth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD18">
			<summary>Eighteenth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD19">
			<summary>Nineteenth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD2">
			<summary>Second 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD20">
			<summary>Twentieth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD3">
			<summary>Third 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD4">
			<summary>Fourth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD5">
			<summary>Fifth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD6">
			<summary>Sixth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD7">
			<summary>Seventh 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD8">
			<summary>Eighth 3-D format.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoPresetThreeDFormat.msoThreeD9">
			<summary>Ninth 3-D format.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoRelativeNodePosition">
			<summary>Specifies where a node is added to a diagram relative to existing nodes.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoRelativeNodePosition.msoAfterLastSibling">
			<summary>Node is added after last sibling.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoRelativeNodePosition.msoAfterNode">
			<summary>Node is added after current node.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoRelativeNodePosition.msoBeforeFirstSibling">
			<summary>Node is added before first sibling.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoRelativeNodePosition.msoBeforeNode">
			<summary>Node is added before current node.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoScaleFrom">
			<summary>Specifies which part of the shape retains its position when the shape is scaled.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScaleFrom.msoScaleFromBottomRight">
			<summary>Shape's bottom right corner retains its position.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScaleFrom.msoScaleFromMiddle">
			<summary>Shape's midpoint retains its position.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScaleFrom.msoScaleFromTopLeft">
			<summary>Shape's top left corner retains its position.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoScreenSize">
			<summary>Specifies the ideal screen resolution to be used to view a document in a Web browser.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize1024x768">
			<summary>1024x768 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize1152x882">
			<summary>1152x882 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize1152x900">
			<summary>1152x900 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize1280x1024">
			<summary>1280x1024 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize1600x1200">
			<summary>1600x1200 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize1800x1440">
			<summary>1800x1440 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize1920x1200">
			<summary>1920x1200 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize544x376">
			<summary>544x376 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize640x480">
			<summary>640x480 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize720x512">
			<summary>720x512 resolution.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScreenSize.msoScreenSize800x600">
			<summary>800x600 resolution.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoScriptLanguage">
			<summary>Specifies scripting language of the active script.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScriptLanguage.msoScriptLanguageASP">
			<summary>Active Server Pages (ASP).</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScriptLanguage.msoScriptLanguageJava">
			<summary>Java.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScriptLanguage.msoScriptLanguageOther">
			<summary>A language other than ASP, Java, or Visual Basic.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScriptLanguage.msoScriptLanguageVisualBasic">
			<summary>Visual Basic.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoScriptLocation">
			<summary>Specifies the location of the script anchor within a document.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScriptLocation.msoScriptLocationInBody">
			<summary>Script anchor is in the body of the document.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoScriptLocation.msoScriptLocationInHead">
			<summary>Script anchor is in the head of the document.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSearchIn">
			<summary>Indicates the area in which the Execute method of the FileSearch object searches for files.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSearchIn.msoSearchInCustom">
			<summary>Searches a custom path. Custom path is defined by the LookIn property of the FileSearch object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSearchIn.msoSearchInMyComputer">
			<summary>Searches the My Computer node.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSearchIn.msoSearchInMyNetworkPlaces">
			<summary>Searches the My Network Places node.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSearchIn.msoSearchInOutlook">
			<summary>Searches Microsoft Outlook folders.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSegmentType">
			<summary>Specifies the type for a segment.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSegmentType.msoSegmentCurve">
			<summary>Curve.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSegmentType.msoSegmentLine">
			<summary>Line.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoShadowType">
			<summary>Specifies the type of shadow displayed with a shape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow1">
			<summary>First shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow10">
			<summary>Tenth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow11">
			<summary>Eleventh shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow12">
			<summary>Twelfth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow13">
			<summary>Thirteenth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow14">
			<summary>Fourteenth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow15">
			<summary>Fifteenth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow16">
			<summary>Sixteenth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow17">
			<summary>Seventeenth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow18">
			<summary>Eighteenth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow19">
			<summary>Nineteenth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow2">
			<summary>Second shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow20">
			<summary>Twentieth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow3">
			<summary>Third shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow4">
			<summary>Fourth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow5">
			<summary>Fifth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow6">
			<summary>Sixth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow7">
			<summary>Seventh shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow8">
			<summary>Eighth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadow9">
			<summary>Ninth shadow type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShadowType.msoShadowMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoShapeType">
			<summary>Specifies the type of a shape or range of shapes.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoAutoShape">
			<summary>AutoShape.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoCallout">
			<summary>Callout.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoCanvas">
			<summary>Canvas.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoChart">
			<summary>Chart.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoComment">
			<summary>Comment.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoDiagram">
			<summary>Diagram.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoEmbeddedOLEObject">
			<summary>Embedded OLE object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoFormControl">
			<summary>Form control.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoFreeform">
			<summary>Freeform.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoGroup">
			<summary>Group.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoInk">
			<summary>Ink.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoInkComment">
			<summary>Ink comment.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoLine">
			<summary>Line.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoLinkedOLEObject">
			<summary>Linked OLE object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoLinkedPicture">
			<summary>Linked picture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoMedia">
			<summary>Media.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoOLEControlObject">
			<summary>OLE control object.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoPicture">
			<summary>Picture.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoPlaceholder">
			<summary>Placeholder.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoScriptAnchor">
			<summary>Script anchor.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoShapeTypeMixed">
			<summary>Return value only; indicates a combination of the other states.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoTable">
			<summary>Table.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoTextBox">
			<summary>Text box.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoShapeType.msoTextEffect">
			<summary>Text effect.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSharedWorkspaceTaskPriority">
			<summary>Specifies the priority for a shared workspace task.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSharedWorkspaceTaskPriority.msoSharedWorkspaceTaskPriorityHigh">
			<summary>High priority.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSharedWorkspaceTaskPriority.msoSharedWorkspaceTaskPriorityLow">
			<summary>Low priority.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSharedWorkspaceTaskPriority.msoSharedWorkspaceTaskPriorityNormal">
			<summary>Normal priority.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSharedWorkspaceTaskStatus">
			<summary>Specifies the status of a shared workspace task.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSharedWorkspaceTaskStatus.msoSharedWorkspaceTaskStatusCompleted">
			<summary>Completed.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSharedWorkspaceTaskStatus.msoSharedWorkspaceTaskStatusDeferred">
			<summary>Deferred.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSharedWorkspaceTaskStatus.msoSharedWorkspaceTaskStatusInProgress">
			<summary>In progress.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSharedWorkspaceTaskStatus.msoSharedWorkspaceTaskStatusNotStarted">
			<summary>Not started.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSharedWorkspaceTaskStatus.msoSharedWorkspaceTaskStatusWaiting">
			<summary>Waiting.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSortBy">
			<summary>Specifies sort order for files in a FileSearch object's FoundFiles collection.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSortBy.msoSortByFileName">
			<summary>File name.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSortBy.msoSortByFileType">
			<summary>File type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSortBy.msoSortByLastModified">
			<summary>Last modified date.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSortBy.msoSortByNone">
			<summary>No sort.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSortBy.msoSortBySize">
			<summary>File size.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSortOrder">
			<summary>Specifies whether files in a FileSearch object's FoundFiles collection should be sorted in ascending or descending order.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSortOrder.msoSortOrderAscending">
			<summary>Ascending order.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSortOrder.msoSortOrderDescending">
			<summary>Descending order.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSyncAvailableType">
			<summary>Specifies whether and under what circumstances synchronization is available for the document.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncAvailableType.msoSyncAvailableAnywhere">
			<summary>Synchronization is available offline and online.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncAvailableType.msoSyncAvailableNone">
			<summary>No synchronization is available.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncAvailableType.msoSyncAvailableOffline">
			<summary>Synchronization is available offline only.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSyncCompareType">
			<summary>Specifies how comparison between local copy and server copy should be done in a synchronization process.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncCompareType.msoSyncCompareAndMerge">
			<summary>Compare and merge versions.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncCompareType.msoSyncCompareSideBySide">
			<summary>Compare versions side-by-side.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSyncConflictResolutionType">
			<summary>Specifies how conflicts should be resolved when synchronizing a shared document.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncConflictResolutionType.msoSyncConflictClientWins">
			<summary>Replace the server copy with the local copy.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncConflictResolutionType.msoSyncConflictMerge">
			<summary>Merge changes made to the server copy into the local copy. In order to resolve the conflict with the merged changes winning, you must save the active document after merging changes, then call the ResolveConflict method again with the <see cref="F:Microsoft.Office.Core.MsoSyncConflictResolutionType.msoSyncConflictClientWins"></see> option.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncConflictResolutionType.msoSyncConflictServerWins">
			<summary>Replace the local copy with the server copy.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSyncErrorType">
			<summary>Specifies a document synchronization error.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorCouldNotCompare">
			<summary>Source and destination files could not be compared.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorCouldNotConnect">
			<summary>Could not connect.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorCouldNotOpen">
			<summary>Could not open file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorCouldNotResolve">
			<summary>Could not resolve files.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorCouldNotUpdate">
			<summary>Could not update destination file.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorFileInUse">
			<summary>Destination file in use.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorFileNotFound">
			<summary>Destination file not found.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorFileTooLarge">
			<summary>File too large to synchronize.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorNone">
			<summary>No error.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorNoNetwork">
			<summary>No network available.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorOutOfSpace">
			<summary>Out of space.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorUnauthorizedUser">
			<summary>Unauthorized user.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorUnknown">
			<summary>Unknown error.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorUnknownDownload">
			<summary>Download error.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorUnknownUpload">
			<summary>Upload error.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorVirusDownload">
			<summary>Virus downloaded.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncErrorType.msoSyncErrorVirusUpload">
			<summary>Virus uploaded.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSyncEventType">
			<summary>Specifies the return value of a Sync event.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncEventType.msoSyncEventDownloadFailed">
			<summary>Download failed.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncEventType.msoSyncEventDownloadInitiated">
			<summary>Download initiated.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncEventType.msoSyncEventDownloadNoChange">
			<summary>No change detected.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncEventType.msoSyncEventDownloadSucceeded">
			<summary>Download succeeded.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncEventType.msoSyncEventOffline">
			<summary>Offline.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncEventType.msoSyncEventUploadFailed">
			<summary>Upload failed.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncEventType.msoSyncEventUploadInitiated">
			<summary>Upload initiated.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncEventType.msoSyncEventUploadSucceeded">
			<summary>Upload succeeded.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSyncStatusType">
			<summary>Specifies the status of the synchronization of the local copy of the active document with the server copy.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncStatusType.msoSyncStatusConflict">
			<summary>Both the local and the server copies have changes.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncStatusType.msoSyncStatusError">
			<summary>An error occurred. Use ErrorType property of Sync object to determine exact error.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncStatusType.msoSyncStatusLatest">
			<summary>Documents are already in sync.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncStatusType.msoSyncStatusLocalChanges">
			<summary>Only local copy has changes.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncStatusType.msoSyncStatusNewerAvailable">
			<summary>Only server copy has changes.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncStatusType.msoSyncStatusNoSharedWorkspace">
			<summary>No shared workspace.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncStatusType.msoSyncStatusSuspended">
			<summary>Synchronization was suspended. You can use the Unsuspend method of the Sync object to resume synchronization.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoSyncVersionType">
			<summary>Specifies which version of a shared document to open alongside the currently open local version.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncVersionType.msoSyncVersionLastViewed">
			<summary>Opens the copy of the document that is created whenever the user overwrites the local copy with the server copy.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoSyncVersionType.msoSyncVersionServer">
			<summary>Opens the server version. </summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoTargetBrowser">
			<summary>Specifies target browser for documents viewed in a Web browser.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTargetBrowser.msoTargetBrowserIE4">
			<summary>Microsoft Internet Explorer 4.0.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTargetBrowser.msoTargetBrowserIE5">
			<summary>Microsoft Internet Explorer 5.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTargetBrowser.msoTargetBrowserIE6">
			<summary>Microsoft Internet Explorer 6.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTargetBrowser.msoTargetBrowserV3">
			<summary>Netscape Navigator 3.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTargetBrowser.msoTargetBrowserV4">
			<summary>Netscape Navigator 4.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoTextEffectAlignment">
			<summary>Specifies alignment for WordArt text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextEffectAlignment.msoTextEffectAlignmentCentered">
			<summary>Centered.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextEffectAlignment.msoTextEffectAlignmentLeft">
			<summary>Left-aligned.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextEffectAlignment.msoTextEffectAlignmentLetterJustify">
			<summary>Text is justified. Spacing between letters may be adjusted to justify text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextEffectAlignment.msoTextEffectAlignmentMixed">
			<summary>Not used.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextEffectAlignment.msoTextEffectAlignmentRight">
			<summary>Right- aligned.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextEffectAlignment.msoTextEffectAlignmentStretchJustify">
			<summary>Text is justified. Letters may be stretched to justify text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextEffectAlignment.msoTextEffectAlignmentWordJustify">
			<summary>Text is justified. Spacing between words (but not letters) may be adjusted to justify text.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoTextOrientation">
			<summary>Specifies orientation for text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextOrientation.msoTextOrientationDownward">
			<summary>Downward.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextOrientation.msoTextOrientationHorizontal">
			<summary>Horizontal.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextOrientation.msoTextOrientationHorizontalRotatedFarEast">
			<summary>Horizontal and rotated as required for Far East language support.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextOrientation.msoTextOrientationMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextOrientation.msoTextOrientationUpward">
			<summary>Upward.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextOrientation.msoTextOrientationVertical">
			<summary>Vertical.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextOrientation.msoTextOrientationVerticalFarEast">
			<summary>Vertical as required for Far East language support.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoTextureType">
			<summary>Specifies the texture type for the selected fill.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextureType.msoTexturePreset">
			<summary>Preset texture type.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextureType.msoTextureTypeMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTextureType.msoTextureUserDefined">
			<summary>User-defined texture type.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoTriState">
			<summary>Specifies a tri-state Boolean value.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTriState.msoCTrue">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTriState.msoFalse">
			<summary>False.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTriState.msoTriStateMixed">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTriState.msoTriStateToggle">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoTriState.msoTrue">
			<summary>True.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoVerticalAnchor">
			<summary>Specifies the vertical alignment of text in a text frame.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorBottom">
			<summary>Aligns text to bottom of text frame.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorBottomBaseLine">
			<summary>Anchors bottom of text string to current position, regardless of text resizing. When you resize text without baseline anchoring, text centers itself on previous position.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorMiddle">
			<summary>Centers text vertically.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorTop">
			<summary>Aligns text to top of text frame.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoVerticalAnchor.msoAnchorTopBaseline">
			<summary>Anchors bottom of text string to current position, regardless of text resizing. When you resize text without baseline anchoring, text centers itself on previous position.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoVerticalAnchor.msoVerticalAnchorMixed">
			<summary>Return value only; indicates a combination of the other states. </summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoWizardActType">
			<summary>Specifies the change to the Office Assistant Help session.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardActType.msoWizardActActive">
			<summary>Make Office Assistant active.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardActType.msoWizardActInactive">
			<summary>Make Office Assistant inactive.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardActType.msoWizardActResume">
			<summary>Resume Office Assistant.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardActType.msoWizardActSuspend">
			<summary>Suspend Office Assistant.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoWizardMsgType">
			<summary>Specifies context under which a wizard's callback procedure is called.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardMsgType.msoWizardMsgLocalStateOff">
			<summary>User clicked the right button in the decision or branch balloon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardMsgType.msoWizardMsgLocalStateOn">
			<summary>Not supported.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardMsgType.msoWizardMsgResuming">
			<summary>Passed to the ActivateWizard method if msoWizardActResume is specified for the Act argument.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardMsgType.msoWizardMsgShowHelp">
			<summary>User clicked the left button in the decision or branch balloon.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoWizardMsgType.msoWizardMsgSuspending">
			<summary>Passed to the ActivateWizard method if msoWizardActSuspend is specified for the Act argument.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.MsoZOrderCmd">
			<summary>Specifies where in the z-order a shape should be moved relative to other shapes.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoZOrderCmd.msoBringForward">
			<summary>Bring shape forward.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoZOrderCmd.msoBringInFrontOfText">
			<summary>Bring shape in front of text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoZOrderCmd.msoBringToFront">
			<summary>Bring shape to the front.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoZOrderCmd.msoSendBackward">
			<summary>Send shape backward.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoZOrderCmd.msoSendBehindText">
			<summary>Send shape behind text.</summary>
		</member>
		<member name="F:Microsoft.Office.Core.MsoZOrderCmd.msoSendToBack">
			<summary>Send shape to the back.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.NewFile">
			<summary>The NewFile object represents items listed on the New Item task pane available in several Microsoft Office applications.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.NewFile.Add(System.String,System.Object,System.Object,System.Object)">
			<summary>Adds a new item to the New Item task pane.</summary>
			<param name="Section">Optional Object. The section to which to add the file. Can be any <see cref="T:Microsoft.Office.Core.MsoFileNewSection"></see> constant.</param>
			<param name="FileName">Required String. The name of the file to add to the list of files on the task pane.</param>
			<param name="Action">Optional Object. The action to take when a user clicks on the item. Can be any <see cref="T:Microsoft.Office.Core.MsoFileNewAction"></see> constant.</param>
			<param name="DisplayName">Optional Object. The text to display in the task pane.</param>
		</member>
		<member name="M:Microsoft.Office.Core.NewFile.Remove(System.String,System.Object,System.Object,System.Object)">
			<summary>Removes an item from the New Item task pane.</summary>
			<param name="Section">Optional Object. The section of the task pane where the file reference exists. Can be any <see cref="T:Microsoft.Office.Core.MsoFileNewSection"></see> constant.</param>
			<param name="FileName">Required String. The name of the file reference.</param>
			<param name="Action">Optional Object. The action taken when a user clicks on the item. Can be any <see cref="T:Microsoft.Office.Core.MsoFileNewAction"></see> constant.</param>
			<param name="DisplayName">Optional Object. The display text of the file reference.</param>
		</member>
		<member name="T:Microsoft.Office.Core.ODSOColumn">
			<summary>Represents a field in a data source.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.ODSOColumns">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.ODSOColumn"></see> objects that represent the data fields in a mail merge data source.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.ODSOColumns.Item(System.Object)">
			<summary>Returns a member of the specified <see cref="T:Microsoft.Office.Core.ODSOColumns"></see> collection.</summary>
			<param name="varIndex">Required Object. The name or index number of the ODSOColumn item to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.ODSOFilter">
			<summary>Represents a filter to be applied to an attached mail merge data source.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.ODSOFilters">
			<summary>Represents all the filters to apply to the data source attached to the mail merge publication.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.ODSOFilters.Add(System.String,Microsoft.Office.Core.MsoFilterComparison,Microsoft.Office.Core.MsoFilterConjunction,System.String,System.Boolean)">
			<summary>Adds a new filter to the <see cref="T:Microsoft.Office.Core.ODSOFilters"></see> collection.</summary>
			<param name="bstrCompareTo">Optional String. If the Comparison argument is something other than msoFilterComparisonIsBlank or msoFilterComparisonIsNotBlank, a string to which the data in the table is compared.</param>
			<param name="Conjunction">Required <see cref="T:Microsoft.Office.Core.MsoFilterConjunction"></see>. Determines how this filter relates to other filters in the ODSOFilters object.</param>
			<param name="DeferUpdate">Optional Boolean. Default value is False.</param>
			<param name="Column">Required String. The name of the table in the data source.</param>
			<param name="Comparison">Required <see cref="T:Microsoft.Office.Core.MsoFilterComparison"></see>. How the data in the table is filtered.</param>
		</member>
		<member name="M:Microsoft.Office.Core.ODSOFilters.Delete(System.Int32,System.Boolean)">
			<summary>Deletes a filter object from the <see cref="T:Microsoft.Office.Core.ODSOFilters"></see> collection.</summary>
			<param name="Index">Required Integer. The number of the filter to delete.</param>
			<param name="DeferUpdate">Optional Boolean.</param>
		</member>
		<member name="M:Microsoft.Office.Core.ODSOFilters.Item(System.Int32)">
			<summary>Returns a member of the specified <see cref="T:Microsoft.Office.Core.ODSOFilters"></see> collection.</summary>
			<param name="Index">Required Integer. The index number of the ODSOFilter to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.OfficeDataSourceObject">
			<summary>Represents the mail merge data source in a mail merge operation.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.OfficeDataSourceObject.Move(Microsoft.Office.Core.MsoMoveRow,System.Int32)">
			<summary>Moves the focus to a specified row in a mail merge data source.</summary>
			<param name="MsoMoveRow">Required <see cref="T:Microsoft.Office.Core.MsoMoveRow"></see>. The row that receives the focus.</param>
			<param name="RowNbr">Optional Integer. The row number of the row that receives the focus.</param>
		</member>
		<member name="M:Microsoft.Office.Core.OfficeDataSourceObject.Open(System.String,System.String,System.String,System.Int32,System.Int32)">
			<summary>Opens a connection to a mail merge data source.</summary>
			<param name="bstrTable">Optional String. The name of a table in the data source.</param>
			<param name="bstrConnect">Optional String. The connection string to the data source.</param>
			<param name="fOpenExclusive">Optional Integer. Sets whether the data source is opened for exclusive access.</param>
			<param name="fNeverPrompt">Optional Integer. Sets whether prompts are displayed.</param>
			<param name="bstrSrc">Optional String. The name of the data source.</param>
		</member>
		<member name="M:Microsoft.Office.Core.OfficeDataSourceObject.SetSortOrder(System.String,System.Boolean,System.String,System.Boolean,System.String,System.Boolean)">
			<summary>Sets the sort order for mail merge data.</summary>
			<param name="SortAscending2">Optional Boolean. True (default) to perform an ascending sort on SortField2; False to perform a descending sort.</param>
			<param name="SortAscending3">Optional Boolean. True (default) to perform an ascending sort on SortField3; False to perform a descending sort.</param>
			<param name="SortAscending1">Optional Boolean. True (default) to perform an ascending sort on SortField1; False to perform a descending sort.</param>
			<param name="SortField2">Optional String. The second field on which to sort the mail merge data. Default is an empty string.</param>
			<param name="SortField3">Optional String. The third field on which to sort the mail merge data. Default is an empty string.</param>
			<param name="SortField1">Required String. The first field on which to sort the mail merge data.</param>
		</member>
		<member name="T:Microsoft.Office.Core.Permission">
			<summary>Use the Permission object to restrict permissions to the active document and to return or set specific permissions settings.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.Permission.Add(System.String,System.Object,System.Object)">
			<summary>Creates a new set of permissions on the active document for the specified user.</summary>
			<param name="UserId">Optional Object. The email address (in <NAME_EMAIL>) of the user to whom permissions on the active document are being granted.</param>
			<param name="ExpirationDate">Required String. The expiration date for the permissions that are being granted.</param>
			<param name="Permission">Optional String. The permissions on the active document that are being granted to the specified user.</param>
		</member>
		<member name="M:Microsoft.Office.Core.Permission.ApplyPolicy(System.String)">
			<summary>Applies the specified permission policy to the active document.</summary>
			<param name="FileName">Required String. The path and filename of the permission policy template file.</param>
		</member>
		<member name="P:Microsoft.Office.Core.Permission.Item(System.Object)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.UserPermission"></see> object that is a member of the <see cref="T:Microsoft.Office.Core.Permission"></see> collection.</summary>
			<param name="Index">Optional Object. The numeric index of the UserPermission in the Permission collection, or the email address of the user whose set of permissions on the active document is to be returned.</param>
		</member>
		<member name="M:Microsoft.Office.Core.PictureFormat.IncrementBrightness(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.PictureFormat.IncrementContrast(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.PropertyTest">
			<summary>Represents a single file search criterion.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.PropertyTests">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.PropertyTest"></see> objects that represent all the search criteria of a file search. Search criteria are listed in the Advanced Find dialog box (File menu, Open command, Advanced Find button).</summary>
		</member>
		<member name="M:Microsoft.Office.Core.PropertyTests.Add(System.String,Microsoft.Office.Core.MsoCondition,System.Object,System.Object,Microsoft.Office.Core.MsoConnector)">
			<summary>Add a new <see cref="T:Microsoft.Office.Core.PropertyTest"></see> object to the <see cref="T:Microsoft.Office.Core.PropertyTests"></see> collection representing the search criteria of a file search.</summary>
			<param name="Condition">Required <see cref="T:Microsoft.Office.Core.MsoCondition"></see>. A constant representing the condition used for the search.</param>
			<param name="Value">Optional Object. A value used by condition.</param>
			<param name="SecondValue">Optional Object. A second value used by condition.</param>
			<param name="Name">Required String. The name of the PropertyTest object.</param>
			<param name="Connector">Optional MsoCondition. A constant representinat a connector such as Or or And used in the criterion.</param>
		</member>
		<member name="M:Microsoft.Office.Core.PropertyTests.Remove(System.Int32)">
			<summary>Removes the specified object from the collection.</summary>
			<param name="Index">Required Integer. The index number of the property test to be removed.</param>
		</member>
		<member name="P:Microsoft.Office.Core.PropertyTests.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.PropertyTest"></see> object from the <see cref="T:Microsoft.Office.Core.PropertyTests"></see> collection.</summary>
			<param name="Index">Optional Integer. The index number of the property test to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.ScopeFolder">
			<summary>Corresponds to a searchable folder.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.ScopeFolders">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.ScopeFolder"></see> objects.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.ScopeFolders.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.ScopeFolder"></see> object that represents a subfolder of the parent object.</summary>
			<param name="Index">Required Integer. Determines which subfolder to return.</param>
		</member>
		<member name="T:Microsoft.Office.Core.Script">
			<summary>Represents a block of HTML script in a Microsoft Word document, on a Microsoft Excel spreadsheet, or on a Microsoft PowerPoint slide.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.Scripts">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.Script"></see> objects that represent the collection of HTML scripts in the specified document.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.Scripts.Add(System.Object,Microsoft.Office.Core.MsoScriptLocation,Microsoft.Office.Core.MsoScriptLanguage,System.String,System.String,System.String)">
			<summary>Adds a <see cref="T:Microsoft.Office.Core.Script"></see> object to the <see cref="T:Microsoft.Office.Core.Scripts"></see> collection of one of the following objects: a Document or Range object in Microsoft Word; a Worksheet or Chart object in Microsoft Excel; or a Slide, SlideRange, slide Master, or title Master object in Microsoft PowerPoint.</summary>
			<param name="ScriptText">Optional String. Specifies the text contained in a block of script. The default is the empty string. The Microsoft Office host application doesn’t check the syntax of the script.</param>
			<param name="Extended">Optional String. Specifies attributes that are to be added to the &lt;SCRIPT&gt; tag (LANGUAGE and ID attributes are exported through the Language and Id parameters and should not be exported through the Extended parameter). The default is the empty string. Attributes are separated by spaces, the same as in HTML. The Microsoft Office host application doesn’t provide any means of checking the syntax of passed attributes.</param>
			<param name="Anchor">Optional Object (Microsoft Excel only). The Anchor argument accepts an Excel Range object, which specifies the placement of the script anchor on an Excel Worksheet. You cannot insert script anchors into Excel charts.</param>
			<param name="Id">Optional String. The ID of the &lt;SCRIPT&gt; tag in HTML. The Id argument specifies an SGML identifier used for naming elements. Valid identifiers include any string that begins with a letter and is composed of alphanumeric characters; the string can also include the underscore character ( _ ). The ID must be unique within the HTML document. This parameter is exported as the ID attribute in the &lt;SCRIPT&gt; tag.</param>
			<param name="Location">Optional <see cref="T:Microsoft.Office.Core.MsoScriptLocation"></see>. Specifies the location of the script anchor in a document. If you’ve specified the Anchor argument, the Location argument isn’t used; the location of the Anchor argument determines the location of the script anchor.</param>
			<param name="Language">Optional <see cref="T:Microsoft.Office.Core.MsoScriptLanguage"></see>. Specifies the script language.</param>
		</member>
		<member name="M:Microsoft.Office.Core.Scripts.Delete">
			<summary>Deletes the specified object from the collection.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.Scripts.Item(System.Object)">
			<summary>Returns a member of the <see cref="T:Microsoft.Office.Core.Scripts"></see> collection.</summary>
			<param name="Index">Required Object. The ID or index number of the script to be returned.</param>
		</member>
		<member name="T:Microsoft.Office.Core.SearchFolders">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.ScopeFolder"></see> objects that determines which folders are searched when the <see cref="M:Microsoft.Office.Core.FileSearch.Execute(Microsoft.Office.Core.MsoSortBy,Microsoft.Office.Core.MsoSortOrder,System.Boolean)"></see> method of the <see cref="T:Microsoft.Office.Core.FileSearch"></see> object is called.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SearchFolders.Add(Microsoft.Office.Core.ScopeFolder)">
			<summary>Adds a search folder to a file search.</summary>
			<param name="ScopeFolder">Required <see cref="T:Microsoft.Office.Core.ScopeFolder"></see>. The folder to add to the search.</param>
		</member>
		<member name="M:Microsoft.Office.Core.SearchFolders.Remove(System.Int32)">
			<summary>Removes the specified object from the collection.</summary>
			<param name="Index">Required Integer. The index number of the property test to be removed.</param>
		</member>
		<member name="P:Microsoft.Office.Core.SearchFolders.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.ScopeFolder"></see> object that represents a subfolder of the parent object.</summary>
			<param name="Index">Required Integer. Determines which subfolder to return.</param>
		</member>
		<member name="T:Microsoft.Office.Core.SearchScope">
			<summary>Corresponds to a type of folder tree that can be searched by using the <see cref="T:Microsoft.Office.Core.FileSearch"></see> object.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.SearchScopes">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.SearchScope"></see> objects.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.SearchScopes.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.SearchScope"></see> object that corresponds to an area in which to perform a file search, such as local drives or Microsoft Outlook folders.</summary>
			<param name="Index">Required Integer. Determines which SearchScope object to return.</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShadowFormat.IncrementOffsetX(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShadowFormat.IncrementOffsetY(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.CanvasCropBottom(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.CanvasCropLeft(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.CanvasCropRight(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.CanvasCropTop(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.Flip(Microsoft.Office.Core.MsoFlipCmd)">
			<param name="FlipCmd">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.IncrementLeft(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.IncrementRotation(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.IncrementTop(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.ScaleHeight(System.Single,Microsoft.Office.Core.MsoTriState,Microsoft.Office.Core.MsoScaleFrom)">
			<param name="RelativeToOriginalSize">hiddenmemberparam</param>
			<param name="Factor">hiddenmemberparam</param>
			<param name="fScale">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.ScaleWidth(System.Single,Microsoft.Office.Core.MsoTriState,Microsoft.Office.Core.MsoScaleFrom)">
			<param name="RelativeToOriginalSize">hiddenmemberparam</param>
			<param name="Factor">hiddenmemberparam</param>
			<param name="fScale">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.Select(System.Object)">
			<param name="Replace">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shape.ZOrder(Microsoft.Office.Core.MsoZOrderCmd)">
			<param name="ZOrderCmd">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeNodes.Delete(System.Int32)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeNodes.Insert(System.Int32,Microsoft.Office.Core.MsoSegmentType,Microsoft.Office.Core.MsoEditingType,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
			<param name="EditingType">hiddenmemberparam</param>
			<param name="X2">hiddenmemberparam</param>
			<param name="X3">hiddenmemberparam</param>
			<param name="Index">hiddenmemberparam</param>
			<param name="Y3">hiddenmemberparam</param>
			<param name="X1">hiddenmemberparam</param>
			<param name="Y1">hiddenmemberparam</param>
			<param name="SegmentType">hiddenmemberparam</param>
			<param name="Y2">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeNodes.Item(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeNodes.SetEditingType(System.Int32,Microsoft.Office.Core.MsoEditingType)">
			<param name="EditingType">hiddenmemberparam</param>
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeNodes.SetPosition(System.Int32,System.Single,System.Single)">
			<param name="Index">hiddenmemberparam</param>
			<param name="X1">hiddenmemberparam</param>
			<param name="Y1">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeNodes.SetSegmentType(System.Int32,Microsoft.Office.Core.MsoSegmentType)">
			<param name="Index">hiddenmemberparam</param>
			<param name="SegmentType">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.Align(Microsoft.Office.Core.MsoAlignCmd,Microsoft.Office.Core.MsoTriState)">
			<param name="AlignCmd">hiddenmemberparam</param>
			<param name="RelativeTo">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.CanvasCropBottom(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.CanvasCropLeft(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.CanvasCropRight(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.CanvasCropTop(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.Distribute(Microsoft.Office.Core.MsoDistributeCmd,Microsoft.Office.Core.MsoTriState)">
			<param name="DistributeCmd">hiddenmemberparam</param>
			<param name="RelativeTo">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.Flip(Microsoft.Office.Core.MsoFlipCmd)">
			<param name="FlipCmd">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.IncrementLeft(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.IncrementRotation(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.IncrementTop(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.Item(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.ScaleHeight(System.Single,Microsoft.Office.Core.MsoTriState,Microsoft.Office.Core.MsoScaleFrom)">
			<param name="RelativeToOriginalSize">hiddenmemberparam</param>
			<param name="Factor">hiddenmemberparam</param>
			<param name="fScale">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.ScaleWidth(System.Single,Microsoft.Office.Core.MsoTriState,Microsoft.Office.Core.MsoScaleFrom)">
			<param name="RelativeToOriginalSize">hiddenmemberparam</param>
			<param name="Factor">hiddenmemberparam</param>
			<param name="fScale">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.Select(System.Object)">
			<param name="Replace">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ShapeRange.ZOrder(Microsoft.Office.Core.MsoZOrderCmd)">
			<param name="ZOrderCmd">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddCallout(Microsoft.Office.Core.MsoCalloutType,System.Single,System.Single,System.Single,System.Single)">
			<param name="Type">hiddenmemberparam</param>
			<param name="Height">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddCanvas(System.Single,System.Single,System.Single,System.Single)">
			<param name="Height">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddConnector(Microsoft.Office.Core.MsoConnectorType,System.Single,System.Single,System.Single,System.Single)">
			<param name="BeginX">hiddenmemberparam</param>
			<param name="Type">hiddenmemberparam</param>
			<param name="EndY">hiddenmemberparam</param>
			<param name="EndX">hiddenmemberparam</param>
			<param name="BeginY">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddCurve(System.Object)">
			<param name="SafeArrayOfPoints">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddDiagram(Microsoft.Office.Core.MsoDiagramType,System.Single,System.Single,System.Single,System.Single)">
			<param name="Type">hiddenmemberparam</param>
			<param name="Height">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddLabel(Microsoft.Office.Core.MsoTextOrientation,System.Single,System.Single,System.Single,System.Single)">
			<param name="Height">hiddenmemberparam</param>
			<param name="Orientation">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddLine(System.Single,System.Single,System.Single,System.Single)">
			<param name="BeginX">hiddenmemberparam</param>
			<param name="EndY">hiddenmemberparam</param>
			<param name="EndX">hiddenmemberparam</param>
			<param name="BeginY">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddPicture(System.String,Microsoft.Office.Core.MsoTriState,Microsoft.Office.Core.MsoTriState,System.Single,System.Single,System.Single,System.Single)">
			<param name="Width">hiddenmemberparam</param>
			<param name="FileName">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Height">hiddenmemberparam</param>
			<param name="LinkToFile">hiddenmemberparam</param>
			<param name="SaveWithDocument">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddPolyline(System.Object)">
			<param name="SafeArrayOfPoints">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddShape(Microsoft.Office.Core.MsoAutoShapeType,System.Single,System.Single,System.Single,System.Single)">
			<param name="Type">hiddenmemberparam</param>
			<param name="Height">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddTextbox(Microsoft.Office.Core.MsoTextOrientation,System.Single,System.Single,System.Single,System.Single)">
			<param name="Height">hiddenmemberparam</param>
			<param name="Orientation">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="Width">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.AddTextEffect(Microsoft.Office.Core.MsoPresetTextEffect,System.String,System.String,System.Single,Microsoft.Office.Core.MsoTriState,Microsoft.Office.Core.MsoTriState,System.Single,System.Single)">
			<param name="PresetTextEffect">hiddenmemberparam</param>
			<param name="Text">hiddenmemberparam</param>
			<param name="FontSize">hiddenmemberparam</param>
			<param name="Left">hiddenmemberparam</param>
			<param name="FontItalic">hiddenmemberparam</param>
			<param name="FontBold">hiddenmemberparam</param>
			<param name="Top">hiddenmemberparam</param>
			<param name="FontName">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.BuildFreeform(Microsoft.Office.Core.MsoEditingType,System.Single,System.Single)">
			<param name="EditingType">hiddenmemberparam</param>
			<param name="X1">hiddenmemberparam</param>
			<param name="Y1">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.Item(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.Shapes.Range(System.Object)">
			<param name="Index">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspace">
			<summary>A SharedWorkspace object allows the developer to add the active document to a Microsoft Windows SharePoint Services document workspace on the server and to manage other objects in the shared workspace.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SharedWorkspace.CreateNew(System.Object,System.Object)">
			<summary>Creates a new document workspace on the server and adds the active document to the new shared workspace.</summary>
			<param name="URL">Optional String. The URL for the parent folder in which the new shared workspace is to be created. If you do not supply a URL, the new shared workspace is created in the user's default server location.</param>
			<param name="Name">Optional String. The name of the new shared workspace. Defaults to the name of the active document without its file extension. For example, if you create a shared workspace for Budget.xls, the name of the new shared workspace becomes "Budget".</param>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceFile">
			<summary>The SharedWorkspaceFile object represents a file saved in a shared document workspace.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceFiles">
			<summary>A collection of the <see cref="T:Microsoft.Office.Core.SharedWorkspaceFile"></see> objects in the current shared workspace.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SharedWorkspaceFiles.Add(System.String,System.Object,System.Object,System.Object)">
			<summary>Adds a file to the document library in a shared workspace.</summary>
			<param name="OverwriteIfFileAlreadyExists">Optional Boolean. True to overwrite an existing file by the same name. Default is False.</param>
			<param name="FileName">Required String. The path and filename of the file to be added to the current shared workspace.</param>
			<param name="KeepInSync">Optional Boolean. True to keep the local copy of the document synchronized with the copy in the shared workspace. Default is False.</param>
			<param name="ParentFolder">Optional <see cref="T:Microsoft.Office.Core.SharedWorkspaceFolder"></see>. The subfolder in which to place the file, if not the main document library folder within the shared workspace. Add the file to the main document library folder by leaving this optional argument empty.</param>
		</member>
		<member name="P:Microsoft.Office.Core.SharedWorkspaceFiles.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.SharedWorkspaceFile"></see> object from the Files collection of the shared workspace.</summary>
			<param name="Index">Optional Integer. Returns the SharedWorkspaceFile at the position specified. Index does not correspond to the order in which the items are displayed in the Shared Workspace pane, and is not affected by re-sorting the display.</param>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceFolder">
			<summary>The SharedWorkspaceFolder object represents a folder in a shared document workspace.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SharedWorkspaceFolder.Delete(System.Object)">
			<summary>Deletes the current shared workspace folder and all data within it.</summary>
			<param name="DeleteEventIfFolderContainsFiles">Optional Boolean. True to delete the folder without warning even if the folder contains files. Default is False.</param>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceFolders">
			<summary>A collection of the <see cref="T:Microsoft.Office.Core.SharedWorkspaceFolder"></see> objects in the current shared workspace.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SharedWorkspaceFolders.Add(System.String,System.Object)">
			<summary>Adds a folder to the document library in a shared workspace.</summary>
			<param name="FolderName">Required String. The name of the folder to be added to the current shared workspace.</param>
			<param name="ParentFolder">Optional <see cref="T:Microsoft.Office.Core.SharedWorkspaceFolder"></see>. The subfolder in which to place the new folder, if not the main document library folder within the shared workspace. Add the folder to the main document library folder by leaving this optional argument empty.</param>
		</member>
		<member name="P:Microsoft.Office.Core.SharedWorkspaceFolders.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.SharedWorkspaceFolder"></see> object from the Folders collection of the shared workspace.</summary>
			<param name="Index">Required Integer. Returns the SharedWorkspaceFolder at the position specified. Index does not correspond to the order in which the items are displayed in the Shared Workspace pane, and is not affected by re-sorting the display.</param>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceLink">
			<summary>The SharedWorkspaceLink object represents a URL link saved in a shared document workspace.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceLinks">
			<summary>A collection of the <see cref="T:Microsoft.Office.Core.SharedWorkspaceLink"></see> objects in the current shared workspace.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SharedWorkspaceLinks.Add(System.String,System.Object,System.Object)">
			<summary>Adds a link to the list of links in a shared workspace.</summary>
			<param name="Description">Optional Object. Optional description of the link.</param>
			<param name="URL">Required Object. The URL of the web site to which a link is being added.</param>
			<param name="Notes">Optional String. Optional notes about the link.</param>
		</member>
		<member name="P:Microsoft.Office.Core.SharedWorkspaceLinks.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.SharedWorkspaceLink"></see> object from the Links collection of the shared workspace.</summary>
			<param name="Index">Required Integer. Returns the SharedWorkspaceLink at the position specified. Index does not correspond to the order in which the items are displayed in the Shared Workspace pane, and is not affected by re-sorting the display.</param>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceMember">
			<summary>The SharedWorkspaceMember object represents a user who has rights in a shared document workspace.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceMembers">
			<summary>A collection of the <see cref="T:Microsoft.Office.Core.SharedWorkspaceMember"></see> objects in the current shared workspace.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SharedWorkspaceMembers.Add(System.String,System.String,System.String,System.Object)">
			<summary>Adds a member to the list of members in a shared workspace.</summary>
			<param name="Role">Optional String. An optional role that determines the tasks the new member can accomplish in the shared workspace; for example, "Contributor". An invalid role name raises an error.</param>
			<param name="Email">Required String. The new member's email address in <NAME_EMAIL>. Raises an error if the user is not a valid candidate for membership in the shared workspace.</param>
			<param name="DomainName">Required String. The new member's Windows user name in the format domain\user.</param>
			<param name="DisplayName">Required String. The friendly name to display for the new member.</param>
		</member>
		<member name="P:Microsoft.Office.Core.SharedWorkspaceMembers.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.SharedWorkspaceMember"></see> object from the Members collection of the shared workspace.</summary>
			<param name="Index">Required Integer. Returns the SharedWorkspaceMember at the position specified. Index does not correspond to the order in which the items are displayed in the Shared Workspace pane.</param>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceTask">
			<summary>The SharedWorkspaceTask object represents a task in a shared document workspace.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.SharedWorkspaceTasks">
			<summary>A collection of the <see cref="T:Microsoft.Office.Core.SharedWorkspaceTask"></see> objects in the current shared workspace.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SharedWorkspaceTasks.Add(System.String,System.Object,System.Object,System.Object,System.Object,System.Object)">
			<summary>Adds a task to the list of tasks in a shared workspace and returns a <see cref="T:Microsoft.Office.Core.SharedWorkspaceTask"></see> object.</summary>
			<param name="DueDate">Optional Date. The due date of the new task.</param>
			<param name="Description">Optional String. The description of the new task.</param>
			<param name="Status">Optional <see cref="T:Microsoft.Office.Core.MsoSharedWorkspaceTaskStatus"></see>. The status of the new task. Default is msoSharedWorkspaceTaskStatusNotStarted.</param>
			<param name="Title">Required String. The title of the new task.</param>
			<param name="Assignee">Optional <see cref="T:Microsoft.Office.Core.SharedWorkspaceMember"></see>. The member to whom the new task is assigned.</param>
			<param name="Priority">Optional <see cref="T:Microsoft.Office.Core.MsoSharedWorkspaceTaskPriority"></see>. The priority of the new task. Default is msoSharedWorkspaceTaskPriorityNormal.</param>
		</member>
		<member name="P:Microsoft.Office.Core.SharedWorkspaceTasks.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.SharedWorkspaceTask"></see> object from the Tasks collection of the shared workspace.</summary>
			<param name="Index">Required Integer. Returns the SharedWorkspaceTask at the position specified. Index does not correspond to the order in which the items are displayed in the Shared Workspace pane, and is not affected by re-sorting the display.</param>
		</member>
		<member name="T:Microsoft.Office.Core.Signature">
			<summary>Corresponds to a digital signature that is attached to a document.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.SignatureSet">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.Signature"></see> objects that correspond to the digital signatures attached to a document.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.SignatureSet.Item(System.Int32)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.Signature"></see> object that corresponds to one of the digital signatures with which the document is currently signed.</summary>
			<param name="iSig">Required Integer. Determines which Signature object to return.</param>
		</member>
		<member name="T:Microsoft.Office.Core.SmartDocument">
			<summary>The SmartDocument property of the Microsoft Office Word 2003 Document object and the Microsoft Office Excel 2003 Workbook object returns a SmartDocument object.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.SmartDocument.PickSolution(System.Boolean)">
			<summary>Displays a dialog box which allows the user to choose an available XML expansion pack to attach to the active Microsoft Office Word 2003 document or Microsoft Office Excel 2003 workbook.</summary>
			<param name="ConsiderAllSchemas">Optional Boolean. True displays all available XML expansion packs installed on the user's computer. False displays only XML expansion packs applicable to the active document. Default value is False.</param>
		</member>
		<member name="T:Microsoft.Office.Core.Sync">
			<summary>Use the Sync object to manage the synchronization of the local and server copies of a shared document stored in a Windows SharePoint Services document workspace.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.Sync.OpenVersion(Microsoft.Office.Core.MsoSyncVersionType)">
			<summary>Opens a different version of the shared document alongside the currently open local version.</summary>
			<param name="SyncVersionType">Required <see cref="T:Microsoft.Office.Core.MsoSyncVersionType"></see>.</param>
		</member>
		<member name="M:Microsoft.Office.Core.Sync.ResolveConflict(Microsoft.Office.Core.MsoSyncConflictResolutionType)">
			<summary>Resolves conflicts between the local and the server copies of a shared document.</summary>
			<param name="SyncConflictResolution">Required <see cref="T:Microsoft.Office.Core.MsoSyncConflictResolutionType"></see>.</param>
		</member>
		<member name="M:Microsoft.Office.Core.ThreeDFormat.IncrementRotationX(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ThreeDFormat.IncrementRotationY(System.Single)">
			<param name="Increment">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ThreeDFormat.SetExtrusionDirection(Microsoft.Office.Core.MsoPresetExtrusionDirection)">
			<param name="PresetExtrusionDirection">hiddenmemberparam</param>
		</member>
		<member name="M:Microsoft.Office.Core.ThreeDFormat.SetThreeDFormat(Microsoft.Office.Core.MsoPresetThreeDFormat)">
			<param name="PresetThreeDFormat">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.UserPermission">
			<summary>The UserPermission object associates a set of permissions on the active document with a single user and an optional expiration date.</summary>
		</member>
		<member name="M:Microsoft.Office.Core.WebComponent.SetPlaceHolderGraphic(System.String)">
			<param name="PlaceHolderGraphic">hiddenmemberparam</param>
		</member>
		<member name="T:Microsoft.Office.Core.WebPageFont">
			<summary>Represents the default font used when documents are saved as Web pages for a particular character set.</summary>
		</member>
		<member name="T:Microsoft.Office.Core.WebPageFonts">
			<summary>A collection of <see cref="T:Microsoft.Office.Core.WebPageFont"></see> objects that describe the proportional font, proportional font size, fixed-width font, and fixed-width font size used when documents are saved as Web pages.</summary>
		</member>
		<member name="P:Microsoft.Office.Core.WebPageFonts.Item(Microsoft.Office.Core.MsoCharacterSet)">
			<summary>Returns a <see cref="T:Microsoft.Office.Core.WebPageFont"></see> object from the <see cref="T:Microsoft.Office.Core.WebPageFonts"></see> collection for a particular value of <see cref="T:Microsoft.Office.Core.MsoCharacterSet"></see>.</summary>
			<param name="Index">Required <see cref="T:Microsoft.Office.Core.MsoCharacterSet"></see>. The specified character set.</param>
		</member>
	</members>
</doc>