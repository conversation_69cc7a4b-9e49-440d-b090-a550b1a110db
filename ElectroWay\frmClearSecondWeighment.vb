﻿Imports System.Configuration

Public Class frmClearSecondWeighment
    Dim cc As New Class1
    Dim functionCtrl As Object
    Dim sapConnection As Object

    Private Sub frmClearSecondWeighment_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        If dr.Read Then
            txtPlant.Text = dr("Plant_Name")
            txtCompany.Text = dr("Company_Code")
        Else
            MsgBox("IP number not mapped as ElectroWay Node ....or You are not connected with ElectroWay Server.")
        End If
        dr.Close()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtTransactionNo.Text = ""
        txtVehicleNo.Text = ""
        txtReasonforCancellation.Text = ""
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        Dim domainAndUserName As String = User_ID & "\\" & Environment.UserDomainName & "\\" & Environment.UserName
        If Trim(txtTransactionNo.Text) <> "" And Trim(txtVehicleNo.Text) <> "" Then
            If txtReasonforCancellation.Text.Trim = "" Then
                MessageBox.Show("Please Enter the Reason!")
                Exit Sub
            End If
            Dim ans = MsgBox("Are you sure ?", vbYesNo, "ElectroWay")
            If ans = vbYes Then
                If rbSecondWeight.Checked Then
                    Dim str As String = "update tbl_GE_Det set WB_Count_ID = '0', S_WT_Node_IP = '',S_WT = '0',S_WT_DoneBy = '',S_WT_DateTime = '',NET_WT = '0' where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
                    Dim strSplit As String = "update tbl_split_det set WB_Count_ID = '0', S_WT_Node_IP = '',S_WT = '0',S_WT_DoneBy = '',S_WT_DateTime = '',NET_WT = '0' where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
                    Dim strHdr As String = "Update tbl_GE_Hdr set Remarks_cancellation = '" & txtReasonforCancellation.Text.Trim & "' where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
                    Dim strLogs As String = "insert into tbl_GE_Det_Logs select *,'" & domainAndUserName & "' from tbl_ge_det where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "' "
                    Try
                        cc.Execute(strLogs)
                        cc.Execute(str)
                        cc.Execute(strSplit)
                        cc.Execute(strHdr)
                        ClearSAPWBDATA()

                    Catch ex As Exception

                    End Try
                    MsgBox("Second Weight Data cleared successfully !", vbInformation, "ElectroWay")
                    txtReasonforCancellation.Text = ""
                    txtVehicleNo.Text = ""
                    txtTransactionNo.Text = ""
                Else
                    Dim str As String = "update tbl_GE_Det set WB_Count_ID = '0', F_WT_Node_IP = '',F_WT = '0',F_WT_DoneBy = '',F_WT_DateTime = '',NET_WT = '0' where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
                    Dim strSplit As String = "update tbl_split_det set WB_Count_ID = '0', F_WT_Node_IP = '',F_WT = '0',F_WT_DoneBy = '',F_WT_DateTime = '',NET_WT = '0' where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
                    Dim strHdr As String = "Update tbl_GE_Hdr set Remarks_cancellation = '" & txtReasonforCancellation.Text.Trim & "' where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
                    Dim strLogs As String = "insert into tbl_GE_Det_Logs select *,'" & domainAndUserName & "' from tbl_ge_det where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "' "
                    Try
                        cc.Execute(strLogs)
                        cc.Execute(str)
                        cc.Execute(strSplit)
                        cc.Execute(strHdr)
                    Catch ex As Exception

                    End Try
                    MsgBox("First Weight Data cleared successfully !", vbInformation, "ElectroWay")
                    txtReasonforCancellation.Text = ""
                    txtVehicleNo.Text = ""
                    txtTransactionNo.Text = ""
                End If
            End If
        Else
            MsgBox("Invalid Transaction No. ....", vbInformation, "ElectroWay")
        End If
    End Sub
    Private Sub ClearSAPWBDATA()
        Dim GE_DET_TRAN_ID As String = ""
        Dim str As String = "select GE_HDR_TRAN_ID FROM tbl_GE_Det where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
        dr = cc.GetDataReader(str)
        Try
            While dr.Read
                GE_DET_TRAN_ID = dr(0).ToString
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        If GE_DET_TRAN_ID = "" Then
            MessageBox.Show("Please check the Transaction No.!")
            Exit Sub
        End If
        '-------------------------
        Dim funcControl, oRFC, oTrnID
        'Dim PLANT_CODE111, GE_HDRID1, Result As String
        '---------------------------
        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection


        sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
        sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
        sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
        sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
        sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
        sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
        sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
        sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")


        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            Exit Sub

        Else
            ''MsgBox "Connected ........"


            funcControl = CreateObject("SAP.Functions")
            funcControl.Connection = sapConnection
            oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
            oTrnID = oRFC.Exports("ZTR_ID")
            oTrnID.Value = GE_DET_TRAN_ID & "\" & Trim(txtPlant.Text)
            If oRFC.Call = True Then
                'If oStatus = 1 Then ' fail
                '    PostCoil = 1
                'End If
                'If oStatus = 0 Then ' successfully deleted from Zwt_bg table
                '    PostCoil = 2
                'End If
            End If
        End If
    End Sub
    Private Sub txtTransactionNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo.KeyPress
        On Error GoTo err

        If AscW(e.KeyChar) = 13 Then
            txtVehicleNo.Text = ""
            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' and Vehicle_Status  = 'IN' and Company_Code = '" & Trim(txtCompany.Text) & "'") '' and Plant_Code  = '" & Trim(Text5.Text) & "' "
            If dr.Read Then
                txtVehicleNo.Text = dr("Vehicle_No")
            Else
                MsgBox("Invalid Transaction No....", vbInformation, "ElectroWay")
                txtTransactionNo.Text = ""
                txtTransactionNo.Focus()

            End If
            dr.Close()

        End If
err:
        ''MsgBox err.Number
        If Err.Description <> "" Then
            MsgBox(Err.Number & "  " & Err.Description)
        End If
    End Sub

    Private Sub txtTransactionNo_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTransactionNo.LostFocus
        If txtTransactionNo.Text.Trim = "" Then
            Exit Sub
        End If
        On Error GoTo err


        txtVehicleNo.Text = ""
        dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID = '" & Trim(txtTransactionNo.Text) & "' and Vehicle_Status  = 'IN' and Company_Code = '" & Trim(txtCompany.Text) & "'") '' and Plant_Code  = '" & Trim(Text5.Text) & "' "
        If dr.Read Then
            txtVehicleNo.Text = dr("Vehicle_No")
        Else
            MsgBox("Invalid Transaction No....", vbInformation, "ElectroWay")
            txtTransactionNo.Text = ""
            txtTransactionNo.Focus()

        End If
        dr.Close()
err:
        ''MsgBox err.Number
        If Err.Description <> "" Then
            MsgBox(Err.Number & "  " & Err.Description)
        End If
    End Sub
End Class