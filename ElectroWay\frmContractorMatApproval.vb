﻿Public Class frmContractorMatApproval
    Dim cc As New Class1
    Private Sub frmContractorMatApproval_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        On Error GoTo err
        dtFrom.Value = Today.Date
        dtTo.Value = Today.Date

err:
        Err.Clear()
        ''-----------ListView--------------
        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False
        'ListView1.CheckBoxes = True
        'Headings
        ListView1.Columns.Add("Transaction No.")
        ListView1.Columns.Add("Entry Date Time")
        ListView1.Columns.Add("Vehicle No")
        ListView1.Columns.Add("Vehicle Type")
        ListView1.Columns.Add("Trptr Code")
        ListView1.Columns.Add("Transporter Name")
        ListView1.Columns.Add("PO No.")
        ListView1.Columns.Add("PO Item")
        ListView1.Columns.Add("SO No.")
        ListView1.Columns.Add("DO No.")
        ListView1.Columns.Add("DO Item")
        ListView1.Columns.Add("Material Code")
        ListView1.Columns.Add("Material Description")
        ListView1.Columns.Add("DO / Ch. Qty")
        ListView1.Columns.Add("Unit")
        ListView1.Columns.Add("Unloading No.")
        ListView1.Columns.Add("Ch. No.")
        ListView1.Columns.Add("Challan Date")
        ListView1.Columns.Add("Cons./LR No.")
        ListView1.Columns.Add("Cons. Date")
        ListView1.Columns.Add("WayBill No.")
        ListView1.Columns.Add("Unloading Remarks")
        ListView1.Columns.Add("Gate Pass No.")
        ListView1.Columns.Add("Vendor Code")
        ListView1.Columns.Add("Vendor Name")
        ListView1.Columns.Add("Customer Code")
        ListView1.Columns.Add("Customer Name")
        ListView1.Columns.Add("First WT (KG)")
        ListView1.Columns.Add("Second WT (KG)")
        ListView1.Columns.Add("Net WT. (KG)")
        ListView1.Columns.Add("Vehicle Status")
        ListView1.Columns.Add("Gate Out Date Time")
        ListView1.Columns.Add("Remarks (Gate IN)")
        ListView1.Columns.Add("Remarks (Gate OUT)")
        ListView1.Columns.Add("Remarks (Cancellation)")
        ListView1.Columns.Add("First WT. Note")
        ListView1.Columns.Add("Second WT. Note")
        ListView1.Columns.Add("Grouping Reference Code")
        ListView1.Columns.Add("Grouping Reference Description")
        ListView1.Columns.Add("Entry Date")
        ListView1.Columns.Add("Entry Time")
        ListView1.Columns.Add("Out Date")
        ListView1.Columns.Add("Out Time")
        ListView1.Columns.Add("Seal No")
        ListView1.Columns.Add("Party Gross WT")
        ListView1.Columns.Add("Party Tare WT")
        ListView1.Columns.Add("Party Net WT")
        ListView1.Columns.Add("F WT Date")
        ListView1.Columns.Add("F WT Time")
        ListView1.Columns.Add("S WT Date")
        ListView1.Columns.Add("S WT Time")
        For i As Integer = 0 To ListView1.Columns.Count - 1
            ListView1.Columns(i).Width = -2
        Next
        '----------------------------ListView2-------------------------------------
        ListView2.Clear()
        ListView2.View = View.Details
        ListView2.GridLines = True
        ListView2.FullRowSelect = True
        ListView2.HideSelection = False
        ListView2.MultiSelect = False
        'ListView2.CheckBoxes = True
        'Headings
        ListView2.Columns.Add("GE_DET_TRAN_ID")
        ListView2.Columns.Add("Material Code")
        ListView2.Columns.Add("Material Name")
        ListView2.Columns.Add("Ch. Qty.")
        ListView2.Columns.Add("UOM")
        ListView2.Columns.Add("Approved Qty.")
        ListView2.Columns.Add("Remarks")
        ListView2.Columns.Add("Returnable/Non - Returnable")


        For i As Integer = 0 To ListView2.Columns.Count - 1
            ListView2.Columns(i).Width = -2
        Next

    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        Try


            ListView1.Items.Clear()
            ListView2.Items.Clear()
            'Text6.Text = ""
            txtTransactionNo.Text = ""

            Dim tot_veh As Integer = 0
            Dim IN_veh As Integer = 0
            Dim OUT_veh As Integer = 0
            Dim CANC_veh As Integer = 0

            ds = cc.GetDataset("select a.TRN_ID , a.GE_HDR_ID , a.Gate_NO , a.Vehicle_No, a.Type_Of_Vehicle , a.Driver_Name, a.Driver_LIC_No , a.Transpoter_Code , a.TransporterName , a.Remarks_IN , a.Remarks_OUT , a.Vehicle_Status , a.Remarks_Cancellation , a.Entry_DoneBy , a.EntryDateTime , a.out_DateTime , a.out_DoneBy , a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT , " & _
                        "b.Unloading_No , b.PO_No , b.PO_Line_Item , b.DO_No , b.DO_Line_Item , b.SO_No , b.SO_Line_Item , b.Mat_Code , b.Mat_Desc , b.Challan_Date , b.Challan_No , DO_Challan_Qty , b.UOM , b.WayBill_No , b.Vendor_Code , b.Vendor_Name , b.Customer_Code , b.Customer_Name , b.GatePass_No , b.Unloading_Remarks , b.CN_No , b.CN_Date , b.WB_Count_ID , b.F_WT , b.F_WT_DateTime , b.S_WT , b.S_WT_DAtetime , b.NET_WT , b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code , b.Grouping_Vehicle_No , b.Grouping_Transaction_No from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and a.Type_Of_Vehicle = 'CONTITEM' and a.EntryDateTime >  '" & Format(dtFrom.Value, "yyyy-MM-dd") & " 00:00:00.000'  and   EntryDateTime < '" & Format(dtTo.Value, "yyyy-MM-dd") & " 23:59:59.999'  and a.GE_HDR_ID not in ( select Distinct GE_HDR_ID from tbl_Contractor_material_approval ) order by EntryDateTime ")
            Dim ki As Integer = ListView1.Items.Count
            For i As Integer = 0 To ds.Tables(0).Rows.Count - 1
                Dim lvi As New ListViewItem
                'ListView1.Items.Add(ki, , dr("GE_HDR_ID"))
                lvi.Text = ds.Tables(0).Rows(i).Item("GE_HDR_ID")
                ''If s_tn_no = dr("TRN_ID") Then
                'ListView1.Items(ki).SubItems.Add (1), , dr("EntryDateTime")
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("EntryDateTime"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Vehicle_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Type_Of_Vehicle"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Transpoter_Code"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("TransporterName"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("PO_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("PO_Line_Item"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("SO_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("DO_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("DO_line_Item"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Mat_Code"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Mat_Desc"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("DO_Challan_Qty"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("UOM"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Unloading_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Challan_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Challan_Date"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("CN_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("CN_Date"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("WayBill_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Unloading_Remarks"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("GatePass_NO"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Vendor_Code"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Vendor_Name"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Customer_Code"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Customer_Name"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("F_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("S_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Net_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Vehicle_Status"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Out_Datetime"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Remarks_IN"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Remarks_OUT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Remarks_Cancellation"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("F_WT_Note"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("S_WT_Note"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Grouping_Ref_Code"))
                Dim RefCode As String = ds.Tables(0).Rows(i).Item("Grouping_Ref_Code")
                Try
                    dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & RefCode & "'")
                    If dr.Read Then
                        lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Reference_Name"))
                    Else
                        lvi.SubItems.Add("")
                    End If
                Catch ex As Exception
                    lvi.SubItems.Add("")
                End Try
                dr.Close()

                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("EntryDateTime"), "dd/MMM/yyyy"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("EntryDateTime"), "HH:MM"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("Out_Datetime"), "dd/MMM/yyyy"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("Out_Datetime"), "HH:MM"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Seal_no"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Party_Gross_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Party_Tare_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Party_Net_WT"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("F_WT_DateTime"), "dd/MMM/yyyy"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("F_WT_DateTime"), "HH:MM"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("S_WT_DateTime"), "dd/MMM/yyyy"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("S_WT_DateTime"), "HH:MM"))


                Dim s_tn_no As String = String.Empty
                If s_tn_no <> ds.Tables(0).Rows(i).Item("TRN_ID") Then
                    tot_veh = tot_veh + 1

                    If ds.Tables(0).Rows(i).Item("Vehicle_Status") = "IN" Then
                        IN_veh = IN_veh + 1
                    End If

                    If ds.Tables(0).Rows(i).Item("Vehicle_Status") = "OUT" Then
                        OUT_veh = OUT_veh + 1
                    End If

                    If ds.Tables(0).Rows(i).Item("Vehicle_Status") = "C" Then
                        CANC_veh = CANC_veh + 1
                    End If
                End If

                s_tn_no = ds.Tables(0).Rows(i).Item("TRN_ID")
                '---------------------------------
                ListView1.Items.Add(lvi)
                '---------------------------------
            Next
            ds.Dispose()
            ds.Clear()

            ''''
            ''''        Text1.Text = tot_veh
            ''''        Text2.Text = IN_veh
            ''''        Text3.Text = OUT_veh
            ''''        Text4.Text = CANC_veh
            Dim C_WT As Double
            Dim T_Net_WT As Double

            For imp1 = 0 To ListView1.Items.Count - 1

                T_Net_WT = T_Net_WT + Val(Trim(ListView1.Items(imp1).SubItems(29).Text)) + 0

                If Trim(ListView1.Items(imp1).SubItems(30).Text) = "C" Then

                    C_WT = C_WT + Val(Trim(ListView1.Items(imp1).SubItems(29).Text)) + 0

                End If
            Next
            ''
            ''        Text5.Text = T_Net_WT
            ''       Text8.Text = C_WT
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try

    End Sub

    Private Sub btnInsert_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnInsert.Click
        If IsNumeric(txtApprovedQty.Text) = False Then
            MsgBox("Invalid Approved Qty !", vbInformation, "ElectroWay")
            txtApprovedQty.Text = ""
            txtApprovedQty.Focus()
            Exit Sub
            ''ElseIf Trim(Text13.Text) = "" Then

        Else
            If Val(txtApprovedQty.Text) = 0 Then
                MsgBox("Invalid Approved Qty !", vbInformation, "ElectroWay")
                txtApprovedQty.Text = ""
                txtApprovedQty.Focus()
                Exit Sub
            End If
            If Val(Trim(txtApprovedQty.Text)) > Val(Trim(txtChallanQty.Text)) Then
                MsgBox("You are not allowed to Approve more than Challan Qty.", vbInformation, "ElectroWay")
                txtApprovedQty.Text = ""
                txtApprovedQty.Focus()
                Exit Sub
            End If

            If cbReturnable.Checked = False Then
                Dim ans = MsgBox("Are you sure you want to approve contractors Non Returnable material?", vbYesNo, "ElectroWay")
                If ans = vbNo Then
                    Exit Sub
                End If
            End If

            'ki = ListView2.Items.Count + 1
            'ListView2.Items.Add(ki, , Trim(Text9.Text))
            Dim lvi As New ListViewItem
            lvi.Text = Text9.Text
            lvi.SubItems.Add(txtMaterialCode.Text)
            lvi.SubItems.Add(txtMaterialName.Text)
            lvi.SubItems.Add(txtChallanQty.Text)
            lvi.SubItems.Add(txtUOM.Text)
            lvi.SubItems.Add(txtApprovedQty.Text)
            lvi.SubItems.Add(txtRemarks.Text)
            If cbReturnable.Checked Then
                lvi.SubItems.Add("Returnable")
            Else
                lvi.SubItems.Add("Non-Returnable")
            End If

            '---------------------------------
            ListView2.Items.Add(lvi)
            '---------------------------------
            'ListView2.Items(ki).SubItems.Add (1), , Trim(Text10.Text)
            'ListView2.Items(ki).SubItems.Add (2), , Trim(Text11.Text)
            'ListView2.Items(ki).SubItems.Add (3), , Trim(Text12.Text)
            'ListView2.Items(ki).SubItems.Add (4), , Trim(Text13.Text)
            'ListView2.Items(ki).SubItems.Add (5), , Trim(Text14.Text)
            'ListView2.Items(ki).SubItems.Add (6), , Trim(Text16.Text)
            'ListView2.Items(ki).SubItems.Add (7), , IIf(Check1.Value = 1, "Returnable", "Non-Returnable")

            Text9.Text = ""
            txtMaterialCode.Text = ""
            txtMaterialName.Text = ""
            txtChallanQty.Text = ""
            txtUOM.Text = ""
            txtApprovedQty.Text = ""
            txtRemarks.Text = ""
            cbReturnable.Checked = 0

            '''''                End If
            '''''                rec1.Close
            '''''
        End If
    End Sub

    Private Sub btnUpdateApprovalDetails_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdateApprovalDetails.Click
        On Error GoTo err
        Dim ans = MsgBox("Are you sure you want to approve contractors material as per list.", vbYesNo, "ElectroWay")

        If ans = vbYes Then
            For i = 0 To ListView2.Items.Count - 1
                ''If Val(Trim(ListView2.Items(i).SubItems(5))) > 0 Then
                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "insert into tbl_Contractor_material_approval values (" & Trim(ListView2.Items(i).Text) & ", '" & Trim(txtTransactionNo.Text) & "' , '" & Trim(ListView2.Items(i).SubItems(1).Text) & "' , '" & Trim(ListView2.Items(i).SubItems(2).Text) & "' , '" & Trim(ListView2.Items(i).SubItems(3).Text) & "' , '" & Trim(ListView2.Items(i).SubItems(4).Text) & "' , '" & Trim(ListView2.Items(i).SubItems(5).Text) & "' , '" & Trim(ListView2.Items(i).SubItems(6).Text) & "' , '" & Trim(ListView2.Items(i).SubItems(7).Text) & "' , '" & User_ID & "' ,  getdate() )"
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.ExecuteNonQuery()
                ''End If
            Next
            MsgBox("Contractor Material has been Approved successfully.", vbInformation, "ElectroWay")
            ListView2.Items.Clear()
            txtTransactionNo.Text = ""
            ListView1.Items.Clear()
        End If

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub ListView1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView1.Click
        Try
            'ListView1.Items.Clear
            ListView2.Items.Clear()
            'Text6.Text = ""

            'Text7.Text = ""
            txtTransactionNo.Text = ""
            'Text7.Text = Trim(ListView1.Items(ListView1.SelectedItem.Index).SubItems(2).Text)
            txtTransactionNo.Text = Trim(ListView1.SelectedItems(0).Text)
            Text9.Text = ""
            txtMaterialCode.Text = ""
            txtMaterialName.Text = ""
            txtChallanQty.Text = ""
            txtUOM.Text = ""
            txtApprovedQty.Text = ""

            Dim tot_veh As Integer = 0
            Dim IN_veh As Integer = 0
            Dim OUT_veh As Integer = 0
            Dim CANC_veh As Integer = 0

            dr = cc.GetDataReader("select b.GE_DET_TRAN_ID , a.TRN_ID , a.GE_HDR_ID , a.Gate_NO , a.Vehicle_No, a.Type_Of_Vehicle , a.Driver_Name, a.Driver_LIC_No , a.Transpoter_Code , a.TransporterName , a.Remarks_IN , a.Remarks_OUT , a.Vehicle_Status , a.Remarks_Cancellation , a.Entry_DoneBy , a.EntryDateTime , a.out_DateTime , a.out_DoneBy , a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT , " & _
                        "b.Unloading_No , b.PO_No , b.PO_Line_Item , b.DO_No , b.DO_Line_Item , b.SO_No , b.SO_Line_Item , b.Mat_Code , b.Mat_Desc , b.Challan_Date , b.Challan_No , DO_Challan_Qty , b.UOM , b.WayBill_No , b.Vendor_Code , b.Vendor_Name , b.Customer_Code , b.Customer_Name , b.GatePass_No , b.Unloading_Remarks , b.CN_No , b.CN_Date , b.WB_Count_ID , b.F_WT , b.F_WT_DateTime , b.S_WT , b.S_WT_DAtetime , b.NET_WT , b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code , b.Grouping_Vehicle_No , b.Grouping_Transaction_No from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and a.GE_HDR_ID = '" & Trim(ListView1.SelectedItems(0).Text) & "'  and a.Remarks_IN NOT like '%Auto%IN%'  order by EntryDateTime")

            While dr.Read
                'ki = ListView2.Items.Count + 1
                'ListView2.Items.Add(ki, , dr("GE_DET_TRAN_ID"))
                '                        ListView2.Items(ki).SubItems.Add (1), , dr("Mat_Code")
                '                        ListView2.Items(ki).SubItems.Add (2), , dr("Mat_Desc")
                '                        ListView2.Items(ki).SubItems.Add (3), , dr("DO_Challan_Qty")
                '                        ListView2.Items(ki).SubItems.Add (4), , dr("UOM")
                '                        ListView2.Items(ki).SubItems.Add (5), , "0"
                '                        ListView2.Items(ki).SubItems.Add (6), , ""
                '                        ListView2.Items(ki).SubItems.Add (7), , ""

                'rec1.MoveNext()
                Dim lvi As New ListViewItem
                lvi.Text = dr("GE_DET_TRAN_ID")
                lvi.SubItems.Add(dr("Mat_Code"))
                lvi.SubItems.Add(dr("Mat_Desc"))
                lvi.SubItems.Add(dr("DO_Challan_Qty"))
                lvi.SubItems.Add(dr("UOM"))
                lvi.SubItems.Add("0")
                lvi.SubItems.Add("")
                lvi.SubItems.Add("")
                '---------------------------------
                ListView2.Items.Add(lvi)
                '---------------------------------
            End While
            dr.Close()

            ''''
            'Text6.Text = tot_veh
            ''''        Text2.Text = IN_veh
            ''''        Text3.Text = OUT_veh
            ''''        Text4.Text = CANC_veh

            Dim T_Net_WT As Double
            Dim C_WT As Double

            For imp1 = 0 To ListView1.Items.Count - 1

                T_Net_WT = T_Net_WT + Val(Trim(ListView1.Items(imp1).SubItems(29).Text)) + 0

                If Trim(ListView1.Items(imp1).SubItems(30).Text) = "C" Then

                    C_WT = C_WT + Val(Trim(ListView1.Items(imp1).SubItems(29).Text)) + 0

                End If

            Next
        Catch ex As Exception

        End Try
    End Sub
    Private Sub ListView2_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView2.DoubleClick
        Try
            Dim sel_item As Boolean
            sel_item = True
            If sel_item = True And ListView2.Items.Count > 0 Then

                If Trim(Text9.Text) = "" Then
                    Text9.Text = ListView2.SelectedItems(0).SubItems(0).Text
                    txtMaterialCode.Text = ListView2.SelectedItems(0).SubItems(1).Text
                    txtMaterialName.Text = ListView2.SelectedItems(0).SubItems(2).Text
                    txtChallanQty.Text = ListView2.SelectedItems(0).SubItems(3).Text
                    txtUOM.Text = ListView2.SelectedItems(0).SubItems(4).Text
                    txtApprovedQty.Text = ListView2.SelectedItems(0).SubItems(5).Text
                    txtRemarks.Text = ListView2.SelectedItems(0).SubItems(6).Text

                    cbReturnable.Checked = IIf((ListView2.SelectedItems(0).SubItems(7).Text = "") Or ((ListView2.SelectedItems(0).SubItems(7).Text) Like "Non*"), 0, 1)

                    ListView2.Items.Remove(ListView2.SelectedItems(0))
                    sel_item = False
                Else
                    MsgBox("You have already selected a record, pls insert first !", vbInformation, "ElectroWay")
                End If

            End If
        Catch ex As Exception

        End Try
    End Sub
End Class