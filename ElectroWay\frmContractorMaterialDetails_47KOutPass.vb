﻿Imports System.Drawing.Printing
Imports Microsoft.Office.Interop
Public Class frmContractorMaterialDetails_47KOutPass
    Dim cc As New Class1
    Dim Load1 As Boolean = False
    Dim TrnNo As String
    Public Event ItemCheck As ItemCheckEventHandler
    Private Sub frmContractorMaterialDetails_47KOutPass_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            dtFrom.Value = Today.Date
            dtTo.Value = Today.Date
            ''-----------ListView--------------
            ListView1.Clear()
            ListView1.View = View.Details
            ListView1.GridLines = True
            ListView1.FullRowSelect = True
            ListView1.HideSelection = False
            ListView1.MultiSelect = False
            ListView1.CheckBoxes = True
            'Headings
            ListView1.Columns.Add("Transaction No.")
            ListView1.Columns.Add("Entry Date Time")
            ListView1.Columns.Add("Vehicle No")
            ListView1.Columns.Add("Vehicle Type")
            ListView1.Columns.Add("Trptr Code")
            ListView1.Columns.Add("Transporter Name")
            ListView1.Columns.Add("PO No.")
            ListView1.Columns.Add("PO Item")
            ListView1.Columns.Add("SO No.")
            ListView1.Columns.Add("DO No.")
            ListView1.Columns.Add("DO Item")
            ListView1.Columns.Add("Material Code")
            ListView1.Columns.Add("Material Description")
            ListView1.Columns.Add("DO / Ch. Qty")
            ListView1.Columns.Add("Unit")
            ListView1.Columns.Add("Unloading No.")
            ListView1.Columns.Add("Ch. No.")
            ListView1.Columns.Add("Challan Date")
            ListView1.Columns.Add("Cons./LR No.")
            ListView1.Columns.Add("Cons. Date")
            ListView1.Columns.Add("WayBill No.")
            ListView1.Columns.Add("Unloading Remarks")
            ListView1.Columns.Add("Gate Pass No.")
            ListView1.Columns.Add("Vendor Code")
            ListView1.Columns.Add("Vendor Name")
            ListView1.Columns.Add("Customer Code")
            ListView1.Columns.Add("Customer Name")
            ListView1.Columns.Add("First WT (KG)")
            ListView1.Columns.Add("Second WT (KG)")
            ListView1.Columns.Add("Net WT. (KG)")
            ListView1.Columns.Add("Vehicle Status")
            ListView1.Columns.Add("Gate Out Date Time")
            ListView1.Columns.Add("Remarks (Gate IN)")
            ListView1.Columns.Add("Remarks (Gate OUT)")
            ListView1.Columns.Add("Remarks (Cancellation)")
            ListView1.Columns.Add("First WT. Note")
            ListView1.Columns.Add("Second WT. Note")
            ListView1.Columns.Add("Grouping Reference Code")
            ListView1.Columns.Add("Grouping Reference Description")
            ListView1.Columns.Add("Entry Date")
            ListView1.Columns.Add("Entry Time")
            ListView1.Columns.Add("Out Date")
            ListView1.Columns.Add("Out Time")
            ListView1.Columns.Add("Seal No")
            ListView1.Columns.Add("Party Gross WT")
            ListView1.Columns.Add("Party Tare WT")
            ListView1.Columns.Add("Party Net WT")
            ListView1.Columns.Add("F WT Date")
            ListView1.Columns.Add("F WT Time")
            ListView1.Columns.Add("S WT Date")
            ListView1.Columns.Add("S WT Time")
            For i As Integer = 0 To ListView1.Columns.Count - 1
                ListView1.Columns(i).Width = -2
            Next
            '----------------------------ListView2-------------------------------------
            ListView2.Clear()
            ListView2.View = View.Details
            ListView2.GridLines = True
            ListView2.FullRowSelect = True
            ListView2.HideSelection = False
            ListView2.MultiSelect = False
            ListView2.CheckBoxes = True
            'Headings
            ListView2.Columns.Add("GE_DET_TRAN_ID")
            ListView2.Columns.Add("Material Code")
            ListView2.Columns.Add("Material Name")
            ListView2.Columns.Add("Ch. Qty.")
            ListView2.Columns.Add("UOM")
            ListView2.Columns.Add("Approved Qty.")
            ListView2.Columns.Add("Remarks")
            ListView2.Columns.Add("Returnable/Non - Returnable")
            For i As Integer = 0 To ListView2.Columns.Count - 1
                ListView2.Columns(i).Width = -2
            Next
        Catch ex As Exception

        End Try
    End Sub

    Private Sub btnShow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShow.Click
        Load1 = False
        TrnNo = ""
        '------------------------
        Dim GE_HDR_ID As String = ""
        Dim strS As String = "select GE_HDR_ID from tbl_Contractor_Material_Return where convert(decimal,cont_mat_Ret_Id) = '" & txtOutPassSearch.Text.Trim & "'"
        dr = cc.GetDataReader(strS)
        Try
            While dr.Read
                GE_HDR_ID = dr(0).ToString
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        '-------------------------
        Try


            ListView1.Items.Clear()
            ListView2.Items.Clear()
            'Text6.Text = ""
            txtTransactionNo.Text = ""

            Dim tot_veh As Integer = 0
            Dim IN_veh As Integer = 0
            Dim OUT_veh As Integer = 0
            Dim CANC_veh As Integer = 0

            ds = cc.GetDataset("select a.TRN_ID , a.GE_HDR_ID , a.Gate_NO , a.Vehicle_No, a.Type_Of_Vehicle , a.Driver_Name, a.Driver_LIC_No , a.Transpoter_Code , a.TransporterName , a.Remarks_IN , a.Remarks_OUT , a.Vehicle_Status , a.Remarks_Cancellation , a.Entry_DoneBy , a.EntryDateTime , a.out_DateTime , a.out_DoneBy , a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT , " & _
                                            "b.Unloading_No , b.PO_No , b.PO_Line_Item , b.DO_No , b.DO_Line_Item , b.SO_No , b.SO_Line_Item , b.Mat_Code , b.Mat_Desc , b.Challan_Date , b.Challan_No , DO_Challan_Qty , b.UOM , b.WayBill_No , b.Vendor_Code , b.Vendor_Name , b.Customer_Code , b.Customer_Name , b.GatePass_No , b.Unloading_Remarks , b.CN_No , b.CN_Date , b.WB_Count_ID , b.F_WT , b.F_WT_DateTime , b.S_WT , b.S_WT_DAtetime , b.NET_WT , b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code , b.Grouping_Vehicle_No , b.Grouping_Transaction_No from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and a.Type_Of_Vehicle = 'CONTITEM' and  a.GE_HDR_ID = '" & GE_HDR_ID & "' and a.GE_HDR_ID in ( select distinct GE_HDR_ID from tbl_Contractor_material_approval ) order by EntryDateTime")
            Dim ki As Integer = ListView1.Items.Count
            For i As Integer = 0 To ds.Tables(0).Rows.Count - 1
                'ListView1.Items.Add(ki, , dr("GE_HDR_ID"))
                Dim lvi As New ListViewItem With {
                    .Text = ds.Tables(0).Rows(i).Item("GE_HDR_ID")
                }
                ''If s_tn_no = dr("TRN_ID") Then
                'ListView1.Items(ki).SubItems.Add (1), , dr("EntryDateTime")
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("EntryDateTime"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Vehicle_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Type_Of_Vehicle"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Transpoter_Code"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("TransporterName"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("PO_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("PO_Line_Item"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("SO_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("DO_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("DO_line_Item"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Mat_Code"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Mat_Desc"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("DO_Challan_Qty"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("UOM"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Unloading_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Challan_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Challan_Date"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("CN_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("CN_Date"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("WayBill_No"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Unloading_Remarks"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("GatePass_NO"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Vendor_Code"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Vendor_Name"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Customer_Code"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Customer_Name"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("F_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("S_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Net_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Vehicle_Status"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Out_Datetime"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Remarks_IN"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Remarks_OUT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Remarks_Cancellation"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("F_WT_Note"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("S_WT_Note"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Grouping_Ref_Code"))
                Dim RefCode As String = ds.Tables(0).Rows(i).Item("Grouping_Ref_Code")
                Try
                    dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & RefCode & "'")
                    If dr.Read Then
                        lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Reference_Name"))
                    Else
                        lvi.SubItems.Add("")
                    End If
                Catch ex As Exception
                    lvi.SubItems.Add("")
                End Try
                dr.Close()

                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("EntryDateTime"), "dd/MMM/yyyy"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("EntryDateTime"), "HH:MM"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("Out_Datetime"), "dd/MMM/yyyy"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("Out_Datetime"), "HH:MM"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Seal_no"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Party_Gross_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Party_Tare_WT"))
                lvi.SubItems.Add(ds.Tables(0).Rows(i).Item("Party_Net_WT"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("F_WT_DateTime"), "dd/MMM/yyyy"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("F_WT_DateTime"), "HH:MM"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("S_WT_DateTime"), "dd/MMM/yyyy"))
                lvi.SubItems.Add(Format(ds.Tables(0).Rows(i).Item("S_WT_DateTime"), "HH:MM"))


                Dim s_tn_no As String = String.Empty
                If s_tn_no <> ds.Tables(0).Rows(i).Item("TRN_ID") Then
                    tot_veh += 1

                    If ds.Tables(0).Rows(i).Item("Vehicle_Status") = "IN" Then
                        IN_veh += 1
                    End If

                    If ds.Tables(0).Rows(i).Item("Vehicle_Status") = "OUT" Then
                        OUT_veh += 1
                    End If

                    If ds.Tables(0).Rows(i).Item("Vehicle_Status") = "C" Then
                        CANC_veh += 1
                    End If
                End If

                s_tn_no = ds.Tables(0).Rows(i).Item("TRN_ID")
                '---------------------------------
                ListView1.Items.Add(lvi)
                '---------------------------------
            Next
            ds.Dispose()
            ds.Clear()

            ''''
            ''''        Text1.Text = tot_veh
            ''''        Text2.Text = IN_veh
            ''''        Text3.Text = OUT_veh
            ''''        Text4.Text = CANC_veh
            Dim C_WT As Double
            Dim T_Net_WT As Double

            For imp1 = 0 To ListView1.Items.Count - 1

                T_Net_WT = T_Net_WT + Val(Trim(ListView1.Items(imp1).SubItems(29).Text)) + 0

                If Trim(ListView1.Items(imp1).SubItems(30).Text) = "C" Then

                    C_WT = C_WT + Val(Trim(ListView1.Items(imp1).SubItems(29).Text)) + 0

                End If
            Next
            ''
            ''        Text5.Text = T_Net_WT
            ''       Text8.Text = C_WT
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try

        '----------------
        Load1 = True
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnPrintOutPass_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnPrintOutPass.Click
        Dim CountF As Integer = 0
        Dim strS As String = "select count(*) from tbl_contractor_Item_47KOutPass_Security where convert(decimal,cont_mat_Ret_Id) = '" & txtOutPassNo.Text.Trim & "'"
        dr = cc.GetDataReader(strS)
        Try
            While dr.Read
                CountF = dr(0).ToString
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        If CountF > 0 Then
            MessageBox.Show("Invalid Entry!")
            Exit Sub
        End If
        '-------------------------
        Dim str As String = "insert into tbl_contractor_Item_47KOutPass_Security select *, getdate(),'OUT','" & User_ID & "' from tbl_VIEW_Cont_Mat_Ret_ID where Cont_Mat_Ret_ID = '" & txtOutPassNo.Text.Trim & "'"
        Try
            cc.Execute(str)
            MessageBox.Show("Successfully Updated!")
        Catch ex As Exception
            MessageBox.Show("Invalid Entry!")
        End Try
    End Sub

    Private Sub ListView1_ItemCheck1(ByVal sender As Object, ByVal e As System.Windows.Forms.ItemCheckEventArgs) Handles ListView1.ItemCheck
        If (e.CurrentValue = CheckState.Unchecked) Then
            'TrnNo = (Me.ListView1.Items(e.Index).SubItems(0).Text)
            Select Case True
                Case TrnNo.Contains(Me.ListView1.Items(e.Index).SubItems(0).Text)
                    TrnNo = TrnNo
            End Select
            Select Case False
                Case TrnNo.Contains(Me.ListView1.Items(e.Index).SubItems(0).Text)
                    If TrnNo = "" Then
                        TrnNo = Me.ListView1.Items(e.Index).SubItems(0).Text
                    Else
                        TrnNo = TrnNo & "," & Me.ListView1.Items(e.Index).SubItems(0).Text
                    End If
            End Select
            'MessageBox.Show(TrnNo)
        ElseIf (e.CurrentValue = CheckState.Checked) Then
            TrnNo = TrnNo.Replace(Me.ListView1.Items(e.Index).SubItems(0).Text, "")
        End If

        Dim transactionNO() As String = TrnNo.Split(",")
        Dim Transaction1 As String = ""
        For i As Integer = 0 To transactionNO.Length - 1
            If i = 0 Then
                Transaction1 = "'" & transactionNO(i).ToString & "'"
                txtTransactionNo.Text = transactionNO(i).ToString
            Else
                Transaction1 = Transaction1 & ",'" & transactionNO(i).ToString & "'"
                txtTransactionNo.Text = transactionNO(i).ToString
            End If
        Next
        Try
            'ListView1.Items.Clear
            ListView2.Items.Clear()
            'Text6.Text = ""
            'Text7.Text = ""
            'txtTransactionNo.Text = ""
            'Text7.Text = Trim(ListView1.Items(ListView1.SelectedItem.Index).SubItems(2).Text)
            'txtTransactionNo.Text = Trim(ListView1.SelectedItems(0).Text)
            Text9.Text = ""
            txtMaterialCode.Text = ""
            txtMaterialName.Text = ""
            txtChallanQty.Text = ""
            txtUOM.Text = ""
            txtApprovedQty.Text = ""

            Dim tot_veh As Integer = 0
            Dim IN_veh As Integer = 0
            Dim OUT_veh As Integer = 0
            Dim CANC_veh As Integer = 0

            ''dr = cc.GetDataReader("select b.GE_DET_TRAN_ID , a.TRN_ID , a.GE_HDR_ID , a.Gate_NO , a.Vehicle_No, a.Type_Of_Vehicle , a.Driver_Name, a.Driver_LIC_No , a.Transpoter_Code , a.TransporterName , a.Remarks_IN , a.Remarks_OUT , a.Vehicle_Status , a.Remarks_Cancellation , a.Entry_DoneBy , a.EntryDateTime , a.out_DateTime , a.out_DoneBy , a.Plant_Code , a.Company_Code , a.Seal_no , a.Party_Gross_WT , a.Party_Tare_WT , a.Party_NET_WT , " & _
            ''"b.Unloading_No , b.PO_No , b.PO_Line_Item , b.DO_No , b.DO_Line_Item , b.SO_No , b.SO_Line_Item , b.Mat_Code , b.Mat_Desc , b.Challan_Date , b.Challan_No , DO_Challan_Qty , b.UOM , b.WayBill_No , b.Vendor_Code , b.Vendor_Name , b.Customer_Code , b.Customer_Name , b.GatePass_No , b.Unloading_Remarks , b.CN_No , b.CN_Date , b.WB_Count_ID , b.F_WT , b.F_WT_DateTime , b.S_WT , b.S_WT_DAtetime , b.NET_WT , b.F_WT_Note , b.S_WT_Note , b.Grouping_ref_Code , b.Grouping_Vehicle_No , b.Grouping_Transaction_No from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and a.GE_HDR_ID = '" & Trim(ListView1.SelectedItems(0).Text) & "'  and a.Remarks_IN NOT like '%Auto%IN%'  order by EntryDateTime")
            dr = cc.GetDataReader("select T1.*,T2.* from(select b.GE_DET_TRAN_ID,b.Mat_Code,b.Mat_Desc,b.DO_Challan_Qty,b.UOM from tbl_GE_HDR a , tbl_GE_DET b " & _
  " where a.GE_HDR_ID = b.GE_HDR_ID and a.GE_HDR_ID in (" & Transaction1 & ")  and a.Remarks_IN NOT like '%Auto%IN%' ) T1 " & _
    " LEFT OUTER JOIN ( select GE_DET_TRAN_ID,Approved_Qty,Ret_Non_Ret from tbl_Contractor_material_approval)T2 " & _
  " on T1.GE_DET_TRAN_ID = T2.GE_DET_TRAN_ID")
            While dr.Read
                'ki = ListView2.Items.Count + 1
                'ListView2.Items.Add(ki, , dr("GE_DET_TRAN_ID"))
                '                        ListView2.Items(ki).SubItems.Add (1), , dr("Mat_Code")
                '                        ListView2.Items(ki).SubItems.Add (2), , dr("Mat_Desc")
                '                        ListView2.Items(ki).SubItems.Add (3), , dr("DO_Challan_Qty")
                '                        ListView2.Items(ki).SubItems.Add (4), , dr("UOM")
                '                        ListView2.Items(ki).SubItems.Add (5), , "0"
                '                        ListView2.Items(ki).SubItems.Add (6), , ""
                '                        ListView2.Items(ki).SubItems.Add (7), , ""

                'rec1.MoveNext()
                Dim lvi As New ListViewItem With {
                    .Text = dr("GE_DET_TRAN_ID")
                }
                lvi.SubItems.Add(dr("Mat_Code"))
                lvi.SubItems.Add(dr("Mat_Desc"))
                lvi.SubItems.Add(dr("DO_Challan_Qty"))
                lvi.SubItems.Add(dr("UOM"))
                lvi.SubItems.Add(FormatNumber((dr("Approved_Qty")), 3))
                lvi.SubItems.Add(dr("Ret_Non_Ret"))
                lvi.SubItems.Add("")
                '---------------------------------
                ListView2.Items.Add(lvi)
                '---------------------------------
            End While
            dr.Close()

            ''''
            'Text6.Text = tot_veh
            ''''        Text2.Text = IN_veh
            ''''        Text3.Text = OUT_veh
            ''''        Text4.Text = CANC_veh

            Dim T_Net_WT As Double
            Dim C_WT As Double

            For imp1 = 0 To ListView1.Items.Count - 1

                T_Net_WT = T_Net_WT + Val(Trim(ListView1.Items(imp1).SubItems(29).Text)) + 0

                If Trim(ListView1.Items(imp1).SubItems(30).Text) = "C" Then

                    C_WT = C_WT + Val(Trim(ListView1.Items(imp1).SubItems(29).Text)) + 0

                End If

            Next
            ''
            ''       Text5.Text = T_Net_WT
            '       Text8.Text = C_WT
            '-------------------------------------
            Dim str As String = "select convert(Int, Cont_Mat_Ret_ID) as OutPassNo,Mat_Code,Mat_Name,UOM,Vehicle_no_for_Return as VehicleNo, Vendor_Party_Name_To_Return as Vendor,Approved_Qty,Challan_No,Remarks,Return_Qty as Qty from tbl_VIEW_Cont_Mat_Ret_ID where GE_HDR_ID in (" & Transaction1 & ") "
            ds = cc.GetDataset(str)
            gvOutPass.DataSource = ds.Tables(0)

        Catch ex As Exception

        End Try
    End Sub

  
    Private Sub cbSelectAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cbSelectAll.CheckedChanged
        If cbSelectAll.Checked = True Then
            For j = 0 To ListView2.Items.Count - 1
                ListView2.Items(j).Checked = True
            Next
        Else
            For j = 0 To ListView2.Items.Count - 1
                ListView2.Items(j).Checked = False
            Next
        End If
    End Sub

    Private Sub gvOutPass_CellContentClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles gvOutPass.CellContentClick

    End Sub

    Private Sub gvOutPass_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvOutPass.CellMouseClick
        Dim index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvOutPass.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvOutPass.SelectedRows(i).Index)

                    txtOutPassNo.Text = Convert.ToString(gvOutPass.Rows(index).Cells(0).Value)
                    'txtCompanyName.Text = Convert.ToString(gvOutPass.Rows(index).Cells(2).Value)
                    'txtAddress.Text = Convert.ToString(gvOutPass.Rows(index).Cells(3).Value)
                    txtStatus.Clear()
                    txtOutDateTime.Clear()
                    Dim str As String = "select Status,Out_dateTime from tbl_contractor_Item_47KOutPass_Security where convert(decimal ,cont_mat_Ret_Id) = '" & txtOutPassNo.Text.Trim & "'"
                    dr = cc.GetDataReader(str)
                    Try
                        While dr.Read
                            txtStatus.Text = dr(0).ToString
                            txtOutDateTime.Text = dr(1).ToString
                        End While
                    Catch ex As Exception

                    End Try
                    dr.Close()
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub btnReport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReport.Click
        Dim dtFrom1 As Date = dtFrom.Text
        Dim dtTo1 As Date = dtTo.Text

        Dim excel1 As Microsoft.Office.Interop.Excel.Application
        Dim wb As Microsoft.Office.Interop.Excel.Workbook

        Dim xlsheet As Excel.Worksheet

        'Dim xlwbook As Excel.Workbook

        excel1 = New Microsoft.Office.Interop.Excel.Application
        wb = excel1.Workbooks.Open(ApplicationPath & "\DetailsReport.xls", True, True, , "jnan")
        excel1.Visible = True
        wb.Activate()
        Try
            Dim row As Integer
            row = 2

            xlsheet = wb.Sheets.Item(1)
            ds = cc.GetDataset("select * from tbl_contractor_Item_47KOutPass_Security where Out_DateTime between '" & Format(dtFrom1, "yyyy-MM-dd") & " 00:00:00.000' and '" & Format(dtTo1, "yyyy-MM-dd") & " 23:59:59.999'")
            For i As Integer = 0 To ds.Tables(0).Columns.Count - 1
                xlsheet.Cells(1, i + 1) = ds.Tables(0).Columns(i).ColumnName
                '.Cells(1, i) = ListView1.ColumnHeaders(i).Text

            Next i
            For j As Integer = 0 To ds.Tables(0).Rows.Count - 1
                xlsheet.Cells(row + j, 1) = ds.Tables(0).Rows(j).Item(0)
                xlsheet.Cells(row + j, 2) = ds.Tables(0).Rows(j).Item(1)
                xlsheet.Cells(row + j, 3) = ds.Tables(0).Rows(j).Item(2)
                xlsheet.Cells(row + j, 4) = ds.Tables(0).Rows(j).Item(3)
                xlsheet.Cells(row + j, 5) = ds.Tables(0).Rows(j).Item(4)
                xlsheet.Cells(row + j, 6) = ds.Tables(0).Rows(j).Item(5)
                xlsheet.Cells(row + j, 7) = ds.Tables(0).Rows(j).Item(6)
                xlsheet.Cells(row + j, 8) = ds.Tables(0).Rows(j).Item(7)
                xlsheet.Cells(row + j, 9) = ds.Tables(0).Rows(j).Item(8)
                xlsheet.Cells(row + j, 10) = ds.Tables(0).Rows(j).Item(9)
                xlsheet.Cells(row + j, 11) = ds.Tables(0).Rows(j).Item(10)
                xlsheet.Cells(row + j, 12) = ds.Tables(0).Rows(j).Item(11)
                xlsheet.Cells(row + j, 13) = ds.Tables(0).Rows(j).Item(12)
                xlsheet.Cells(row + j, 14) = ds.Tables(0).Rows(j).Item(13)
            Next
        Catch ex As Exception

        End Try
    End Sub
End Class