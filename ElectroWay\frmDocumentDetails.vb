﻿Public Class frmDocumentDetails
    Dim cc As New Class1
    Private Sub frmDocumentDetails_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

    End Sub
    Private Sub txtVehicleNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVehicleNo.KeyPress
        Dim ReadF As Boolean = False
        If AscW(e.KeyChar) = 13 Then
            dr = cc.GetDataReader("select * from tbl_DocumentsValidity where Vehicle_No = '" & Trim(txtVehicleNo.Text.Trim) & "'")
            Try
                While dr.Read
                    ReadF = True
                    dtRC.Text = dr(1).ToString
                    dtTax.Text = dr(2).ToString
                    dtInsurance.Text = dr(3).ToString
                    dtFitness.Text = dr(4).ToString
                    dtPollution.Text = dr(5).ToString
                    dtDL.Text = dr(6).ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
        If Convert.ToInt32(e.<PERSON><PERSON>har) = Keys.Enter Then
            If ReadF = False Then
                MessageBox.Show("Not Found!!")
            Else
                dtRC.Focus()
            End If
        End If
    End Sub

    Private Sub txtVehicleNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVehicleNo.TextChanged
        txtVehicleNo.Text = UCase(Trim(txtVehicleNo.Text))
        txtVehicleNo.SelectionStart = Len(txtVehicleNo.Text)
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        ' Validate input
        If String.IsNullOrWhiteSpace(txtVehicleNo.Text.Trim()) Then
            MessageBox.Show("Please enter a Vehicle Number")
            txtVehicleNo.Focus()
            Return
        End If

        ' Format dates properly - assuming dtRC, etc. are DateTimePicker controls
        ' If they're text fields, you need to validate them as valid dates first
        Dim rcDate As String = Format(CDate(dtRC.Text), "yyyy-MM-dd")
        Dim taxDate As String = Format(CDate(dtTax.Text), "yyyy-MM-dd")
        Dim insuranceDate As String = Format(CDate(dtInsurance.Text), "yyyy-MM-dd")
        Dim fitnessDate As String = Format(CDate(dtFitness.Text), "yyyy-MM-dd")
        Dim pollutionDate As String = Format(CDate(dtPollution.Text), "yyyy-MM-dd")
        Dim dlDate As String = Format(CDate(dtDL.Text), "yyyy-MM-dd")

        ' Check if UserNameinDB is defined
        If String.IsNullOrEmpty(UserNameinDB) Then
            MessageBox.Show("User information is missing. Please log in again.")
            Return
        End If

        ' First check if record exists
        Dim vehicleExists As Boolean = False
        Dim vehicleNo As String = txtVehicleNo.Text.Trim()

        Try
            Dim checkQuery As String = "SELECT COUNT(*) FROM tbl_DocumentsValidity WHERE Vehicle_No = '" & vehicleNo & "'"
            Dim count As Integer = CInt(cc.GetScalarValue(checkQuery))
            vehicleExists = (count > 0)

            Dim query As String
            If vehicleExists Then
                ' Update existing record
                query = "UPDATE tbl_DocumentsValidity SET " &
                   "RC_Validity = '" & rcDate & "', " &
                   "Tax_Validity = '" & taxDate & "', " &
                   "Insurance_Validity = '" & insuranceDate & "', " &
                   "Fitness_Validity = '" & fitnessDate & "', " &
                   "Pollution_Validity = '" & pollutionDate & "', " &
                   "DL_Validity = '" & dlDate & "', " &
                   "Updated_BY = '" & UserNameinDB & "', " &
                   "Updated_DATE = GETDATE() " &
                   "WHERE Vehicle_No = '" & vehicleNo & "'"
            Else
                ' Insert new record - specify column names explicitly
                query = "INSERT INTO tbl_DocumentsValidity " &
                   "(Vehicle_No, RC_Validity, Tax_Validity, Insurance_Validity, " &
                   "Fitness_Validity, Pollution_Validity, DL_Validity, Updated_BY, Updated_DATE) " &
                   "VALUES ('" & vehicleNo & "', '" & rcDate & "', '" &
                   taxDate & "', '" & insuranceDate & "', '" &
                   fitnessDate & "', '" & pollutionDate & "', '" &
                   dlDate & "', '" & UserNameinDB & "', GETDATE())"
            End If

            ' Execute the query
            cc.Execute(query)
            MessageBox.Show("Successfully Updated!")

            ' Clear form after successful update if desired
            btnCancel_Click(sender, e)

        Catch ex As Exception
            MessageBox.Show("Error updating record: " & ex.Message & Environment.NewLine &
                       "Error details: " & ex.ToString())
        End Try
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtVehicleNo.Clear()
        dtRC.Text = Today.Date
        dtTax.Text = Today.Date
        dtInsurance.Text = Today.Date
        dtFitness.Text = Today.Date
        dtPollution.Text = Today.Date
        dtDL.Text = Today.Date
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub dtRC_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtRC.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtTax.Focus()
        End If
    End Sub

    Private Sub dtTax_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtTax.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtInsurance.Focus()
        End If
    End Sub

    Private Sub dtInsurance_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtInsurance.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtFitness.Focus()
        End If
    End Sub

    Private Sub dtFitness_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtFitness.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtPollution.Focus()
        End If
    End Sub

    Private Sub dtPollution_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtPollution.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            dtDL.Focus()
        End If
    End Sub

    Private Sub dtDL_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles dtDL.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            btnUpdate.Focus()
        End If
    End Sub
End Class