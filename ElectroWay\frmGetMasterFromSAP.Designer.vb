﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmGetMasterFromSAP
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmGetMasterFromSAP))
        Me.GroupBox1 = New System.Windows.Forms.GroupBox
        Me.btnImportMaterialMaster = New System.Windows.Forms.Button
        Me.txtImportMaterialMaster = New System.Windows.Forms.TextBox
        Me.Label3 = New System.Windows.Forms.Label
        Me.btnImportCustomerMaster = New System.Windows.Forms.Button
        Me.txtImportCustomerMaster = New System.Windows.Forms.TextBox
        Me.Label1 = New System.Windows.Forms.Label
        Me.btnImportVendorMaster = New System.Windows.Forms.Button
        Me.txtImportVendorMaster = New System.Windows.Forms.TextBox
        Me.Label2 = New System.Windows.Forms.Label
        Me.GroupBox1.SuspendLayout()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.btnImportMaterialMaster)
        Me.GroupBox1.Controls.Add(Me.txtImportMaterialMaster)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.btnImportCustomerMaster)
        Me.GroupBox1.Controls.Add(Me.txtImportCustomerMaster)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.btnImportVendorMaster)
        Me.GroupBox1.Controls.Add(Me.txtImportVendorMaster)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Location = New System.Drawing.Point(12, 12)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(449, 257)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'btnImportMaterialMaster
        '
        Me.btnImportMaterialMaster.Location = New System.Drawing.Point(6, 191)
        Me.btnImportMaterialMaster.Name = "btnImportMaterialMaster"
        Me.btnImportMaterialMaster.Size = New System.Drawing.Size(270, 36)
        Me.btnImportMaterialMaster.TabIndex = 5
        Me.btnImportMaterialMaster.Text = "Import Material Master Data from SAP"
        Me.btnImportMaterialMaster.UseVisualStyleBackColor = True
        '
        'txtImportMaterialMaster
        '
        Me.txtImportMaterialMaster.Location = New System.Drawing.Point(285, 198)
        Me.txtImportMaterialMaster.Name = "txtImportMaterialMaster"
        Me.txtImportMaterialMaster.Size = New System.Drawing.Size(158, 20)
        Me.txtImportMaterialMaster.TabIndex = 6
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label3.Location = New System.Drawing.Point(282, 178)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(128, 17)
        Me.Label3.TabIndex = 25
        Me.Label3.Text = "Last Date of Import"
        '
        'btnImportCustomerMaster
        '
        Me.btnImportCustomerMaster.Location = New System.Drawing.Point(6, 110)
        Me.btnImportCustomerMaster.Name = "btnImportCustomerMaster"
        Me.btnImportCustomerMaster.Size = New System.Drawing.Size(270, 36)
        Me.btnImportCustomerMaster.TabIndex = 3
        Me.btnImportCustomerMaster.Text = "Import Customer Master Data from SAP"
        Me.btnImportCustomerMaster.UseVisualStyleBackColor = True
        '
        'txtImportCustomerMaster
        '
        Me.txtImportCustomerMaster.Location = New System.Drawing.Point(285, 117)
        Me.txtImportCustomerMaster.Name = "txtImportCustomerMaster"
        Me.txtImportCustomerMaster.Size = New System.Drawing.Size(158, 20)
        Me.txtImportCustomerMaster.TabIndex = 4
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label1.Location = New System.Drawing.Point(282, 97)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(128, 17)
        Me.Label1.TabIndex = 22
        Me.Label1.Text = "Last Date of Import"
        '
        'btnImportVendorMaster
        '
        Me.btnImportVendorMaster.Location = New System.Drawing.Point(7, 29)
        Me.btnImportVendorMaster.Name = "btnImportVendorMaster"
        Me.btnImportVendorMaster.Size = New System.Drawing.Size(270, 36)
        Me.btnImportVendorMaster.TabIndex = 1
        Me.btnImportVendorMaster.Text = "Import Vendor Master Data from SAP"
        Me.btnImportVendorMaster.UseVisualStyleBackColor = True
        '
        'txtImportVendorMaster
        '
        Me.txtImportVendorMaster.Location = New System.Drawing.Point(286, 36)
        Me.txtImportVendorMaster.Name = "txtImportVendorMaster"
        Me.txtImportVendorMaster.Size = New System.Drawing.Size(158, 20)
        Me.txtImportVendorMaster.TabIndex = 2
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label2.Location = New System.Drawing.Point(283, 16)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(128, 17)
        Me.Label2.TabIndex = 19
        Me.Label2.Text = "Last Date of Import"
        '
        'frmGetMasterFromSAP
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(473, 281)
        Me.Controls.Add(Me.GroupBox1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmGetMasterFromSAP"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "IMPORT MASTER DATA FROM SAP"
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents btnImportCustomerMaster As System.Windows.Forms.Button
    Friend WithEvents txtImportCustomerMaster As System.Windows.Forms.TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents btnImportVendorMaster As System.Windows.Forms.Button
    Friend WithEvents txtImportVendorMaster As System.Windows.Forms.TextBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents btnImportMaterialMaster As System.Windows.Forms.Button
    Friend WithEvents txtImportMaterialMaster As System.Windows.Forms.TextBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
End Class
