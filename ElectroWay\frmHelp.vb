﻿Public Class frmHelp
    Dim cc As New Class1
    Private Sub frmHelp_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        'frmHelp.ListView1.ListItems.Clear()
        If Help_callfrom = "VENDOR" Then
            Try
                Dim str As String = "select * from tbl_Vendor_Mst where Vendor_Name like '%" & Trim(txtName.Text) & "%' order by Vendor_code"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "MATERIAL" Then
            Try
                Dim str As String = "select * from tbl_Material_Mst where Material_Name like '%" & Trim(txtName.Text) & "%' order by Material_code"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "CUSTOMER" Then
             Try
                Dim str As String = "select * from tbl_Customer_Mst where Customer_Name like '%" & Trim(txtName.Text) & "%' order by Customer_code"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "REFERENCE" Then
            Try
                Dim str As String = "select * from tbl_Reference_Mst where Reference_Name like '%" & Trim(txtName.Text) & "%' order by Reference_code"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "CHANGEGRREF" Then
            Try
                Dim str As String = "select * from tbl_GE_HDR where Vehicle_No like '%" & Trim(txtName.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try

            ''TRANSACTION_NO
        ElseIf Help_callfrom = "TRANSACTION_NO" Then
            Try
                Dim str As String = "select * from tbl_GE_HDR where Vehicle_No like '%" & Trim(txtName.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No , EntryDateTime"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
            ''TRANSACTION_NO   SLIP_DIRECT_TRANSACTION_NO    SLIP_PRINT_VIEW
        ElseIf Help_callfrom = "SLIP_DIRECT_TRANSACTION_NO" Then
            Try
                Dim str As String = "select * from tbl_GE_HDR where Vehicle_No like '%" & Trim(txtName.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by EntryDateTime DESC"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "SLIP_PRINT_VIEW" Then
            Try
                Dim str As String = "select * from tbl_GE_HDR where Vehicle_No like '%" & Trim(txtName.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try

        ElseIf Help_callfrom = "TRANSFER_TO_OTHER_PLANT" Then
            Try
                Dim str As String = "select * from tbl_GE_HDR where Vehicle_No like '%" & Trim(txtName.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "VEHICLEACTIVITY" Then
            Try
                Dim str As String = "select * from tbl_GE_HDR where Vehicle_No like '%" & Trim(txtName.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "VEHICLENOCHANGE" Then
            Try
                Dim str As String = "select * from tbl_GE_HDR where Vehicle_No like '%" & Trim(txtName.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try

        ElseIf Help_callfrom = "TRANSPORTER" Then
            Try
                Dim str As String = "select * from tbl_TRANSPORTER_Mst where Transporter_Code like '%" & Trim(txtName.Text) & "%'  order by Transporter_Code"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try

        ElseIf Help_callfrom = "GATEENTRYTRPT" Then
            Try
                Dim str As String = "select * from tbl_TRANSPORTER_Mst where Transporter_Code like '%" & Trim(txtName.Text) & "%'  order by Transporter_Code"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "CANCEL_VEHICLE_ENTRY" Then
            Try
                Dim str As String = "select * from tbl_GE_HDR where Vehicle_No like '%" & Trim(txtName.Text) & "%' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        ElseIf Help_callfrom = "VEHICLE_TRANSPORTER" Then
            Try
                Dim str As String = "select * from tbl_Vehicle_Transporter where Vehicle_No like '%" & Trim(txtName.Text) & "%' order by Vehicle_No"
                ds = cc.GetDataset(str)
                gvHelp.DataSource = ds.Tables(0)
            Catch ex As Exception

            End Try
        End If
        ''TRANSPORTER
        ''CANCEL_VEHICLE_ENTRY
        ''VEHICLE_TRANSPORTER
    End Sub

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        lblMessage.Text = "Please Wait.........."
        lblMessage.Refresh()
        If ((gvHelp.Columns.Count = 0) Or (gvHelp.Rows.Count = 0)) Then
            Exit Sub
        End If

        'Creating dataset to export
        Dim dset As New DataSet
        'add table to dataset
        dset.Tables.Add()
        'add column to that table
        For i As Integer = 0 To gvHelp.ColumnCount - 1
            dset.Tables(0).Columns.Add(gvHelp.Columns(i).HeaderText)
        Next
        'add rows to the table
        Dim dr1 As DataRow
        For i As Integer = 0 To gvHelp.RowCount - 1
            dr1 = dset.Tables(0).NewRow
            For j As Integer = 0 To gvHelp.Columns.Count - 1
                dr1(j) = gvHelp.Rows(i).Cells(j).Value
            Next
            dset.Tables(0).Rows.Add(dr1)
        Next

        Dim excel As New Microsoft.Office.Interop.Excel.ApplicationClass
        Dim wBook As Microsoft.Office.Interop.Excel.Workbook
        Dim wSheet As Microsoft.Office.Interop.Excel.Worksheet

        wBook = excel.Workbooks.Add()
        wSheet = wBook.ActiveSheet()

        Dim dt As System.Data.DataTable = dset.Tables(0)
        Dim dc As System.Data.DataColumn
        Dim dr As System.Data.DataRow
        Dim colIndex As Integer = 0
        Dim rowIndex As Integer = 0

        For Each dc In dt.Columns
            colIndex = colIndex + 1
            excel.Cells(1, colIndex) = dc.ColumnName
        Next

        For Each dr In dt.Rows
            rowIndex = rowIndex + 1
            colIndex = 0
            For Each dc In dt.Columns
                colIndex = colIndex + 1
                excel.Cells(rowIndex + 1, colIndex) = dr(dc.ColumnName)

            Next
        Next

        wSheet.Columns.AutoFit()
        'Dim strFileName As String = "D:\ss.xls"
        'Dim blnFileOpen As Boolean = False
        'Try
        '    Dim fileTemp As System.IO.FileStream = System.IO.File.OpenWrite(strFileName)
        '    fileTemp.Close()
        'Catch ex As Exception
        '    blnFileOpen = False
        'End Try

        'If System.IO.File.Exists(strFileName) Then
        '    System.IO.File.Delete(strFileName)
        'End If

        'wBook.SaveAs(strFileName)
        'excel.Workbooks.Open(strFileName)
        excel.Visible = True
        lblMessage.Text = ""
        lblMessage.Refresh()
    End Sub
    Private Sub gvHelp_CellMouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvHelp.CellMouseDoubleClick
        If Help_callfrom = "TRANSACTION_NO" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem As String = selectedCell.FormattedValue
            Try
                Dim f2 As frmReport = CType(Application.OpenForms("frmReport"), frmReport)
                f2.txtTransactionNo.Text = strItem
            Catch ex As Exception

            End Try
            Me.Close()
        End If

        If Help_callfrom = "REFERENCE" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem As String = selectedCell.FormattedValue
            Try
                Dim f2 As frmReferenceMaster = CType(Application.OpenForms("frmReferenceMaster"), frmReferenceMaster)
                f2.txtRakeRefCode.Text = strItem
            Catch ex As Exception

            End Try
            Me.Close()
        End If

        ''TRANSFER_TO_OTHER_PLANT
        ''SLIP_DIRECT_TRANSACTION_NO   SLIP_PRINT_VIEW
        If Help_callfrom = "SLIP_DIRECT_TRANSACTION_NO" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem As String = selectedCell.FormattedValue
            Try
                Dim f2 As frmSlipPrintDirect = CType(Application.OpenForms("frmSlipPrintDirect"), frmSlipPrintDirect)
                f2.txtTransactionNo.Text = strItem
            Catch ex As Exception

            End Try
            Me.Close()
        End If


        If Help_callfrom = "SLIP_PRINT_VIEW" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem As String = selectedCell.FormattedValue
            Try
                Dim f2 As frmSlipPrint = CType(Application.OpenForms("frmSlipPrint"), frmSlipPrint)
                f2.txtTransactionNo.Text = strItem
            Catch ex As Exception

            End Try
            Me.Close()
        End If


        If Help_callfrom = "TRANSFER_TO_OTHER_PLANT" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem As String = selectedCell.FormattedValue
            Try
                Dim f2 As frmTransferToPlant = CType(Application.OpenForms("frmTransferToPlant"), frmTransferToPlant)
                f2.txtTransactionNo.Text = strItem
            Catch ex As Exception

            End Try
            Me.Close()
        End If

        ''TRANSPORTER
        ''GATEENTRYTRPT


        If Help_callfrom = "TRANSPORTER" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(0)
            Dim strItem As String = selectedCell.FormattedValue
            Try
                Dim f2 As frmTransporter1 = CType(Application.OpenForms("frmTransporter1"), frmTransporter1)
                f2.txtTransporterCode.Text = strItem
            Catch ex As Exception

            End Try
            Me.Close()
        End If

        If Help_callfrom = "GATEENTRYTRPT" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(0)
            Dim strItem As String = selectedCell.FormattedValue
            Dim selectedCell1 As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem1 As String = selectedCell1.FormattedValue
            Try
                Dim f2 As frmGateEntry = CType(Application.OpenForms("frmGateEntry"), frmGateEntry)
                f2.txtTransporterCode.Text = strItem
                f2.txtTransporterName.Text = strItem1
            Catch ex As Exception

            End Try
            Me.Close()
        End If

        If Help_callfrom = "CHANGEGRREF" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem As String = selectedCell.FormattedValue
            Try
                Dim f2 As frmChangeGroupRef = CType(Application.OpenForms("frmChangeGroupRef"), frmChangeGroupRef)
                f2.txtTransactionNo.Text = strItem
            Catch ex As Exception

            End Try
            Me.Close()
        End If


        If Help_callfrom = "CANCEL_VEHICLE_ENTRY" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem As String = selectedCell.FormattedValue
            Try
                Dim f2 As frmCancelVehicle = CType(Application.OpenForms("frmCancelVehicle"), frmCancelVehicle)
                f2.txtTransactionNo.Text = strItem
            Catch ex As Exception

            End Try
            Me.Close()
        End If


        If Help_callfrom = "VEHICLE_TRANSPORTER" Then
            Dim selectedCell As DataGridViewCell = gvHelp.SelectedCells(0)
            Dim strItem As String = selectedCell.FormattedValue

            Dim selectedCell1 As DataGridViewCell = gvHelp.SelectedCells(1)
            Dim strItem1 As String = selectedCell1.FormattedValue

            Dim selectedCell2 As DataGridViewCell = gvHelp.SelectedCells(2)
            Dim strItem2 As String = selectedCell2.FormattedValue

            Dim selectedCell3 As DataGridViewCell = gvHelp.SelectedCells(3)
            Dim strItem3 As String = selectedCell3.FormattedValue

            Dim selectedCell4 As DataGridViewCell = gvHelp.SelectedCells(4)
            Dim strItem4 As String = selectedCell4.FormattedValue

            Try
                Dim f2 As frmVehicleTransporter = CType(Application.OpenForms("frmVehicleTransporter"), frmVehicleTransporter)
                f2.txtVehicleNo.Text = strItem
                f2.txtTransporterCode.Text = strItem1
                f2.txtTransporterName.Text = strItem2
                f2.txtVendorCode.Text = strItem3
                f2.txtCustomerCode.Text = strItem4
            Catch ex As Exception

            End Try
            Me.Close()
        End If
    End Sub

    Private Sub txtName_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtName.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            btnSearch.Focus()
        End If
    End Sub
End Class