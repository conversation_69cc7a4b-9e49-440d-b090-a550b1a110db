﻿Imports System.Configuration
Imports System.Data.SqlClient
Imports System.IO
Imports System.Net
Imports System.Security.Cryptography
Imports System.Security.Principal
Public Class frmLogin
    Dim cc As New Class1
    Dim bool As Boolean = False

    '-----------------------
    Private enc As System.Text.UTF8Encoding
    Private encryptor As ICryptoTransform
    Private decryptor As ICryptoTransform

#Region "WindowsAuthentication"
    '-----------Windiws validation-----------------
    Private Declare Auto Function LogonUser Lib "advapi32.dll" (ByVal lpszUsername As String,
   ByVal lpszDomain As String, ByVal lpszPassword As String, ByVal dwLogonType As Integer,
   ByVal dwLogonProvider As Integer, ByRef phToken As IntPtr) As Integer
    Private Declare Auto Function CloseHandle Lib "kernel32.dll" (ByVal handle As IntPtr) As Boolean

    Private Const LOGON32_LOGON_INTERACTIVE = 2
    Private Const LOGON32_PROVIDER_DEFAULT = 0
    Private Function Login(ByVal userName As String,
                           ByVal password As String,
                           Optional ByVal domain As String = "ESL01") As System.Security.Principal.WindowsIdentity
        'If String.IsNullOrEmpty(domain) Then domain = Environment.UserDomainName
        Dim hToken As IntPtr
        If LogonUser(userName, domain, password, LOGON32_LOGON_INTERACTIVE,
                     LOGON32_PROVIDER_DEFAULT, hToken) Then
            If Not hToken.Equals(IntPtr.Zero) Then
                Dim newId As New WindowsIdentity(hToken)
                CloseHandle(hToken)
                Return newId
            End If
        End If
        Return Nothing
    End Function
    Private Function GetUserGroups(ByVal id As WindowsIdentity) As String
        Dim sb As New System.Text.StringBuilder
        Dim p As New WindowsPrincipal(id)
        For Each knownRole In [Enum].GetValues(GetType(WindowsBuiltInRole))
            If p.IsInRole(knownRole) Then
                sb.AppendLine(knownRole.ToString)
            End If
        Next
        Return vbCrLf & sb.ToString
    End Function
    '----------------------------------------
#End Region
    Private Sub frmLogin_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        lblerror.Text = String.Empty
        'Dim domainAndUserName As String = Environment.UserDomainName & "\\" & Environment.UserName
        txtUsername.Focus()
        '---------Cryptology-------------------
        Dim KEY_128 As Byte() = {42, 1, 52, 67, 231, 13, 94, 101, 123, 6, 0, 12, 32, 91, 4, 111, 31, 70, 21, 141, 123, 142, 234, 82, 95, 129, 187, 162, 12, 55, 98, 23}
        Dim IV_128 As Byte() = {234, 12, 52, 44, 214, 222, 200, 109, 2, 98, 45, 76, 88, 53, 23, 78}
        Dim symmetricKey As New RijndaelManaged With {
            .Mode = CipherMode.CBC
        }

        Me.enc = New System.Text.UTF8Encoding
        Me.encryptor = symmetricKey.CreateEncryptor(KEY_128, IV_128)
        Me.decryptor = symmetricKey.CreateDecryptor(KEY_128, IV_128)
    End Sub
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Application.Exit()
    End Sub

    Private Sub btnLogin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLogin.Click
        'txtUsername.Text = "ti9000706"
        txtUsername.Text = "ADA1553"
        Dim hostname As String = Dns.GetHostName()
        ipaddress = CType(Dns.GetHostEntry(hostname).AddressList.GetValue(0), IPAddress).ToString
        UserNameinDB = txtUsername.Text
        Me.Hide()
        User_ID = UserNameinDB
        Dim V As New MDIForm1() With {
                                    .MdiParent = MdiParent
                                }
        V.Show()
        '-----------------------------
        'If txtUsername.Text.Trim = "" And txtPassword.Text.Trim = "" Then
        '    lblError.Text = "User Name and Password are Required"
        'ElseIf txtUsername.Text.Trim = "" Then
        '    lblError.Text = "User Name is Required"
        'ElseIf txtPassword.Text.Trim = "" Then
        '    lblError.Text = "Password is Required"
        'Else
        '    lblError.Text = String.Empty ' Clear error if both fields are filled
        '    Dim userID As WindowsIdentity = Login(txtUsername.Text.Trim, txtPassword.Text.Trim)
        '    If userID Is Nothing Then
        '        lblError.Text = "Invalid domain user name or password"
        '        Exit Sub
        '    Else
        '        If lblError.Text <> String.Empty Then
        '            Exit Sub
        '        End If
        '        Try
        '            Dim hostname As String = Dns.GetHostName()
        '            ipaddress = CType(Dns.GetHostEntry(hostname).AddressList.GetValue(0), IPAddress).ToString

        '            Dim message As String = ""

        '            Using connection As New SqlConnection(ConfigurationManager.ConnectionStrings("MyDatabase").ConnectionString)

        '                connection.Open()
        '                Using command As New SqlCommand("UpdateUserIfIPAllowed", connection)
        '                    command.CommandType = CommandType.StoredProcedure
        '                    command.Parameters.AddWithValue("@UserID", txtUsername.Text)
        '                    command.Parameters.AddWithValue("@IPAddress", ipaddress)

        '                    Dim outputParam As New SqlParameter("@Message", SqlDbType.NVarChar, 255)
        '                    outputParam.Direction = ParameterDirection.Output
        '                    command.Parameters.Add(outputParam)

        '                    command.ExecuteNonQuery()
        '                    message = outputParam.Value.ToString()
        '                    If message = "success" Then
        '                        bool = True
        '                        UserNameinDB = txtUsername.Text
        '                        Me.Hide()
        '                        User_ID = UserNameinDB
        '                        Dim V As New MDIForm1() With {
        '                            .MdiParent = MdiParent
        '                        }
        '                        V.Show()
        '                    Else
        '                        lblError.Text = message
        '                    End If

        '                End Using
        '            End Using
        '        Catch ex As Exception
        '            SendFormattedErrorMail(ex)
        '        End Try
        '    End If
        'End If
    End Sub

    Private Function Encrypt(ByVal clearText As String) As String
        Dim sPlainText As String = clearText
        If Not String.IsNullOrEmpty(sPlainText) Then
            Dim memoryStream As New MemoryStream()
            Dim cryptoStream As New CryptoStream(memoryStream, Me.encryptor, CryptoStreamMode.Write)
            cryptoStream.Write(Me.enc.GetBytes(sPlainText), 0, sPlainText.Length)
            cryptoStream.FlushFinalBlock()
            sPlainText = Convert.ToBase64String(memoryStream.ToArray())
            memoryStream.Close()
            cryptoStream.Close()
        End If
        Return sPlainText
    End Function

    Private Sub txtUsername_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtUsername.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            txtPassword.Focus()
        End If
    End Sub

    Private Sub txtPassword_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtPassword.KeyPress
        If Convert.ToInt32(e.KeyChar) = Keys.Enter Then
            btnLogin.Focus()
            btnLogin_Click(sender, e)
        End If
    End Sub

    Private Sub lblForgotPassword_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lblForgotPassword.LinkClicked
        ' Add your password recovery logic here
        MessageBox.Show("Please contact your system administrator to reset your password.", "Password Recovery", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub chkRememberMe_CheckedChanged(sender As Object, e As EventArgs) Handles chkRememberMe.CheckedChanged
        ' Add your "remember me" functionality here
        ' You might want to save the username (not password) in a secure way
    End Sub
End Class