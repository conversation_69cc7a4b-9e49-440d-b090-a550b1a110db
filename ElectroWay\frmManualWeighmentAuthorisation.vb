﻿Public Class frmManualWeighmentAuthorisation
    Dim cc As New Class1
    Private Sub btnAuthorise_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAuthorise.Click
        'dr = cc.GetDataReader("select * from tbl_User_Mst where User_ID = '" & Trim(txtUserId.Text) & "' and User_password  = '" & Trim(txtPassword.Text) & "' and User_ID <> '" & User_ID & "' and Per_Manual_Weighment = '1'")
        dr = cc.GetDataReader("select * from tbl_User_Mst where User_ID = '" & Trim(txtUserId.Text) & "' and User_password  = '" & Trim(txtPassword.Text) & "' and Per_Manual_Weighment = '1'")
        If dr.Read Then
            'MsgBox "Authorised for the Manual Weighment !", vbInformation, "ElectroWay"
            FirstLevelAuth = 1
            OperationType = "MANUAL"
            dr.Close()
            <PERSON>.Close()
            Exit Sub
        Else
            MsgBox("Either Wrong Password or manual weighment is not authorised for this user !", vbCritical, "ElectroWay")
        End If
        dr.Close()
        txtUserId.Text = ""
        txtUserName.Text = ""
        txtPassword.Text = ""
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtUserId.Text = ""
        txtUserName.Text = ""
        txtPassword.Text = ""
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub txtUserId_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtUserId.KeyPress
        If AscW(e.KeyChar) = 13 Then
            dr = cc.GetDataReader("select * from tbl_User_Mst where User_ID = '" & Trim(txtUserId.Text) & "'")
            If dr.Read Then
                txtUserName.Text = dr("User_Name")
            Else
                MsgBox("Wrong User ID !", vbInformation, "ElectroWay")
                txtUserId.Text = ""
                txtUserId.Focus()
            End If
            dr.Close()
        End If
    End Sub
End Class