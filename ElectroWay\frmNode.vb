﻿Public Class frmNode
    Dim cc As New Class1
    Dim Load1 As Boolean = False
    Private Sub frmNode_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        SelectCompany()
        SelectPlant()
        Load1 = True
        SelectCompanyName()
        SelectPlantName()
        LoadGrid()
    End Sub
    Private Sub SelectPlant()
        Try
            Dim str As String = "select * from tbl_plant_mst"
            dt = cc.GetDataTable(str)
            ddlPlantCode.DataSource = dt

            ddlPlantCode.DisplayMember = "Plant_Code"
            ddlPlantCode.ValueMember = "Plant_Code"
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SelectPlantName()
        If Load1 = True Then
            Dim str As String = "select Plant_Name from tbl_plant_mst where Plant_Code ='" & ddlPlantCode.SelectedValue & "'"
            dr = cc.GetDataReader(str)
            Try
                While dr.Read
                    txtPlantName.Text = dr("Plant_Name").ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub
    Private Sub SelectCompany()
        Try
            Dim str As String = "select * from tbl_Company_mst"
            dt = cc.GetDataTable(str)
            ddlCompnayCode.DataSource = dt

            ddlCompnayCode.DisplayMember = "Company_Code"
            ddlCompnayCode.ValueMember = "Company_Code"
        Catch ex As Exception

        End Try
    End Sub
    Private Sub SelectCompanyName()
        If Load1 = True Then
            Dim str As String = "select * from tbl_Company_mst where company_code = '" & Trim(ddlCompnayCode.SelectedValue) & "'"
            dr = cc.GetDataReader(str)
            Try
                While dr.Read
                    txtCompanyName.Text = dr("Company_Name").ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        clear()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtCompanyName.Text) = "" Or Trim(ddlNode.Text) = "" Or Trim(txtNodeNo.Text) = "" Or Trim(txtNodeIP.Text) = "" Then
            MsgBox("Blank Entry cannot be updated !!", vbInformation, "ElectroWay")
        Else
            ds = cc.GetDataset("select * from tbl_Node_Mst where Node_IP = '" & Trim(txtNodeIP.Text) & "'")
            If ds.Tables(0).Rows.Count <> 0 Then
                Dim ans = MsgBox("IP Already exists !, You want to replace ?", vbYesNo, "ElectroWay")
                If ans = vbYes Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_Node_Mst"

                    cm.Parameters.AddWithValue("@val_Plant_Name", Trim(ddlPlantCode.Text))
                    cm.Parameters.AddWithValue("@VAL_Node_Name", Trim(ddlNode.Text))
                    cm.Parameters.AddWithValue("@val_Node_No", Trim(txtNodeNo.Text))
                    cm.Parameters.AddWithValue("@val_Node_IP", Trim(txtNodeIP.Text))
                    cm.Parameters.AddWithValue("@val_Port_No", Trim(txtPortNo.Text))
                    cm.Parameters.AddWithValue("@val_Settings", Trim(txtSettings.Text))
                    cm.Parameters.AddWithValue("@val_ThreshpldValue", Trim(txtRThreshold.Text))
                    cm.Parameters.AddWithValue("@val_Input_Len", Trim(txtInputLen.Text))
                    cm.Parameters.AddWithValue("@val_UpdatedBy", User_ID)
                    cm.Parameters.AddWithValue("@val_PlantName", Trim(txtPlantName.Text))
                    cm.Parameters.AddWithValue("@val_Company_Code", Trim(ddlCompnayCode.Text))
                    cm.ExecuteNonQuery()
                    clear()
                    MsgBox("Updated Successfully !", vbInformation, "ElectroWay")
                End If
            Else
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_Node_Mst"
                cm.Parameters.AddWithValue("@val_Plant_Name", Trim(ddlPlantCode.Text))
                cm.Parameters.AddWithValue("@VAL_Node_Name", Trim(ddlNode.Text))
                cm.Parameters.AddWithValue("@val_Node_No", Trim(txtNodeNo.Text))
                cm.Parameters.AddWithValue("@val_Node_IP", Trim(txtNodeIP.Text))
                cm.Parameters.AddWithValue("@val_Port_No", Trim(txtPortNo.Text))
                cm.Parameters.AddWithValue("@val_Settings", Trim(txtSettings.Text))
                cm.Parameters.AddWithValue("@val_ThreshpldValue", Trim(txtRThreshold.Text))
                cm.Parameters.AddWithValue("@val_Input_Len", Trim(txtInputLen.Text))
                cm.Parameters.AddWithValue("@val_UpdatedBy", User_ID)
                cm.Parameters.AddWithValue("@val_PlantName", Trim(txtPlantName.Text))
                cm.Parameters.AddWithValue("@val_Company_Code", Trim(ddlCompnayCode.Text))
                cm.ExecuteNonQuery()
                clear()
                MsgBox("Updated Successfully !", vbInformation, "ElectroWay")
            End If
            LoadGrid()
            'Call FetchRec()
        End If
    End Sub
    Private Sub clear()
        txtPortNo.Text = ""
        txtNodeIP.Text = ""
        txtInputLen.Text = ""
        txtNodeNo.Text = ""
        txtSettings.Text = ""
        txtRThreshold.Text = ""
    End Sub

    Private Sub ddlNode_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlNode.SelectedIndexChanged
        If Trim(ddlNode.Text) = "WEIGH BRIDGE" Then
            gbWB.Enabled = True
        Else
            gbWB.Enabled = False
            txtPortNo.Text = ""
            txtSettings.Text = ""
            txtRThreshold.Text = ""
            txtInputLen.Text = ""
        End If
    End Sub
    Private Sub LoadGrid()
        Try
            Dim str As String = "select * from tbl_Node_Mst order by Plant_Name , Node_name , Node_No"
            ds = cc.GetDataset(str)
            gvNode.DataSource = ds.Tables(0)
        Catch ex As Exception

        End Try
    End Sub
    Private Sub gvNode_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvNode.CellMouseClick
        Dim index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvNode.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvNode.SelectedRows(i).Index)
                    ddlNode.SelectedItem = Convert.ToString(gvNode.Rows(index).Cells(2).Value)
                    txtNodeNo.Text = Convert.ToString(gvNode.Rows(index).Cells(3).Value)
                    txtNodeIP.Text = Convert.ToString(gvNode.Rows(index).Cells(4).Value)
                    txtPortNo.Text = Convert.ToString(gvNode.Rows(index).Cells(5).Value)
                    txtSettings.Text = Convert.ToString(gvNode.Rows(index).Cells(6).Value)
                    txtRThreshold.Text = Convert.ToString(gvNode.Rows(index).Cells(7).Value)
                    txtInputLen.Text = Convert.ToString(gvNode.Rows(index).Cells(8).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
End Class