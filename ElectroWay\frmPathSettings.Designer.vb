﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmPathSettings
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmPathSettings))
        Me.GroupBox1 = New System.Windows.Forms.GroupBox
        Me.Label6 = New System.Windows.Forms.Label
        Me.txtScannedBlue_RCBookStoragePath = New System.Windows.Forms.TextBox
        Me.Label5 = New System.Windows.Forms.Label
        Me.txtScannedDDrivingLicenseStoragePath = New System.Windows.Forms.TextBox
        Me.txtVehicleImageStoragePath = New System.Windows.Forms.TextBox
        Me.txtCameraImagePath = New System.Windows.Forms.TextBox
        Me.Label3 = New System.Windows.Forms.Label
        Me.Label2 = New System.Windows.Forms.Label
        Me.GroupBox2 = New System.Windows.Forms.GroupBox
        Me.btnExit = New System.Windows.Forms.Button
        Me.btnCancel = New System.Windows.Forms.Button
        Me.btnUpdate = New System.Windows.Forms.Button
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.SuspendLayout()
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.Label6)
        Me.GroupBox1.Controls.Add(Me.txtScannedBlue_RCBookStoragePath)
        Me.GroupBox1.Controls.Add(Me.Label5)
        Me.GroupBox1.Controls.Add(Me.txtScannedDDrivingLicenseStoragePath)
        Me.GroupBox1.Controls.Add(Me.txtVehicleImageStoragePath)
        Me.GroupBox1.Controls.Add(Me.txtCameraImagePath)
        Me.GroupBox1.Controls.Add(Me.Label3)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Location = New System.Drawing.Point(7, 0)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(434, 257)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label6.Location = New System.Drawing.Point(9, 123)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(308, 17)
        Me.Label6.TabIndex = 24
        Me.Label6.Text = "Scanned Blue/RC Book  Storage Path at Server"
        '
        'txtScannedBlue_RCBookStoragePath
        '
        Me.txtScannedBlue_RCBookStoragePath.Location = New System.Drawing.Point(12, 143)
        Me.txtScannedBlue_RCBookStoragePath.Name = "txtScannedBlue_RCBookStoragePath"
        Me.txtScannedBlue_RCBookStoragePath.Size = New System.Drawing.Size(414, 20)
        Me.txtScannedBlue_RCBookStoragePath.TabIndex = 3
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label5.Location = New System.Drawing.Point(9, 69)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(245, 17)
        Me.Label5.TabIndex = 23
        Me.Label5.Text = "Vehicle Image Storage Path at Server"
        '
        'txtScannedDDrivingLicenseStoragePath
        '
        Me.txtScannedDDrivingLicenseStoragePath.Location = New System.Drawing.Point(12, 200)
        Me.txtScannedDDrivingLicenseStoragePath.Name = "txtScannedDDrivingLicenseStoragePath"
        Me.txtScannedDDrivingLicenseStoragePath.Size = New System.Drawing.Size(414, 20)
        Me.txtScannedDDrivingLicenseStoragePath.TabIndex = 4
        '
        'txtVehicleImageStoragePath
        '
        Me.txtVehicleImageStoragePath.Location = New System.Drawing.Point(12, 89)
        Me.txtVehicleImageStoragePath.Name = "txtVehicleImageStoragePath"
        Me.txtVehicleImageStoragePath.Size = New System.Drawing.Size(414, 20)
        Me.txtVehicleImageStoragePath.TabIndex = 2
        '
        'txtCameraImagePath
        '
        Me.txtCameraImagePath.Location = New System.Drawing.Point(12, 36)
        Me.txtCameraImagePath.Name = "txtCameraImagePath"
        Me.txtCameraImagePath.Size = New System.Drawing.Size(414, 20)
        Me.txtCameraImagePath.TabIndex = 1
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label3.Location = New System.Drawing.Point(9, 178)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(318, 17)
        Me.Label3.TabIndex = 18
        Me.Label3.Text = "Scanned Driving License  Storage Path at Server"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("Microsoft Sans Serif", 10.0!)
        Me.Label2.Location = New System.Drawing.Point(9, 16)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(132, 17)
        Me.Label2.TabIndex = 17
        Me.Label2.Text = "Camera Image Path"
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.btnExit)
        Me.GroupBox2.Controls.Add(Me.btnCancel)
        Me.GroupBox2.Controls.Add(Me.btnUpdate)
        Me.GroupBox2.Location = New System.Drawing.Point(8, 258)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(433, 73)
        Me.GroupBox2.TabIndex = 14
        Me.GroupBox2.TabStop = False
        '
        'btnExit
        '
        Me.btnExit.Location = New System.Drawing.Point(340, 20)
        Me.btnExit.Name = "btnExit"
        Me.btnExit.Size = New System.Drawing.Size(75, 30)
        Me.btnExit.TabIndex = 16
        Me.btnExit.Text = "Exit"
        Me.btnExit.UseVisualStyleBackColor = True
        '
        'btnCancel
        '
        Me.btnCancel.Location = New System.Drawing.Point(245, 20)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Size = New System.Drawing.Size(75, 30)
        Me.btnCancel.TabIndex = 15
        Me.btnCancel.Text = "Cancel"
        Me.btnCancel.UseVisualStyleBackColor = True
        '
        'btnUpdate
        '
        Me.btnUpdate.Location = New System.Drawing.Point(152, 20)
        Me.btnUpdate.Name = "btnUpdate"
        Me.btnUpdate.Size = New System.Drawing.Size(75, 30)
        Me.btnUpdate.TabIndex = 14
        Me.btnUpdate.Text = "Update"
        Me.btnUpdate.UseVisualStyleBackColor = True
        '
        'frmPathSettings
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(447, 342)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.GroupBox1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "frmPathSettings"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "SCANNED DOCUMENT PATH SETTINGS"
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents btnExit As System.Windows.Forms.Button
    Friend WithEvents btnCancel As System.Windows.Forms.Button
    Friend WithEvents btnUpdate As System.Windows.Forms.Button
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents txtScannedBlue_RCBookStoragePath As System.Windows.Forms.TextBox
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents txtScannedDDrivingLicenseStoragePath As System.Windows.Forms.TextBox
    Friend WithEvents txtVehicleImageStoragePath As System.Windows.Forms.TextBox
    Friend WithEvents txtCameraImagePath As System.Windows.Forms.TextBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
End Class
