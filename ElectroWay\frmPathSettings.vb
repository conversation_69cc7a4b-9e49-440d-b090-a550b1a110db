﻿Public Class frmPathSettings
    Dim cc As New Class1
    Private Sub frmPathSettings_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            ds = cc.GetDataset("select * from tbl_ScannedDoc_Path_Mst")
            If ds.Tables(0).Rows.Count <> 0 Then
                txtCameraImagePath.Text = ds.Tables(0).Rows(0).Item("Camera_ImagePath")
                txtVehicleImageStoragePath.Text = ds.Tables(0).Rows(0).Item("Vehicle_ImagePath")
                txtScannedBlue_RCBookStoragePath.Text = ds.Tables(0).Rows(0).Item("BluBook_Path")
                txtScannedDDrivingLicenseStoragePath.Text = ds.Tables(0).Rows(0).Item("DL_Path")
            End If

        Catch ex As Exception

        End Try
    End Sub
    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtCameraImagePath.Clear()
        txtScannedBlue_RCBookStoragePath.Clear()
        txtScannedDDrivingLicenseStoragePath.Clear()
        txtVehicleImageStoragePath.Clear()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtCameraImagePath.Text) <> "" And Trim(txtVehicleImageStoragePath.Text) <> "" And Trim(txtScannedBlue_RCBookStoragePath.Text) <> "" And Trim(txtScannedDDrivingLicenseStoragePath.Text) <> "" Then
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_ins_tbl_ScannedDoc_Path_Mst"
            cm.Parameters.Clear()
            cm.Parameters.AddWithValue("@val_Camera_ImagePath", txtCameraImagePath.Text.Trim)
            cm.Parameters.AddWithValue("@val_Vehicle_ImagePath", txtVehicleImageStoragePath.Text.Trim)
            cm.Parameters.AddWithValue("@val_BlueBook_Path", txtScannedBlue_RCBookStoragePath.Text.Trim)
            cm.Parameters.AddWithValue("@val_DL_Path", txtScannedDDrivingLicenseStoragePath.Text.Trim)
            cm.Parameters.AddWithValue("@val_UpdatedBy", User_ID)
            cm.ExecuteNonQuery()
            MsgBox("All the Path settings has been updated successfully !", vbInformation, "ElectroWay")
            txtCameraImagePath.Text = ""
            txtVehicleImageStoragePath.Text = ""
            txtScannedBlue_RCBookStoragePath.Text = ""
            txtScannedDDrivingLicenseStoragePath.Text = ""
        Else
            MsgBox("Blank Path cannot be updated ...", vbInformation, "ElectroWay")
        End If
    End Sub
End Class