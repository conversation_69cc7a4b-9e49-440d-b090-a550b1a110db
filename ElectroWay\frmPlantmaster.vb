﻿Public Class frmPlantmaster
    Dim cc As New Class1
    Dim Load1 As Boolean = False
    Private Sub frmPlantmaster_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        SelectCompany()
        Load1 = True
        SelectCompanyName()
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_plant_mst"
            ds = cc.GetDataset(str)
            gvPlant.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub SelectCompany()
        Try
            Dim str As String = "select * from tbl_Company_mst"
            dt = cc.GetDataTable(str)
            ddlCompnayCode.DataSource = dt

            ddlCompnayCode.DisplayMember = "Company_Code"
            ddlCompnayCode.ValueMember = "Company_Code"
        Catch ex As Exception

        End Try

    End Sub
    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(ddlCompnayCode.Text) <> "" And Trim(txtPlantCode.Text) <> "" Then
            ds = cc.GetDataset("select * from tbl_plant_mst where Plant_code = '" & Trim(txtPlantCode.Text) & "' and Company_Code  = '" & Trim(ddlCompnayCode.SelectedValue) & "'")
            If ds.Tables(0).Rows.Count = 0 Then
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_plant_mst"

                cm.Parameters.Clear()
                cm.Parameters.AddWithValue("@val_Plant_Code", txtPlantCode.Text.Trim)
                cm.Parameters.AddWithValue("@val_Plant_Name", txtPlantName.Text.Trim)
                cm.Parameters.AddWithValue("@val_Plant_Address", txtPlantAddress.Text.Trim)
                cm.Parameters.AddWithValue("@val_Company_Code", ddlCompnayCode.Text.Trim)
                cm.ExecuteNonQuery()
                loadGrid()
                MsgBox("Plant created successfully !", vbInformation, "ElectroWay")

                txtPlantCode.Text = ""
                txtPlantName.Text = ""
                txtPlantAddress.Text = ""
            Else
                Dim ans As String = MsgBox("Plant Code already exists with the seleted company ! Do you want to update?", vbYesNo, "ElectroWay")
                If ans = vbYes Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_plant_mst"
                    cm.Parameters.Clear()
                    cm.Parameters.AddWithValue("@val_Plant_Code", txtPlantCode.Text.Trim)
                    cm.Parameters.AddWithValue("@val_Plant_Name", txtPlantName.Text.Trim)
                    cm.Parameters.AddWithValue("@val_Plant_Address", txtPlantAddress.Text.Trim)
                    cm.Parameters.AddWithValue("@val_Company_Code", ddlCompnayCode.Text.Trim)
                    cm.ExecuteNonQuery()
                    loadGrid()
                    MsgBox("Plant Updated successfully !", vbInformation, "ElectroWay")
                    txtPlantCode.Text = ""
                    txtPlantName.Text = ""
                    txtPlantAddress.Text = ""
                End If
            End If
            ds.Dispose()
        Else
            MsgBox("Blank Company Code / Plant Code Not allowed .", vbInformation, "ElectroWay")

        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtPlantAddress.Clear()
        txtPlantCode.Clear()
        txtPlantName.Clear()
    End Sub

    Private Sub ddlCompnayCode_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlCompnayCode.SelectedIndexChanged
        SelectCompanyName()
    End Sub
    Private Sub SelectCompanyName()
        If Load1 = True Then
            Dim str As String = "select * from tbl_Company_mst where company_code = '" & Trim(ddlCompnayCode.SelectedValue) & "'"
            dr = cc.GetDataReader(str)
            Try
                While dr.Read
                    txtCompanyName.Text = dr("Company_Name").ToString
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub gvPlant_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvPlant.CellMouseClick
        Dim index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvPlant.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvPlant.SelectedRows(i).Index)

                    txtPlantCode.Text = Convert.ToString(gvPlant.Rows(index).Cells(1).Value)
                    txtPlantName.Text = Convert.ToString(gvPlant.Rows(index).Cells(2).Value)
                    txtPlantAddress.Text = Convert.ToString(gvPlant.Rows(index).Cells(3).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
End Class