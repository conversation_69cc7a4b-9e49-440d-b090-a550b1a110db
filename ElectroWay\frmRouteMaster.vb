﻿Imports System.Configuration
Imports System.Data
Imports System.Data.SqlClient
Public Class frmRouteMaster
    Dim c As String
    Dim d As String
    Dim i As Integer
    Dim WBCountID As Double
    Dim WtVal As String
    Dim AcceptClick As Integer
    Dim sel_item_index As Double
    Dim WT_UPDATE As String
    Dim HDDR_ID As Double
    Dim TVehType11 As String
    Dim Ch_no_RFID As String
    Dim Ch_no_RFID_SWT As String

    ''''''''''''''''''''''''''''''
    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table


    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    'Dim i As Double


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object

    Dim WBWeightDet As String

    Dim pono_wb As String
    Dim ponoLinItm_wb As String
    '''''''''''''''''''''''''''''
    Dim SAP_CON_NOT_AVAIL As Integer
    Dim cc As New Class1
    Private Sub frmRouteMaster_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Sys_loc_IP & "' and Node_Name = 'WEIGH BRIDGE'")
        'Try
        '    If dr.Read Then
        '        PortNum = rec1.Fields("Port_Number")
        '        BaudRateSettings = rec1.Fields("Settings")

        '    End If
        'Catch ex As Exception

        'End Try

        'dr.Close()
        ''LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL
        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        Try
            If dr.Read Then
                txtPlant.Text = dr("Plant_Name")
                txtCompany.Text = dr("Company_Code")
                txtGateNo.Text = dr("Node_No")
            Else
                MsgBox("IP number not mapped as ElectroWay Node ....or You are not connected with ElectroWay Server.")

            End If
        Catch ex As Exception

        End Try

        dr.Close()


        'Combo2.AddItem("")

        'If rec1.State = 1 Then rec1.Close()
        'rec1.ActiveConnection = con
        'rec1.Open("select * from tbl_Reference_Mst where Disabled = '0' order by Reference_Code Desc")
        'While rec1.EOF = False
        '    Combo2.AddItem(rec1.Fields("Reference_Code"))

        '    rec1.MoveNext()

        'End While

        'rec1.Close()


        txtvehicleNo.BackColor = Color.Gold
        ''-----------ListView--------------
        Dim lvwItem As New ListViewItem()
        lvwItem.Checked = True
        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False

        'Headings
        ListView1.Columns.Add("TRAN_ID")
        ListView1.Columns.Add("PO / SO No.", 250, HorizontalAlignment.Left)
        ListView1.Columns.Add("DO No.")
        ListView1.Columns.Add("DO/PO Line Item")
        ListView1.Columns.Add("Material Code")
        ListView1.Columns.Add("Material Description")
        ListView1.Columns.Add("DO/Ch Qty.")
        ListView1.Columns.Add("Unit")
        ListView1.Columns.Add("SAP Gate Entry No.")
        ListView1.Columns.Add("Ch. No.")
        ListView1.Columns.Add("Challan Date")
        'ListView1.Columns.Add("RR No.")
        'ListView1.Columns.Add("RR Date")
        'ListView1.Columns.Add("LR No.")
        'ListView1.Columns.Add("LR Date")
        'ListView1.Columns.Add("Rake No.")
        ListView1.Columns.Add("SO Line Item")
        ListView1.Columns.Add("Customer/Vendor")
        ListView1.Columns.Add("Customer/Vendor Name")
        'ListView1.Items.Clear()
        For i As Integer = 0 To ListView1.Columns.Count - 1
            'ListView1.Columns(i).Width = -2
            ListView1.Columns(i).Width = 100
        Next

        '------------------------------------
        lvCheckPost.View = View.Details
        lvCheckPost.GridLines = True
        lvCheckPost.FullRowSelect = True
        lvCheckPost.HideSelection = False
        lvCheckPost.MultiSelect = False
        lvCheckPost.Columns.Add("TRAN_ID")
        lvCheckPost.Columns.Add("IN_Date_Time")
        lvCheckPost.Columns.Add("OUT_Date_Time")
        For i As Integer = 0 To lvCheckPost.Columns.Count - 1
            lvCheckPost.Columns(i).Width = 100
        Next
    End Sub

    Private Sub btnView_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnView.Click
        On Error GoTo err

        ListView1.Items.Clear()
        lvCheckPost.Items.Clear()
        txtvehicleNo.Enabled = True
        txtTransactionNo.Text = ""
        txtvehicleNo.Text = ""
        txtTransporter.Text = ""

        txtTransporterDetails.Text = ""
        'Text24.Text = ""
        Label24.Text = "#"
        Label29.Text = "#"
        Label16.Text = "#"
        AcceptClick = 0
        'Text24.Enabled = True
        WT_UPDATE = ""
        'Text26.Visible = False
        'Label33.Visible = False
        'Label34.Visible = False
        'Text17.Text = ""
        txtMainGateINRemarks.Text = ""
        Ch_no_RFID = ""
        Ch_no_RFID_SWT = ""
        Label13.Text = ""
        Label13.ForeColor = Color.Black

        txtvehicleNo.Focus()

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        On Error GoTo err

        If ListView1.Items.Count > 0 Then

            dr = cc.GetDataReader("select * from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'")
            Dim GE_HDR_IDFlag As Boolean = False
            If dr.Read Then
                GE_HDR_IDFlag = True
            Else
                GE_HDR_IDFlag = False
            End If
            dr.Close()
            '----------------------
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            If GE_HDR_IDFlag = True Then
                dr = cc.GetDataReader("select * from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "' and IN_Date_Time <> '' and OUT_Date_Time = ''")
                Dim INOFlag As Boolean = False
                If dr.Read Then
                    INOFlag = True
                Else
                    INOFlag = False
                End If
                dr.Close()
                '============================
                If INOFlag = True Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    Dim cm1 As New SqlCommand
                    cm1.Connection = con
                    cm1.CommandType = CommandType.Text
                    cm1.CommandText = "update tbl_Allowed_Route_CheckPost_Det set OUT_Date_Time = getdate() , OUT_Done_By_userID = '" & User_ID & "' , OUT_Route_CheckGate_IP_No = '" & Sys_loc_IP & "' where  GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "' and OUT_Date_Time = ''"
                    cm1.ExecuteNonQuery()
                    cm1.Dispose()
                End If
                '-----------------
                If INOFlag = False Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    Dim cm2 As New SqlCommand
                    cm2.Connection = con
                    cm2.CommandType = CommandType.Text
                    cm2.CommandText = "insert into tbl_Allowed_Route_CheckPost_Det values ('" & Trim(txtTransactionNo.Text) & "' , '" & Trim(txtGateNo.Text) & "', '" & Sys_loc_IP & "', getdate() , '' ,  '' , '" & User_ID & "' , '' , '' , '' )"
                    cm2.ExecuteNonQuery()
                    cm2.Dispose()
                End If
            End If
            '---------------------------
            If GE_HDR_IDFlag = False Then
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                Dim cm3 As New SqlCommand
                cm3.Connection = con
                cm3.CommandType = CommandType.Text
                cm3.CommandText = "insert into tbl_Allowed_Route_CheckPost_Det values ('" & Trim(txtTransactionNo.Text) & "' , '" & Trim(txtGateNo.Text) & "', '" & Sys_loc_IP & "', getdate() , '' ,  '' , '" & User_ID & "' , '' , '' , '' )"
                cm3.ExecuteNonQuery()
            End If
            '-------------------------------
            MsgBox("Updated Successfully !", vbInformation, "ElectroWay")

            ListView1.Items.Clear()
            lvCheckPost.Items.Clear()
            txtvehicleNo.Enabled = True
            txtTransactionNo.Text = ""
            txtvehicleNo.Text = ""
            txtTransporter.Text = ""

            txtTransporterDetails.Text = ""
            'Text24.Text = ""
            Label24.Text = "#"
            Label29.Text = "#"
            AcceptClick = 0
            'Text24.Enabled = True
            WT_UPDATE = ""
            'Text26.Visible = False
            'Label33.Visible = False
            'Label34.Visible = False
            Label16.Text = "#"
            'Text17.Text = ""
            txtMainGateINRemarks.Text = ""
            Ch_no_RFID = ""
            Ch_no_RFID_SWT = ""
            Label13.Text = ""
            Label13.ForeColor = Color.Black

            txtvehicleNo.Focus()
        Else

            MsgBox("Invalid Vehicle.", vbInformation, "ElectroWay")
        End If



err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        On Error GoTo err
        'Text24.Enabled = True
        Me.Close()
err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        On Error GoTo err

        ListView1.Items.Clear()
        lvCheckPost.Items.Clear()
        txtvehicleNo.Enabled = True
        txtTransactionNo.Text = ""
        txtvehicleNo.Text = ""
        txtTransporter.Text = ""

        txtTransporterDetails.Text = ""
        'Text24.Text = ""
        Label24.Text = "#"
        Label29.Text = "#"
        Label16.Text = "#"
        AcceptClick = 0
        'Text24.Enabled = True
        WT_UPDATE = ""
        'Text26.Visible = False
        'Label33.Visible = False
        'Label34.Visible = False
        'Text17.Text = ""
        txtMainGateINRemarks.Text = ""
        Ch_no_RFID = ""
        Ch_no_RFID_SWT = ""
        Label13.Text = ""
        Label13.ForeColor = Color.Black

        txtvehicleNo.Focus()

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub ListView1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles ListView1.DoubleClick
        On Error GoTo err

        If ListView1.Items.Count = 1 And Trim((ListView1.Items(1).SubItems(2).Text)) = "" And Trim((ListView1.Items(1).SubItems(8).Text)) = "" Then
            Dim frmUpdateLineItems1 As New frmUpdateLineItems
            frmUpdateLineItems1.Owner = Me
            frmUpdateLineItems1.ShowDialog()


            frmUpdateLineItems1.txtTransactionNo.Text = Trim(txtTransactionNo.Text)
            frmUpdateLineItems1.txtVehicleNo.Text = Trim(txtvehicleNo.Text)
            frmUpdateLineItems1.Text8.Text = Trim(ListView1.Items(1).Text)


            frmUpdateLineItems1.txtMaterialCode.Text = Trim(ListView1.Items(1).SubItems(4).Text)
            frmUpdateLineItems1.txtMaterialName.Text = Trim(ListView1.Items(1).SubItems(5).Text)

            frmUpdateLineItems1.txtCustomerVendorcode.Text = Trim(ListView1.Items(1).SubItems(12).Text)
            frmUpdateLineItems1.txtCustomerVendorName.Text = Trim(ListView1.Items(1).SubItems(13).Text)

            'frmUpdateLineItems1.Text1.Text = Trim(Text30.Text)
            frmUpdateLineItems1.txtChallanNo.Text = Trim(ListView1.Items(1).SubItems(9).Text)
            frmUpdateLineItems1.txtChallanDate.Text = Trim(ListView1.Items(1).SubItems(10).Text)



            frmUpdateLineItems1.txtTransactionNo.Enabled = False

            'frmUpdateLineItems1.Show(vbMethod)
        End If

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description)
        End If
        Err.Clear()

    End Sub

    Private Sub ListView1_ItemCheck(ByVal sender As Object, ByVal e As System.Windows.Forms.ItemCheckEventArgs) Handles ListView1.ItemCheck
        On Error GoTo err

        If WT_UPDATE = "FWT" Then

            MsgBox("You are not allowed to uncheck any item during First WT.", vbInformation, "ElectroWay")
            'MsgBox Item

            For i As Integer = 0 To ListView1.Items.Count - 1
                ListView1.Items(i).Checked = True
            Next
            Exit Sub
        End If

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()


    End Sub

    Private Sub Text13_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTransporter.GotFocus
        On Error GoTo err

        txtTransporter.BackColor = Color.Gold

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub Text13_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransporter.KeyPress
        On Error GoTo err

        If AscW(e.KeyChar) = 13 Then
            txtTransporterDetails.Text = ""
            dr = cc.GetDataReader("select * from TRANSPORTER_MASTER where TRPT_CODE ='" & Trim(txtTransporter.Text) & "'")
            If dr.Read Then

                txtTransporterDetails.Text = dr("TRPT_NAME")
            Else
                MsgBox("Invalid Transporter Code.")
            End If
            dr.Close()
        End If

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()


    End Sub

    Private Sub Text13_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTransporter.LostFocus
        On Error GoTo err

        txtTransporter.BackColor = Color.White

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub Text14_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTransporterDetails.GotFocus
        On Error GoTo err

        txtTransporterDetails.BackColor = Color.Gold
err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub Text14_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTransporterDetails.LostFocus
        Try
            txtTransporterDetails.BackColor = Color.White
        Catch ex As Exception
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End Try

    End Sub

    Private Sub Text6_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtvehicleNo.GotFocus
        Try
            If vehicle_sel = 1 Then
                'Call Text6_KeyPress(13)
                Text6_KeyPress(sender, e)
                vehicle_sel = 0
            End If
            txtvehicleNo.BackColor = Color.Gold
        Catch ex As Exception
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End Try
    End Sub

    Private Sub Text6_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtvehicleNo.KeyPress
        Dim type_O_F_Veh As String = String.Empty
        Try
            ''MsgBox KeyAscii

FOR_REENTRY:
            Dim TRN_ID_1 As String = String.Empty
            Dim TypeOfVehicle As String = String.Empty
            'Dim maxWbCountID1 As Integer
            'Text26.Visible = False
            'Label33.Visible = False
            'Label34.Visible = False
            Label29.Text = ""
            ListView1.HideSelection = True
            'Dim Date_Time As String
            WBCountID = 0

            'If (KeyAscii >= 65 And KeyAscii <= 90) Or (KeyAscii >= 48 And KeyAscii <= 57) Or KeyAscii = 8 Or KeyAscii = 13 Then

            'Text6.Text = UCase(Text6.Text)
            '-----------------------
            'Dim lvi As New ListViewItem
            '----------------------------
            If AscW(e.KeyChar) = 13 Then

                txtvehicleNo.BackColor = Color.White
                txtvehicleNo.Enabled = False
                dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtvehicleNo.Text) & "' and Vehicle_Status = 'IN' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")
                ''OperationType = "AUTO"
                Dim VehicleFlag As Boolean = False
                Try
                    If dr.Read Then
                        VehicleFlag = True
                        TRN_ID_1 = dr("TRN_ID")

                        type_O_F_Veh = dr("Type_OF_Vehicle")

                        HDDR_ID = dr("TRN_ID")
                        TypeOfVehicle = dr("Type_Of_Vehicle")
                        TVehType11 = TypeOfVehicle
                        Label24.Text = dr("EntryDateTime")
                        txtTransactionNo.Text = dr("GE_HDR_ID")

                        txtTransporter.Text = dr("Transpoter_code")
                        txtTransporterDetails.Text = dr("TransporterName")
                        Label29.Text = dr("Type_Of_Vehicle")
                        Label16.Text = dr("EntryDateTime")
                        ''Text27.Text = dr("Plant_Code")

                        ''"""""""""""""""""""""""""""""""""""""""""""""""""
                        ''                    Text30.Text = dr("Seal_No")
                        ''                    Text31.Text = dr("Party_Gross_WT")
                        ''                    Text32.Text = dr("Party_Tare_WT")
                        ''                    Text33.Text = dr("Party_Net_WT")
                        txtMainGateINRemarks.Text = dr("Remarks_IN")
                        ''""""""""""""""""""""""""""""""""""""""""""""""""""192.1.71.225
                    Else
                        MsgBox("Invalid Vehicle Number !", vbInformation, "Electrosteel Castings Limited.")
                        txtvehicleNo.Text = ""
                        txtvehicleNo.Enabled = True
                        txtvehicleNo.Focus()
                        dr.Close()
                        Exit Sub
                    End If
                    dr.Close()
                    If VehicleFlag = True Then
                        dr = cc.GetDataReader("select * from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'")
                        Try
                            While dr.Read
                                Dim lvi As New ListViewItem
                                lvi.Text = dr("TRANS_ID")
                                lvi.SubItems.Add(dr("IN_Date_Time"))
                                lvi.SubItems.Add(dr("OUT_Date_Time"))
                                'i = lvCheckPost.Items.Count + 1
                                'ListView3.Items.Add(i, , dr("IN_Date_Time"))
                                'lvCheckPost.Items.Add(dr("IN_Date_Time"))
                                'lvCheckPost.Items(i).SubItems.Add(dr("OUT_Date_Time"))
                                'rec2.MoveNext()
                                lvCheckPost.Items.Add(lvi)
                            End While
                        Catch ex As Exception

                        End Try
                        dr.Close()
                    End If
                Catch ex As Exception

                End Try
                dr.Close()
                ''JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ

                dr = cc.GetDataReader("select isnull(sum(b.F_WT),0) from tbl_GE_HDR a, tbl_GE_DET b where a.GE_HDR_ID  =  b.GE_HDR_ID and  a.Vehicle_No = '" & Trim(txtvehicleNo.Text) & "' and a.Vehicle_Status = 'IN' and a.Plant_Code = '" & Trim(txtPlant.Text) & "' and a.Company_Code  = '" & Trim(txtCompany.Text) & "' and a.Remarks_IN not like '%Auto%IN%Grouping%'")
                Try
                    If dr.Read Then
                        If dr(0) > 0 Then

                        Else
                            Dim amms = MsgBox("FIRST WT Not done of this Vehicle... Are you want to proceed without weighment?", vbYesNo, "ElectroWay")

                            If amms = vbNo Then
                                txtvehicleNo.Text = ""
                                ListView1.Items.Clear()
                                'text6.Enabled=True
                                dr.Close()
                                Exit Sub
                            End If
                        End If

                    End If
                Catch ex As Exception

                End Try
                dr.Close()
                ''JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
                ''JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
                dr = cc.GetDataReader("select * from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "' and IN_Date_Time <> '' and OUT_Date_Time <> '' order by Trans_ID Desc")
                Try
                    If dr.Read Then
                        Dim ammsA = MsgBox("This Vehicle is already OUT from checkpost, Are you sure you want to IN again?", vbYesNo, "ElectroWay")

                        If ammsA = vbNo Then
                            txtvehicleNo.Text = ""
                            ListView1.Items.Clear()
                            lvCheckPost.Items.Clear()
                            txtvehicleNo.Enabled = True
                            txtTransactionNo.Text = ""
                            txtvehicleNo.Text = ""
                            txtTransporter.Text = ""

                            txtTransporterDetails.Text = ""
                            'Text24.Text = ""
                            Label24.Text = "#"
                            Label29.Text = "#"
                            Label16.Text = "#"

                            'text6.Enabled=True
                            Exit Sub
                        End If
                    End If
                Catch ex As Exception

                End Try
                dr.Close()
                ''JJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ
                dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1)
                i = 0
                Try
                    While dr.Read
                        Dim lvi As New ListViewItem
                        'i = ListView1.Items.Count + 1
                        'ListView1.Items.Add(dr("GE_DET_TRAN_ID"))
                        lvi.Text = dr("GE_DET_TRAN_ID")
                        'ListView1.Items(i).Checked = True
                        lvi.Checked = True
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("PO_NO"))
                            'ListView1.Items(i).SubItems.Add(dr("PO_NO"))
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("SO_NO"))
                            'ListView1.Items(i).SubItems.Add(dr("SO_NO"))
                        End If
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add("")
                            'ListView1.Items(i).SubItems.Add("")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("DO_NO"))
                            'ListView1.Items(i).SubItems.Add(dr("DO_NO"))
                        End If
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("PO_Line_Item"))
                            'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item"))
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("DO_Line_Item"))
                            'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item"))
                        End If
                        lvi.SubItems.Add(dr("Mat_CODE"))
                        'ListView1.Items(i).SubItems.Add(dr("Mat_CODE"))
                        lvi.SubItems.Add(dr("Mat_Desc"))
                        'ListView1.Items(i).SubItems.Add(dr("Mat_Desc"))

                        lvi.SubItems.Add(dr("DO_Challan_Qty") + 0)
                        lvi.SubItems.Add(dr("UOM") & "")
                        lvi.SubItems.Add(dr("Unloading_No") & "")
                        lvi.SubItems.Add(dr("Challan_No") & "")
                        lvi.SubItems.Add(dr("Challan_Date") & "")
                        lvi.SubItems.Add(dr("SO_Line_Item") & "")
                        'ListView1.Items(i).SubItems.Add(dr("DO_Challan_Qty") + 0)
                        'ListView1.Items(i).SubItems.Add(dr("UOM") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Unloading_No") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Challan_No") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Challan_Date") & "")
                        'ListView1.Items(i).SubItems.Add(dr("SO_Line_Item") & "")
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("Vendor_Code") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Vendor_Code") & "")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("Customer_Code") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Customer_Code") & "")
                        End If
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("Vendor_Name") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Vendor_Name") & "")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("Customer_Name") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Customer_Name") & "")
                        End If

                        txtRakeGroupRef.Text = dr("Grouping_Ref_Code")

                        'rec1.MoveNext()
                        ListView1.Items.Add(lvi)
                    End While
                Catch ex As Exception

                End Try
                dr.Close()

                btnUpdate.Focus()

                ''KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK
                dr = cc.GetDataReader("select * from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'")
                Try
                    Dim AllowFlag As Boolean = False
                    If dr.Read Then
                        AllowFlag = True

                    Else
                        Label13.Text = "IN"
                        Label13.ForeColor = Color.Blue
                    End If
                    dr.Close()
                    '------------
                    If AllowFlag = True Then
                        dr = cc.GetDataReader("select * from tbl_Allowed_Route_CheckPost_Det where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "' and IN_Date_Time <> '' and OUT_Date_Time = ''")
                        If dr.Read Then

                            Label13.Text = "OUT"
                            Label13.ForeColor = Color.Red
                        Else

                            Label13.Text = "IN"
                            Label13.ForeColor = Color.Blue

                        End If
                        dr.Close()
                    End If
                Catch ex As Exception

                End Try
                ''KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK
            End If


            If Label29.Text = "INTRDEPT" Then
                Label29.Font = New Font(Label1.Font.FontFamily, 14)
                Label29.ForeColor = Color.Red

            Else
                ''  &H00FF0000&
                Label29.Font = New Font(Label1.Font.FontFamily, 10)
                Label29.ForeColor = Color.Blue
                Label29.Font = New Font(Label1.Font.FontFamily, 10, FontStyle.Bold)
                txtvehicleNo.BackColor = Color.White
            End If

            ''''    If Text24.Enabled = True Then
            ''''            Text24.SetFocus
            ''''            Text24.BackColor = RGB(255, 255, 0)
            ''''    ElseIf Text26.Enabled = True Then
            ''''            Text26.SetFocus
            ''''            Text26.BackColor = RGB(255, 255, 0)
            ''''    End If
            ''''
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Text6_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtvehicleNo.LostFocus
        txtvehicleNo.BackColor = Color.White
    End Sub

    Private Sub Text6_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtvehicleNo.TextChanged
        On Error GoTo err

        txtvehicleNo.Text = UCase(Trim(txtvehicleNo.Text))
        txtvehicleNo.SelectionStart = Len(txtvehicleNo.Text)
        'Text6.SetFocus
err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        On Error GoTo err


        'If OperationType = "AUTO" Then
        ''''''''''''MsgBox MSComm1.Input
        Label24.Text = Format(Today.Date, "dd-MM-yyyy") & "  " & Format(TimeOfDay.ToString("HH-mm-ss"))

        'End If
err:
        If Err.Number <> 0 Then
            MsgBox(Err.Number & "   " & Err.Description)
            Me.Close()
        End If
        Err.Clear()
    End Sub
    Private Sub push_data_weighment()
        Dim TypeOfVehicle As String = String.Empty, Unld_Nmbr As String = String.Empty, DO_Nmbr As String = String.Empty, PLANT_CODE111 As String = String.Empty, GE_HDRID1 As String = String.Empty, Result As String = String.Empty
        Dim oStatus, PostCoil, oItmID, oVhclNo, oVendor, oPONo, oPOItem, oUnloadingNo, oTrType, oWerks, oTrnDate, oTrnTime, oZInOut, oWTDet, oChWT, oUOM, oGrossWT, oTareWT, oNetWT, oWBTrID, funcControl, oRFC, oTrnID, oCustomer, oSONo, oSOLItem
        On Error GoTo err

        Dim Date_v As String = String.Empty
        Dim Time_v As String = String.Empty
        Dim WT_UOM As String = String.Empty
        Dim ch_wt As Double
        Dim tareeWt As Double
        Dim GrosWT As Double
        Dim NettWT As Double


        Date_v = Format(Today.Date, "yyyy") & Format(Today.Date, "MM") & Format(Today.Date, "dd")
        Time_v = Format(TimeOfDay.ToString("HH")) & Format(TimeOfDay.ToString("mm"), "00") & Format(TimeOfDay.ToString("mm"))

        dr = cc.GetDataReader("select * FROM tbl_GE_Det WHERE GE_HDR_TRAN_ID = " & HDDR_ID)
        Do While dr.Read

            If dr("NET_WT") > 0 And (dr("Unloading_No") <> "" Or dr("DO_No") <> "") Then

                TypeOfVehicle = TVehType11
                Unld_Nmbr = dr("Unloading_no")
                DO_Nmbr = dr("DO_NO")

                If TypeOfVehicle = "PURCH" Or TypeOfVehicle = "PURCHRET" Or TypeOfVehicle = "INTRDEPT" Or TypeOfVehicle = "GATEPASS" Then

                    WBWeightDet = ""

                    pono_wb = dr("PO_No")
                    ponoLinItm_wb = dr("PO_Line_Item")


                    '''''  Call WB_Determin   '' BLOCKED on 11 MAR 2015 during testing


                    functionCtrl = CreateObject("SAP.Functions")
                    sapConnection = functionCtrl.Connection
                    sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
                    sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
                    sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
                    sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
                    sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
                    sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
                    sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
                    sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub

                    Else
                        ''MsgBox "Connected ........"


                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        oTrnID.Value = Trim(dr("GE_DET_TRAN_ID") & "\" & Trim(txtPlant.Text))
                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully deleted from Zwt_bg table
                                PostCoil = 2
                            End If
                        End If
                        ''**************************************************

                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_data_update")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        oItmID = oRFC.Exports("ZMATNR")
                        oVhclNo = oRFC.Exports("ZVHCL_NMBR")

                        oVendor = oRFC.Exports("zlifnr")

                        oPONo = oRFC.Exports("ZPO_SO_NO")
                        oPOItem = oRFC.Exports("ZPO_ITEM")
                        oUnloadingNo = oRFC.Exports("ZGATENO")
                        oTrType = oRFC.Exports("ZTR_TYPE")

                        oWerks = oRFC.Exports("zwerks")


                        oTrnDate = oRFC.Exports("ZTRN_DATE")
                        oTrnTime = oRFC.Exports("ZTRN_TIME")

                        oZInOut = oRFC.Exports("zinout")

                        oWTDet = oRFC.Exports("ztweight")

                        oChWT = oRFC.Exports("ZCHL_GTY")
                        oUOM = oRFC.Exports("ZWT_UNIT")
                        oGrossWT = oRFC.Exports("ZGROSS_WT")
                        oTareWT = oRFC.Exports("ZTARE_WT")
                        oNetWT = oRFC.Exports("ZNET_WT")
                        oWBTrID = oRFC.Exports("ZWB_TR_ID")

                        oTrnID.Value = Trim(dr("GE_DET_TRAN_ID") & "\" & Trim(txtPlant.Text))            ''    "TEST0001136"
                        oItmID.Value = dr("Mat_Code")
                        oVhclNo.Value = Trim(txtvehicleNo.Text)

                        oVendor.Value = dr("Vendor_Code")

                        oPONo.Value = dr("PO_No")
                        oPOItem.Value = dr("PO_Line_Item")
                        oUnloadingNo.Value = dr("Unloading_No")
                        oTrType.Value = dr("Type_Of_Vehicle")


                        '>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            dr.Close()
                            Exit Sub

                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "ZTM_HGATE_ENTRY"


                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        ''objOptTab.FreeTable
                        objOptTab1.FreeTable()
                        'Then set values
                        objOptTab1.Rows.Add()
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "GATENO = '" & Unld_Nmbr & "'"

                        objFldTab1.FreeTable()

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  ''''''"WERKS"  ''



                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If

                        For Each objDatRec1 In objDatTab1.Rows


                            For Each objFldRec1 In objFldTab1.Rows
                                PLANT_CODE111 = "ES01"   ''''Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))


                            Next
                        Next


                        ''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        oWerks.Value = PLANT_CODE111


                        '                                        If PLANT_CODE_UPL_ZWT_BG = "" Then
                        '                                         oWerks.Value = Trim(Text27.Text)
                        '                                       Else
                        '                                           oWerks.Value = PLANT_CODE_UPL_ZWT_BG
                        '                                       End If
                        '
                        oTrnDate.Value = Date_v    ' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oTrnTime.Value = Time_v    '   "103442"      ''format used in ingress is Hour Minute Second without having any space    ''format(Time, "hh:mm:ss")



                        If TypeOfVehicle = "PURCH" Then
                            oZInOut.Value = "I"
                        ElseIf TypeOfVehicle = "PURCHRET" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "INTRDEPT" Then
                            oZInOut.Value = "D"
                        ElseIf TypeOfVehicle = "GATEPASS" Then
                            oZInOut.Value = "G"
                        End If


                        oWTDet.Value = WBWeightDet

                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_DET_TRAN_ID = " & dr("GE_DET_TRAN_ID"))
                        If dr.Read Then
                            GE_HDRID1 = dr("GE_HDR_ID")
                            ch_wt = dr("DO_Challan_QTY")
                            WT_UOM = Trim(dr("UOM"))
                            If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "DMT") Then
                                GrosWT = (dr("F_WT")) / 1000
                                tareeWt = (dr("S_WT")) / 1000
                                NettWT = (dr("Net_WT")) / 1000

                            Else
                                GrosWT = dr("F_WT")
                                tareeWt = dr("S_WT")
                                NettWT = dr("Net_WT")
                            End If

                        End If
                        dr.Close()
                        oChWT.Value = ch_wt
                        'oUOM.Value = WT_UOM
                        If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "KG" Or WT_UOM = "DMT") Then
                            oUOM.Value = WT_UOM
                        Else
                            oUOM.Value = "KG"
                        End If
                        oGrossWT.Value = GrosWT
                        oTareWT.Value = tareeWt    ' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oNetWT.Value = NettWT
                        oWBTrID.Value = GE_HDRID1

                        ''   "TEST-VEHICLE052014"


                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                                '''''''''''''''''''''''''''
                                cm.Connection = con
                                cm.CommandType = CommandType.Text
                                cm.CommandText = "update tbl_GE_DET set DataUploadedIN_SAP = 'U' where GE_HDR_ID  ='" & GE_HDRID1 & "'"
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()

                                '''''''''''''''''''''''''''
                            End If
                            If oStatus = 2 Then ' Data Exists in SAP
                                PostCoil = 5
                            End If
                        End If

                        Result = oRFC.Call
                        'MsgBox Result

                    End If
                ElseIf TypeOfVehicle = "SALES" Or TypeOfVehicle = "STKTROUT" Or TypeOfVehicle = "SALESRET" Then


                    functionCtrl = CreateObject("SAP.Functions")
                    sapConnection = functionCtrl.Connection


                    sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
                    sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
                    sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
                    sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
                    sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
                    sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
                    sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
                    sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub

                    Else
                        ''MsgBox "Connected ........"


                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        oTrnID.Value = dr("GE_DET_TRAN_ID")
                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                            End If
                        End If
                        ''**************************************************

                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_data_update")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        oItmID = oRFC.Exports("ZMATNR")
                        oVhclNo = oRFC.Exports("ZVHCL_NMBR")

                        oCustomer = oRFC.Exports("zkunnr")

                        '''''Set oLIFNR = oRFC.Exports("zlifnr")

                        oSONo = oRFC.Exports("ZPO_SO_NO")
                        oSOLItem = oRFC.Exports("ZPO_ITEM")

                        oPONo = oRFC.Exports("zvbeln")  ' DO no
                        oPOItem = oRFC.Exports("zposnr")   ''   DO Line item
                        'Set oUnloadingNo = oRFC.Exports("ZUN_NO")
                        oTrType = oRFC.Exports("ZTR_TYPE")

                        oWerks = oRFC.Exports("zwerks")

                        oTrnDate = oRFC.Exports("ZTRN_DATE")
                        oTrnTime = oRFC.Exports("ZTRN_TIME")

                        oZInOut = oRFC.Exports("zinout")

                        oChWT = oRFC.Exports("ZCHL_GTY")     ''''oRFC.Exports("ZCHAL_QTY")
                        oUOM = oRFC.Exports("ZWT_UNIT")
                        oGrossWT = oRFC.Exports("ZTARE_WT")
                        oTareWT = oRFC.Exports("ZGROSS_WT")
                        oNetWT = oRFC.Exports("ZNET_WT")
                        oWBTrID = oRFC.Exports("ZWB_TR_ID")

                        oTrnID.Value = Trim(dr("GE_DET_TRAN_ID") & "\" & Trim(txtPlant.Text))              ''    "TEST0001136"
                        oItmID.Value = dr("Mat_Code")
                        oVhclNo.Value = Trim(txtvehicleNo.Text)

                        oCustomer.Value = dr("Customer_Code")

                        oSONo.Value = dr("SO_No")
                        oSOLItem.Value = dr("SO_Line_Item")

                        oPONo.Value = dr("DO_No")
                        oPOItem.Value = dr("DO_Line_Item")
                        ''oUnloadingNo.Value = ListView1.Items(i).ListSubItems(11).Text
                        oTrType.Value = dr("Type_Of_Vehicle")


                        '>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            dr.Close()
                            Exit Sub

                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "LIPS"


                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        ''objOptTab.FreeTable
                        objOptTab1.FreeTable()
                        'Then set values
                        objOptTab1.Rows.Add()
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & DO_Nmbr & "'"

                        objFldTab1.FreeTable()

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "WERKS"  ''



                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If

                        For Each objDatRec1 In objDatTab1.Rows


                            For Each objFldRec1 In objFldTab1.Rows
                                PLANT_CODE111 = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))


                            Next
                        Next


                        ''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        oWerks.Value = PLANT_CODE111

                        '                                       If PLANT_CODE_UPL_ZWT_BG = "" Then
                        '
                        '                                            oWerks.Value = Trim(Text27.Text)
                        '                                        Else
                        '                                            oWerks.Value = PLANT_CODE_UPL_ZWT_BG
                        '                                        End If
                        '
                        oTrnDate.Value = Date_v    ' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oTrnTime.Value = Time_v    '   "103442"      ''format used in ingress is Hour Minute Second without having any space    ''format(Time, "hh:mm:ss")





                        If TypeOfVehicle = "SALES" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "STKTROUT" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "SALESRET" Then
                            oZInOut.Value = "I"
                        End If



                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_DET_TRAN_ID = " & dr("GE_DET_TRAN_ID"))
                        If dr.Read Then
                            GE_HDRID1 = dr("GE_HDR_ID")
                            ch_wt = dr("DO_Challan_QTY")
                            WT_UOM = Trim(dr("UOM"))
                            If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "DMT") Then
                                GrosWT = (dr("F_WT")) / 1000
                                tareeWt = (dr("S_WT")) / 1000
                                NettWT = (dr("Net_WT")) / 1000

                            Else
                                GrosWT = dr("F_WT")
                                tareeWt = dr("S_WT")
                                NettWT = dr("Net_WT")
                            End If
                        End If
                        dr.Close()
                        oChWT.Value = ch_wt
                        If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "KG") Then
                            oUOM.Value = WT_UOM
                        Else
                            oUOM.Value = "KG"
                        End If
                        oGrossWT.Value = GrosWT
                        oTareWT.Value = tareeWt    ' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oNetWT.Value = NettWT
                        oWBTrID.Value = GE_HDRID1
                        ''   "TEST-VEHICLE052014"


                        If oRFC.Call = True Then
                            'oStatus = oRFC.Imports("matnr")
                            ''MsgBox oStatus
                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                                '''''''''''''''''''''''''''
                                cm.Connection = con
                                cm.CommandType = CommandType.Text
                                cm.CommandText = "update tbl_GE_DET set DataUploadedIN_SAP = 'U' where GE_HDR_ID  ='" & GE_HDRID1 & "'"
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()

                                '''''''''''''''''''''''''''

                            End If
                            If oStatus = 2 Then ' Data Exists in SAP
                                PostCoil = 5
                            End If
                        End If

                        Result = oRFC.Call
                        'MsgBox Result

                    End If


                End If

            End If

            'rec4.MoveNext()

        Loop
        dr.Close()

err:
        If Err.Number <> 0 Then
            MsgBox(Err.Number & "   " & Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()

    End Sub


    Private Sub WB_Determin()
        Dim TWeight_1 As String = String.Empty
        On Error GoTo err

        Call SAP_Con1()

        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            SAP_CON_NOT_AVAIL = 1
            'Label25.Text = "SAP CONNECTION NOT AVAILABLE."
            Exit Sub

        Else

            'Label25.Text = ""

            Dim objRfcFunc As Object
            'Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
            objQueryTab.Value = "EKPO"

            objOptTab = objRfcFunc.Tables("OPTIONS")
            objFldTab = objRfcFunc.Tables("FIELDS")
            objDatTab = objRfcFunc.Tables("DATA")
            'First we set the condition
            'Refresh Table
            objOptTab.FreeTable()
            'Then set values
            ''objOptTab.Rows.Add
            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
            objOptTab.Rows.Add()
            objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & pono_wb & "' and EBELP = '" & ponoLinItm_wb & "'"  ' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

            objFldTab.FreeTable()

            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "TWEIGHT"

            If objRfcFunc.Call = False Then
                MsgBox(objRfcFunc.Exception)
            End If

            'i = 5

            If objDatTab.Rows.Count = 0 Then
                ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
            Else

                For Each objDatRec In objDatTab.Rows
                    'i = i + 1
                    For Each objFldRec In objFldTab.Rows
                        TWeight_1 = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))


                    Next
                Next
            End If

            WBWeightDet = TWeight_1

        End If

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()

    End Sub

    Private Sub SAP_Con1()

        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
        sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
        sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
        sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
        sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
        sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
        sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
        sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")
    End Sub

    Private Sub SAP_Con2()
        functionCtr2 = CreateObject("SAP.Functions")
        sapConnection = functionCtr2.Connection
        sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
        sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
        sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
        sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
        sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
        sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
        sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
        sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")
    End Sub



    'Private Function breakpoint()
    '    Do
    '        dummy = DoEvents
    '    Loop Until MSComm1.InBufferCount >= 7
    'End Function

    Private Sub Timer2_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer2.Tick
        Dim WB_TRAN_TYPE As String = String.Empty
        If Trim(txtvehicleNo.Text) = "" Then

            dr = cc.GetDataReader("select * from tbl_WB_IP_VS_ANTENA_IP where WB_MC_IP ='" & Trim(Sys_loc_IP) & "'")
            Try
                If dr.Read Then
                    If dr("WB_TRAN_TYPE") = "F" Then
                        WB_TRAN_TYPE = "F"
                    ElseIf dr("WB_TRAN_TYPE") = "S" Then
                        WB_TRAN_TYPE = "S"
                    End If
                End If
            Catch ex As Exception

            End Try
            dr.Close()
            '----------------------------
            If WB_TRAN_TYPE = "F" Then
                dr = cc.GetDataReader("select * from TRCK_WB_TMP where isnull(TWT_FST_WT1,'') = '' and TWT_IP_ADD1 ='" & dr("WB_RFID_ANTENA_IP") & "' and datediff(minute,TWT_ENT_TM1, getdate())< = 1274")
                Try
                    If dr.Read Then
                        txtvehicleNo.Text = dr("TWT_TRK_NO")
                        Ch_no_RFID = dr("CHALLAN_NO")
                        'Call Text6_KeyPress(13)
                        Text6_KeyPress(sender, e)
                    End If
                Catch ex As Exception

                End Try
                dr.Close()
            End If
            '------------------------
            If WB_TRAN_TYPE = "S" Then
                dr = cc.GetDataReader("select * from TRCK_WB_TMP where isnull(TWT_FST_WT1,'') <> '' and isnull(TWT_FST_WT2,'') = '' and TWT_IP_ADD2 ='" & dr("WB_RFID_ANTENA_IP") & "' and datediff(minute,TWT_ENT_TM2, getdate())< = 1274")
                Try
                    If dr.Read Then
                        txtvehicleNo.Text = dr("TWT_TRK_NO")
                        Ch_no_RFID_SWT = dr("CHALLAN_NO")
                        'Call Text6_KeyPress(13)
                        Text6_KeyPress(sender, e)
                    End If
                Catch ex As Exception

                End Try
                dr.Close()
            End If
        End If
    End Sub

    Private Sub ListView1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ListView1.SelectedIndexChanged

    End Sub
End Class