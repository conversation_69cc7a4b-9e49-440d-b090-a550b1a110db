﻿Public Class frmSelectMaterial
    Dim cc As New Class1
    Private Sub txtMaterialCode_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtMaterialCode.KeyDown
       
    End Sub

    Private Sub txtMaterialCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtMaterialCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            dr = cc.GetDataReader("select * from tbl_Material_Mst where Material_code  = '" & Trim(txtMaterialCode.Text) & "'")
            If dr.Read Then
                txtMaterialDesc.Text = dr("Material_Name")
                frmWM.ListView1.Items(1).SubItems.Add(dr("Material_Code"))  '''''  mat code
                frmWM.ListView1.Items(1).SubItems.Add(dr("Material_Name"))   '''''  mat desc

                frmWM.ListView1.Items(1).SubItems.Add("")
                frmWM.ListView1.Items(1).SubItems.Add("")
                frmWM.ListView1.Items(1).SubItems.Add("")

                'Text1.Text = rec4.Fields("Material_Name")
                'frmWM.ListView1.ListItems(1).ListSubItems.Add(4, , rec4.Fields("Material_Code"))  '''''  mat code
                'frmWM.ListView1.ListItems(1).ListSubItems.Add(5, , rec4.Fields("Material_Name"))   '''''  mat desc

                'frmWM.ListView1.ListItems(1).ListSubItems.Add(6, , "")
                'frmWM.ListView1.ListItems(1).ListSubItems.Add(7, , "")
                'frmWM.ListView1.ListItems(1).ListSubItems.Add(8, , "")
                ''KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK

                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "update tbl_GE_DET set Mat_Code  ='" & Trim(txtMaterialCode.Text) & "' , Mat_Desc = '" & Trim(txtMaterialDesc.Text) & "' where GE_HDR_ID  = '" & Trim(frmWM.txtTransactionNo.Text) & "' and GE_DET_TRAN_ID  =" & Trim(frmWM.ListView1.Items(1).Text)
                cm.ExecuteNonQuery()


                ''KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK
                Me.Close()
            Else
                MsgBox("Material Code Not exists in master.", vbInformation, "ElectroWay")
                txtMaterialCode.Text = ""
                txtMaterialCode.Focus()

            End If
            dr.Close()
        End If
    End Sub
    Private Sub txtMaterialCode_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtMaterialCode.TextChanged

    End Sub
End Class