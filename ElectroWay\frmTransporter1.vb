﻿Public Class frmTransporter1
    Dim cc As New Class1
    Private Sub frmTransporter1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_Transporter_mst"
            ds = cc.GetDataset(str)
            gvTransporter.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtTransporterCode.Text) <> "" And Trim(txtTransporterName.Text) <> "" Then
            ds = cc.GetDataset("select * from tbl_Transporter_mst where Transporter_Code = '" & Trim(txtTransporterCode.Text) & "'")
            If ds.Tables(0).Rows.Count = 0 Then
                'WB_ID = rec1.Fields(0)
                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "insert into tbl_Transporter_mst values ('" & Trim(txtTransporterCode.Text) & "' , '" & Trim(txtTransporterName.Text) & "' )"
                cm.ExecuteNonQuery()
                loadGrid()
                MsgBox("Transporter Master record has been updated successfully !", vbInformation, "ElectroWay")
                txtTransporterCode.Text = ""
                txtTransporterName.Text = ""
            Else
                Dim ans = MsgBox("Transporter Already Exists ! Do you want to Update..", vbYesNo, "ElectroWay")
                If ans = vbYes Then

                    cm.Connection = con
                    cm.CommandType = CommandType.Text
                    cm.CommandText = "delete from tbl_Transporter_mst where Transporter_Code = '" & Trim(txtTransporterCode.Text) & "'"
                    cm.ExecuteNonQuery()

                    cm.Connection = con
                    cm.CommandType = CommandType.Text
                    cm.CommandText = "insert into tbl_Transporter_mst values ('" & Trim(txtTransporterCode.Text) & "' , '" & Trim(txtTransporterName.Text) & "' )"
                    cm.ExecuteNonQuery()
                    loadGrid()
                    MsgBox("Transporter Master record has been updated successfully !", vbInformation, "ElectroWay")
                    txtTransporterCode.Text = ""
                    txtTransporterName.Text = ""
                End If
            End If
            ds.Dispose()
        Else
            MsgBox("Blank Transporter Master Record cannot be updated !", vbInformation, "ElectroWay")
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtTransporterCode.Text = ""
        txtTransporterName.Text = ""
        txtTransporterCode.Focus()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub
    Private Sub txtTransporterCode_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransporterCode.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "TRANSPORTER"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
        End If
    End Sub

    Private Sub txtTransporterCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransporterCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtTransporterName.Focus()
            dr = cc.GetDataReader("select * from tbl_Transporter_mst where Transporter_Code = '" & Trim(txtTransporterCode.Text.Trim) & "'")
            Try
                While dr.Read
                    txtTransporterName.Text = dr("Transporter_Name")
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Try
            Dim str As String = "SELECT * FROM tbl_Transporter_mst where Transporter_Code   like '%" & txtSearch.Text & "%' or Transporter_Name like '%" & txtSearch.Text & "%'"
            ds = cc.GetDataset(str)
            gvTransporter.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub

    Private Sub gvTransporter_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvTransporter.CellMouseClick
        Dim index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvTransporter.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvTransporter.SelectedRows(i).Index)

                    txtTransporterCode.Text = Convert.ToString(gvTransporter.Rows(index).Cells(0).Value)
                    txtTransporterName.Text = Convert.ToString(gvTransporter.Rows(index).Cells(1).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub
End Class