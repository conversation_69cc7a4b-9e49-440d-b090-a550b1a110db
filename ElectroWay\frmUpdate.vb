﻿Public Class frmUpdate
    Dim cc As New Class1
    Private Sub frmUpdate_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub
    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        Dim ans = MsgBox("Are you sure to update Challan & Transporter ?", vbYesNo, "ElectroWay")

        If ans = vbYes Then
            cm.Connection = con
            cm.CommandType = CommandType.Text
            cm.CommandText = "update tbl_GE_DET set DO_Challan_Qty = " & Val(Trim(txtChallanQty.Text)) & " where GE_HDR_ID  ='" & Trim(txtTransactionNo.Text) & "'"
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.ExecuteNonQuery()

            cm.Connection = con
            cm.CommandType = CommandType.Text
            cm.CommandText = "update tbl_GE_HDR set Transpoter_Code = '" & Trim(txtTransporterCode.Text) & "' , TransporterName  = '" & Trim(txtTransporterName.Text) & "' where GE_HDR_ID  ='" & Trim(txtTransactionNo.Text) & "'"
            If con.State = ConnectionState.Closed Then
                con.Open()
            End If
            cm.ExecuteNonQuery()

        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtChallanQty.Text = ""
        txtTransporterName.Text = ""
        txtTransporterCode.Text = ""
    End Sub

    Private Sub txtChallanQty_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtChallanQty.LostFocus
        If IsNumeric(txtChallanQty.Text) = False Then
            MsgBox("Challan Qty should be only numeric ", vbInformation, "ElectroWay")
            txtChallanQty.Text = ""
            txtChallanQty.Focus()
        End If
    End Sub

    Private Sub txtTransporterCode_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtTransporterCode.KeyDown
        If e.KeyCode = 8 Or e.KeyCode = 46 Then
            txtTransporterName.Text = ""
        End If
    End Sub

    Private Sub txtTransporterCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransporterCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            ''Text6.SetFocus
            dr = cc.GetDataReader("select * from tbl_Transporter_mst where Transporter_Code = '" & Trim(txtTransporterCode.Text) & "'")
            If dr.Read Then
                txtTransporterName.Text = dr("Transporter_Name")
                ''Text2.SetFocus
            Else
                MsgBox("Transporter Code not exists..", vbInformation, "ElectroWay")
            End If
            dr.Close()
        Else
            txtTransporterName.Text = ""
        End If
    End Sub
End Class