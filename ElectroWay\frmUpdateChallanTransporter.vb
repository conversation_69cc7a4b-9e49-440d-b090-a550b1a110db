﻿Public Class frmUpdateChallanTransporter
    Dim cc As New Class1
    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        On Error GoTo err
        Text5.Text = ""
        Text6.Text = ""
        Text1.Text = ""
        Text14.Text = ""
        Text15.Text = ""
        Text5.Enabled = True

        Text5.Focus()
        Text1.Enabled = True
err:
        Err.Clear()
    End Sub

    Private Sub Text1_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles Text1.KeyPress
        On Error GoTo err
        If AscW(e.KeyChar) = 13 Then
            ''Text6.SetFocus
            dr = cc.GetDataReader("select * from tbl_Transporter_mst where Transporter_Code = '" & Trim(Text1.Text) & "'")
            If dr.Read Then
                Text15.Text = dr("Transporter_Name")
                ''Text2.SetFocus
                Text1.Enabled = False
            Else
                <PERSON>("Not valid Transporter Code.", vbInformation, "ElectroWay")
                Text15.Text = ""
                Text1.Text = ""
                Text1.Enabled = True

            End If
            dr.Close()

        End If
err:
        Err.Clear()
    End Sub

    Private Sub Text5_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles Text5.KeyPress
        On Error GoTo err
        If AscW(e.KeyChar) = 13 Then
            Text5.Enabled = False

            dr = cc.GetDataReader("select sum(F_WT) from tbl_GE_DET where GE_HDR_ID  ='" & Trim(Text5.Text) & "'")
            If dr.Read Then

                If dr(0) = 0 Then

                Else
                    MsgBox("First WT. already done, you are not allowed to modify Challan No/Transporter.", vbInformation, "ElectroWay")
                    ''Text5.Text = ""
                    ''Text5.Enabled = True
                    Exit Sub
                End If

            End If
            dr.Close()

            dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID  ='" & Trim(Text5.Text) & "' and Type_Of_Vehicle = 'PURCH' and Grouping_Ref_Code <> ''")
            If dr.Read = True Then


                dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID  ='" & Trim(Text5.Text) & "'")
                If dr.Read Then
                    Text6.Text = dr("Vehicle_No")
                    Text1.Text = dr("Transpoter_Code")
                    Text15.Text = dr("TransporterName")
                End If
                dr.Close()

                dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID  ='" & Trim(Text5.Text) & "'")
                If dr.Read Then
                    Text14.Text = dr("Challan_No")
                End If
                dr.Close()
            Else
                MsgBox("Not a valid transaction number to modify the Challan No/Transporter", vbInformation, "ElectroWay")
            End If
            dr.Close()
        End If

err:
        ''MsgBox err.Description
        Err.Clear()
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        On Error GoTo err

        If Trim(Text5.Text) <> "" Then

            Dim ans = MsgBox("Are you sure to update Challan No. & Transporter ?", vbYesNo, "ElectroWay")

            If ans = vbYes Then
                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "update tbl_GE_DET set Challan_No = '" & Trim(Text14.Text) & "' where GE_HDR_ID  ='" & Trim(Text5.Text) & "'"
                cm.ExecuteNonQuery()

                cm.Connection = con
                cm.CommandType = CommandType.Text
                cm.CommandText = "update tbl_GE_HDR set Transpoter_Code = '" & UCase(Trim(Text1.Text)) & "' , TransporterName  = '" & UCase(Trim(Text15.Text)) & "' where GE_HDR_ID  ='" & Trim(Text5.Text) & "'"
                cm.ExecuteNonQuery()

            End If
        End If

err:
        Err.Clear()

    End Sub
End Class