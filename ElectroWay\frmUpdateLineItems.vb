﻿Public Class frmUpdateLineItems
    Dim Vl_typee As String
    Dim GE_DEET_TRAAN_IID As Double

    Dim cc As New Class1
    Private Sub frmUpdateLineItems_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            Vl_typee = frmWM.Label29.Text
            GE_DEET_TRAAN_IID = frmWM.ListView1.Items(1).Text
        Catch ex As Exception

        End Try
    End Sub
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtTransactionNo.Text = ""
        txtVehicleNo.Text = ""

        txtMaterialName.Text = ""

        txtCustomerVendorName.Text = ""

        txtSealNo.Text = ""
        txtTransactionNo.Enabled = True
        txtMaterialCode.Text = ""
        txtCustomerVendorcode.Text = ""
        txtChallanDate.Text = ""
        txtChallanNo.Text = ""
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtTransactionNo.Text) <> "" And Trim(txtVehicleNo.Text) <> "" Then
            '-------------------------------------------------
            frmWM.ListView1.Items.Clear()
            Dim item1 As New ListViewItem("PO / SO No.", 0)
            item1.SubItems.Add("")
            item1.SubItems.Add("")
            item1.SubItems.Add("")
            item1.SubItems.Add(txtMaterialCode.Text)
            item1.SubItems.Add(txtMaterialName.Text)

            item1.SubItems.Add(txtVehicleNo.Text)
            item1.SubItems.Add(txtCustomerVendorcode.Text)
            item1.SubItems.Add("")
            item1.SubItems.Add(txtChallanNo.Text)
            item1.SubItems.Add(txtChallanDate.Text)
            item1.SubItems.Add("")

            item1.SubItems.Add(txtCustomerVendorcode.Text)
            item1.SubItems.Add(txtCustomerVendorName.Text)

            frmWM.txtSealNo.Text = Trim(txtSealNo.Text)    ''''  Seal No
            frmWM.ListView1.Items(1).Checked = True
            frmWM.ListView1.Items.AddRange(New ListViewItem() {item1})
            '---------------------------------------------------
            cm.Connection = con
            cm.CommandType = CommandType.StoredProcedure
            cm.CommandText = "sp_UPD_line_Items"
            If Vl_typee = "PURCH" Or Vl_typee = "INTRDEPT" Or Vl_typee = "CONTITEM" Or Vl_typee = "PURCHRET" Or Vl_typee = "GATEPASS" Then

                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "V")
            ElseIf Vl_typee = "SALES" Or Vl_typee = "STKTROUT" Or Vl_typee = "SALESRET" Then
                cm.Parameters.AddWithValue("@val_Type_Of_Vehicle", "C")
            End If

            cm.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(txtTransactionNo.Text) & "")
            cm.Parameters.AddWithValue("@val_GE_DET_TRAN_ID", GE_DEET_TRAAN_IID)
            cm.Parameters.AddWithValue("@val_Mat_Code", Trim(txtMaterialCode.Text) & "")
            cm.Parameters.AddWithValue("@val_Mat_Name", Trim(txtMaterialName.Text) & "")
            cm.Parameters.AddWithValue("@val_Cust_Vend_Code", Trim(txtCustomerVendorcode.Text) & "")
            cm.Parameters.AddWithValue("@val_Cust_Vend_Name", Trim(txtCustomerVendorName.Text) & "")
            cm.Parameters.AddWithValue("@val_Seal_No", Trim(txtSealNo.Text) & "")
            cm.Parameters.AddWithValue("@val_Challan_No", Trim(txtChallanNo.Text) & "")
            cm.Parameters.AddWithValue("@val_Challan_Date", Trim(txtChallanDate.Text) & "")

            cm.ExecuteNonQuery()


            MsgBox("Line item data updated successfully !", vbInformation, "ElectroWay")

            Me.Close()
        End If
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub Text4_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtMaterialCode.KeyDown
        If AscW(e.KeyCode) = 13 And Trim(txtMaterialCode.Text) <> "" Then
            dr = cc.GetDataReader("select * from tbl_Material_Mst where Material_code  = '" & Trim(txtMaterialCode.Text) & "'")
            If dr.Read Then
                txtMaterialName.Text = dr("Material_Name")

            Else
                MsgBox("Material Code Not exists in master.", vbInformation, "ElectroWay")
                txtMaterialCode.Text = ""
                txtMaterialName.Text = ""
                txtMaterialCode.Focus()

            End If
            dr.Close()
        End If
    End Sub

    Private Sub Text5_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransactionNo.KeyPress
        If AscW(e.KeyChar) = 13 And Trim(txtTransactionNo.Text) <> "" Then

            dr = cc.GetDataReader("select * from tbl_GE_HDR where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'")
            If dr.Read = True Then
                txtVehicleNo.Text = dr("Vehicle_no")
                txtSealNo.Text = dr("Seal_No")
                Vl_typee = dr("Type_Of_Vehicle")
            End If
            dr.Close()

            dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'")
            If dr.Read Then
                txtMaterialCode.Text = Trim(dr("Mat_Code"))
                txtMaterialName.Text = Trim(dr("Mat_Desc"))

                txtChallanNo.Text = Trim(dr("Challan_No"))
                txtChallanDate.Text = Trim(dr("Challan_date"))

                GE_DEET_TRAAN_IID = dr("GE_DET_TRAN_ID")

            End If
            dr.Close()

            If Vl_typee = "SALES" Or Vl_typee = "STKTROUT" Or Vl_typee = "SALESRET" Then

                dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'")
                If dr.Read Then
                    txtCustomerVendorcode.Text = Trim(dr("Customer_Code"))
                    txtCustomerVendorName.Text = Trim(dr("Customer_Name"))
                End If
                dr.Close()



            ElseIf Vl_typee = "PURCH" Or Vl_typee = "INTRDEPT" Or Vl_typee = "CONTITEM" Or Vl_typee = "PURCHRET" Or Vl_typee = "GATEPASS" Then

                dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_ID  = '" & Trim(txtTransactionNo.Text) & "'")
                If dr.Read Then
                    txtCustomerVendorcode.Text = Trim(dr("Vendor_Code"))
                    txtCustomerVendorName.Text = Trim(dr("Vendor_Name"))
                End If
                dr.Close()

            End If

            txtTransactionNo.Enabled = False
        End If
    End Sub

    Private Sub Text7_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtCustomerVendorcode.KeyDown
        If AscW(e.KeyCode) = 13 And Trim(txtCustomerVendorcode.Text) <> "" Then


            If Vl_typee = "PURCH" Or Vl_typee = "INTRDEPT" Or Vl_typee = "CONTITEM" Or Vl_typee = "PURCHRET" Or Vl_typee = "GATEPASS" Then

                dr = cc.GetDataReader("select * from tbl_Vendor_Mst where Vendor_code  = '" & Trim(txtCustomerVendorcode.Text) & "'")
                If dr.Read Then
                    txtCustomerVendorName.Text = dr("Vendor_Name")
                Else
                    MsgBox("Vendor Code Not exists in master.", vbInformation, "ElectroWay")
                    txtCustomerVendorcode.Text = ""
                    txtCustomerVendorName.Text = ""
                    txtCustomerVendorcode.Focus()
                End If
                dr.Close()

            ElseIf Vl_typee = "SALES" Or Vl_typee = "STKTROUT" Or Vl_typee = "SALESRET" Then
                dr = cc.GetDataReader("select * from tbl_Customer_Mst where Customer_code  = '" & Trim(txtCustomerVendorcode.Text) & "'")
                If dr.Read Then
                    txtCustomerVendorName.Text = dr("Customer_Name")

                Else
                    MsgBox("Customer Code Not exists in master.", vbInformation, "ElectroWay")
                    txtCustomerVendorcode.Text = ""
                    txtCustomerVendorName.Text = ""
                    txtCustomerVendorcode.Focus()
                End If
                dr.Close()
            End If
        End If
    End Sub
End Class