﻿Public Class frmVendor
    Dim cc As New Class1
    Private Sub frmVendor_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        loadGrid()
    End Sub
    Private Sub loadGrid()
        Try
            Dim str As String = "SELECT * FROM tbl_Vendor_Mst"
            ds = cc.GetDataset(str)
            gvVendor.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        If Trim(txtVendorCode.Text) <> "" And Trim(txtVendorName.Text) <> "" Then
            ds = cc.GetDataset("select * from tbl_Vendor_Mst where Vendor_Code = '" & Trim(txtVendorCode.Text) & "'")
            If ds.Tables(0).Rows.Count = 0 Then
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm.Connection = con
                cm.CommandType = CommandType.StoredProcedure
                cm.CommandText = "sp_ins_tbl_Vendor_Mst"
                cm.Parameters.AddWithValue("@val_Vendor_Code", Trim(txtVendorCode.Text))
                cm.Parameters.AddWithValue("@val_Vendor_Name", Trim(txtVendorName.Text))
                cm.Parameters.AddWithValue("@val_Vendor_Address", Trim(txtAddress.Text))
                cm.Parameters.AddWithValue("@val_NON_SAP_Item", "1")
                cm.ExecuteNonQuery()
                loadGrid()
                MsgBox("Vendor Master updated successfully !", vbInformation, "ElectroWay")
                txtVendorCode.Text = ""
                txtVendorName.Text = ""
                txtAddress.Text = ""
            Else
                Dim ans = MsgBox("Vendor Already Exists ! Do you want to Update..", vbYesNo, "ElectroWay")
                If ans = vbYes Then
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm.Connection = con
                    cm.CommandType = CommandType.StoredProcedure
                    cm.CommandText = "sp_ins_tbl_Vendor_Mst"
                    cm.Parameters.AddWithValue("@val_Vendor_Code", Trim(txtVendorCode.Text))
                    cm.Parameters.AddWithValue("@val_Vendor_Name", Trim(txtVendorName.Text))
                    cm.Parameters.AddWithValue("@val_Vendor_Address", Trim(txtAddress.Text))
                    cm.Parameters.AddWithValue("@val_NON_SAP_Item", "1")
                    cm.ExecuteNonQuery()
                    MsgBox("Vendor Master updated successfully !", vbInformation, "ElectroWay")
                    loadGrid()
                    txtVendorCode.Text = ""
                    txtVendorName.Text = ""
                    txtAddress.Text = ""
                End If
            End If
            ds.Dispose()

        Else
            MsgBox("Blank Vendor record cannot be updated !", vbInformation, "ElectroWay")

        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        txtVendorCode.Clear()
        txtVendorName.Clear()
        txtAddress.Clear()
    End Sub

    Private Sub btnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExit.Click
        Me.Close()
    End Sub

    Private Sub txtVendorCode_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtVendorCode.KeyDown
        If e.KeyCode = 112 Then
            Help_callfrom = "VENDOR"
            Dim frmHelp1 As New frmHelp
            frmHelp1.Show()
            ''''        If rec1.State = 1 Then rec1.Close
            ''''        rec1.ActiveConnection = con
            ''''        rec1.Open "select * from tbl_GE_Hdr where Vehicle_Status = 'IN' and Plant_Code = '" & Trim(Text27.Text) & "' and Company_Code = '" & Trim(Text28.Text) & "' order by Vehicle_No"
            ' '''
            ' '''
            ''''            While rec1.EOF = False
            ''''                    i = frmVehicle.ListView1.ListItems.Count + 1
            ''''                        frmVehicle.ListView1.ListItems.Add i, , rec1.Fields("Vehicle_no")
            ''''                        frmVehicle.ListView1.ListItems(i).ListSubItems.Add (1), , rec1.Fields("Type_Of_Vehicle")
            ' '''
            ''''                        '' ListView1.ListItems(k).ListSubItems.Add (m), , ""
            ''''                        ''ListView1.ListItems(i).ListSubItems.Add (1), , rec1.Fields("PO_NO") & ""
            ''''                    rec1.MoveNext
            ''''            Wend
            ''''            rec1.Close
            ''''If frmVehicle.ListView1.ListItems.Count > 0 Then
            ''''    frmVehicle.Show vbModal
            ''''Else
            ''''    MsgBox "No Vehicle exists !", vbInformation
            ''''End If


        End If
    End Sub

    Private Sub txtVendorCode_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVendorCode.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtVendorName.Focus()
            dr = cc.GetDataReader("select * from tbl_Vendor_Mst where Vendor_Code = '" & Trim(txtVendorCode.Text) & "'")
            Try
                While dr.Read
                    txtVendorName.Text = dr("Vendor_Name")
                    txtAddress.Text = dr("Vendor_Address")
                End While
            Catch ex As Exception

            End Try
            dr.Close()
        End If
    End Sub

    Private Sub gvVendor_CellMouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles gvVendor.CellMouseClick
        Dim index As Int32
        Try
            Dim selectedRowCount As Int32
            Dim i As Int32
            selectedRowCount = gvVendor.Rows.GetRowCount(DataGridViewElementStates.Selected)

            If (selectedRowCount > 0) Then
                For i = 0 To selectedRowCount - 1
                    index = Convert.ToInt32(gvVendor.SelectedRows(i).Index)

                    txtVendorCode.Text = Convert.ToString(gvVendor.Rows(index).Cells(1).Value)
                    txtVendorName.Text = Convert.ToString(gvVendor.Rows(index).Cells(2).Value)
                    txtAddress.Text = Convert.ToString(gvVendor.Rows(index).Cells(3).Value)
                Next i
            End If
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Try
            Dim str As String = "SELECT * FROM tbl_Vendor_Mst where Vendor_Code like '%" & txtSearch.Text & "%' or Vendor_Name like '%" & txtSearch.Text & "%'"
            ds = cc.GetDataset(str)
            gvVendor.DataSource = ds.Tables(0)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
End Class