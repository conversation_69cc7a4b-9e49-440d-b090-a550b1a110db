﻿Imports System.Data
Imports System.Data.SqlClient
Imports System.Drawing.Printing
Imports System.Runtime.InteropServices
Imports Microsoft.Win32.SafeHandles
Imports System.IO
Imports System.Text.RegularExpressions
Imports System.Configuration

Public Class frmWM
    Dim c As String
    Dim d As String
    Dim i As Integer
    Dim WBCountID As Double
    Dim WtVal As String
    Dim AcceptClick As Integer
    Dim sel_item_index As Double
    Dim WT_UPDATE As String
    Dim HDDR_ID As String
    Dim TVehType11 As String
    Dim Ch_no_RFID As String
    Dim Ch_no_RFID_SWT As String
    Dim NON_AVERY_System As Integer
    Dim BaudRateSettings As String


    ''''''''''''''''''''''''''''''
    Dim MyFunc, App As Object
    Dim ENTRIES As SAPTableFactoryCtrl.Table

    Dim SAP_CON_NOT_AVAIL As Integer
    Dim functionCtrl As Object
    Dim functionCtr2 As Object
    Dim sapConnection As Object
    Dim theFunc As Object
    Dim objRfcFunc As Object
    Dim objRfcFunc1 As Object

    Dim objQueryTab, objRowCount As Object
    Dim objOptTab, objFldTab, objDatTab As Object
    Dim objDatRec As Object
    Dim objFldRec As Object
    'Dim i As Double


    Dim objQueryTab1, objRowCount1 As Object
    Dim objOptTab1, objFldTab1, objDatTab1 As Object
    Dim objDatRec1 As Object
    Dim objFldRec1 As Object

    Dim WBWeightDet As String

    Dim pono_wb As String
    Dim ponoLinItm_wb As String

    Dim PortNumNE As String
    Dim cc As New Class1
    '------------------------------
    Private readBuffer As String = String.Empty
    Private Bytenumber As Integer
    Private ByteToRead As Integer
    Private byteEnd(2) As Char
    Private comOpen As Boolean
    '-------------------------------
#Region "ComPort read data"

    ''' <summary> 
    ''' async read on secondary thread 
    ''' </summary> 
    Private Sub SerialPort1_DataReceived(ByVal sender As System.Object, _
                                         ByVal e As System.IO.Ports.SerialDataReceivedEventArgs) _
                                         Handles SerialPort1.DataReceived
        If comOpen Then
            Try
                byteEnd = SerialPort1.NewLine.ToCharArray

                ' get number off bytes in buffer 
                Bytenumber = SerialPort1.BytesToRead

                ' read one byte from buffer 
                'ByteToRead = SerialPort1.ReadByte() 

                ' read one char from buffer 
                'CharToRead = SerialPort1.ReadChar() 

                ' read until string "90" 
                'readBuffer1 = SerialPort1.ReadTo("90") 

                ' read entire string until .Newline  
                readBuffer = SerialPort1.ReadLine()

                'data to UI thread 
                Me.Invoke(New EventHandler(AddressOf DoUpdate))

            Catch ex As Exception
                'MsgBox("read " & ex.Message)
            End Try
        End If
    End Sub

    ''' <summary> 
    ''' update received string in UI 
    ''' </summary> 
    ''' <remarks></remarks> 
    Public Sub DoUpdate(ByVal sender As Object, ByVal e As System.EventArgs)
        'Dim X As String = readBuffer
        'Dim Y() As String = X.Split(" ")
        Try
            If AcceptClick <> 1 Then
                Dim X As String = readBuffer
                'X = TextBox1.Text
                X = RTrim(X)
                'txtSecondWtNote.Text = X
                'Dim Y() As String = X.Split(" ")
                Dim Y() As String = Regex.Split(X, "\s+")
                Try
                    ' ''Dim Length1 As Integer = Y.Length - 2 '---------rmhs ------
                    ''Dim Length1 As Integer = Y.Length - 5 '---------38 Khata ---***********---************
                    ' ''Dim Length1 As Integer = Y.Length - 4 '---------38 Khata --***********----
                    ' ''txtweight.Text = Y(Length1)

                    Dim Length1 As Integer
                    'Old IPs
                    'If ipaddress = "***********" Then
                    '    Length1 = Y.Length - 5
                    'ElseIf ipaddress = "************" Then
                    '    Length1 = Y.Length - 5
                    'ElseIf ipaddress = "************" Then
                    '    Length1 = Y.Length - 5
                    'ElseIf ipaddress = "***********" Then
                    '    Length1 = Y.Length - 5
                    'Else
                    '    Length1 = Y.Length - 2
                    'End If

                    'New IPs
                    If ipaddress = "***********" Or ipaddress = "***********" Or ipaddress = "***********" Or ipaddress = "***********" Then
                        Length1 = Y.Length - 5
                    Else
                        Length1 = Y.Length - 2
                    End If
                    If Y(Length1) <> txtWt.Text.Trim Then
                        txtWt.Text = Y(Length1)
                    End If
                Catch ex As Exception

                End Try
                'Try
                '    If IsNumeric(Y(0)) Then
                '        If Y(0) <> txtWt.Text.Trim Then
                '            txtWt.Text = Y(0)
                '        End If
                '    End If
                'Catch ex As Exception

                'End Try
                'Try
                '    If IsNumeric(Y(1)) Then
                '        If Y(1) <> txtWt.Text.Trim Then
                '            txtWt.Text = Y(1)
                '        End If
                '    End If
                'Catch ex As Exception

                'End Try
                'Try
                '    If IsNumeric(Y(2)) Then
                '        If Y(2) <> txtWt.Text.Trim Then
                '            txtWt.Text = Y(2)
                '        End If
                '    End If
                'Catch ex As Exception

                'End Try
                'Try
                '    If IsNumeric(Y(3)) Then
                '        If Y(3) <> txtWt.Text.Trim Then
                '            txtWt.Text = Y(3)
                '        End If
                '    End If
                'Catch ex As Exception

                'End Try
                'Try
                '    If IsNumeric(Y(4)) Then
                '        If Y(4) <> txtWt.Text.Trim Then
                '            txtWt.Text = Y(4)
                '        End If
                '    End If
                'Catch ex As Exception

                'End Try
            End If
        Catch ex As Exception

        End Try

    End Sub

#End Region

    Private Sub frmWM_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'txtSecondWtNote.Text = "10 160 10"
        'Dim X As String = RTrim("10     00    00")
        'X = RTrim(X)
        'Dim Y() As String = Regex.Split(X, "\s+")
        ''Dim Y() As String = X.Split("  ")

        'Try
        '    Dim Length1 As Integer = Y.Length - 2
        '    'txtweight.Text = Y(Length1)
        '    If Y(Length1) <> txtWt.Text.Trim Then
        '        txtWt.Text = Y(Length1)
        '    End If
        'Catch ex As Exception

        'End Try
        ''-----------ListView--------------
        Dim lvwItem As New ListViewItem With {
            .Checked = True
        }
        ListView1.Clear()
        ListView1.View = View.Details
        ListView1.GridLines = True
        ListView1.FullRowSelect = True
        ListView1.HideSelection = False
        ListView1.MultiSelect = False

        'Headings
        ListView1.Columns.Add("TRAN_ID")
        ListView1.Columns.Add("PO / SO No.", 250, HorizontalAlignment.Left)
        ListView1.Columns.Add("DO No.")
        ListView1.Columns.Add("DO/PO Line Item")
        ListView1.Columns.Add("Material Code")
        ListView1.Columns.Add("Material Description")
        ListView1.Columns.Add("DO/Ch Qty.")
        ListView1.Columns.Add("Unit")
        ListView1.Columns.Add("SAP Gate Entry No.")
        ListView1.Columns.Add("Ch. No.")
        ListView1.Columns.Add("Challan Date")
        'ListView1.Columns.Add("RR No.")
        'ListView1.Columns.Add("RR Date")
        'ListView1.Columns.Add("LR No.")
        'ListView1.Columns.Add("LR Date")
        'ListView1.Columns.Add("Rake No.")
        ListView1.Columns.Add("SO Line Item")
        ListView1.Columns.Add("Customer/Vendor")
        ListView1.Columns.Add("Customer/Vendor Name")
        'ListView1.Items.Clear()
        For i As Integer = 0 To ListView1.Columns.Count - 1
            ListView1.Columns(i).Width = -2
        Next
        '------------------------------------
        Dim PortNum As String = String.Empty, BaudRate As String = String.Empty
        ''LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL
        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Sys_loc_IP & "' and Node_Name = 'WEIGH BRIDGE'") '" & Sys_loc_IP & "'
        Try
            While dr.Read
                PortNum = "COM" & dr("Port_Number")
                BaudRateSettings = dr("Settings")
                NON_AVERY_System = dr("Non_Avery_WB_MC")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        'MessageBox.Show(PortNum & "/" & BaudRateSettings & "/" & NON_AVERY_System)
        PortNumNE = PortNum
        ''LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL
        If OperationType = "MANUAL" Then
            txtWt.Text = ""
            txtWt.Enabled = True

            gbTareWt.Enabled = False

        ElseIf OperationType = "AUTO" Then

            If NON_AVERY_System = 0 Then
                txtWt.Text = ""
                dtFirst.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-mm-ss")
                dtSecond.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-mm-ss")     '''' Format(Date, "dd-MM-yyyy")
                txtTime.Text = TimeOfDay.ToString("HH-mm-ss")
                '--------------------------------
                'MSComm1.CommPort = PortNum  ' comm port no.
                ''MSComm1.CommPort = 1  ' comm port no.
                'MSComm1.Settings = BaudRateSettings
                ''MSComm1.Settings = "9600,e,7,1"
                'MSComm1.RThreshold = 24    'no. of chr to recive
                'MSComm1.InputLen = 0  '  no. of chr on which oncomm  event fires
                'MSComm1.PortOpen = True
                '-------------------------------------
                Try
                    With SerialPort1

                        .ParityReplace = &H3B                    ' replace ";" when parity error occurs 
                        .PortName = PortNum
                        '.BaudRate = "9600"
                        .BaudRate = BaudRateSettings.ToString.Substring(0, 4)
                        .Parity = IO.Ports.Parity.Even
                        ''.DataBits = 7
                        '.DataBits = 8
                        .DataBits = BaudRateSettings.ToString.Substring(7, 1)
                        .StopBits = IO.Ports.StopBits.One
                        .Handshake = IO.Ports.Handshake.None
                        .RtsEnable = False
                        .ReceivedBytesThreshold = 1             'threshold: one byte in buffer > event is fired 
                        .NewLine = vbCr         ' CR must be the last char in frame. This terminates the SerialPort.readLine 
                        .ReadTimeout = 10000

                    End With
                    SerialPort1.Open()
                    comOpen = SerialPort1.IsOpen
                Catch ex As Exception
                    comOpen = False
                    MsgBox("Error Open: " & ex.Message)
                End Try
                '----------------------------------------
                txtWt.Enabled = False
            ElseIf NON_AVERY_System = 1 Then

                txtWt.Text = ""
                dtFirst.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-mm-ss")
                dtSecond.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-mm-ss")     '''' Format(Date, "dd-mm-yyyy")
                txtTime.Text = TimeOfDay.ToString("HH-mm-ss")
                'MSComm1.CommPort = PortNum  ' comm port no.
                ''MSComm1.CommPort = 1  ' comm port no.
                'MSComm1.Settings = BaudRateSettings
                ''MSComm1.Settings = "9600,e,7,1"
                'MSComm1.RThreshold = 24    'no. of chr to recive
                'MSComm1.InputLen = 0  '  no. of chr on which oncomm  event fires
                'MSComm1.PortOpen = True
                Try
                    With SerialPort1

                        .ParityReplace = &H3B                    ' replace ";" when parity error occurs 
                        .PortName = PortNum
                        '.BaudRate = "9600"
                        .BaudRate = BaudRateSettings.ToString.Substring(0, 4)
                        .Parity = IO.Ports.Parity.Even
                        ''.DataBits = 7
                        '.DataBits = 8
                        .DataBits = BaudRateSettings.ToString.Substring(7, 1)
                        .StopBits = IO.Ports.StopBits.One
                        .Handshake = IO.Ports.Handshake.None
                        .RtsEnable = False
                        .ReceivedBytesThreshold = 1             'threshold: one byte in buffer > event is fired 
                        .NewLine = vbCr         ' CR must be the last char in frame. This terminates the SerialPort.readLine 
                        .ReadTimeout = 10000

                    End With
                    SerialPort1.Open()
                    comOpen = SerialPort1.IsOpen
                Catch ex As Exception
                    comOpen = False
                    MsgBox("Error Open: " & ex.Message)
                End Try
                '------------------------
                txtWt.Enabled = True

            End If

        End If

        Label26.Text = OperationType

        dr = cc.GetDataReader("select * from tbl_Node_Mst where Node_IP = '" & Trim(Sys_loc_IP) & "'")
        Try
            While dr.Read
                txtPlant.Text = dr("Plant_Name")
                txtCompany.Text = dr("Company_Code")
                'Else
                '    MsgBox("IP number not mapped as ElectroWay Node ....or You are not connected with ElectroWay Server.")
            End While

        Catch ex As Exception

        End Try
        dr.Close()

        ddlRakeNo.Items.Add("")

        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Disabled = '0' order by Reference_Code Desc")
        Try
            While dr.Read
                ddlRakeNo.Items.Add(dr("Reference_Code"))
            End While
        Catch ex As Exception

        End Try

        dr.Close()
        txtVehicleNo.BackColor = Color.Gold
        txtVehicleNo.Focus()
    End Sub

    Private Sub ddlRakeNo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ddlRakeNo.Click
        txtRakeGrouping.Text = ""

        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlRakeNo.Text) & "'")
        Try
            While dr.Read
                txtRakeGrouping.Text = dr("Reference_Name")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
    End Sub

    Private Sub Command2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Command2.Click
        Try
            AcceptClick = 1
            btnUpdate.Focus()
        Catch ex As Exception
            If Err.Description <> "" Then
                MsgBox(Err.Description, vbInformation, "ElectroWay")
            End If
        End Try
    End Sub

    Private Sub btnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUpdate.Click
        Dim count_wb_id As Integer
        Dim ListItemChecked As Integer
        Dim ge_det_ID_FOR_ft_UPD As Double

        ListItemChecked = 0
        'txtWt.Text = "46220"
        If Val(txtWt.Text) <= 0 Then
            MsgBox("WRONG WEIGHT Value ....", vbInformation, "ElectroWay")
            Exit Sub
        End If

        Dim WB_ID, WB_Counter_WB_TRAN As Integer
        Dim PO_No_Disp, DO_No_Disp As String
        Dim MatCode As String = String.Empty, VenCustName As String = String.Empty, Unchekced_Item As String = String.Empty, S_WT_DAtetme_Print As String = String.Empty, Date_v1 As String = String.Empty, Time_v1 As String = String.Empty, Date_v2 As String = String.Empty, Time_v2 As String = String.Empty, Firstweight As String = String.Empty, SecondWeight As String = String.Empty, SWT_DtTime_11 As String = String.Empty
        If AcceptClick = 1 Then
            Dim Vehicle_Status As String = SearchData("select Vehicle_Status from tbl_GE_Hdr where Vehicle_No = '" & txtVehicleNo.Text.Trim & "' and Vehicle_Status = 'CANCEL' and Type_Of_Vehicle = 'SALES'")
            If Vehicle_Status = "CANCEL" Then
                CancelVehilcle()
                AcceptClick = 0
                Exit Sub
            End If
            If WT_UPDATE = "FWT" Then
                dr = cc.GetDataReader("select max(WB_Count_ID) from tbl_GE_Det where GE_HDR_Tran_ID = " & HDDR_ID)
                Try
                    While dr.Read
                        WB_ID = dr(0)
                    End While
                Catch ex As Exception

                End Try
                dr.Close()
                ''GGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG

                dr = cc.GetDataReader("select max(WB_Counter) from tbl_GE_Det")
                Try
                    While dr.Read
                        WB_Counter_WB_TRAN = dr(0) + 1
                    End While
                Catch ex As Exception

                End Try
                dr.Close()


                ''GGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG

                For i As Integer = 0 To ListView1.Items.Count - 1
                    If (ListView1.Items(i).Checked = True) Then '' And ListItemChecked = 0 Then

                        If ListItemChecked = 0 Then
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            Dim cm1 As New SqlCommand
                            cm1.Connection = con
                            cm1.CommandType = CommandType.StoredProcedure
                            cm1.CommandText = "sp_upd_tbl_GE_Det_F_WT"
                            cm1.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                            cm1.Parameters.AddWithValue("@val_F_WT_Node_IP", Sys_loc_IP)
                            cm1.Parameters.AddWithValue("@val_F_WT", Val(Trim(txtWt.Text)))
                            cm1.Parameters.AddWithValue("@val_F_WT_DoneBy", User_ID & "")
                            cm1.Parameters.AddWithValue("@val_F_WT_Note", Trim(txtFirstWeightNote.Text) & "")
                            cm1.ExecuteNonQuery()
                            cm1.Dispose()
                            ListItemChecked = 1

                            MatCode = ListView1.Items(i).SubItems(5).Text

                            VenCustName = ListView1.Items(i).SubItems(12).Text
                            PO_No_Disp = ListView1.Items(i).SubItems(1).Text '4700012546
                            DO_No_Disp = ListView1.Items(i).SubItems(2).Text '""

                            'cm.Connection = con
                            'cm.CommandType = CommandType.StoredProcedure
                            'cm.CommandText = "sp_upd_tbl_GE_DET_WB_Counter"
                            'cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                            'cm.Parameters.AddWithValue("@val_WB_Counter", WB_Counter_WB_TRAN)
                            'cm.ExecuteNonQuery() 'Procedure or function sp_upd_tbl_GE_DET_WB_Counter has too many arguments specified.
                            Try
                                Dim str1 As String = "update tbl_GE_DET	set	WB_counter = " & ListView1.Items(i).Text & " where GE_DET_TRan_ID = " & WB_Counter_WB_TRAN & ""
                                cc.Execute(str1)
                            Catch ex As Exception

                            End Try

                            'KKKKKKKKKKKKKKK   for INSERT tbl_SPLIT_DET
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            Dim cm2 As New SqlCommand
                            cm2.Connection = con
                            cm2.CommandType = CommandType.Text
                            cm2.CommandText = "insert into tbl_SPLIT_DET select * from tbl_GE_DET where GE_DET_TRAN_ID ='" & Trim(ListView1.Items(i).Text) & "' and GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "'"
                            Try
                                cm2.ExecuteNonQuery()
                            Catch ex As Exception

                            End Try
                            cm2.Dispose()
                        End If

                        'cmd.ActiveConnection = con
                        'cm.CommandType = CommandType.StoredProcedure
                        'cm.CommandText = "sp_upd_tbl_GE_Det_WB_Count_ID"
                        'cmd.Parameters("@val_GE_DET_Tran_ID") = ListView1.Items(i).Text
                        'cmd.Parameters("@val_WB_Count_ID") = WB_ID + 1
                        'cmd.Execute
                        '
                        ''Exit For

                    End If
                Next i

                ''??????????????????????????????????????????

                If Trim(TVehType11) = "PURCH" Or Trim(TVehType11) = "SALESRET" Then
                    Dim anss As MsgBoxResult
                    ''FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
                    dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'")
                    Try
                        While dr.Read
                            anss = MsgBox("Would you like to take Tare Wt. from master ? ", vbYesNo, "ElectroWay")
                        End While
                    Catch ex As Exception

                    End Try
                    dr.Close()
                    ''FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF

                    If anss = vbYes Then

                        dr = cc.GetDataReader("select max(WB_Count_ID) from tbl_GE_Det where GE_HDR_Tran_ID = " & HDDR_ID)
                        Try
                            WB_ID = dr(0)
                        Catch ex As Exception

                        End Try
                        dr.Close()
                        '----------------|^
                        dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and TareWtValidUpto >=  '" & Format(Today.Date, "yyyy-mm-dd") & " 00:00:00.000'")
                        If dr.Read Then
                            txtSecondWt.Text = dr("Vehicle_Tare_Wt")
                            dtSecond.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-MM-SS")

                            Dim cm3 As New SqlCommand
                            cm3.Connection = con
                            cm3.CommandType = CommandType.StoredProcedure
                            cm3.CommandText = "sp_upd_tbl_GE_Det_SWT"
                            cm3.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(1).Text)
                            cm3.Parameters.AddWithValue("@val_S_WT_Node_IP", Sys_loc_IP)
                            cm3.Parameters.AddWithValue("@val_S_WT", Val(Trim(txtSecondWt.Text)))
                            cm3.Parameters.AddWithValue("@val_S_WT_DoneBy", User_ID & "")
                            cm3.Parameters.AddWithValue("@val_S_WT_Note", Trim(txtSecondWtNote.Text) & "")
                            cm3.ExecuteNonQuery()
                            cm3.Dispose()
                            '*************** code for NET WT Update

                            Dim cm4 As New SqlCommand
                            cm4.Connection = con
                            cm4.CommandType = CommandType.StoredProcedure
                            cm4.CommandText = "sp_upd_NET_WT"
                            cm4.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(1).Text)
                            cm4.ExecuteNonQuery()
                            cm4.Dispose()
                            '***************

                            For l1 = 0 To ListView1.Items.Count - 1
                                Dim cm5 As New SqlCommand With {
                                    .Connection = con,
                                    .CommandType = CommandType.StoredProcedure,
                                    .CommandText = "sp_upd_tbl_GE_Det_WB_Count_ID"
                                }
                                cm5.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(l1).Text)
                                cm5.Parameters.AddWithValue("@val_WB_Count_ID", WB_ID + 1)
                                cm5.ExecuteNonQuery()
                                cm5.Dispose()

                                'KKKKKKKKKKKKKKK   for INSERT tbl_SPLIT_DET
                                Dim cm6 As New SqlCommand With {
                                    .Connection = con,
                                    .CommandType = CommandType.Text,
                                    .CommandText = "insert into tbl_SPLIT_DET select * from tbl_GE_DET where GE_DET_TRAN_ID ='" & Trim(ListView1.Items(l1).Text) & "' and GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "'"
                                }
                                cm6.ExecuteNonQuery()
                                cm6.Dispose()

                                'KKKKKKKKKKKKKKK

                            Next

                        Else
                            MsgBox("Vehicle tare Wt not maintained for this Vehicle or Validity period expired.", vbInformation, "ElectroWay")

                        End If
                        dr.Close()
                    End If
                End If
                ''????????????????????????????????????????????

            ElseIf WT_UPDATE = "SWT" Then

                For i As Integer = 0 To ListView1.Items.Count - 1
                    If (ListView1.Items(i).Checked = False) Then
                        Unchekced_Item = ListView1.Items(i).Text
                        Exit For
                    Else
                        Unchekced_Item = ""
                    End If
                Next i

                If Trim(TVehType11) = "PURCH" Or Trim(TVehType11) = "SALESRET" Then '''''Or Trim(TVehType11) = "CONTITEM" Then   ' ''''' Or Trim(TVehType11) = "PURCHRET" Or Trim(TVehType11) = "GATEPASS" Then

                    If Val(txtWt.Text) > Val(txtFirstWt.Text) Then
                        MsgBox("Invalid weighment .... Pls check Vehicle Type. (Second Wt Cannot be more than First Wt.)", vbInformation, "ElectroWay")
                        Exit Sub
                    End If
                End If


                If Trim(TVehType11) = "SALES" Or Trim(TVehType11) = "STKTROUT" Or Trim(TVehType11) = "PURCHRET" Or Trim(TVehType11) = "GATEPASS" Then     ''  Or Trim(TVehType11) = "INTRDEPT"
                    If Val(txtWt.Text) < Val(txtFirstWt.Text) Then
                        MsgBox("Invalid weighment .... Pls check Vehicle Type.(Second Wt Cannot be less than First Wt.)", vbInformation, "ElectroWay")
                        Exit Sub
                    End If

                End If

                dr = cc.GetDataReader("select max(WB_Count_ID) from tbl_GE_Det where GE_HDR_Tran_ID = " & HDDR_ID)
                Try
                    While dr.Read
                        WB_ID = dr(0)
                    End While
                Catch ex As Exception

                End Try
                dr.Close()

                For i As Integer = 0 To ListView1.Items.Count - 1
                    If (ListView1.Items(i).Checked = True) Then '' And ListItemChecked = 0 Then

                        If ListItemChecked = 0 Then

                            'select count(wb_count_ID) from tbl_GE_Det where WB_Count_ID  = 0
                            dr = cc.GetDataReader("select count(wb_count_ID) FROM tbl_GE_Det WHERE f_wt > 0 AND s_wt = 0 and WB_Count_ID  = 0 AND GE_HDR_Tran_ID = " & HDDR_ID)
                            Try
                                While dr.Read
                                    count_wb_id = dr(0)
                                End While
                            Catch ex As Exception

                            End Try
                            dr.Close()
                            'dr = cc.GetDataReader("select ge_det_tran_id FROM tbl_GE_Det WHERE f_wt > 0 AND s_wt = 0 AND GE_HDR_Tran_ID = " & HDDR_ID & "order by ge_det_tran_id")
                            'Try
                            '    If dr.Read = True And count_wb_id > 0 Then
                            '        ge_det_ID_FOR_ft_UPD = dr(0)
                            '        If ge_det_ID_FOR_ft_UPD <> ListView1.Items(i).Text Then
                            '            'cmd.ActiveConnection = con
                            '            'cm.CommandType = CommandType.StoredProcedure
                            '            'cm.CommandText = "sp_upd_tbl_GE_Det_fwt_Agnst_SWT"
                            '            'cmd.Parameters("@val_GE_DET_Tran_ID") = Unchekced_Item
                            '            'cmd.Parameters("@val_ge_det_ID_FOR_ft_UPD") = ge_det_ID_FOR_ft_UPD

                            '            '''''cmd.Execute
                            '            dr = cc.GetDataReader("select ge_det_tran_id FROM tbl_GE_Det WHERE f_wt = 0 AND s_wt = 0 AND GE_HDR_Tran_ID = " & HDDR_ID & "order by ge_det_tran_id")
                            '            While dr.Read
                            '                'cmd.ActiveConnection = con
                            '                'cm.CommandType = CommandType.StoredProcedure
                            '                'cm.CommandText = "sp_upd_tbl_GE_Det_F_WT"
                            '                'cmd.Parameters("@val_GE_DET_Tran_ID") = rec.Fields(0)
                            '                'cmd.Parameters("@val_F_WT_Node_IP") = Sys_loc_IP
                            '                'cmd.Parameters("@val_F_WT") = Val(Trim(txtWt.Text))
                            '                'cmd.Parameters("@val_F_WT_DoneBy") = User_ID & ""
                            '                'cmd.Parameters("@val_F_WT_Note") = Trim(txtFirstWeightNote.Text) & ""
                            '                '''''cmd.Execute
                            '            End While
                            '            dr.Close()
                            '        End If

                            '    End If
                            'Catch ex As Exception

                            'End Try
                            'dr.Close()


                            S_WT_DAtetme_Print = ListView1.Items(i).Text

                            Dim cm6 As New SqlCommand With {
                                .Connection = con,
                                .CommandType = CommandType.StoredProcedure,
                                .CommandText = "sp_upd_tbl_GE_Det_SWT"
                            }
                            cm6.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                            cm6.Parameters.AddWithValue("@val_S_WT_Node_IP", Sys_loc_IP)
                            cm6.Parameters.AddWithValue("@val_S_WT", Val(Trim(txtWt.Text)))
                            cm6.Parameters.AddWithValue("@val_S_WT_DoneBy", User_ID & "")
                            cm6.Parameters.AddWithValue("@val_S_WT_Note", Trim(txtSecondWtNote.Text) & "")
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            cm6.ExecuteNonQuery()
                            cm6.Dispose()

                            dr = cc.GetDataReader("select * FROM tbl_GE_Det WHERE F_WT = 0 and GE_DET_TRAN_ID = " & ListView1.Items(i).Text)
                            Dim DetFlag As Boolean = False
                            Try
                                While dr.Read
                                    DetFlag = True
                                End While
                            Catch ex As Exception

                            End Try
                            dr.Close()
                            If DetFlag = True Then
                                Dim cm7 As New SqlCommand
                                cm7.Connection = con
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm7.CommandType = CommandType.StoredProcedure
                                cm7.CommandText = "sp_upd_tbl_GE_Det_fwt_Agnst_SWT"
                                cm7.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                                cm7.Parameters.AddWithValue("@val_ge_det_ID_FOR_ft_UPD", ge_det_ID_FOR_ft_UPD)
                                cm7.ExecuteNonQuery()
                                cm7.Dispose()
                            End If
                            '*************** code for NET WT Update

                            Dim cm8 As New SqlCommand With {
                                .Connection = con,
                                .CommandType = CommandType.StoredProcedure,
                                .CommandText = "sp_upd_NET_WT"
                            }
                            cm8.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            cm8.ExecuteNonQuery()
                            cm8.Dispose()
                            '***************


                            ''select ge_det_tran_id FROM tbl_GE_Det WHERE f_wt > 0 AND s_wt = 0


                            MatCode = ListView1.Items(i).SubItems(5).Text
                            VenCustName = ListView1.Items(i).SubItems(13).Text & ""
                            PO_No_Disp = ListView1.Items(i).SubItems(1).Text
                            DO_No_Disp = ListView1.Items(i).SubItems(2).Text

                            ListItemChecked = 1

                        End If
                        '--------------not working - Jnan---------------
                        'cm.Connection = con
                        'cm.CommandType = CommandType.StoredProcedure
                        'cm.CommandText = "sp_upd_tbl_GE_Det_WB_Count_ID"
                        'cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                        'cm.Parameters.AddWithValue("@val_WB_Count_ID", WB_ID + 1)
                        'If con.State = ConnectionState.Closed Then
                        '    con.Open()
                        'End If
                        'cm.ExecuteNonQuery()
                        '-------------------------------------------
                        Dim str1 As String = "update tbl_GE_Det set WB_Count_ID = " & WB_ID + 1 & " where GE_DET_Tran_ID = " & ListView1.Items(i).Text
                        cc.Execute(str1)

                        'KKKKKKKKKKKKKKK   for INSERT tbl_SPLIT_DET
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        Dim cm9 As New SqlCommand
                        cm9.Connection = con
                        cm9.CommandType = CommandType.Text
                        cm9.CommandText = "insert into tbl_SPLIT_DET select * from tbl_GE_DET where GE_DET_TRAN_ID ='" & Trim(ListView1.Items(i).Text) & "' and GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "'"
                        cm9.ExecuteNonQuery()
                        cm9.Dispose()

                        'KKKKKKKKKKKKKKK
                    End If
                Next i


                '''''''''''''''  Push to ZWT_BG TABLE  start @@@@@@@@@@@@@@@@@@@@@@@@
                Dim uploadInSAP As String = "0"
                If (TVehType11 <> "INTRDEPT" And Trim(ddlRakeNo.Text) = "") Then
                    'dr = cc.GetDataReader("select * FROM tbl_GE_Det WHERE GE_HDR_TRAN_ID = " & HDDR_ID)
                    dr = cc.GetDataReader("select * FROM tbl_GE_Det WHERE GE_HDR_TRAN_ID = '" & HDDR_ID & "' order by WB_Count_ID ")
                    Do While dr.Read

                        If ((Trim(dr("UOM")) Like "KG" Or Trim(dr("UOM")) Like "TON*") And Val(dr("NET_WT")) > 0) Then
                            uploadInSAP = 1
                        ElseIf ((Trim(dr("UOM")) Like "KG" Or Trim(dr("UOM")) Like "TO*") And Val(dr("NET_WT")) = 0) Then
                            uploadInSAP = 0
                            Exit Do

                        ElseIf ((Trim(dr("UOM")) <> "KG") And ((Trim(dr("UOM")) <> "TO") Or (Trim(dr("UOM")) <> "TON"))) Then
                            uploadInSAP = 1
                        Else
                            uploadInSAP = 0
                            Exit Do

                        End If

                        'dr.MoveNext()

                    Loop
                    dr.Close()
                    '--------------
                    Dim HDR_ID As Boolean = searchSales_DO(ListView1.Items(0).Text)

                    '--------------
                    If HDR_ID = False Then
                        If uploadInSAP = 1 Then
                            Call push_data_weighment()
                            SAP_Close1()
                        End If
                    End If

                End If

                '''''''''''''''  Push to ZWT_BG TABLE  End @@@@@@@@@@@@@@@@@@@@@@@@

            End If

            If ListItemChecked = 0 Then
                MsgBox("No Item has been selected for Weighment, Pls select at least one Item", vbInformation, "Electrosteel Steels Limited.")
                Exit Sub
            Else
                Try
                    If WT_UPDATE = "SWT" Then
                        dr = cc.GetDataReader("select ge_det_tran_id FROM tbl_GE_Det WHERE f_wt = 0 AND s_wt = 0 AND  WB_Count_ID  = 0 and GE_HDR_Tran_ID = '" & HDDR_ID & "' order by ge_det_tran_id")
                        Dim SWTFlag As Boolean = False
                        Dim TranId As String = String.Empty
                        Try
                            If dr.Read Then
                                SWTFlag = True
                                TranId = dr(0).ToString
                            End If
                        Catch ex As Exception

                        End Try
                        dr.Close()
                        If SWTFlag = True Then
                            Dim cm9 As New SqlCommand With {
                                .Connection = con
                            }
                            If con.State = ConnectionState.Closed Then
                                con.Open()
                            End If
                            cm9.CommandType = CommandType.StoredProcedure
                            cm9.CommandText = "sp_upd_tbl_GE_Det_F_WT"
                            cm9.Parameters.AddWithValue("@val_GE_DET_Tran_ID", TranId)
                            cm9.Parameters.AddWithValue("@val_F_WT_Node_IP", Sys_loc_IP)
                            cm9.Parameters.AddWithValue("@val_F_WT", Val(Trim(txtWt.Text)))
                            cm9.Parameters.AddWithValue("@val_F_WT_DoneBy", User_ID & "")
                            cm9.Parameters.AddWithValue("@val_F_WT_Note", Trim(txtFirstWeightNote.Text) & "")
                            cm9.ExecuteNonQuery()
                            cm9.Dispose()
                        End If

                        If TVehType11 = "INTRDEPT" Then
                            Dim InterDeptFlag As Boolean = False
                            dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_TRAN_ID = '" & HDDR_ID & "' and F_WT = 0 and S_WT = 0 and WB_Count_ID  = 0")
                            If dr.Read = True Then
                                InterDeptFlag = True
                            End If
                            dr.Close()

                            If InterDeptFlag = True Then
                                Dim cm10 As New SqlCommand With {
                                    .Connection = con
                                }
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm10.CommandType = CommandType.StoredProcedure
                                cm10.CommandText = "sp_ins_tbl_GE_DET_Blank_Record_for_InterDept"
                                cm10.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(1).Text)
                                cm10.ExecuteNonQuery()
                                cm10.Dispose()
                            End If
                        End If

                    End If
                Catch ex As Exception

                End Try
                ''KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK
                Dim x, y
                x = 20                           ' We display msgbox at this location
                y = 500

                'If TVehType11 = "INTRDEPT" Or WT_UPDATE = "FWT" Then
                '   ans = MsgBoxMove(hwnd, "Weighment Updated, Print Slip ?", "ElectroWay", vbYesNo + vbDefaultButton2, x, y)
                'Else

                'Dim ans = MsgBoxMove(hwnd, "Weighment Updated, Print Slip ?", "ElectroWay", vbYesNo + vbDefaultButton1, x, y)

                'Dim ans = MsgBox("Weighment Updated, Print Slip ?", "ElectroWay", vbYesNo + vbDefaultButton1)
                Dim ans = MsgBox("Weighment Updated, Print Slip ?", vbYesNo, "Electrosteel Steels Limited.")
                'End If

                ''KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK

                '''''''''''''''''''''ans = MsgBox("Weighment Updated Sucessfully , Do you want to print slip ?", vbYesNo + vbDefaultButton2, "ElectroWay")


                'YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY
                'For Gint = 1 To ListView1.Items.Count

                'HDDR_ID

                If Trim(ddlRakeNo.Text) <> "" Then
                    Dim cm11 As New SqlCommand With {
                        .Connection = con
                    }
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    cm11.CommandType = CommandType.StoredProcedure
                    cm11.CommandText = "sp_upd_tbl_GE_DET_Grouping"
                    cm11.Parameters.AddWithValue("@val_GE_DET_Tran_ID", HDDR_ID)
                    cm11.Parameters.AddWithValue("@val_Ref_Code", Trim(ddlRakeNo.Text))
                    cm11.ExecuteNonQuery()
                    cm11.Dispose()

                    dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlRakeNo.Text) & "'")
                    Dim AutoFlag As Boolean = False
                    Try
                        While dr.Read
                            If dr("Auto_Update") = 1 Then
                                AutoFlag = True
                            End If
                        End While
                    Catch ex As Exception

                    End Try
                    dr.Close()

                    If AutoFlag = True Then
                        Dim cm12 As New SqlCommand With {
                            .Connection = con
                        }
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        cm12.CommandType = CommandType.Text
                        cm12.CommandText = "update tbl_GE_DET set Mat_Code  ='" & Trim(dr("Mat_Code")) & "' , Mat_Desc = '" & Trim(dr("Mat_Desc")) & "'  , Vendor_Code = '" & Trim(dr("Vendor_Code")) & "' , Vendor_Name  = '" & Trim(dr("Vendor_Name")) & "'  where GE_HDR_TRAN_ID ='" & HDDR_ID & "' and GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "'"
                        cm12.ExecuteNonQuery()
                        cm12.Dispose()

                        Dim cm13 As New SqlCommand With {
                            .Connection = con
                        }
                        If con.State = ConnectionState.Closed Then
                            con.Open()
                        End If
                        cm13.CommandType = CommandType.Text
                        cm13.CommandText = "update tbl_GE_HDR set Transpoter_Code  ='" & Trim(dr("Transporter_Code")) & "' , TransporterName = '" & Trim(dr("Transporter_Name")) & "'  where GE_HDR_ID ='" & Trim(txtTransactionNo.Text) & "'"
                        cm13.ExecuteNonQuery()
                    End If
                End If


                'Next Gint
                'YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY

                'PO_No_Disp = ListView1.Items(1).SubItems(1).Text
                'DO_No_Disp = ListView1.Items(1).SubItems(2).Text
                '':::::::::::::::::::::::::::::::::

                'Date_v1 = Format(Date, "DD") & "-" & Format(Date, "MM") & "-" & Format(Date, "yyyy")
                'Time_v1 = Format(Time, "HH") & ":" & Format(Minute(Time), "00") ''''& Format(Time, "SS")

                'Date_v1 = Format(Trim(dtFirst.Text), "DD") & "-" & Format(Trim(dtFirst.Text), "MM") & "-" & Format(Trim(dtFirst.Text), "yyyy")
                Date_v1 = dtFirst.Text
                'Time_v1 = Format(Trim(dtFirst.Text), "HH") & ":" & Format(Minute(Trim(dtFirst.Text)), "00") ''& Format(Second(Trim(dtFirst.Text)), "00")
                Time_v1 = Format(TimeOfDay.ToString("hh:ss"))
                'Time_v1 = "01:39"
                If Trim(dtSecond.Text) <> "" Then
                    'Date_v2 = Format(Trim(dtSecond.Text), "dd") & "-" & Format(Trim(dtSecond.Text), "MM") & "-" & Format(Trim(dtSecond.Text), "yyyy")
                    Date_v2 = dtSecond.Text.ToString.Substring(0, 10)
                    'Time_v2 = Format(Trim(dtSecond.Text), "hh") & ":" & Format(Minute(Trim(dtSecond.Text)), "mm") '''& Format(Second(Trim(dtSecond.Text)), "00")
                    Time_v2 = Format(TimeOfDay.ToString("hh:ss"))
                End If
                Firstweight = Date_v1 & " " & Time_v1
                SecondWeight = Date_v2 & " " & Time_v2
                '':::::::::::::::::::::::::::::::::


                'MMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMM
                Dim cm14 As New SqlCommand With {
                    .Connection = con
                }
                If con.State = ConnectionState.Closed Then
                    con.Open()
                End If
                cm14.CommandType = CommandType.StoredProcedure
                cm14.CommandText = "sp_upd_Seal_no_Party_Wt_tbl_GE_HDR"
                cm14.Parameters.AddWithValue("@val_GE_HDR_ID", Trim(txtTransactionNo.Text))
                cm14.Parameters.AddWithValue("@val_Seal_No", Trim(txtSealNo.Text) & "")
                cm14.Parameters.AddWithValue("@val_Party_Gross_WT", Val(Trim(txtGrossWt.Text)) + 0)
                cm14.Parameters.AddWithValue("@val_Party_Tare_WT", Val(Trim(txtTareWt.Text)) + 0)
                cm14.Parameters.AddWithValue("@val_Party_Net_WT", Val(Trim(txtNetWt.Text)) + 0)
                cm14.ExecuteNonQuery()
                cm14.Dispose()
                '-----------------use str and update------
                'MMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMM

                If ans = vbYes Then

Repeat:
                    ''VVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV

                    If WT_UPDATE = "SWT" Then
                        dr = cc.GetDataReader("select S_WT_DateTime from tbl_GE_DET where GE_DET_TRan_ID = '" & S_WT_DAtetme_Print & "'")
                        Try
                            While dr.Read
                                SWT_DtTime_11 = dr("S_WT_DateTime")
                            End While
                        Catch ex As Exception

                        End Try
                        dr.Close()
                    End If
                    ''MsgBox SWT_DtTime_11
                    ''VVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV

                    ''no_of_copy = InputBox("Please input number of copies to be print .", "ElectroWay", 1)
                    Dim no_of_copy As Integer = 1

                    If Trim(no_of_copy) = "" Then no_of_copy = 1

                    Dim lines As String = ""

                    For ncp = 1 To no_of_copy
                        Try

                            lines = "             ESL STEEL LIMITED ( " & Trim(txtPlant.Text) & " - WORKS )"
                            lines = lines & vbCrLf & "                           WEIGHMENT SLIP  -  ( " & Label29.Text & " )"
                            lines = lines & vbCrLf & "--------------------------------------------------------------------------------"

                            lines = lines & vbCrLf & " Gate Pass No              : " & txtTransactionNo.Text & ""
                            lines = lines & vbCrLf & " VEHICLE NO                : " & txtVehicleNo.Text & ""
                            lines = lines & vbCrLf & " Vendor/Customer           : " & VenCustName & ""
                            lines = lines & vbCrLf & " Material                  : " & MatCode & ""

                            lines = lines & vbCrLf & " Challan/DO No.            : " & (ListView1.Items(0).SubItems(9).Text) & " " & (ListView1.Items(0).SubItems(2).Text) & ""
                            If IsNumeric(ListView1.Items(0).SubItems(6).Text) = True Then
                                lines = lines & vbCrLf & " Challan Qty.              : " & CDbl((ListView1.Items(0).SubItems(6).Text)) & "" & (ListView1.Items(0).SubItems(7).Text)
                            Else
                                lines = lines & vbCrLf & " Challan Qty.              : " & (ListView1.Items(0).SubItems(6).Text) & "" & (ListView1.Items(0).SubItems(7).Text)
                            End If

                            lines = lines & vbCrLf & " Transporter Name          : " & Text14.Text
                            lines = lines & vbCrLf & Chr(13) & Chr(5)
                            'printer.prt(Chr(13) & Chr(5))

                            lines = lines & vbCrLf & " 1st WT. Date & Time       : " & dtFirst.Text
                            lines = lines & vbCrLf & " 2nd WT. Date & Time       : " & SWT_DtTime_11

                            lines = lines & vbCrLf & Chr(13) & Chr(5)
                            If IsNumeric(txtFirstWt.Text.Trim) = True Then
                                lines = lines & vbCrLf & " 1st WT. (KG)              : " & CInt(Trim(txtFirstWt.Text.Trim))
                            Else
                                lines = lines & vbCrLf & " 1st WT. (KG)              : " & Trim(txtFirstWt.Text.Trim)
                            End If

                            If txtSecondWt.Text.Trim = "" Then
                                lines = lines & vbCrLf & " 2nd WT. (KG)              : "
                                lines = lines & vbCrLf & " Net WT. (KG)              : "
                            Else
                                If IsNumeric(txtFirstWt.Text.Trim) = True Then
                                    lines = lines & vbCrLf & " 2nd WT. (KG)              : " & CInt(Trim(txtSecondWt.Text.Trim))
                                    lines = lines & vbCrLf & " Net WT. (KG)              : " & CInt(Trim(Text17.Text.Trim))
                                Else
                                    lines = lines & vbCrLf & " 2nd WT. (KG)              : " & Trim(txtSecondWt.Text.Trim)
                                    lines = lines & vbCrLf & " Net WT. (KG)              : " & Trim(Text17.Text.Trim)
                                End If
                            End If

                            lines = lines & vbCrLf & "                                                          (Signature)"
                            lines = lines & vbCrLf & "-------------------------------------------------------------------------------"
                            lines = lines & vbCrLf & Chr(13) & Chr(5)
                            Print2LPT.Print(lines)
                        Catch ex As Exception
                            MessageBox.Show(ex.Message)
                        End Try
                        '------------------------------
                        'Dim printer As New myPrinter

                        'lines = "             ELECTROSTEEL STEELS LIMITED ( " & Trim(txtPlant.Text) & " - WORKS )"
                        'lines = lines & vbCrLf & "                           WEIGHMENT SLIP  -  ( " & Label29.Text & " )"
                        'lines = lines & vbCrLf & " ---------------------------------------------------------------------------------"

                        'lines = lines & vbCrLf & " Gate Pass No                         : " & txtTransactionNo.Text & ""
                        'lines = lines & vbCrLf & " VEHICLE NO                           :" & txtVehicleNo.Text & ""
                        'lines = lines & vbCrLf & " Vendor/Customer                      :" & VenCustName & ""
                        'lines = lines & vbCrLf & "Material                              :" & MatCode & ""

                        'lines = lines & vbCrLf & "Challan/DO No.                        : " & (ListView1.Items(0).SubItems(9).Text) & " " & (ListView1.Items(0).SubItems(2).Text) & ""
                        'lines = lines & vbCrLf & "Challan Qty.                          : " & (ListView1.Items(0).SubItems(6).Text) & "" & (ListView1.Items(0).SubItems(7).Text)

                        'lines = lines & vbCrLf & "Transporter Name                      : " & (Text14.Text)
                        'lines = lines & vbCrLf
                        ''printer.prt(Chr(13) & Chr(5))

                        'lines = lines & vbCrLf & "1st WT. Date & Time                   : " & (dtFirst.Text)
                        'lines = lines & vbCrLf & "2nd WT. Date & Time                   : " & SWT_DtTime_11 ' Tab(72); S_WT_Print; ""

                        'lines = lines & vbCrLf

                        'lines = lines & vbCrLf & "1st WT. (KG)                          : " & Trim(txtFirstWt.Text) ' Tab(72); F_WT_Print; ""
                        'lines = lines & vbCrLf & "2nd WT. (KG)                          : " & Trim(txtSecondWt.Text) ' Tab(72); S_WT_Print; ""
                        'lines = lines & vbCrLf & "Net WT. (KG)                          : " & Trim(Text17.Text) ' Tab(72); S_WT_Print; ""


                        'lines = lines & vbCrLf & "                                                          (Signature)"
                        'lines = lines & vbCrLf & "-------------------------------------------------------------------------------"
                        'lines = lines & vbCrLf
                        'lines = lines & vbCrLf
                        'lines = lines & vbCrLf
                        'lines = lines & vbCrLf
                        'lines = lines & vbCrLf
                        'lines = lines & "."
                        'Close(nPrinter)

                        '''''                                        Printer.Print "FIRST WT."; Tab(21); Trim(dtFirst.Text); Tab(72); Trim(txtFirstWt.Text); ""
                        '''''                                        Printer.Print "SECOND WT."; Tab(21); SWT_DtTime_11; Tab(72); Trim(txtSecondWt.Text); ""
                        '''''                                        Printer.Print Tab(72); "==========="
                        '''''                                        Printer.Print "NET WT."; Tab(21); Tab(72); Trim(Text17.Text); Tab(100)
                        '''''                                        Printer.Print Tab(95); "(Signature)"; ""
                        '''''                                        'Printer.Print " "
                        '''''                                        Printer.Print "(F.WT)Note:"; Tab(21); Trim(txtFirstWeightNote.Text)
                        '''''                                        'Printer.Print " "
                        '''''                                        Printer.Print "(S.WT)Note:"; Tab(21); Trim(txtSecondWtNote.Text); Tab(72); "Seal No:"; Trim(txtSealNo.Text)
                        '''''                                        Printer.EndDoc
                        '''''

                        'printer.prt(lines)
                        '----------------------------------
                        'Print2LPT.Print("             ELECTROSTEEL STEELS LIMITED ( " & Trim(txtPlant.Text) & " - WORKS )")
                        'Print2LPT.Print("                           WEIGHMENT SLIP  -  ( " & Label29.Text & " )")
                        'Print2LPT.Print(" ---------------------------------------------------------------------------------")
                        'Print2LPT.Print(" Gate Pass No                                  : " & txtTransactionNo.Text & "")
                        'Print2LPT.Print(" VEHICLE NO                                    :" & txtVehicleNo.Text & "")
                        'Print2LPT.Print(" Vendor/Customer                               :" & VenCustName & "")
                        'Print2LPT.Print(" Material                                      :" & MatCode & "")
                        'Print2LPT.Print(" Challan/DO No.                                : " & (ListView1.Items(0).SubItems(9).Text) & " " & (ListView1.Items(0).SubItems(2).Text) & "")
                        'Print2LPT.Print(" Challan Qty.                                  : " & (ListView1.Items(0).SubItems(6).Text) & "" & (ListView1.Items(0).SubItems(7).Text))
                        'Print2LPT.Print(" Transporter Name                              : " & (Text14.Text))
                        'Print2LPT.Print(" 1st WT. Date & Time                           : " & (dtFirst.Text))
                        'Print2LPT.Print(" 2nd WT. Date & Time                           : " & SWT_DtTime_11 & "")
                        'Print2LPT.Print(" 1st WT. (KG)                                  : " & Trim(txtFirstWt.Text) & "")
                        'Print2LPT.Print(" 2nd WT. (KG)                                  : " & Trim(txtSecondWt.Text) & "")
                        'Print2LPT.Print(" Net WT. (KG)                                  : " & Trim(Text17.Text) & "")
                        'Print2LPT.Print("                                                          (Signature)")
                        'Print2LPT.Print("-------------------------------------------------------------------------------")
                    Next


                    'ans = MsgBox("Weighment Updated, Print Slip ?", "ElectroWay", vbYesNo + vbDefaultButton1)
                    ans = MsgBox("Weighment Updated, Print Slip ?", vbYesNo, "Electrosteel Steels Limited.")
                    If ans = vbYes Then
                        GoTo Repeat
                    End If

                End If

                ''OOOOOOOOOOOOOOOOOOO

                If WT_UPDATE = "FWT" Then
                    'cm.Connection = con
                    'cm.CommandType = CommandType.Text
                    'cm.CommandText = "update TRCK_WB_TMP set TWT_FST_WT1 = '" & Trim(txtWt.Text) & "' ,  TWT_ENT_TM1 = getdate()  where TWT_TRK_NO = '" & Trim(txtVehicleNo.Text) & "' and TWT_CHL_NO = '" & Trim(ListView1.Items(0).SubItems(0).Text) & "'"
                    'cm.ExecuteNonQuery()
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    Dim str1 As String = "update TRCK_WB_TMP set TWT_FST_WT1 = '" & Trim(txtWt.Text) & "' ,  TWT_ENT_TM1 = getdate()  where TWT_TRK_NO = '" & Trim(txtVehicleNo.Text) & "' and TWT_CHL_NO = '" & Trim(ListView1.Items(0).SubItems(0).Text) & "'"
                    Try
                        cc.Execute(str1)
                    Catch ex As Exception

                    End Try
                ElseIf WT_UPDATE = "SWT" Then
                    cm.Connection = con
                    If con.State = ConnectionState.Closed Then
                        con.Open()
                    End If
                    'cm.CommandType = CommandType.Text
                    'cm.CommandText = "update TRCK_WB_TMP set TWT_SND_WT2 = '" & Trim(txtWt.Text) & "' ,  TWT_ENT_TM2 = getdate()  where TWT_TRK_NO = '" & Trim(txtVehicleNo.Text) & "' and TWT_CHL_NO = '" & Trim(ListView1.Items(0).SubItems(9).Text) & "'"
                    'cm.ExecuteNonQuery()
                    Dim str1 As String = "update TRCK_WB_TMP set TWT_SND_WT2 = '" & Trim(txtWt.Text) & "' ,  TWT_ENT_TM2 = getdate()  where TWT_TRK_NO = '" & Trim(txtVehicleNo.Text) & "' and TWT_CHL_NO = '" & Trim(ListView1.Items(0).SubItems(9).Text) & "'"
                    Try
                        cc.Execute(str1)
                    Catch ex As Exception

                    End Try
                End If

                ''OOOOOOOOOOOOOOOOOOO
                ''OOOOOOOOOOOOOOOOOOO
                Try
                    If WT_UPDATE = "FWT" Then
                        Dim str As String = "update TRCK_WB_TMP set TWT_FST_WT1 = '" & Trim(txtWt.Text) & "' ,  TWT_ENT_TM1 = getdate()  where TWT_TRK_NO = '" & Trim(txtVehicleNo.Text) & "'" '''' and TWT_CHL_NO = '" & Trim(ListView1.ListItems(1).ListSubItems(9).Text) & "'"
                        cc.Execute(str)

                    ElseIf WT_UPDATE = "SWT" Then
                        Dim str As String = "update TRCK_WB_TMP set TWT_SND_WT2 = '" & Trim(txtWt.Text) & "' ,  TWT_ENT_TM2 = getdate()  where TWT_TRK_NO = '" & Trim(txtVehicleNo.Text) & "'" '''''' and TWT_CHL_NO = '" & Trim(ListView1.ListItems(1).ListSubItems(9).Text) & "'"
                        cc.Execute(str)
                    End If
                Catch ex As Exception

                End Try
                ''OOOOOOOOOOOOOOOOOOO

                AcceptClick = 0
                ListView1.Items.Clear()
                txtFirstWeightNote.Enabled = True
                txtFirstWeightNote.Text = ""
                txtTransactionNo.Text = ""
                txtTransporter.Text = ""
                Text14.Text = ""
                Text7.Text = ""
                txtFirstWt.Text = ""
                txtWt.Text = ""
                txtVehicleNo.Text = ""
                txtSecondWtNote.Text = ""
                txtFirstWeightNote.Text = ""
                txtVehicleNo.Enabled = True
                txtSecondWtNote.Visible = False
                Label33.Visible = False
                Label34.Visible = False
                Text17.Text = ""
                dtFirst.Text = ""
                txtSecondWt.Text = ""
                dtSecond.Text = ""

                txtSealNo.Text = ""
                txtGrossWt.Text = ""
                txtTareWt.Text = ""
                txtNetWt.Text = ""

                txtgateInRemarks.Text = ""
                Ch_no_RFID = ""
                Ch_no_RFID_SWT = ""

                btnCancel.Focus()

                If txtVehicleNo.Enabled = True Then
                    txtVehicleNo.Focus()
                End If

                ddlRakeNo.SelectedIndex = 0

            End If

            WT_UPDATE = ""

        Else
            MsgBox("Click on Accept Please !", vbInformation, "Electrosteel Steels Limited.")
        End If


    End Sub
    Private Sub CancelVehilcle()
        Dim Vehicle_No As String = String.Empty, Customer_Name As String = String.Empty, Vendor_Name As String = String.Empty, Mat_Desc As String = String.Empty, Challan_No As String = String.Empty, DO_Challan_Qty As String = String.Empty, TransporterName As String = String.Empty, F_WT As String = String.Empty, F_WT_DateTime As String = String.Empty
        Dim GE_HDR_ID As String = SearchData("select GE_HDR_ID from tbl_GE_Hdr where Vehicle_No = '" & txtVehicleNo.Text.Trim & "' and Vehicle_Status = 'CANCEL' and Type_Of_Vehicle = 'SALES'")
        Dim strUpdate As String = "update tbl_GE_Hdr set Vehicle_Status = 'C' where GE_HDR_ID = '" & GE_HDR_ID & "'"
        Dim strUpdateCancel As String = "update tbl_GE_Det_Cancellation set F_WT='" & Val(Trim(txtWt.Text)) & "', F_WT_DateTime= getdate(),F_WT_DoneBy='" & User_ID & "',F_WT_Node_IP ='" & Sys_loc_IP & "' where GE_HDR_ID = '" & GE_HDR_ID & "'"
        Try
            cc.Execute(strUpdate)
            cc.Execute(strUpdateCancel)
            Dim ans = MsgBox("Weighment Updated, Print Slip ?", vbYesNo, "Electrosteel Steels Limited.")
            Dim str As String = "select distinct A.GE_HDR_ID,Vehicle_No,B.Customer_Name,B.Vendor_Name,B.Mat_Desc,B.Challan_No ,B.DO_Challan_Qty,A.TransporterName,B.F_WT,B.F_WT_DateTime from tbl_GE_hdr_Cancellation A,tbl_GE_Det_Cancellation B where A.GE_HDR_ID = B.GE_HDR_ID and A.GE_HDR_ID = '" & GE_HDR_ID & "' and B.F_WT > 0"
            dr = cc.GetDataReader(str)
            Try
                While dr.Read
                    Vehicle_No = dr("Vehicle_No").ToString
                    Customer_Name = dr("Customer_Name").ToString
                    Mat_Desc = dr("Mat_Desc").ToString
                    Challan_No = dr("Challan_No").ToString
                    DO_Challan_Qty = dr("DO_Challan_Qty").ToString
                    TransporterName = dr("TransporterName").ToString
                    F_WT = dr("F_WT").ToString
                    F_WT_DateTime = dr("F_WT_DateTime").ToString
                End While
            Catch ex As Exception

            End Try

            If ans = vbYes Then

Repeat:
                ''VVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV
                Dim no_of_copy As Integer = 1

                If Trim(no_of_copy) = "" Then no_of_copy = 1

                Dim lines As String = ""

                For ncp = 1 To no_of_copy
                    Try

                        lines = "             ELECTROSTEEL STEELS LIMITED ( " & Trim(txtPlant.Text) & " - WORKS )"
                        lines = lines & vbCrLf & "                           WEIGHMENT SLIP  -  ( " & Label29.Text & " )"
                        lines = lines & vbCrLf & "--------------------------------------------------------------------------------"

                        lines = lines & vbCrLf & " Gate Pass No              : " & GE_HDR_ID & ""
                        lines = lines & vbCrLf & " VEHICLE NO                : " & Vehicle_No & ""
                        lines = lines & vbCrLf & " Vendor/Customer           : " & Customer_Name & ""
                        lines = lines & vbCrLf & " Material                  : " & Mat_Desc & ""

                        lines = lines & vbCrLf & " Challan/DO No.            : " & Challan_No & ""
                        lines = lines & vbCrLf & " Challan Qty.              : " & DO_Challan_Qty & ""

                        lines = lines & vbCrLf & " Transporter Name          : " & TransporterName
                        lines = lines & vbCrLf & Chr(13) & Chr(5)
                        'printer.prt(Chr(13) & Chr(5))

                        lines = lines & vbCrLf & " 1st WT. Date & Time       : " & F_WT_DateTime
                        lines = lines & vbCrLf & " 2nd WT. Date & Time       : "

                        lines = lines & vbCrLf & Chr(13) & Chr(5)

                        lines = lines & vbCrLf & " 1st WT. (KG)              : " & F_WT


                        lines = lines & vbCrLf & " 2nd WT. (KG)              : "
                        lines = lines & vbCrLf & " Net WT. (KG)              : "


                        lines = lines & vbCrLf & "                                                          (Signature)"
                        lines = lines & vbCrLf & "-------------------------------------------------------------------------------"
                        lines = lines & vbCrLf & Chr(13) & Chr(5)
                        Print2LPT.Print(lines)
                    Catch ex As Exception
                        MessageBox.Show(ex.Message)
                    End Try
                    '------------------------------
                Next
                'ans = MsgBox("Weighment Updated, Print Slip ?", "ElectroWay", vbYesNo + vbDefaultButton1)
                ans = MsgBox("Weighment Updated, Print Slip ?", vbYesNo, "Electrosteel Steels Limited.")
                If ans = vbYes Then
                    GoTo Repeat
                End If
            End If
        Catch ex As Exception

        End Try
    End Sub
    Private Function searchSales_DO(ByVal TRAN_ID As String)
        Dim SALES_DOF As Boolean = False
        'Dim str As String = "select GE_HDR_ID from tbl_ge_det where GE_DET_Tran_ID = '248760' and Type_Of_Vehicle = 'SALES' and DO_No = ''"
        Dim str As String = "select GE_HDR_ID from tbl_ge_det where GE_DET_Tran_ID = '" & TRAN_ID & "' and Type_Of_Vehicle = 'SALES' and DO_No = ''"
        dr = cc.GetDataReader(str)
        Try
            While dr.Read
                SALES_DOF = True
            End While
        Catch ex As Exception

        End Try
        dr.Close()
        Return SALES_DOF
    End Function
    Private Function SearchData(ByVal Str As String) As String
        Dim ValueName As String = ""
        dr = cc.GetDataReader(Str)
        Try
            While dr.Read
                If ValueName = "" Then
                    ValueName = dr(0).ToString
                Else
                    ValueName = ValueName & "," & dr(0).ToString
                End If

            End While
        Catch ex As Exception

        End Try
        dr.Close()
        Return ValueName
    End Function
    Private Sub btExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btExit.Click
        Try
            txtFirstWeightNote.Enabled = True
            If comOpen Then SerialPort1.Close()
            Me.Close()
        Catch ex As Exception
            If Err.Description <> "" Then
                MsgBox(Err.Description, vbInformation, "ElectroWay")
            End If
        End Try
    End Sub
    Private Sub SAP_Close1()
        Try
            functionCtrl.Connection.Logoff()
            'functionCtr2.Connection.Logoff()
            sapConnection = Nothing
            functionCtrl = Nothing
            functionCtr2 = Nothing
        Catch ex As Exception

        End Try
    End Sub
    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        ListView1.Items.Clear()
        txtVehicleNo.Enabled = True
        txtTransactionNo.Text = ""
        txtVehicleNo.Text = ""

        Text7.Text = ""
        txtTransporter.Text = ""

        Text14.Text = ""
        txtFirstWeightNote.Text = ""

        txtFirstWt.Text = ""
        txtSecondWt.Text = ""
        Text17.Text = ""
        dtFirst.Text = ""
        dtSecond.Text = ""
        txtTime.Text = ""
        'Text21.Text = ""
        Label24.Text = "#"
        Label29.Text = "#"

        txtSealNo.Text = ""
        txtGrossWt.Text = ""
        txtTareWt.Text = ""
        txtNetWt.Text = ""

        AcceptClick = 0
        txtFirstWeightNote.Enabled = True
        WT_UPDATE = ""
        txtSecondWtNote.Visible = False
        Label33.Visible = False
        Label34.Visible = False
        Text17.Text = ""
        txtgateInRemarks.Text = ""
        Ch_no_RFID = ""
        Ch_no_RFID_SWT = ""

        ''''<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
        txtFirstWeightNote.BackColor = Color.White
        txtSecondWtNote.BackColor = Color.White

        txtSealNo.BackColor = Color.White
        txtGrossWt.BackColor = Color.White

        txtTareWt.BackColor = Color.White
        txtNetWt.BackColor = Color.White

        txtRakeGrouping.Text = ""
        ddlRakeNo.Items.Clear()
        ddlRakeNo.Items.Add("")

        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Disabled = '0' order by Reference_Code Desc")
        Try
            While dr.Read
                ddlRakeNo.Items.Add(dr("Reference_Code"))

            End While
        Catch ex As Exception

        End Try
        dr.Close()

        '<<<<<<<<<<<<<<<<<<<<<<<<<<<<
        txtVehicleNo.Focus()
    End Sub
    Private Sub btnTareWt_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnTareWt.Click
        btnCancel_Click(sender, e)
        OperatMode = "AUTO"
        'If MSComm1.PortOpen = True Then
        '    MSComm1.PortOpen = False
        'End If
        Call_From_Veh_WT = "FROM_WM"
        Me.Close()

        Dim frmVehicleWt1 As New frmVehicleWt With {
            .MdiParent = Me
        }
        frmVehicleWt1.Show()
    End Sub

    Private Sub ListView1_ItemCheck(ByVal sender As Object, ByVal e As System.Windows.Forms.ItemCheckEventArgs)
        Try

            If WT_UPDATE = "FWT" Then

                MsgBox("You are not allowed to uncheck any item during First WT.", vbInformation, "ElectroWay")
                'MsgBox Item

                For i As Integer = 0 To ListView1.Items.Count - 1
                    ListView1.Items(i).Checked = True
                Next
                Exit Sub
            End If

        Catch ex As Exception
            If Err.Description <> "" Then
                MsgBox(Err.Description, vbInformation, "ElectroWay")
            End If
        End Try

    End Sub
    Private Sub txtTransporter_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTransporter.KeyPress
        If AscW(e.KeyChar) = 13 Then
            Text14.Text = ""
            dr = cc.GetDataReader("select * from TRANSPORTER_MASTER where TRPT_CODE ='" & Trim(txtTransporter.Text) & "'")
            If dr.Read Then
                Text14.Text = dr("TRPT_NAME")
            Else
                MsgBox("Invalid Transporter Code.")
            End If
            dr.Close()
        End If
    End Sub

    Private Sub txtTransporter_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTransporter.LostFocus
        txtTransporter.BackColor = Color.White
    End Sub

    Private Sub Text14_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles Text14.GotFocus
        Text14.BackColor = Color.Gold
    End Sub

    Private Sub Text14_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles Text14.LostFocus
        Text14.BackColor = Color.White
    End Sub

    Private Sub txtSecondWt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSecondWt.TextChanged

        Try
            If (txtFirstWt.Text <> "" And txtSecondWt.Text <> "") Then

                If Val(Trim(txtFirstWt.Text)) > Val(Trim(txtSecondWt.Text)) Then
                    Text17.Text = Val(Trim(txtFirstWt.Text)) - Val(Trim(txtSecondWt.Text))
                Else
                    Text17.Text = Val(Trim(txtSecondWt.Text)) - Val(Trim(txtFirstWt.Text))
                End If
            End If
        Catch ex As Exception
            If Err.Description <> "" Then
                MsgBox(Err.Description, vbInformation, "ElectroWay")
            End If
        End Try
    End Sub

    Private Sub txtFirstWeightNote_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtFirstWeightNote.GotFocus
        Try
            txtFirstWeightNote.BackColor = Color.Gold
        Catch ex As Exception
            If Err.Description <> "" Then
                MsgBox(Err.Description, vbInformation, "ElectroWay")
            End If
        End Try
    End Sub

    Private Sub txtFirstWeightNote_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtFirstWeightNote.KeyPress
        If AscW(e.KeyChar) = 13 Then
            'Command2.Focus
            txtGrossWt.Focus()

        End If
    End Sub

    Private Sub txtFirstWeightNote_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtFirstWeightNote.LostFocus
        txtFirstWeightNote.BackColor = Color.White
    End Sub

    Private Sub txtSecondWtNote_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtSecondWtNote.GotFocus
        txtSecondWtNote.BackColor = Color.Gold
    End Sub

    Private Sub txtSecondWtNote_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtSecondWtNote.KeyPress
        If AscW(e.KeyChar) = 13 Then
            'Command2.Focus
            txtGrossWt.Focus()

        End If
    End Sub

    Private Sub txtSecondWtNote_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtSecondWtNote.LostFocus
        txtSecondWtNote.BackColor = Color.White
    End Sub

    Private Sub txtSealNo_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtSealNo.GotFocus
        txtSealNo.BackColor = Color.Gold

        txtSealNo.SelectionStart = 0
        txtSealNo.SelectionLength = Len(txtSealNo.Text)
    End Sub

    Private Sub txtSealNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtSealNo.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtGrossWt.Focus()
        End If
    End Sub

    Private Sub txtSealNo_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtSealNo.LostFocus
        txtSealNo.BackColor = Color.White
    End Sub

    Private Sub txtGrossWt_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtGrossWt.GotFocus
        txtGrossWt.BackColor = Color.Gold

        txtGrossWt.SelectionStart = 0
        txtGrossWt.SelectionLength = Len(txtGrossWt.Text)
    End Sub

    Private Sub txtGrossWt_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtGrossWt.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtTareWt.Focus()
        End If
    End Sub

    Private Sub txtGrossWt_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtGrossWt.LostFocus
        If Trim(txtGrossWt.Text) <> "" Then
            If IsNumeric(txtGrossWt.Text) = False Then
                MsgBox("Pls input only numeric data .", vbInformation, "ElectroWay")
                txtGrossWt.Text = ""
                txtGrossWt.Focus()
            End If
        End If

        txtGrossWt.BackColor = Color.White
    End Sub

    Private Sub txtTareWt_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTareWt.GotFocus
        txtTareWt.BackColor = Color.Gold

        txtTareWt.SelectionStart = 0
        txtTareWt.SelectionLength = Len(txtTareWt.Text)
    End Sub

    Private Sub txtTareWt_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtTareWt.KeyPress
        If AscW(e.KeyChar) = 13 Then
            txtNetWt.Focus()
        End If
    End Sub

    Private Sub txtTareWt_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtTareWt.LostFocus
        If Trim(txtTareWt.Text) <> "" Then
            If IsNumeric(txtTareWt.Text) = False Then
                MsgBox("Pls input only numeric data .", vbInformation, "ElectroWay")
                txtTareWt.Text = ""
                txtTareWt.Focus()
            End If
        End If
        txtTareWt.BackColor = Color.White
    End Sub

    Private Sub txtNetWt_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtNetWt.GotFocus
        txtNetWt.BackColor = Color.Gold

        txtNetWt.SelectionStart = 0
        txtNetWt.SelectionLength = Len(txtNetWt.Text)
    End Sub

    Private Sub txtNetWt_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtNetWt.KeyPress
        If AscW(e.KeyChar) = 13 Then
            Command2.Focus()
        End If
    End Sub

    Private Sub txtNetWt_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtNetWt.LostFocus
        If Trim(txtNetWt.Text) <> "" Then
            If IsNumeric(txtNetWt.Text) = False Then
                MsgBox("Pls input only numeric data .", vbInformation, "ElectroWay")
                txtNetWt.Text = ""
                txtNetWt.Focus()
            End If
        End If

        txtNetWt.BackColor = Color.White
    End Sub

    Private Sub txtWt_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtWt.LostFocus
        If IsNumeric(Trim(txtWt.Text)) = False Then
            'MsgBox("Please input numeric value only !", vbInformation, "ElectroWay")
            txtWt.Text = ""
            txtWt.Focus()
            Exit Sub
        End If
    End Sub

    Private Sub txtWt_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles txtWt.MouseClick
        txtWt.Enabled = False
    End Sub

    Private Sub txtWt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtWt.TextChanged
        If WT_UPDATE = "FWT" Then
            txtFirstWt.Text = Val(txtWt.Text)

        ElseIf WT_UPDATE = "SWT" Then
            txtSecondWt.Text = Val(txtWt.Text)
        End If
    End Sub

    Private Sub txtVehicleNo_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtVehicleNo.GotFocus
        If vehicle_sel = 1 Then
            ''ListView1.Items.Clear
            'Call txtVehicleNo_KeyPress(13)
            txtVehicleNo_KeyPress(sender, e)
            vehicle_sel = 0
        End If
        txtVehicleNo.BackColor = Color.Gold
    End Sub

    Private Sub txtVehicleNo_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtVehicleNo.KeyDown
        If e.KeyCode = 112 Then

            Dim frmVehicle1 As New frmVehicle
            frmVehicle1.Owner = Me
            frmVehicle1.Show()


            frmVehicle1.ListView1.Items.Clear()
            dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_Status = 'IN' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%' order by Vehicle_No")


            While dr.Read
                'i = frmVehicle.ListView1.Items.Count + 1
                For i As Integer = 0 To frmVehicle.ListView1.Items.Count - 1

                    frmVehicle.ListView1.Items.Add(dr("Vehicle_no"), i)
                    frmVehicle.ListView1.Items(i).SubItems.Add(dr("Type_Of_Vehicle"))
                    frmVehicle.ListView1.Items(i).SubItems.Add(dr("EntryDateTime"))

                Next
                '' ListView1.Items(k).SubItems.Add (m), , ""
                ''ListView1.Items(i).SubItems.Add (1), , dr("PO_NO") & ""
                'rec1.MoveNext()
            End While
            dr.Close()
            If frmVehicle.ListView1.Items.Count > 0 Then
                'frmVehicle.Show(vbModal)
            Else
                MsgBox("No Vehicle exists !", vbInformation)
            End If


        End If

        If e.KeyCode = 113 Then
            Dim frmVehicle1 As New frmVehicle
            frmVehicle1.Owner = Me
            frmVehicle1.Show()

            frmVehicle.ListView1.Items.Clear()
            dr = cc.GetDataReader("select distinct a.Vehicle_no , a.Type_of_Vehicle , a.EntryDateTime  from tbl_GE_Hdr a , tbl_GE_DET b where a.GE_HDR_ID = b.GE_HDR_ID and  a.Vehicle_Status = 'IN' and a.Plant_Code = '" & Trim(txtPlant.Text) & "' and a.Company_Code = '" & Trim(txtCompany.Text) & "' and b.F_WT > 0 and b.S_WT = 0 and a.Remarks_IN not like '%Auto%IN%Grouping%' group by a.Vehicle_no , a.Type_of_Vehicle , a.EntryDateTime order by a.Vehicle_No")

            Try
                While dr.Read
                    'i = frmVehicle.ListView1.Items.Count + 1
                    For i As Integer = 1 To frmVehicle.ListView1.Items.Count + 1
                        frmVehicle.ListView1.Items.Add(dr("Vehicle_no"), i)
                        frmVehicle.ListView1.Items(i).SubItems.Add(dr("Type_Of_Vehicle"))
                        frmVehicle.ListView1.Items(i).SubItems.Add(dr("EntryDateTime"))
                    Next
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            If frmVehicle.ListView1.Items.Count > 0 Then
                'frmVehicle.Show(vbModal)
            Else
                MsgBox("No Vehicle exists !", vbInformation)
            End If


        End If

        If e.KeyCode = 114 Then
            Dim frmDetailsReport1 As New frmDetailsReport
            frmDetailsReport1.MdiParent = Me
            frmDetailsReport1.Show()
        End If
    End Sub

    Private Sub txtVehicleNo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtVehicleNo.KeyPress
        Dim Vehicle_Status As String = SearchData("select Vehicle_Status from tbl_GE_Hdr where Vehicle_No = '" & txtVehicleNo.Text.Trim & "' and Vehicle_Status = 'CANCEL' and Type_Of_Vehicle = 'SALES'")
        If Vehicle_Status = "CANCEL" Then
            MessageBox.Show("Cancel Type Vehicle, will take the Tare Weight!")
            Exit Sub
        End If
        Dim Tran_No_for_Manual As String = String.Empty, GHDR_I_D As String = String.Empty, type_O_F_Veh As String = String.Empty, WB_Completed As String = String.Empty, F_WT_REQ As String = String.Empty
        Try

            ''MsgBox KeyAscii

FOR_REENTRY:

            Dim TRN_ID_1 As String = String.Empty
            Dim TypeOfVehicle As String = String.Empty
            Dim maxWbCountID1 As Integer
            txtSecondWtNote.Visible = False
            Label33.Visible = False
            Label34.Visible = False
            Label29.Text = ""
            ListView1.HideSelection = True
            Dim Date_Time As String = String.Empty
            WBCountID = 0

            'If (KeyAscii >= 65 And KeyAscii <= 90) Or (KeyAscii >= 48 And KeyAscii <= 57) Or KeyAscii = 8 Or KeyAscii = 13 Then

            'txtVehicleNo.Text = UCase(txtVehicleNo.Text)


            Dim TRNFLAG As Boolean = False
            Dim min_WB_Count_ID_Flag As Boolean = False
            If AscW(e.KeyChar) = 13 Then

                txtVehicleNo.BackColor = Color.White
                txtVehicleNo.Enabled = False

                If OperationType = "MANUAL" Then
                    Tran_No_for_Manual = InputBox("Please input the Transaction No. ", "ElectroWay")
                    dr = cc.GetDataReader("select * from tbl_GE_Hdr where GE_HDR_ID  = '" & Trim(Tran_No_for_Manual) & "' and Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status <> 'C' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")
                Else
                    dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")

                End If
                Try
                    If dr.Read Then
                        TRN_ID_1 = dr("TRN_ID")
                        GHDR_I_D = dr("GE_HDR_ID")
                        type_O_F_Veh = dr("Type_OF_Vehicle")
                        TRNFLAG = True
                    End If
                Catch ex As Exception

                End Try
                dr.Close()

                If TRNFLAG = True Then
                    dr = cc.GetDataReader("select min(WB_Count_ID) from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1)
                    Try
                        While dr.Read
                            min_WB_Count_ID_Flag = True
                        End While
                    Catch ex As Exception

                    End Try
                    dr.Close()
                    If min_WB_Count_ID_Flag = True Then
                        dr = cc.GetDataReader("select max(WB_Count_ID) from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1)
                        Try
                            If dr.Read Then
                                maxWbCountID1 = dr(0)
                            End If
                        Catch ex As Exception

                        End Try
                        dr.Close()

                        dr = cc.GetDataReader("select sum(S_WT) from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1 & " and WB_Count_ID = " & maxWbCountID1)
                        Try
                            While dr.Read
                                If dr(0) > 0 Then
                                    WB_Completed = "T"
                                End If
                            End While

                        Catch ex As Exception

                        End Try
                        dr.Close()

                        If WB_Completed = "T" Then
                            Dim ans_reentry = MsgBox("Weighment completed for this Vehicle !", vbInformation, "Electrosteel Steels Limited.")
                            ''''ans_reentry = MsgBox("Would you like to do Multiple weighment ?", vbYesNo, "ElectroWay") '' MULTIPLE WEIGHMENT BLOCKED on 18 May 2015 as per instruction of Mr. PM Mishra

                            If ans_reentry = vbYes Then
                                '<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
                                '' FOR Blank Line item insert
                                Dim type_O_F_VehFlag As Boolean = False
                                If type_O_F_Veh = "SALES" Or type_O_F_Veh = "PURCH" Or type_O_F_Veh = "STKTROUT" Then
                                    dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_TRAN_ID = " & TRN_ID_1 & " and F_WT = 0 and S_WT = 0 and WB_Count_ID  = 0")
                                    Try
                                        If dr.Read = True Then
                                            type_O_F_VehFlag = True
                                        End If
                                    Catch ex As Exception

                                    End Try
                                    dr.Close()
                                    '-----------------
                                    If type_O_F_VehFlag = True Then
                                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_TRAN_ID = " & TRN_ID_1 & " and NET_WT > 0 order by WB_count_ID Desc")
                                        If dr.Read Then
                                            cm.Connection = con
                                            cm.CommandType = CommandType.StoredProcedure
                                            cm.CommandText = "sp_ins_tbl_GE_DET_Blank_Record_for_SALES"
                                            cm.Parameters("@val_GE_DET_Tran_ID") = dr("GE_DET_TRAN_ID")
                                            If con.State = ConnectionState.Closed Then
                                                con.Open()
                                            End If
                                            cm.ExecuteNonQuery()
                                        End If
                                        dr.Close()
                                    End If
                                End If

                                GoTo FOR_REENTRY

                                ''<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
                            End If

                            Exit Sub
                        End If
                    End If
                    '----------------------------------------------------
                    If OperationType = "MANUAL" Then
                        'Tran_No_for_Manual = InputBox("Please input the Transaction No. ", "ElectroWay")
                        dr = cc.GetDataReader("select * from tbl_GE_Hdr where GE_HDR_ID  = '" & Trim(Tran_No_for_Manual) & "' and Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status <> 'C' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")
                    Else
                        dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")

                    End If
                    Try
                        While dr.Read
                            HDDR_ID = dr("TRN_ID")
                            TypeOfVehicle = dr("Type_Of_Vehicle")
                            TVehType11 = TypeOfVehicle
                            Label24.Text = dr("EntryDateTime")
                            txtTransactionNo.Text = dr("GE_HDR_ID")
                            Text7.Text = dr("Gate_No")
                            txtTransporter.Text = dr("Transpoter_code")
                            Text14.Text = dr("TransporterName")
                            Label29.Text = dr("Type_Of_Vehicle")
                            ''txtPlant.Text = dr("Plant_Code")

                            ''"""""""""""""""""""""""""""""""""""""""""""""""""
                            txtSealNo.Text = dr("Seal_No")
                            txtGrossWt.Text = dr("Party_Gross_WT")
                            txtTareWt.Text = dr("Party_Tare_WT")
                            txtNetWt.Text = dr("Party_Net_WT")
                            txtgateInRemarks.Text = dr("Remarks_IN")
                            ''""""""""""""""""""""""""""""""""""""""""""""""""""
                        End While
                    Catch ex As Exception

                    End Try
                    dr.Close()
                Else
                    MsgBox("Invalid Vehicle Number !", vbInformation, "Electrosteel Steels Limited.")
                    txtVehicleNo.Text = ""
                    txtVehicleNo.Enabled = True
                    txtVehicleNo.Focus()
                    dr.Close()
                    Exit Sub
                End If
                '-------------------------------------
                Dim TrnFFlag As Boolean = False
                dr = cc.GetDataReader("select sum(WB_Count_ID), sum(F_WT) , sum(S_WT) from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1)
                Try
                    While dr.Read
                        If dr(0) = 0 And dr(1) = 0 And dr(2) = 0 Then
                            TrnFFlag = True
                        End If
                    End While
                Catch ex As Exception

                End Try
                dr.Close()
                '-----------------------
                Dim lvi As New ListViewItem
                '----------------------------
                If TrnFFlag Then

                    dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1 & " and WB_count_ID = 0")

                    While dr.Read
                        'i = ListView1.Items.Count + 1
                        For i As Integer = 0 To ListView1.Items.Count
                            '---------List----------
                            lvi.Text = dr("GE_DET_TRAN_ID")
                            '------------------------
                            'ListView1.Items.Add(dr("GE_DET_TRAN_ID"), i)
                            'ListView1.Items(i).Checked = True
                            lvi.Checked = True
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("PO_NO"))
                                'ListView1.Items(i).SubItems.Add(dr("PO_NO"))
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("SO_NO"))
                                'ListView1.Items(i).SubItems.Add(dr("SO_NO"))
                            End If
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add("")
                                'ListView1.Items(i).SubItems.Add("")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("DO_NO"))
                                'ListView1.Items(i).SubItems.Add(dr("DO_NO"))
                            End If

                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("PO_Line_Item"))
                                'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item"))
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("DO_Line_Item"))
                                'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item"))
                            End If
                            lvi.SubItems.Add(dr("Mat_CODE"))
                            'ListView1.Items(i).SubItems.Add(dr("Mat_CODE"))
                            lvi.SubItems.Add(dr("Mat_Desc"))
                            'ListView1.Items(i).SubItems.Add(dr("Mat_Desc"))
                            lvi.SubItems.Add(dr("DO_Challan_Qty"))
                            lvi.SubItems.Add(dr("UOM"))
                            lvi.SubItems.Add(dr("Unloading_No"))
                            lvi.SubItems.Add(dr("Challan_No"))
                            lvi.SubItems.Add(dr("Challan_Date"))
                            lvi.SubItems.Add(dr("SO_Line_Item"))

                            'ListView1.Items(i).SubItems.Add(dr("DO_Challan_Qty"))
                            'ListView1.Items(i).SubItems.Add(dr("UOM"))
                            'ListView1.Items(i).SubItems.Add(dr("Unloading_No"))
                            'ListView1.Items(i).SubItems.Add(dr("Challan_No"))
                            'ListView1.Items(i).SubItems.Add(dr("Challan_Date"))
                            'ListView1.Items(i).SubItems.Add(dr("SO_Line_Item"))
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("Vendor_Code"))
                                'ListView1.Items(i).SubItems.Add(dr("Vendor_Code"))
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("Customer_Code"))
                                'ListView1.Items(i).SubItems.Add(dr("Customer_Code"))
                            End If
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("Vendor_Name"))
                                'ListView1.Items(i).SubItems.Add(dr("Vendor_Name"))
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("Customer_Name"))
                                'ListView1.Items(i).SubItems.Add(dr("Customer_Name"))
                            End If
                        Next
                    End While
                    dr.Close()
                    WT_UPDATE = "FWT"
                    dtFirst.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("hh-mm-ss")

                    If TypeOfVehicle = "INTRDEPT" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Then
                        ''If TypeOfVehicle <> "" Then
                        For lk = 0 To ListView1.Items.Count - 1
                            If Trim(ListView1.Items(lk).SubItems(4).Text) = "" And Trim(ListView1.Items(lk).SubItems(5).Text) = "" Then
                                Dim frmSelectMaterial1 As New frmSelectMaterial
                                frmSelectMaterial1.MdiParent = Me
                                frmSelectMaterial1.Show()
                            End If
                        Next
                        ''FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF

                        'dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'")
                        'If dr.Read = True Then
                        '    Dim strrTWT = "Would you like to take Tare Wt. from master." & Chr(13) & "Tare WT  = " & dr("Vehicle_Tare_Wt") & "  and  Tare WT Date = " & dr("Last_Updated_On")
                        '    ''ans = MsgBox(strrTWT, vbYesNo, "ElectroWay")  ''' Blocked on 20 may 2015
                        '    'ans = MsgBox("Would you like to take Tare Wt. from master ?", vbYesNo, "ElectroWay")
                        'End If
                        'dr.Close()
                        ''FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF

                        'Dim ans = MsgBox("Would you like to take Tare Wt. from master ? ", vbYesNo, "ElectroWay")

                        'If ans = vbYes Then

                        '    Dim ans1 = MsgBox("Are you sure you want to take Tare Wt. from master ?  Pls confirm...", vbYesNo, "ElectroWay")

                        '    If ans1 = vbYes Then
                        ''updat_SWT
                        'dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and TareWtValidUpto >=  '" & Format(Today.Date, "yyyy-mm-dd") & " 00:00:00.000'")
                        'If dr.Read = True Then
                        '    txtFirstWt.Text = dr("Vehicle_Tare_Wt")
                        '    cm.Connection = con
                        '    cm.CommandType = CommandType.StoredProcedure
                        '    cm.CommandText = "sp_upd_tbl_GE_Det_F_WT"
                        '    cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                        '    cm.Parameters.AddWithValue("@val_F_WT_Node_IP", Sys_loc_IP)
                        '    cm.Parameters.AddWithValue("@val_F_WT", Val(Trim(txtFirstWt.Text)))
                        '    cm.Parameters.AddWithValue("@val_F_WT_DoneBy", User_ID & "")
                        '    cm.Parameters.AddWithValue("@val_F_WT_Note", "")
                        '    If con.State = ConnectionState.Closed Then
                        '        con.Open()
                        '    End If
                        '    cm.ExecuteNonQuery()

                        '    cm.Connection = con
                        '    cm.CommandType = CommandType.StoredProcedure
                        '    cm.CommandText = "sp_upd_tbl_GE_DET_Mat_Code_Nae"
                        '    cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                        '    cm.Parameters.AddWithValue("@val_Mat_Code", Trim(ListView1.Items(i).SubItems(4).Text))
                        '    cm.Parameters.AddWithValue("@val_Mat_Desc", Trim(ListView1.Items(i).SubItems(5).Text))
                        '    If con.State = ConnectionState.Closed Then
                        '        con.Open()
                        '    End If
                        '    cm.ExecuteNonQuery()

                        '    dtSecond.Text = Format(Today.Date, "dd-mm-yyyy") & " " & TimeOfDay.ToString("HH-MM-SS")

                        '    WT_UPDATE = "SWT"

                        'Else
                        '    MsgBox("Tare Wt. not maintaned for this Vehicle in master or validity period expired.", vbInformation, "ElectroWay")


                        'End If
                        'dr.Close()
                        '    End If
                        'End If
                    End If
                    ''rec1.Close


                ElseIf TypeOfVehicle = "INTRDEPT" Then

                    dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1 & " and WB_count_ID = 0")

                    While dr.Read
                        'i = ListView1.Items.Count + 1
                        For i As Integer = 0 To ListView1.Items.Count

                            'ListView1.Items.Add(dr("GE_DET_TRAN_ID"), i)
                            'ListView1.Items(i).Checked = True
                            lvi.Checked = True
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.Text = dr("PO_NO")
                                'ListView1.Items(i).SubItems.Add(dr("PO_NO"))
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.Text = dr("SO_NO")
                                'ListView1.Items(i).SubItems.Add(dr("SO_NO"))
                            End If
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add("")
                                'ListView1.Items(i).SubItems.Add("")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add("DO_NO")
                                'ListView1.Items(i).SubItems.Add(dr("DO_NO"))
                            End If

                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add("PO_Line_Item")
                                'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item"))
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add("DO_Line_Item")
                                'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item"))
                            End If

                            lvi.SubItems.Add("Mat_CODE")

                            'ListView1.Items(i).SubItems.Add(dr("Mat_CODE"))

                            'ListView1.Items(i).SubItems.Add(dr("Mat_Desc"))
                            lvi.SubItems.Add("Mat_Desc")
                            lvi.SubItems.Add(dr("DO_Challan_Qty") + 0)
                            lvi.SubItems.Add(dr("UOM") & "")
                            lvi.SubItems.Add(dr("Unloading_No") & "")
                            lvi.SubItems.Add(dr("Challan_No") & "")
                            lvi.SubItems.Add(dr("Challan_Date") & "")
                            lvi.SubItems.Add(dr("SO_Line_Item") & "")

                            'ListView1.Items(i).SubItems.Add(dr("DO_Challan_Qty") + 0)
                            'ListView1.Items(i).SubItems.Add(dr("UOM") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Unloading_No") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Challan_No") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Challan_Date") & "")
                            'ListView1.Items(i).SubItems.Add(dr("SO_Line_Item") & "")
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("Vendor_Code") & "")
                                'ListView1.Items(i).SubItems.Add(dr("Vendor_Code") & "")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("Customer_Code") & "")
                                'ListView1.Items(i).SubItems.Add(dr("Customer_Code") & "")
                            End If

                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("Vendor_Name") & "")
                                'ListView1.Items(i).SubItems.Add(dr("Vendor_Name") & "")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                                lvi.SubItems.Add(dr("Customer_Name") & "")
                                'ListView1.Items(i).SubItems.Add(dr("Customer_Name") & "")
                            End If

                        Next
                    End While


                    WT_UPDATE = "FWT"
                    dtFirst.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-mm-ss")
                    dr = cc.GetDataReader("select F_WT from tbl_GE_DET where GE_HDR_TRAN_ID = " & TRN_ID_1 & " and F_WT > 0 and S_WT = 0")
                    If dr.Read Then
                        F_WT_REQ = 1
                        txtFirstWt.Text = dr("F_WT")

                        WT_UPDATE = "SWT"
                    End If
                    dr.Close()

                    'WT_UPDATE = "FWT"
                    'dtFirst.Text = Format(Date, "dd-mm-yyyy") & " " & Time


                    If TypeOfVehicle = "INTRDEPT" And F_WT_REQ = 0 Then
                        For lk = 0 To ListView1.Items.Count - 1
                            If Trim(ListView1.Items(lk).SubItems(4).Text) = "" And Trim(ListView1.Items(lk).SubItems(5).Text) = "" Then
                                Dim frmSelectMaterial1 As New frmSelectMaterial
                                frmSelectMaterial1.MdiParent = Me
                                frmSelectMaterial1.Show()
                            End If
                        Next

                        dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'")
                        If dr.Read Then
                            Dim strrTWT = "Would you like to take Tare Wt. from master." & Chr(13) & "Tare WT  = " & dr("Vehicle_Tare_Wt") & "  and  Tare WT Date = " & dr("Last_Updated_On")
                            ''ans = MsgBox(strrTWT, vbYesNo, "ElectroWay") ''' blocked on 20 MAY 2015
                        End If
                        dr.Close()

                        Dim ans = MsgBox("Would you like to take Tare Wt. from master ? ", vbYesNo, "ElectroWay")

                        If ans = vbYes Then

                            Dim ans1 = MsgBox("Are you sure you want to take Trae Wt. from master ?", vbYesNo, "ElectroWay")

                            If ans1 = vbYes Then
                                dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'and TareWtValidUpto >=  '" & Format(Today.Date, "yyyy-mm-dd") & " 00:00:00.000'")
                                If dr.Read = True Then
                                    txtFirstWt.Text = dr("Vehicle_Tare_Wt")
                                    cm.Connection = con
                                    cm.CommandType = CommandType.StoredProcedure
                                    cm.CommandText = "sp_upd_tbl_GE_Det_F_WT"
                                    cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                                    cm.Parameters.AddWithValue("@val_F_WT_Node_IP", Sys_loc_IP)
                                    cm.Parameters.AddWithValue("@val_F_WT", Val(Trim(txtFirstWt.Text)))
                                    cm.Parameters.AddWithValue("@val_F_WT_DoneBy", User_ID & "")
                                    cm.Parameters.AddWithValue("@val_F_WT_Note", "")
                                    If con.State = ConnectionState.Closed Then
                                        con.Open()
                                    End If
                                    cm.ExecuteNonQuery()

                                    cm.Connection = con
                                    cm.CommandType = CommandType.StoredProcedure
                                    cm.CommandText = "sp_upd_tbl_GE_DET_Mat_Code_Nae"
                                    cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                                    cm.Parameters.AddWithValue("@val_Mat_Code", Trim(ListView1.Items(i).SubItems(4).Text))
                                    cm.Parameters.AddWithValue("@val_Mat_Desc", Trim(ListView1.Items(i).SubItems(5).Text))
                                    If con.State = ConnectionState.Closed Then
                                        con.Open()
                                    End If
                                    cm.ExecuteNonQuery()

                                    dtSecond.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-MM-SS")

                                    WT_UPDATE = "SWT"

                                Else
                                    MsgBox("Tare Wt. not maintaned for this Vehicle in master.", vbInformation, "ElectroWay")


                                End If
                                dr.Close()
                            End If
                        End If
                    End If
                    dr.Close()
                Else
                    '**************************** edited for S WT start

                    dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1 & " and F_WT > 0 and WB_count_ID = 0 and S_WT  = 0")
                    Try

                        While dr.Read
                            dtFirst.Text = Format(CDate(dr("F_WT_DateTime")), "dd-MM-yyyy hh-mm-ss")
                            txtFirstWt.Text = dr("F_WT")
                            txtFirstWeightNote.Text = dr("F_WT_Note")
                            dtSecond.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("hh-mm-ss")

                            'While dr.Read
                            i = ListView1.Items.Count + 1
                            'ListView1.Items.Add(dr("GE_DET_TRAN_ID"), i)
                            'ListView1.Items(i).Checked = True
                            lvi.Text = dr("GE_DET_TRAN_ID")
                            lvi.Checked = True
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("PO_NO"))
                                'ListView1.Items(i).SubItems.Add(dr("PO_NO") & "")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("SO_NO"))
                                'ListView1.Items(i).SubItems.Add(dr("SO_NO") & "")
                            End If
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add("")
                                'ListView1.Items(i).SubItems.Add("")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("DO_NO") & "")
                                'ListView1.Items(i).SubItems.Add(dr("DO_NO") & "")
                            End If

                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("PO_Line_Item") & "")
                                'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item") & "")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("DO_Line_Item") & "")
                                'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item") & "")
                            End If


                            lvi.SubItems.Add(dr("Mat_CODE") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Mat_CODE") & "")
                            lvi.SubItems.Add(dr("Mat_Desc") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Mat_Desc") & "")
                            lvi.SubItems.Add(dr("DO_Challan_Qty") & "")
                            'ListView1.Items(i).SubItems.Add(dr("DO_Challan_Qty") + 0)
                            lvi.SubItems.Add(dr("UOM") & "")
                            'ListView1.Items(i).SubItems.Add(dr("UOM") & "")
                            lvi.SubItems.Add(dr("Unloading_No") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Unloading_No") & "")
                            lvi.SubItems.Add(dr("Challan_No") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Challan_No") & "")
                            lvi.SubItems.Add(dr("Mat_CODE") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Challan_Date") & "")
                            lvi.SubItems.Add(dr("SO_Line_Item") & "")
                            'ListView1.Items(i).SubItems.Add(dr("SO_Line_Item") & "")
                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("Vendor_Code") & "")
                                'ListView1.Items(i).SubItems.Add(dr("Vendor_Code") & "")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("Customer_Code") & "")
                                'ListView1.Items(i).SubItems.Add(dr("Customer_Code") & "")
                            End If

                            If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                                lvi.SubItems.Add(dr("Vendor_Name") & "")
                                'ListView1.Items(i).SubItems.Add(dr("Vendor_Name") & "")
                            ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Or Trim(TypeOfVehicle) = "FLYASH" Then
                                lvi.SubItems.Add(dr("Customer_Name") & "")
                                'ListView1.Items(i).SubItems.Add(dr("Customer_Name") & "")
                            End If

                            'rec4.MoveNext()
                        End While

                    Catch ex As Exception

                    End Try
                    'End If

                    WT_UPDATE = "SWT"

                    txtSecondWtNote.Visible = True
                    Label33.Visible = True
                    Label34.Visible = True
                    txtFirstWeightNote.Enabled = False


                    dr.Close()


                End If
                ListView1.Items.Add(lvi)

                If Label29.Text = "INTRDEPT" Then
                    Label29.Font = New Drawing.Font("Times New Roman", 16, FontStyle.Bold Or FontStyle.Italic)
                    Label29.ForeColor = Color.Red
                Else
                    ' &H00FF0000&
                    Label29.Font = New Drawing.Font("Times New Roman", 10, FontStyle.Bold Or FontStyle.Italic)
                    Label29.ForeColor = Color.Blue
                    'Label29.FontBold = True
                    txtVehicleNo.BackColor = Color.White
                End If

                If txtFirstWeightNote.Enabled = True Then
                    txtFirstWeightNote.Focus()
                    txtFirstWeightNote.BackColor = Color.Gold
                ElseIf txtSecondWtNote.Enabled = True Then
                    txtSecondWtNote.Focus()
                    txtSecondWtNote.BackColor = Color.Gold
                End If


                dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_ID ='" & GHDR_I_D & "'")
                If dr.Read Then
                    If Trim(dr("Grouping_Ref_Code")) <> "" Then
                        ddlRakeNo.Items.Add(Trim(dr("Grouping_Ref_Code")))
                        ddlRakeNo.Text = Trim(dr("Grouping_Ref_Code"))
                    End If
                End If
                dr.Close()


            End If

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
        '----------------------
        Try
            Dim str As String = "select GE_HDR_ID,Type_Of_Vehicle,Unloading_No,PO_No,PO_Line_Item,DO_No,DO_Line_Item,SO_No,SO_Line_Item,Mat_Code,Mat_Desc,Challan_No,DO_Challan_Qty from tbl_GE_Det where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
            ds = cc.GetDataset(str)
            gvsow.DataSource = ds.Tables(0)
        Catch ex As Exception

        End Try

    End Sub

    Private Sub txtVehicleNo_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtVehicleNo.LostFocus
        txtVehicleNo.BackColor = Color.White
    End Sub

    Private Sub txtVehicleNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtVehicleNo.TextChanged
        On Error GoTo err

        txtVehicleNo.Text = UCase(Trim(txtVehicleNo.Text))
        txtVehicleNo.SelectionStart = Len(txtVehicleNo.Text)

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()
    End Sub

    Private Sub Timer1_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer1.Tick
        Try
            If AcceptClick = 0 Then
                'If OperationType = "AUTO" Then
                '    If NON_AVERY_System = 0 Then
                '        txtTime.Text = TimeOfDay.ToString("HH-MM-SS")
                '        val_text = MSComm1.Input
                '        If Len(val_text) >= 24 Then       '''''''>= 11 Then    ---  set for OLD Weigh Bridge and tested working Fine
                '            Weight = Mid(val_text, 4, 11)
                '            Text2.Text = Mid(val_text, 4, 11)
                '            Text3.Text = Trim(Text2.Text)
                '            For num = 1 To Len(Text3.Text)
                '                If (IsNumeric(Mid(Text3.Text, num, 1)) = True) Then
                '                    WtVal = WtVal & Mid(Text3.Text, num, 1)

                '                ElseIf Mid(Text3.Text, num, 1) = " " And Mid(Text3.Text, num + 1, 1) = "k" Then
                '                    txtWt.Text = WtVal
                '                    Exit For
                '                End If
                '            Next num
                '            WtVal = ""
                '            val_text = ""
                '            Text2.Text = ""
                '            Text3.Text = 0
                '        End If
                '        val_text = ""
                '        Text2.Text = ""
                '        Text3.Text = 0
                '    ElseIf NON_AVERY_System = 1 Then

                '        txtTime.Text = Format(Time, "HH-MM-SS")

                '        val_text = MSComm1.Input
                '        If Len(val_text) >= 10 Then  ''24     '''''''>= 11 Then    ---  set for OLD Weigh Bridge and tested working Fine
                '            Weight = Mid(val_text, 5, 10)
                '            Text2.Text = Mid(val_text, 5, 10)
                '            Text3.Text = Trim(Text2.Text)
                '            For num = 1 To Len(Text3.Text)
                '                If (IsNumeric(Mid(Text3.Text, num, 1)) = True) Then
                '                    WtVal = WtVal & Mid(Text3.Text, num, 1)

                '                    ''ElseIf Mid(Text3.Text, num, 1) = " " And Mid(Text3.Text, num + 1, 1) = "k" Then
                '                    txtWt.Text = WtVal
                '                    ''     Exit For
                '                End If
                '            Next num
                '            WtVal = ""
                '            val_text = ""
                '            Text2.Text = ""
                '            Text3.Text = 0
                '        End If
                '        val_text = ""
                '        Text2.Text = ""
                '        Text3.Text = 0

                '    End If
                'End If

                If WT_UPDATE = "FWT" Then
                    txtFirstWt.Text = Val(txtWt.Text)

                ElseIf WT_UPDATE = "SWT" Then
                    txtSecondWt.Text = Val(txtWt.Text)
                End If


            End If

        Catch ex As Exception
            MsgBox(Err.Number & "   " & Err.Description)
        End Try
    End Sub
    Private Sub push_data_weighment()
        '--------------------------
        If txtPlant.Text.Trim = "" Then
            txtPlant.Text = "ES01"
        End If
        Dim oStatus As Integer
        Dim PostCoil, oItmID, oVhclNo, oVendor, oPONo, oPOItem, oUnloadingNo, oTrType, oWerks, oTrnDate, oTrnTime, oZInOut, oWTDet, oChWT, oUOM, oGrossWT, oTareWT, oNetWT, oWBTrID, funcControl, oRFC, oTrnID, oCustomer, oSONo, oSOLItem
        Dim PLANT_CODE111 As String = String.Empty, GE_HDRID1 As String = String.Empty, Result As String = String.Empty

        ''
        Dim Date_v As String = String.Empty
        Dim Time_v As String = String.Empty
        Dim WT_UOM As String = String.Empty
        Dim ch_wt As Double
        Dim tareeWt As Double
        Dim GrosWT As Double
        Dim NettWT As Double

        'txtVehicleNo.Text = "BR01GL9425"
        'HDDR_ID = "2162364"
        '------------TEMP 123---------------------
        'Dim strVU1 As String = "select Vehicle_no from Update_V_WT"
        'Dim dsVU As DataSet = cc.GetDataset(strVU1)
        'For VU As Integer = 0 To dsVU.Tables(0).Rows.Count - 1
        '    VehNoU = dsVU.Tables(0).Rows(VU).Item("Vehicle_no")
        'txtVehicleNo.Text = VehNoU
        'Dim hdr As String = "select TRN_ID from tbl_ge_hdr where Vehicle_No  =  '" & VehNoU & "' and vehicle_status = 'IN'"
        Dim hdr As String = "select TRN_ID from tbl_ge_hdr where Vehicle_No  =  '" & txtVehicleNo.Text.Trim & "' and vehicle_status = 'IN'"
        dr = cc.GetDataReader(hdr)
        Try
            While dr.Read
                HDDR_ID = dr(0).ToString
            End While
        Catch ex As Exception

        End Try
        dr.Close()

        ''----------------------------------

        Date_v = Format(Today.Date, "yyyy") & Format(Today.Date, "MM") & Format(Today.Date, "dd")
        'Time_v = Format(TimeOfDay.ToString, "hh") & Format(Minute(TimeOfDay.ToString), "mm") & Format(TimeOfDay.ToString, "ss")
        Time_v = TimeOfDay.ToString("HHmmss")

        ds = cc.GetDataset("select * FROM tbl_GE_Det WHERE GE_HDR_TRAN_ID = " & HDDR_ID)

        For i As Integer = 0 To ds.Tables(0).Rows.Count - 1

            If ds.Tables(0).Rows(i).Item("NET_WT") > 0 And (ds.Tables(0).Rows(i).Item("Unloading_No") <> "" Or ds.Tables(0).Rows(i).Item("DO_No") <> "") Then

                'Dim TypeOfVehicle As String = TVehType11
                Dim TypeOfVehicle As String = ds.Tables(0).Rows(i).Item("Type_Of_Vehicle")
                Dim Unld_Nmbr As String = ds.Tables(0).Rows(i).Item("Unloading_no")
                Dim DO_Nmbr As String = ds.Tables(0).Rows(i).Item("DO_NO")

                If TypeOfVehicle = "PURCH" Or TypeOfVehicle = "PURCHRET" Or TypeOfVehicle = "INTRDEPT" Or TypeOfVehicle = "GATEPASS" Then

                    WBWeightDet = ""

                    pono_wb = ds.Tables(0).Rows(i).Item("PO_No")
                    ponoLinItm_wb = ds.Tables(0).Rows(i).Item("PO_Line_Item")


                    '''''  Call WB_Determin   '' BLOCKED on 11 MAR 2015 during testing


                    functionCtrl = CreateObject("SAP.Functions")
                    sapConnection = functionCtrl.Connection


                    sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
                    sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
                    sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
                    sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
                    sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
                    sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
                    sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
                    sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub

                    Else
                        ''MsgBox "Connected ........"


                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        oTrnID.Value = Trim(ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID") & "\" & Trim(txtPlant.Text))
                        If oRFC.Call = True Then

                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully deleted from Zwt_bg table
                                PostCoil = 2
                            End If
                        End If
                        ''**************************************************

                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_data_update")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        oItmID = oRFC.Exports("ZMATNR")
                        oVhclNo = oRFC.Exports("ZVHCL_NMBR")

                        oVendor = oRFC.Exports("zlifnr")

                        oPONo = oRFC.Exports("ZPO_SO_NO")
                        oPOItem = oRFC.Exports("ZPO_ITEM")
                        oUnloadingNo = oRFC.Exports("ZGATENO")
                        oTrType = oRFC.Exports("ZTR_TYPE")

                        oWerks = oRFC.Exports("zwerks")


                        oTrnDate = oRFC.Exports("ZTRN_DATE")
                        oTrnTime = oRFC.Exports("ZTRN_TIME")

                        oZInOut = oRFC.Exports("zinout")

                        oWTDet = oRFC.Exports("ztweight")

                        oChWT = oRFC.Exports("ZCHL_GTY")
                        oUOM = oRFC.Exports("ZWT_UNIT")
                        oGrossWT = oRFC.Exports("ZGROSS_WT")
                        oTareWT = oRFC.Exports("ZTARE_WT")
                        oNetWT = oRFC.Exports("ZNET_WT")
                        oWBTrID = oRFC.Exports("ZWB_TR_ID")

                        oTrnID.Value = Trim(ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID") & "\" & Trim(txtPlant.Text))            ''    "TEST0001136"
                        oItmID.Value = ds.Tables(0).Rows(i).Item("Mat_Code")
                        oVhclNo.Value = Trim(txtVehicleNo.Text)

                        oVendor.Value = ds.Tables(0).Rows(i).Item("Vendor_Code")

                        oPONo.Value = ds.Tables(0).Rows(i).Item("PO_Line_Item")
                        oPOItem.Value = ds.Tables(0).Rows(i).Item("PO_No")
                        oUnloadingNo.Value = ds.Tables(0).Rows(i).Item("Unloading_No")
                        oTrType.Value = ds.Tables(0).Rows(i).Item("Type_Of_Vehicle")


                        '>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            dr.Close()
                            Exit Sub

                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "ZTM_HGATE_ENTRY"


                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        ''objOptTab.FreeTable
                        objOptTab1.FreeTable()
                        'Then set values
                        objOptTab1.Rows.Add()
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "GATENO = '" & Unld_Nmbr & "'"

                        objFldTab1.FreeTable()

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "GATENO"  ''''''"WERKS"  ''



                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If

                        'Dim PLANT_CODE111, GE_HDRID1, Result As String

                        For Each objDatRec1 In objDatTab1.Rows
                            For Each objFldRec1 In objFldTab1.Rows
                                PLANT_CODE111 = "ES01"   ''''Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))
                            Next
                        Next


                        ''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        oWerks.Value = PLANT_CODE111


                        oTrnDate.Value = Date_v    ' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-mm-yyyy")
                        oTrnTime.Value = Time_v    '   "103442"      ''format used in ingress is Hour Minute Second without having any space    ''format(Time, "hh:mm:ss")

                        If TypeOfVehicle = "PURCH" Then
                            oZInOut.Value = "I"
                        ElseIf TypeOfVehicle = "PURCHRET" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "INTRDEPT" Then
                            oZInOut.Value = "D"
                        ElseIf TypeOfVehicle = "GATEPASS" Then
                            oZInOut.Value = "G"
                        End If


                        oWTDet.Value = WBWeightDet

                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_DET_TRAN_ID = " & ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID"))
                        If dr.Read Then
                            GE_HDRID1 = dr("GE_HDR_ID")
                            ch_wt = dr("DO_Challan_QTY")
                            WT_UOM = Trim(dr("UOM"))
                            If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "DMT") Then
                                GrosWT = (dr("F_WT")) / 1000
                                tareeWt = (dr("S_WT")) / 1000
                                NettWT = (dr("Net_WT")) / 1000

                            Else
                                GrosWT = dr("F_WT")
                                tareeWt = dr("S_WT")
                                NettWT = dr("Net_WT")
                            End If

                        End If
                        dr.Close()

                        oChWT.Value = ch_wt
                        'oUOM.Value = WT_UOM
                        If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "KG" Or WT_UOM = "DMT") Then
                            oUOM.Value = WT_UOM
                        Else
                            oUOM.Value = "KG"
                        End If
                        oGrossWT.Value = GrosWT
                        oTareWT.Value = tareeWt    ' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oNetWT.Value = NettWT
                        oWBTrID.Value = GE_HDRID1

                        ''   "TEST-VEHICLE052014"


                        If oRFC.Call = True Then

                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                                '''''''''''''''''''''''''''
                                cm.Connection = con
                                cm.CommandType = CommandType.Text
                                cm.CommandText = "update tbl_GE_DET set DataUploadedIN_SAP = 'U' where GE_HDR_ID  ='" & GE_HDRID1 & "'"
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()

                                '''''''''''''''''''''''''''
                            End If
                            If oStatus = 2 Then ' Data Exists in SAP
                                PostCoil = 5
                            End If
                        End If

                        Result = oRFC.Call
                        'MsgBox Result

                    End If
                ElseIf TypeOfVehicle = "SALES" Or TypeOfVehicle = "STKTROUT" Or TypeOfVehicle = "SALESRET" Then


                    functionCtrl = CreateObject("SAP.Functions")
                    sapConnection = functionCtrl.Connection


                    sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
                    sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
                    sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
                    sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
                    sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
                    sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
                    sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
                    sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub

                    Else
                        ''MsgBox "Connected ........"


                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_UPLDWD")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        'oTrnID.Value = ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID")
                        oTrnID.Value = Trim(ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID") & "\" & Trim(txtPlant.Text))
                        If oRFC.Call = True Then

                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                            End If
                        End If
                        ''**************************************************

                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_WB_data_update")
                        oTrnID = oRFC.Exports("ZTR_ID")
                        oItmID = oRFC.Exports("ZMATNR")
                        oVhclNo = oRFC.Exports("ZVHCL_NMBR")

                        oCustomer = oRFC.Exports("zkunnr")

                        '''''Set oLIFNR = oRFC.Exports("zlifnr")

                        oSONo = oRFC.Exports("ZPO_SO_NO")
                        oSOLItem = oRFC.Exports("ZPO_ITEM")

                        oPONo = oRFC.Exports("zvbeln")  ' DO no
                        oPOItem = oRFC.Exports("zposnr")   ''   DO Line item
                        'Set oUnloadingNo = oRFC.Exports("ZUN_NO")
                        oTrType = oRFC.Exports("ZTR_TYPE")

                        oWerks = oRFC.Exports("zwerks")

                        oTrnDate = oRFC.Exports("ZTRN_DATE")
                        oTrnTime = oRFC.Exports("ZTRN_TIME")

                        oZInOut = oRFC.Exports("zinout")

                        oChWT = oRFC.Exports("ZCHL_GTY")     ''''oRFC.Exports("ZCHAL_QTY")
                        oUOM = oRFC.Exports("ZWT_UNIT")
                        oGrossWT = oRFC.Exports("ZTARE_WT")
                        oTareWT = oRFC.Exports("ZGROSS_WT")
                        oNetWT = oRFC.Exports("ZNET_WT")
                        oWBTrID = oRFC.Exports("ZWB_TR_ID")

                        oTrnID.Value = Trim(ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID") & "\" & Trim(txtPlant.Text))              ''    "TEST0001136"
                        oItmID.Value = ds.Tables(0).Rows(i).Item("Mat_Code")
                        oVhclNo.Value = Trim(txtVehicleNo.Text)

                        oCustomer.Value = ds.Tables(0).Rows(i).Item("Customer_Code")

                        oSONo.Value = ds.Tables(0).Rows(i).Item("SO_Line_Item")
                        oSOLItem.Value = ds.Tables(0).Rows(i).Item("SO_No")

                        oPONo.Value = ds.Tables(0).Rows(i).Item("DO_No")
                        oPOItem.Value = ds.Tables(0).Rows(i).Item("DO_Line_Item")
                        ''oUnloadingNo.Value = ListView1.Items(i).SubItems(11).Text
                        oTrType.Value = ds.Tables(0).Rows(i).Item("Type_Of_Vehicle")


                        '>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        Call SAP_Con2()

                        If sapConnection.Logon(0, True) <> True Then
                            MsgBox("No connection to SAP System .......")
                            dr.Close()
                            Exit Sub

                        End If

                        objRfcFunc1 = functionCtr2.Add("RFC_READ_TABLE")

                        objQueryTab1 = objRfcFunc1.Exports("QUERY_TABLE")
                        objQueryTab1.Value = "LIPS"


                        objOptTab1 = objRfcFunc1.Tables("OPTIONS")
                        objFldTab1 = objRfcFunc1.Tables("FIELDS")
                        objDatTab1 = objRfcFunc1.Tables("DATA")
                        'First we set the condition
                        'Refresh table
                        ''objOptTab.FreeTable
                        objOptTab1.FreeTable()
                        'Then set values
                        objOptTab1.Rows.Add()
                        objOptTab1(objOptTab1.RowCount, "TEXT") = "VBELN = '" & DO_Nmbr & "'"

                        objFldTab1.FreeTable()

                        objFldTab1.Rows.Add()
                        objFldTab1(objFldTab1.RowCount, "FIELDNAME") = "WERKS"  ''



                        If objRfcFunc1.Call = False Then
                            MsgBox(objRfcFunc1.Exception)
                        End If

                        For Each objDatRec1 In objDatTab1.Rows


                            For Each objFldRec1 In objFldTab1.Rows
                                PLANT_CODE111 = Mid(objDatRec1("WA"), objFldRec1("OFFSET") + 1, objFldRec1("LENGTH"))


                            Next
                        Next


                        ''>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

                        oWerks.Value = PLANT_CODE111

                        '                                        If PLANT_CODE_UPL_ZWT_BG = "" Then
                        '
                        '                                            oWerks.Value = Trim(txtPlant.Text)
                        '                                        Else
                        '                                            oWerks.Value = PLANT_CODE_UPL_ZWT_BG
                        '                                        End If
                        '
                        oTrnDate.Value = Date_v    ' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oTrnTime.Value = Time_v    '   "103442"      ''format used in ingress is Hour Minute Second without having any space    ''format(Time, "hh:mm:ss")





                        If TypeOfVehicle = "SALES" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "STKTROUT" Then
                            oZInOut.Value = "O"
                        ElseIf TypeOfVehicle = "SALESRET" Then
                            oZInOut.Value = "I"
                        End If



                        dr = cc.GetDataReader("select * from tbl_GE_DET where GE_DET_TRAN_ID = " & ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID"))
                        Try
                            If dr.Read Then
                                GE_HDRID1 = dr("GE_HDR_ID")
                                ch_wt = dr("DO_Challan_QTY")
                                WT_UOM = Trim(dr("UOM"))
                                If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "DMT") Then
                                    GrosWT = (dr("F_WT")) / 1000
                                    tareeWt = (dr("S_WT")) / 1000
                                    NettWT = (dr("Net_WT")) / 1000
                                    ' ------------------Required by DIP------
                                ElseIf WT_UOM = "M" Then
                                    GrosWT = (dr("F_WT")) / 1000
                                    tareeWt = (dr("S_WT")) / 1000
                                    NettWT = (dr("Net_WT")) / 1000
                                    '-----------------------------------------
                                Else
                                    GrosWT = dr("F_WT")
                                    tareeWt = dr("S_WT")
                                    NettWT = dr("Net_WT")
                                End If
                            End If
                        Catch ex As Exception

                        End Try

                        dr.Close()
                        oChWT.Value = ch_wt
                        If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "KG") Then
                            oUOM.Value = WT_UOM
                        ElseIf WT_UOM = "M" Then
                            oUOM.Value = "TON"
                        Else
                            oUOM.Value = "KG"
                        End If
                        oGrossWT.Value = GrosWT
                        oTareWT.Value = tareeWt    ' format used in ingress is year month Date without having any space     ''Format(DateValue(Now), "dd-MM-yyyy")
                        oNetWT.Value = NettWT
                        oWBTrID.Value = GE_HDRID1
                        ''   "TEST-VEHICLE052014"


                        If oRFC.Call = True Then

                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                                '''''''''''''''''''''''''''
                                cm.Connection = con
                                cm.CommandType = CommandType.Text
                                cm.CommandText = "update tbl_GE_DET set DataUploadedIN_SAP = 'U' where GE_HDR_ID  ='" & GE_HDRID1 & "'"
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()

                                '''''''''''''''''''''''''''

                            End If
                            If oStatus = 2 Then ' Data Exists in SAP
                                PostCoil = 5
                            End If
                        End If

                        Result = oRFC.Call
                        'MsgBox Result

                    End If
                ElseIf TypeOfVehicle = "FLYASH" Then
                    '---------------
                    dr = cc.GetDataReader("select * from tbl_GE_DET where GE_DET_TRAN_ID = " & ds.Tables(0).Rows(i).Item("GE_DET_TRAN_ID"))
                    Try
                        If dr.Read Then
                            WT_UOM = Trim(dr("UOM"))
                            If (WT_UOM = "TO" Or WT_UOM = "TON" Or WT_UOM = "DMT") Then
                                GrosWT = (dr("S_WT")) / 1000
                                tareeWt = (dr("F_WT")) / 1000
                                NettWT = (dr("Net_WT")) / 1000
                                ' ------------------Required by DIP------
                            ElseIf WT_UOM = "M" Then
                                GrosWT = (dr("S_WT")) / 1000
                                tareeWt = (dr("F_WT")) / 1000
                                NettWT = (dr("Net_WT")) / 1000
                                '-----------------------------------------
                            Else
                                GrosWT = dr("S_WT")
                                tareeWt = dr("F_WT")
                                NettWT = dr("Net_WT")
                            End If
                        End If
                    Catch ex As Exception

                    End Try

                    dr.Close()
                    '---------------
                    functionCtrl = CreateObject("SAP.Functions")
                    sapConnection = functionCtrl.Connection


                    sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
                    sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
                    sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
                    sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
                    sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
                    sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
                    sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
                    sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")


                    If sapConnection.Logon(0, True) <> True Then
                        MsgBox("No connection to SAP System .......")
                        Exit Sub

                    Else
                        ''MsgBox "Connected ........"


                        funcControl = CreateObject("SAP.Functions")
                        funcControl.Connection = sapConnection
                        oRFC = funcControl.Add("ZRFC_FLYASH_WB")
                        oTrnID = oRFC.Exports("YZORDER_NO")
                        Dim oTR_WEIGHT = oRFC.Exports("YTR_WEIGHT")
                        Dim oGR_WEIGHT = oRFC.Exports("YGR_WEIGHT")
                        oTrnID.Value = Trim(ds.Tables(0).Rows(i).Item("DO_No"))
                        oTR_WEIGHT.Value = tareeWt
                        oGR_WEIGHT.Value = GrosWT
                        'oTR_WEIGHT.Value = Trim(ds.Tables(0).Rows(i).Item("F_WT"))
                        'oGR_WEIGHT.Value = Trim(ds.Tables(0).Rows(i).Item("S_WT"))
                        If oRFC.Call = True Then

                            If oStatus = 1 Then ' fail
                                PostCoil = 1
                            End If
                            If oStatus = 0 Then ' successfully inserted in Zwt_bg table
                                PostCoil = 2
                            End If
                        End If
                    End If
                    ''*********************
                End If

            End If

            'rec4.MoveNext()

        Next

        '------------TEMP 123---------------------
        'Next
    End Sub


    Private Sub WB_Determin()
        On Error GoTo err
        Dim TWeight_1 As String = String.Empty
        Call SAP_Con1()

        If sapConnection.Logon(0, True) <> True Then
            MsgBox("No connection to SAP System .......")
            SAP_CON_NOT_AVAIL = 1
            'Label25.Caption = "SAP CONNECTION NOT AVAILABLE."
            Exit Sub

        Else

            'Label25.Caption = ""

            Dim objRfcFunc As Object
            'Set theFunc = functionCtrl.Add("RFC_READ_TABLE")
            objRfcFunc = functionCtrl.Add("RFC_READ_TABLE")




            objQueryTab = objRfcFunc.Exports("QUERY_TABLE")
            objQueryTab.Value = "EKPO"

            objOptTab = objRfcFunc.Tables("OPTIONS")
            objFldTab = objRfcFunc.Tables("FIELDS")
            objDatTab = objRfcFunc.Tables("DATA")
            'First we set the condition
            'Refresh Table
            objOptTab.FreeTable()
            'Then set values
            ''objOptTab.Rows.Add
            ''objOptTab(objOptTab.RowCount, "TEXT") = "VBELN = '0080007013'"  ''' and "
            objOptTab.Rows.Add()
            objOptTab(objOptTab.RowCount, "TEXT") = "EBELN = '" & pono_wb & "' and EBELP = '" & ponoLinItm_wb & "'"  '' and GATEOUT NE 'O'" '' and LOEKZ NE 'L'"

            objFldTab.FreeTable()

            objFldTab.Rows.Add()
            objFldTab(objFldTab.RowCount, "FIELDNAME") = "TWEIGHT"

            If objRfcFunc.Call = False Then
                MsgBox(objRfcFunc.Exception)
            End If

            'i = 5

            If objDatTab.Rows.Count = 0 Then
                ''MsgBox "Vendor Not Mentioned in PO !", vbInformation, "ElectroSteel Castings Limited"
            Else

                For Each objDatRec In objDatTab.Rows
                    'i = i + 1
                    For Each objFldRec In objFldTab.Rows
                        TWeight_1 = Mid(objDatRec("WA"), objFldRec("OFFSET") + 1, objFldRec("LENGTH"))


                    Next
                Next
            End If

            WBWeightDet = TWeight_1

        End If

err:
        If Err.Description <> "" Then
            MsgBox(Err.Description, vbInformation, "ElectroWay")
        End If
        Err.Clear()

    End Sub

    Private Sub SAP_Con1()

        functionCtrl = CreateObject("SAP.Functions")
        sapConnection = functionCtrl.Connection
        sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
        sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
        sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
        sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
        sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
        sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
        sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
        sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")
    End Sub

    Private Sub SAP_Con2()
        functionCtr2 = CreateObject("SAP.Functions")
        sapConnection = functionCtr2.Connection
        sapConnection.User = ConfigurationManager.AppSettings("SAPUser_ID")
        sapConnection.Password = ConfigurationManager.AppSettings("SAPUser_Pass")
        sapConnection.System = ConfigurationManager.AppSettings("SAPSys_name")
        sapConnection.ApplicationServer = ConfigurationManager.AppSettings("SAPApp_Server")
        sapConnection.SystemNumber = ConfigurationManager.AppSettings("SAPSys_No")
        sapConnection.Client = ConfigurationManager.AppSettings("SAP_Client")
        sapConnection.Language = ConfigurationManager.AppSettings("SAP_Lang")
        sapConnection.CodePage = ConfigurationManager.AppSettings("SAP_CodePage")
    End Sub

    'Private Function breakpoint()
    'Do
    '    dummy = DoEvents
    'Loop Until MSComm1.InBufferCount >= 7
    'End Function

    Private Sub Timer2_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer2.Tick
        Timer2.Enabled = False
        Try
            If AcceptClick = 0 Then
                If WT_UPDATE = "FWT" Then
                    txtFirstWt.Text = Val(txtWt.Text)

                ElseIf WT_UPDATE = "SWT" Then
                    txtSecondWt.Text = Val(txtWt.Text)
                End If


            End If

        Catch ex As Exception
            MsgBox(Err.Number & "   " & Err.Description)
        End Try
        '-----------------------------------------------

        Try
            If Trim(txtVehicleNo.Text) = "" Then
                Dim WB_TRAN_TYPEFLAG As Boolean = False
                Dim WB_TRAN_TYPE As String = ""
                Dim WB_RFID_ANTENA_IP As String = ""
                dr = cc.GetDataReader("select * from tbl_WB_IP_VS_ANTENA_IP where WB_MC_IP ='" & Trim(Sys_loc_IP) & "'")
                Try
                    While dr.Read
                        WB_TRAN_TYPEFLAG = True
                        WB_TRAN_TYPE = dr("WB_TRAN_TYPE").ToString
                        WB_RFID_ANTENA_IP = dr("WB_RFID_ANTENA_IP").ToString
                    End While
                Catch ex As Exception

                End Try
                dr.Close()
                '----------------------------
                If WB_TRAN_TYPEFLAG = True Then
                    If WB_TRAN_TYPE = "F" Then
                        dr = cc.GetDataReader("select * from TRCK_WB_TMP where isnull(TWT_FST_WT1,'') = '' and TWT_IP_ADD1 ='" & WB_RFID_ANTENA_IP & "'") ''''''' and datediff(minute,TWT_ENT_TM1, getdate())> = 1274"
                        Try
                            While dr.Read
                                If Trim(Sys_loc_IP) = "************" Then
                                    txtVehicleNo.Text = dr("TWT_TRK_NO")
                                    'Call txtVehicleNo_KeyPress(sender, e)
                                End If
                                'txtVehicleNo.Text = dr("TWT_TRK_NO")
                                ' ''Ch_no_RFID = rec7.Fields("CHALLAN_NO")
                                'Call txtVehicleNo_KeyPress(sender, e)
                            End While
                        Catch ex As Exception

                        End Try
                        dr.Close()

                    ElseIf WB_TRAN_TYPE = "S" Then
                        Dim SecondWTFlag As Boolean = True
                        Dim Vehicle_NO As String = Nothing
                        Dim TWT_CHL_NO As String = Nothing
                        dr = cc.GetDataReader("select * from TRCK_WB_TMP where isnull(TWT_FST_WT1,'') <> '' and isnull(TWT_SND_WT2,'') = '' and TWT_IP_ADD2 ='" & WB_RFID_ANTENA_IP & "'") '''' and datediff(minute,TWT_ENT_TM2, getdate())> = 1274"
                        Try
                            While dr.Read
                                If Trim(Sys_loc_IP) = "************" Then
                                    SecondWTFlag = True
                                    Vehicle_NO = dr("TWT_TRK_NO").ToString.Trim
                                    TWT_CHL_NO = dr("TWT_CHL_NO")
                                End If
                            End While
                        Catch ex As Exception

                        End Try
                        dr.Close()
                        If SecondWTFlag = True Then
                            dr = cc.GetDataReader("select a.*,b.* from tbl_GE_HDR a , tbl_GE_DET b where a.GE_HDR_ID  = b.GE_HDR_ID and a.VEHICLE_NO  ='" & Vehicle_NO & "' and a.Vehicle_Status = 'IN' and b.F_WT > 0 and b.S_WT  = 0")
                            Try
                                While dr.Read
                                    txtVehicleNo.Text = Vehicle_NO
                                    Ch_no_RFID_SWT = TWT_CHL_NO
                                    'Call txtVehicleNo_KeyPress(sender, e)
                                End While
                            Catch ex As Exception

                            End Try
                            dr.Close()
                        End If
                    End If
                    'Call txtVehicleNo_KeyPress(sender, e)
                End If
                If txtVehicleNo.Text.Trim <> "" Then
                    WBRFID()
                End If
            End If
        Catch ex As Exception

        End Try

        Timer2.Enabled = True
    End Sub
    Private Sub WBRFID()
        Dim Tran_No_for_Manual As String = String.Empty, GHDR_I_D As String = String.Empty, type_O_F_Veh As String = String.Empty, WB_Completed As String = String.Empty, F_WT_REQ As String = String.Empty
        Try

            ''MsgBox KeyAscii

FOR_REENTRY:

            Dim TRN_ID_1 As String = String.Empty
            Dim TypeOfVehicle As String = String.Empty
            Dim maxWbCountID1 As Integer
            txtSecondWtNote.Visible = False
            Label33.Visible = False
            Label34.Visible = False
            Label29.Text = ""
            ListView1.HideSelection = True
            Dim Date_Time As String = String.Empty
            WBCountID = 0


            Dim TRNFLAG As Boolean = False
            Dim min_WB_Count_ID_Flag As Boolean = False
            'If AscW(e.KeyChar) = 13 Then

            txtVehicleNo.BackColor = Color.White
            txtVehicleNo.Enabled = False

            If OperationType = "MANUAL" Then
                Tran_No_for_Manual = InputBox("Please input the Transaction No. ", "ElectroWay")
                dr = cc.GetDataReader("select * from tbl_GE_Hdr where GE_HDR_ID  = '" & Trim(Tran_No_for_Manual) & "' and Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status <> 'C' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")
            Else
                dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")

            End If
            Try
                If dr.Read Then
                    TRN_ID_1 = dr("TRN_ID")
                    GHDR_I_D = dr("GE_HDR_ID")
                    type_O_F_Veh = dr("Type_OF_Vehicle")
                    TRNFLAG = True
                End If
            Catch ex As Exception

            End Try
            dr.Close()

            If TRNFLAG = True Then
                dr = cc.GetDataReader("select min(WB_Count_ID) from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1)
                Try
                    While dr.Read
                        min_WB_Count_ID_Flag = True
                    End While
                Catch ex As Exception

                End Try
                dr.Close()
                If min_WB_Count_ID_Flag = True Then
                    dr = cc.GetDataReader("select max(WB_Count_ID) from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1)
                    Try
                        If dr.Read Then
                            maxWbCountID1 = dr(0)
                        End If
                    Catch ex As Exception

                    End Try
                    dr.Close()

                    dr = cc.GetDataReader("select sum(S_WT) from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1 & " and WB_Count_ID = " & maxWbCountID1)
                    Try
                        While dr.Read
                            If dr(0) > 0 Then
                                WB_Completed = "T"
                            End If
                        End While

                    Catch ex As Exception

                    End Try
                    dr.Close()

                    If WB_Completed = "T" Then
                        Dim ans_reentry = MsgBox("Weighment completed for this Vehicle !", vbInformation, "Electrosteel Steels Limited.")
                        ''''ans_reentry = MsgBox("Would you like to do Multiple weighment ?", vbYesNo, "ElectroWay") '' MULTIPLE WEIGHMENT BLOCKED on 18 May 2015 as per instruction of Mr. PM Mishra

                        If ans_reentry = vbYes Then
                            '<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
                            '' FOR Blank Line item insert
                            Dim type_O_F_VehFlag As Boolean = False
                            If type_O_F_Veh = "SALES" Or type_O_F_Veh = "PURCH" Or type_O_F_Veh = "STKTROUT" Then
                                dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_TRAN_ID = " & TRN_ID_1 & " and F_WT = 0 and S_WT = 0 and WB_Count_ID  = 0")
                                Try
                                    If dr.Read = True Then
                                        type_O_F_VehFlag = True
                                    End If
                                Catch ex As Exception

                                End Try
                                dr.Close()
                                '-----------------
                                If type_O_F_VehFlag = True Then
                                    dr = cc.GetDataReader("select * from tbl_GE_DET where GE_HDR_TRAN_ID = " & TRN_ID_1 & " and NET_WT > 0 order by WB_count_ID Desc")
                                    If dr.Read Then
                                        cm.Connection = con
                                        cm.CommandType = CommandType.StoredProcedure
                                        cm.CommandText = "sp_ins_tbl_GE_DET_Blank_Record_for_SALES"
                                        cm.Parameters("@val_GE_DET_Tran_ID") = dr("GE_DET_TRAN_ID")
                                        If con.State = ConnectionState.Closed Then
                                            con.Open()
                                        End If
                                        cm.ExecuteNonQuery()
                                    End If
                                    dr.Close()
                                End If
                            End If

                            GoTo FOR_REENTRY

                            ''<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
                        End If

                        Exit Sub
                    End If
                End If
                '----------------------------------------------------
                If OperationType = "MANUAL" Then
                    'Tran_No_for_Manual = InputBox("Please input the Transaction No. ", "ElectroWay")
                    dr = cc.GetDataReader("select * from tbl_GE_Hdr where GE_HDR_ID  = '" & Trim(Tran_No_for_Manual) & "' and Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status <> 'C' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")
                Else
                    dr = cc.GetDataReader("select * from tbl_GE_Hdr where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "' and Vehicle_Status = 'IN' and Plant_Code = '" & Trim(txtPlant.Text) & "' and Company_Code  = '" & Trim(txtCompany.Text) & "' and Remarks_IN not like '%Auto%IN%Grouping%'")

                End If
                Try
                    While dr.Read
                        HDDR_ID = dr("TRN_ID")
                        TypeOfVehicle = dr("Type_Of_Vehicle")
                        TVehType11 = TypeOfVehicle
                        Label24.Text = dr("EntryDateTime")
                        txtTransactionNo.Text = dr("GE_HDR_ID")
                        Text7.Text = dr("Gate_No")
                        txtTransporter.Text = dr("Transpoter_code")
                        Text14.Text = dr("TransporterName")
                        Label29.Text = dr("Type_Of_Vehicle")
                        ''txtPlant.Text = dr("Plant_Code")

                        ''"""""""""""""""""""""""""""""""""""""""""""""""""
                        txtSealNo.Text = dr("Seal_No")
                        txtGrossWt.Text = dr("Party_Gross_WT")
                        txtTareWt.Text = dr("Party_Tare_WT")
                        txtNetWt.Text = dr("Party_Net_WT")
                        txtgateInRemarks.Text = dr("Remarks_IN")
                        ''""""""""""""""""""""""""""""""""""""""""""""""""""
                    End While
                Catch ex As Exception

                End Try
                dr.Close()
            Else
                MsgBox("Invalid Vehicle Number !", vbInformation, "Electrosteel Steels Limited.")
                txtVehicleNo.Text = ""
                txtVehicleNo.Enabled = True
                txtVehicleNo.Focus()
                dr.Close()
                Exit Sub
            End If
            '-------------------------------------
            Dim TrnFFlag As Boolean = False
            dr = cc.GetDataReader("select sum(WB_Count_ID), sum(F_WT) , sum(S_WT) from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1)
            Try
                While dr.Read
                    If dr(0) = 0 And dr(1) = 0 And dr(2) = 0 Then
                        TrnFFlag = True
                    End If
                End While
            Catch ex As Exception

            End Try
            dr.Close()
            '-----------------------
            Dim lvi As New ListViewItem
            '----------------------------
            If TrnFFlag Then

                dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1 & " and WB_count_ID = 0")

                While dr.Read
                    'i = ListView1.Items.Count + 1
                    For i As Integer = 0 To ListView1.Items.Count
                        '---------List----------
                        lvi.Text = dr("GE_DET_TRAN_ID")
                        '------------------------
                        'ListView1.Items.Add(dr("GE_DET_TRAN_ID"), i)
                        'ListView1.Items(i).Checked = True
                        lvi.Checked = True
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("PO_NO"))
                            'ListView1.Items(i).SubItems.Add(dr("PO_NO"))
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("SO_NO"))
                            'ListView1.Items(i).SubItems.Add(dr("SO_NO"))
                        End If
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add("")
                            'ListView1.Items(i).SubItems.Add("")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("DO_NO"))
                            'ListView1.Items(i).SubItems.Add(dr("DO_NO"))
                        End If

                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("PO_Line_Item"))
                            'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item"))
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("DO_Line_Item"))
                            'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item"))
                        End If
                        lvi.SubItems.Add(dr("Mat_CODE"))
                        'ListView1.Items(i).SubItems.Add(dr("Mat_CODE"))
                        lvi.SubItems.Add(dr("Mat_Desc"))
                        'ListView1.Items(i).SubItems.Add(dr("Mat_Desc"))
                        lvi.SubItems.Add(dr("DO_Challan_Qty"))
                        lvi.SubItems.Add(dr("UOM"))
                        lvi.SubItems.Add(dr("Unloading_No"))
                        lvi.SubItems.Add(dr("Challan_No"))
                        lvi.SubItems.Add(dr("Challan_Date"))
                        lvi.SubItems.Add(dr("SO_Line_Item"))
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("Vendor_Code"))
                            'ListView1.Items(i).SubItems.Add(dr("Vendor_Code"))
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("Customer_Code"))
                            'ListView1.Items(i).SubItems.Add(dr("Customer_Code"))
                        End If
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("Vendor_Name"))
                            'ListView1.Items(i).SubItems.Add(dr("Vendor_Name"))
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("Customer_Name"))
                            'ListView1.Items(i).SubItems.Add(dr("Customer_Name"))
                        End If
                    Next
                End While
                dr.Close()
                WT_UPDATE = "FWT"
                dtFirst.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("hh-mm-ss")

                If TypeOfVehicle = "INTRDEPT" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Then
                    For lk = 0 To ListView1.Items.Count - 1
                        If Trim(ListView1.Items(lk).SubItems(4).Text) = "" And Trim(ListView1.Items(lk).SubItems(5).Text) = "" Then
                            Dim frmSelectMaterial1 As New frmSelectMaterial
                            frmSelectMaterial1.MdiParent = Me
                            frmSelectMaterial1.Show()
                        End If
                    Next
                    ''FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
                End If

            ElseIf TypeOfVehicle = "INTRDEPT" Then

                dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1 & " and WB_count_ID = 0")

                While dr.Read
                    'i = ListView1.Items.Count + 1
                    For i As Integer = 0 To ListView1.Items.Count

                        'ListView1.Items.Add(dr("GE_DET_TRAN_ID"), i)
                        'ListView1.Items(i).Checked = True
                        lvi.Checked = True
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.Text = dr("PO_NO")
                            'ListView1.Items(i).SubItems.Add(dr("PO_NO"))
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.Text = dr("SO_NO")
                            'ListView1.Items(i).SubItems.Add(dr("SO_NO"))
                        End If
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add("")
                            'ListView1.Items(i).SubItems.Add("")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add("DO_NO")
                            'ListView1.Items(i).SubItems.Add(dr("DO_NO"))
                        End If

                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add("PO_Line_Item")
                            'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item"))
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add("DO_Line_Item")
                            'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item"))
                        End If

                        lvi.SubItems.Add("Mat_CODE")

                        'ListView1.Items(i).SubItems.Add(dr("Mat_CODE"))

                        'ListView1.Items(i).SubItems.Add(dr("Mat_Desc"))
                        lvi.SubItems.Add("Mat_Desc")
                        lvi.SubItems.Add(dr("DO_Challan_Qty") + 0)
                        lvi.SubItems.Add(dr("UOM") & "")
                        lvi.SubItems.Add(dr("Unloading_No") & "")
                        lvi.SubItems.Add(dr("Challan_No") & "")
                        lvi.SubItems.Add(dr("Challan_Date") & "")
                        lvi.SubItems.Add(dr("SO_Line_Item") & "")

                        'ListView1.Items(i).SubItems.Add(dr("DO_Challan_Qty") + 0)
                        'ListView1.Items(i).SubItems.Add(dr("UOM") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Unloading_No") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Challan_No") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Challan_Date") & "")
                        'ListView1.Items(i).SubItems.Add(dr("SO_Line_Item") & "")
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("Vendor_Code") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Vendor_Code") & "")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("Customer_Code") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Customer_Code") & "")
                        End If

                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("Vendor_Name") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Vendor_Name") & "")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("Customer_Name") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Customer_Name") & "")
                        End If

                    Next
                End While


                WT_UPDATE = "FWT"
                dtFirst.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-mm-ss")
                dr = cc.GetDataReader("select F_WT from tbl_GE_DET where GE_HDR_TRAN_ID = " & TRN_ID_1 & " and F_WT > 0 and S_WT = 0")
                If dr.Read Then
                    F_WT_REQ = 1
                    txtFirstWt.Text = dr("F_WT")

                    WT_UPDATE = "SWT"
                End If
                dr.Close()

                'WT_UPDATE = "FWT"
                'dtFirst.Text = Format(Date, "dd-MM-yyyy") & " " & Time


                If TypeOfVehicle = "INTRDEPT" And F_WT_REQ = 0 Then
                    For lk = 0 To ListView1.Items.Count - 1
                        If Trim(ListView1.Items(lk).SubItems(4).Text) = "" And Trim(ListView1.Items(lk).SubItems(5).Text) = "" Then
                            Dim frmSelectMaterial1 As New frmSelectMaterial
                            frmSelectMaterial1.MdiParent = Me
                            frmSelectMaterial1.Show()
                        End If
                    Next

                    dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'")
                    If dr.Read Then
                        Dim strrTWT = "Would you like to take Tare Wt. from master." & Chr(13) & "Tare WT  = " & dr("Vehicle_Tare_Wt") & "  and  Tare WT Date = " & dr("Last_Updated_On")
                        ''ans = MsgBox(strrTWT, vbYesNo, "ElectroWay") ''' blocked on 20 MAY 2015
                    End If
                    dr.Close()

                    Dim ans = MsgBox("Would you like to take Tare Wt. from master ? ", vbYesNo, "ElectroWay")

                    If ans = vbYes Then

                        Dim ans1 = MsgBox("Are you sure you want to take Trae Wt. from master ?", vbYesNo, "ElectroWay")

                        If ans1 = vbYes Then
                            dr = cc.GetDataReader("select * from tbl_Vehicle_Tare_WT_Mst where Vehicle_No = '" & Trim(txtVehicleNo.Text) & "'and TareWtValidUpto >=  '" & Format(Today.Date, "yyyy-mm-dd") & " 00:00:00.000'")
                            If dr.Read = True Then
                                txtFirstWt.Text = dr("Vehicle_Tare_Wt")
                                cm.Connection = con
                                cm.CommandType = CommandType.StoredProcedure
                                cm.CommandText = "sp_upd_tbl_GE_Det_F_WT"
                                cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                                cm.Parameters.AddWithValue("@val_F_WT_Node_IP", Sys_loc_IP)
                                cm.Parameters.AddWithValue("@val_F_WT", Val(Trim(txtFirstWt.Text)))
                                cm.Parameters.AddWithValue("@val_F_WT_DoneBy", User_ID & "")
                                cm.Parameters.AddWithValue("@val_F_WT_Note", "")
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()

                                cm.Connection = con
                                cm.CommandType = CommandType.StoredProcedure
                                cm.CommandText = "sp_upd_tbl_GE_DET_Mat_Code_Nae"
                                cm.Parameters.AddWithValue("@val_GE_DET_Tran_ID", ListView1.Items(i).Text)
                                cm.Parameters.AddWithValue("@val_Mat_Code", Trim(ListView1.Items(i).SubItems(4).Text))
                                cm.Parameters.AddWithValue("@val_Mat_Desc", Trim(ListView1.Items(i).SubItems(5).Text))
                                If con.State = ConnectionState.Closed Then
                                    con.Open()
                                End If
                                cm.ExecuteNonQuery()

                                dtSecond.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("HH-MM-SS")

                                WT_UPDATE = "SWT"

                            Else
                                MsgBox("Tare Wt. not maintaned for this Vehicle in master.", vbInformation, "ElectroWay")


                            End If
                            dr.Close()
                        End If
                    End If
                End If
                dr.Close()
            Else
                '**************************** edited for S WT start

                dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_TRAN_ID =" & TRN_ID_1 & " and F_WT > 0 and WB_count_ID = 0 and S_WT  = 0")
                Try

                    While dr.Read
                        dtFirst.Text = Format(CDate(dr("F_WT_DateTime")), "dd-MM-yyyy HH-mm-ss")
                        txtFirstWt.Text = dr("F_WT")
                        txtFirstWeightNote.Text = dr("F_WT_Note")
                        dtSecond.Text = Format(Today.Date, "dd-MM-yyyy") & " " & TimeOfDay.ToString("hh-mm-ss")

                        'While dr.Read
                        i = ListView1.Items.Count + 1
                        'ListView1.Items.Add(dr("GE_DET_TRAN_ID"), i)
                        'ListView1.Items(i).Checked = True
                        lvi.Text = dr("GE_DET_TRAN_ID")
                        lvi.Checked = True
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("PO_NO"))
                            'ListView1.Items(i).SubItems.Add(dr("PO_NO") & "")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("SO_NO"))
                            'ListView1.Items(i).SubItems.Add(dr("SO_NO") & "")
                        End If
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add("")
                            'ListView1.Items(i).SubItems.Add("")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("DO_NO") & "")
                            'ListView1.Items(i).SubItems.Add(dr("DO_NO") & "")
                        End If

                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("PO_Line_Item") & "")
                            'ListView1.Items(i).SubItems.Add(dr("PO_Line_Item") & "")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("DO_Line_Item") & "")
                            'ListView1.Items(i).SubItems.Add(dr("DO_Line_Item") & "")
                        End If


                        lvi.SubItems.Add(dr("Mat_CODE") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Mat_CODE") & "")
                        lvi.SubItems.Add(dr("Mat_Desc") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Mat_Desc") & "")
                        lvi.SubItems.Add(dr("DO_Challan_Qty") & "")
                        'ListView1.Items(i).SubItems.Add(dr("DO_Challan_Qty") + 0)
                        lvi.SubItems.Add(dr("UOM") & "")
                        'ListView1.Items(i).SubItems.Add(dr("UOM") & "")
                        lvi.SubItems.Add(dr("Unloading_No") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Unloading_No") & "")
                        lvi.SubItems.Add(dr("Challan_No") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Challan_No") & "")
                        lvi.SubItems.Add(dr("Mat_CODE") & "")
                        'ListView1.Items(i).SubItems.Add(dr("Challan_Date") & "")
                        lvi.SubItems.Add(dr("SO_Line_Item") & "")
                        'ListView1.Items(i).SubItems.Add(dr("SO_Line_Item") & "")
                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("Vendor_Code") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Vendor_Code") & "")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("Customer_Code") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Customer_Code") & "")
                        End If

                        If Trim(TypeOfVehicle) = "PURCH" Or Trim(TypeOfVehicle) = "INTRDEPT" Or Trim(TypeOfVehicle) = "CONTITEM" Or Trim(TypeOfVehicle) = "PURCHRET" Or Trim(TypeOfVehicle) = "GATEPASS" Then
                            lvi.SubItems.Add(dr("Vendor_Name") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Vendor_Name") & "")
                        ElseIf Trim(TypeOfVehicle) = "SALES" Or Trim(TypeOfVehicle) = "STKTROUT" Or Trim(TypeOfVehicle) = "SALESRET" Then
                            lvi.SubItems.Add(dr("Customer_Name") & "")
                            'ListView1.Items(i).SubItems.Add(dr("Customer_Name") & "")
                        End If

                        'rec4.MoveNext()
                    End While

                Catch ex As Exception

                End Try
                'End If

                WT_UPDATE = "SWT"

                txtSecondWtNote.Visible = True
                Label33.Visible = True
                Label34.Visible = True
                txtFirstWeightNote.Enabled = False


                dr.Close()


            End If
            ListView1.Items.Add(lvi)

            If Label29.Text = "INTRDEPT" Then
                Label29.Font = New Drawing.Font("Times New Roman", 16, FontStyle.Bold Or FontStyle.Italic)
                Label29.ForeColor = Color.Red
            Else
                ' &H00FF0000&
                Label29.Font = New Drawing.Font("Times New Roman", 10, FontStyle.Bold Or FontStyle.Italic)
                Label29.ForeColor = Color.Blue
                'Label29.FontBold = True
                txtVehicleNo.BackColor = Color.White
            End If

            If txtFirstWeightNote.Enabled = True Then
                txtFirstWeightNote.Focus()
                txtFirstWeightNote.BackColor = Color.Gold
            ElseIf txtSecondWtNote.Enabled = True Then
                txtSecondWtNote.Focus()
                txtSecondWtNote.BackColor = Color.Gold
            End If


            dr = cc.GetDataReader("select * from tbl_GE_Det where GE_HDR_ID ='" & GHDR_I_D & "'")
            If dr.Read Then
                If Trim(dr("Grouping_Ref_Code")) <> "" Then
                    ddlRakeNo.Items.Add(Trim(dr("Grouping_Ref_Code")))
                    ddlRakeNo.Text = Trim(dr("Grouping_Ref_Code"))
                End If
            End If
            dr.Close()


            'End If

        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
        '----------------------
        Try
            Dim str As String = "select GE_HDR_ID,Type_Of_Vehicle,Unloading_No,PO_No,PO_Line_Item,DO_No,DO_Line_Item,SO_No,SO_Line_Item,Mat_Code,Mat_Desc,Challan_No,DO_Challan_Qty from tbl_GE_Det where GE_HDR_ID = '" & txtTransactionNo.Text.Trim & "'"
            ds = cc.GetDataset(str)
            gvsow.DataSource = ds.Tables(0)
        Catch ex As Exception

        End Try
    End Sub
    Public Class myPrinter
        Friend TextToBePrinted As String
        Dim settings As PrinterSettings = New PrinterSettings()
        Public Sub prt(ByVal text As String)
            TextToBePrinted = text
            Dim prn As New Printing.PrintDocument
            Using (prn)
                'prn.PrinterSettings.PrinterName _
                '   = "Kyocera FS-1035MFP KX"
                prn.PrinterSettings.PrinterName = settings.PrinterName
                AddHandler prn.PrintPage, _
                   AddressOf Me.PrintPageHandler
                prn.Print()
                RemoveHandler prn.PrintPage, _
                   AddressOf Me.PrintPageHandler
            End Using
        End Sub
        Private Sub PrintPageHandler(ByVal sender As Object, _
           ByVal args As Printing.PrintPageEventArgs)
            Dim myFont As New Font("Draft 10 cpi", 10)
            args.Graphics.DrawString(TextToBePrinted, _
               New Font(myFont, FontStyle.Regular), _
               Brushes.Black, 50, 50)
        End Sub
    End Class

    Private Sub txtTransactionNo_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTransactionNo.TextChanged

    End Sub

    Private Sub ListView1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ListView1.SelectedIndexChanged

    End Sub

    'Private Sub PrintDocument1_PrintPage(ByVal sender As Object, ByVal e As System.Drawing.Printing.PrintPageEventArgs)
    '    e.Graphics.DrawString("ELECTROSTEEL STEELS LIMITED  (  ES01  -  WORKS  )", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(250, 250))
    '    e.Graphics.DrawString("WEIGHMENT  SLIP  -  ( SALES )", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(350, 270))
    '    e.Graphics.DrawString("-----------------------------------------------------------------------------------------------------------------------------------------------------------", New Font("Arial", 10), Brushes.Black, New Point(50, 280))
    '    e.Graphics.DrawString("Gate Pass No                                                     :", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(50, 320))
    '    e.Graphics.DrawString("VEHICLE  NO                                                     :", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(50, 340))
    '    e.Graphics.DrawString("Vendor/Customer                                               :", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(50, 360))
    '    e.Graphics.DrawString("Material                                                               :", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(50, 380))
    '    e.Graphics.DrawString("Challan/DO                                                         :", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(50, 400))
    '    e.Graphics.DrawString("Challan Qty                                                         :", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(50, 420))
    '    e.Graphics.DrawString("Transporter Name                                              :", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(50, 440))
    '    e.Graphics.DrawString("1st WT. Date & Time                                          :", New Font("Draft 10 cpi", 10), Brushes.Black, New Point(50, 480))
    'End Sub

    Public NotInheritable Class Print2LPT
        Private Sub New()
        End Sub
        <DllImport("kernel32.dll", SetLastError:=True)> _
        Private Shared Function CreateFile(ByVal lpFileName As String, ByVal dwDesiredAccess As FileAccess, ByVal dwShareMode As UInteger, ByVal lpSecurityAttributes As IntPtr, ByVal dwCreationDisposition As FileMode, ByVal dwFlagsAndAttributes As UInteger, _
         ByVal hTemplateFile As IntPtr) As SafeFileHandle
        End Function

        Public Shared Function Print(ByVal SText As String) As Boolean
            Dim nl As String = Convert.ToChar(13).ToString() + Convert.ToChar(10).ToString()
            Dim IsConnected As Boolean = False

            Dim sampleText As String = (Convert.ToString(SText) & nl)
            Try
                Dim buffer As [Byte]() = New Byte(sampleText.Length - 1) {}
                buffer = System.Text.Encoding.ASCII.GetBytes(sampleText)

                Dim fh As SafeFileHandle = CreateFile("LPT1:", FileAccess.Write, 0, IntPtr.Zero, FileMode.OpenOrCreate, 0, _
                 IntPtr.Zero)
                If Not fh.IsInvalid Then
                    IsConnected = True
                    Dim lpt1 As New FileStream(fh, FileAccess.ReadWrite)
                    lpt1.Write(buffer, 0, buffer.Length)
                    lpt1.Close()

                End If
            Catch ex As Exception
                MessageBox.Show(ex.Message)
            End Try

            Return IsConnected
        End Function
    End Class
    ''-----------------------USE THIS LOGIC-------------------------------------------------------------
    'Public Const FILE_ATTRIBUTE_NORMAL As Short = &H80
    'Public Const INVALID_HANDLE_VALUE As Short = -1
    'Public Const GENERIC_READ As UInteger = &H80000000L
    'Public Const GENERIC_WRITE As UInteger = &*********
    'Public Const CREATE_NEW As UInteger = 1
    'Public Const CREATE_ALWAYS As UInteger = 2
    'Public Const OPEN_EXISTING As UInteger = 3

    '<DllImport("kernel32.dll", SetLastError:=True)> _
    'Shared Function CreateFile(ByVal lpFileName As String, ByVal dwDesiredAccess As UInteger, ByVal dwShareMode As UInteger, ByVal lpSecurityAttributes As IntPtr, ByVal dwCreationDisposition As UInteger, ByVal dwFlagsAndAttributes As UInteger, ByVal hTemplateFile As IntPtr) As IntPtr
    'End Function

    'Public Shared Sub sendTextToLPT1(ByVal receiptText As String)
    '    Dim ptr As IntPtr = CreateFile("LPT1", GENERIC_WRITE, 0, IntPtr.Zero, OPEN_EXISTING, 0, IntPtr.Zero)
    '    ' Is bad handle? INVALID_HANDLE_VALUE 
    '    If ptr.ToInt32() = -1 Then
    '        ' ask the framework to marshall the win32 error code to an exception 
    '        Marshal.ThrowExceptionForHR(Marshal.GetHRForLastWin32Error())
    '    Else
    '        Dim lpt As New FileStream(ptr, FileAccess.ReadWrite)
    '        Dim buffer(2047) As Byte
    '        buffer = System.Text.Encoding.Unicode.GetBytes(receiptText)
    '        lpt.Write(buffer, 0, buffer.Length)
    '        lpt.Close()
    '    End If
    'End Sub
    ''--------------------------------------------------------------------------------------

    Private Sub ddlRakeNo_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlRakeNo.SelectedIndexChanged
        txtRakeGrouping.Text = ""

        dr = cc.GetDataReader("select * from tbl_Reference_Mst where Reference_Code = '" & Trim(ddlRakeNo.Text) & "'")
        Try
            While dr.Read
                txtRakeGrouping.Text = dr("Reference_Name")
            End While
        Catch ex As Exception

        End Try
        dr.Close()
    End Sub

    Private Sub txtFirstWeightNote_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtFirstWeightNote.TextChanged

    End Sub

    Private Sub txtGrossWt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtGrossWt.TextChanged

    End Sub

    Private Sub txtTareWt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtTareWt.TextChanged

    End Sub

    Private Sub txtNetWt_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtNetWt.TextChanged

    End Sub

    Private Sub txtSecondWtNote_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSecondWtNote.TextChanged

    End Sub

    Private Sub cbRFID_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cbRFID.CheckedChanged
        If cbRFID.Checked = True Then
            Timer2.Enabled = True
            Timer1.Enabled = False
        Else
            Timer2.Enabled = False
            Timer1.Enabled = True
        End If
    End Sub
End Class