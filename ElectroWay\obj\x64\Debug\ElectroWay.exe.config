<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<connectionStrings>
		<add name="MyDatabase" connectionString="Server=***********;Database=ELECTROWAYESL;User ID=sdadmin;Password=***********;TrustServerCertificate=True;" providerName="System.Data.SqlClient"/>

		<!--<add name="MyDatabase" connectionString="Data Source=************;database=ELECTROWAYESL;User ID=sdadmin;password=***********" providerName="System.Data.SqlClient" />-->
	</connectionStrings>
	<appSettings>
		<add key="DBUser_ID" value="sdadmin"/>
		<add key="DBUser_Pass" value="***********"/>
		<!--Production Server IP-->
		<add key="DB_Server" value="***********"/>

		<!--Test Server IP-->
		<!--<add key="DB_Server" value="************" />-->
		<add key="DB_Name" value="ELECTROWAYESL"/>

		<!--<add key="SAPUser_ID" value="WBUSER" />
		<add key="SAPUser_Pass" value="Eslhana123#" />
		<add key="SAPSys_name" value="ESP" />
		<add key="SAPSys_No" value="00" />
		--><!--Production Server IP--><!--
		--><!--<add key="SAPApp_Server" value="************" />--><!--
		--><!--Test Server IP--><!--
		<add key="SAPApp_Server" value="************" />
		<add key="SAP_Client" value="100" />
		<add key="SAP_Lang" value="EN" />
		<add key="SAP_CodePage" value="8600" />-->

		<add key="SAP_USER" value="WBUSER"/>
		<add key="SAP_PASSWD" value="Eslhana123#"/>
		<add key="SAP_Name" value="ESP"/>
		<add key="SAP_SYSNR" value="00"/>
		<add key="SAP_ASHOST" value="************"/>
		<add key="SAP_CLIENT" value="100"/>
		<add key="SAP_LANG" value="EN"/>


		<add key="smtpHost" value="************"/>
		<add key="smtpPort" value="25"/>
		<add key="smtpUserName" value="<EMAIL>"/>
		<add key="smtpPassword" value=""/>
		<add key="smtpEnableSSL" value="false"/>

	</appSettings>
    <system.diagnostics>
        <sources>
            <!-- This section defines the logging configuration for My.Application.Log -->
            <source name="DefaultSource" switchName="DefaultSwitch">
                <listeners>
                    <add name="FileLog"/>
                    <!-- Uncomment the below section to write to the Application Event Log -->
                    <!--<add name="EventLog"/>-->
                </listeners>
            </source>
        </sources>
        <switches>
            <add name="DefaultSwitch" value="Information"/>
        </switches>
        <sharedListeners>
            <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter"/>
            <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
        </sharedListeners>
    </system.diagnostics>

<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/></startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
