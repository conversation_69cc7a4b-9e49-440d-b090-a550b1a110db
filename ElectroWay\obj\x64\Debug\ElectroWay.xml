﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
ElectroWay
</name>
</assembly>
<members>
<member name="T:ElectroWay.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:ElectroWay.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:ElectroWay.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="M:ElectroWay.GateEntrySlipPDF.GenerateGateEntrySlipPDF(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Collections.Generic.List{System.String[]})">
 <summary>
 Generates a Gate Entry Slip PDF in a professional format.
 </summary>
 <param name="outputPath">The file path to save the PDF.</param>
 <param name="headerData">Dictionary with header info (GatePassNo, GateEntryDate, VehicleNo, VehicleType, TransporterCode, TransporterName).</param>
 <param name="materialData">List of string arrays, each representing a table row.</param>
</member>
<member name="T:ElectroWay.GenericDocumentPrinter">
 <summary>
 Provides generic document printing functionality for tabular data with headers and signature panels
 </summary>
</member>
<member name="T:ElectroWay.GenericDocumentPrinter.HeaderInfo">
 <summary>
 Holds document header information as key-value pairs
 </summary>
</member>
<member name="T:ElectroWay.GenericDocumentPrinter.ColumnDefinition">
 <summary>
 Represents a column in the table
 </summary>
</member>
<member name="T:ElectroWay.GenericDocumentPrinter.SignatureField">
 <summary>
 Represents a signature field in the footer
 </summary>
</member>
<member name="P:ElectroWay.GenericDocumentPrinter.A4Width">
 <summary>
 A4 paper width in hundredths of an inch (portrait)
 </summary>
</member>
<member name="P:ElectroWay.GenericDocumentPrinter.A4Height">
 <summary>
 A4 paper height in hundredths of an inch (portrait)
 </summary>
</member>
<member name="P:ElectroWay.GenericDocumentPrinter.Margin">
 <summary>
 Page margins in pixels
 </summary>
</member>
<member name="P:ElectroWay.GenericDocumentPrinter.RowHeight">
 <summary>
 Row height for table data
 </summary>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.#ctor(System.String)">
 <summary>
 Creates a new instance of the GenericDocumentPrinter
 </summary>
 <param name="documentTitle">The title of the document</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.SetHeaderInfo(System.Collections.Generic.Dictionary{System.String,System.String})">
 <summary>
 Sets the header information for the document
 </summary>
 <param name="headerData">Dictionary of header labels and values</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.AddHeaderField(System.String,System.String)">
 <summary>
 Adds a header field with label and value
 </summary>
 <param name="label">The label for the header field</param>
 <param name="value">The value for the header field</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.SetColumns(System.Collections.Generic.List{ElectroWay.GenericDocumentPrinter.ColumnDefinition})">
 <summary>
 Defines the columns for the table
 </summary>
 <param name="columns">List of column definitions</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.AddColumn(System.String,System.Int32,System.String)">
 <summary>
 Adds a column definition to the table
 </summary>
 <param name="header">Column header text</param>
 <param name="width">Column width in pixels</param>
 <param name="dataField">Field name in data dictionary</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.SetData(System.Collections.Generic.List{System.Collections.Generic.Dictionary{System.String,System.String}})">
 <summary>
 Sets the data rows for the table
 </summary>
 <param name="rowsData">List of dictionaries containing row data</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.AddDataRow(System.Collections.Generic.Dictionary{System.String,System.String})">
 <summary>
 Adds a single data row to the table
 </summary>
 <param name="rowData">Dictionary containing row data</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.AddSignatureGroup(System.Collections.Generic.List{ElectroWay.GenericDocumentPrinter.SignatureField})">
 <summary>
 Adds a signature group (row of signature fields) to the document footer
 </summary>
 <param name="signatureFields">List of signature fields for this group</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.Print(System.Boolean)">
 <summary>
 Prints the document
 </summary>
 <param name="showDialog">Whether to show the print dialog</param>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.PrintPreview">
 <summary>
 Prints a preview of the document
 </summary>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.PrintPage(System.Object,System.Drawing.Printing.PrintPageEventArgs)">
 <summary>
 Print page event handler
 </summary>
</member>
<member name="M:ElectroWay.GenericDocumentPrinter.TruncateText(System.String,System.Drawing.Font,System.Int32,System.Drawing.Graphics)">
 <summary>
 Truncates text to fit within a specified width
 </summary>
</member>
<member name="T:ElectroWay.GateEntrySlipPrinter">
 <summary>
 Example usage class showing how to implement the generic document printer
 </summary>
</member>
<member name="M:ElectroWay.GateEntrySlipPrinter.PrintGateEntrySlip(System.String)">
 <summary>
 Creates and prints a gate entry slip for the specified transaction
 </summary>
</member>
<member name="M:ElectroWay.GateEntrySlipPrinter.FetchGateEntryData(System.String,System.Collections.Generic.Dictionary{System.String,System.String}@,System.Collections.Generic.List{System.Collections.Generic.Dictionary{System.String,System.String}}@)">
 <summary>
 Fetches gate entry data from the database
 </summary>
</member>
<member name="M:ElectroWay.GateEntrySlipPrinter.GetDbValue(System.Data.SqlClient.SqlDataReader,System.String)">
 <summary>
 Helper function to safely get values from database
 </summary>
</member>
<member name="M:ElectroWay.PDFPrinter.PrintPDF(System.String)">
 <param name="pdfFilePath">Path to the PDF file</param>
 <returns>True if print command was successfully sent</returns>
</member>
<member name="M:ElectroWay.PDFPrinter.FindAdobeReaderPath">
 <summary>
 Finds the path to Adobe Reader executable
 </summary>
 <returns>Path to Adobe Reader executable</returns>
</member>
<member name="T:ElectroWay.SAPHelper.ConnectionResult">
 <summary>
 Connection result class to represent connection status
 </summary>
</member>
<member name="T:ElectroWay.SAPHelper.RFCExecutionResult`1">
 <summary>
 Generic RFC Execution Result class
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.GetSAPConfig">
 <summary>
 Reads SAP connection parameters from app.config
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ParameterExists(SAP.Middleware.Connector.IRfcFunction,System.String)">
 <summary>
 Checks if a parameter exists in the function module metadata
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.EstablishSAPConnection">
 <summary>
 Establishes and validates SAP connection with comprehensive error handling
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.SetInputParameters(SAP.Middleware.Connector.IRfcFunction,System.Object)">
 <summary>
 Dynamically sets input parameters for RFC function module
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.MapOutputToType``1(SAP.Middleware.Connector.IRfcFunction,System.Type)">
 <summary>
 Maps RFC output to a strongly-typed object
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.MapOutputWithEnhancedTyping``1(SAP.Middleware.Connector.IRfcFunction)">
 <summary>
 Maps RFC output to a dynamic object if no specific type is provided
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ExecuteRFC``1(System.String,System.Object,System.Type)">
 <summary>
 Advanced RFC execution with comprehensive support for all RFC parameter types and directions
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ProcessSAPTabularData(System.Collections.Generic.Dictionary{System.String,System.Object})">
 <summary>
 Processes SAP tabular data with FIELDS and DATA structure
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.SerializeToJson(System.Collections.Generic.List{System.Collections.Generic.Dictionary{System.String,System.String}})">
 <summary>
 Serializes data to JSON
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ManualJsonSerialize(System.Collections.Generic.List{System.Collections.Generic.Dictionary{System.String,System.String}})">
 <summary>
 Simple manual JSON serializer
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ConvertToDataTable(System.Collections.Generic.List{System.Collections.Generic.Dictionary{System.String,System.String}})">
 <summary>
 Converts processed SAP data to DataTable
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.CheckForSAPErrors``1(SAP.Middleware.Connector.IRfcFunction,ElectroWay.SAPHelper.RFCExecutionResult{``0}@)">
 <summary>
 Checks for SAP-specific error indicators in function module
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ConvertSAPDate(System.Object)">
 <summary>
 Converts SAP-specific date format to .NET DateTime
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ConvertSAPTime(System.Object)">
 <summary>
 Converts SAP-specific time format to TimeSpan
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ConvertSAPValue(System.Object,SAP.Middleware.Connector.RfcDataType)">
 <summary>
 Converts SAP values to appropriate .NET types
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.ConvertTableWithTyping(SAP.Middleware.Connector.IRfcTable)">
 <summary>
 Converts SAP table with type-specific conversions
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.GetValue``1(ElectroWay.SAPHelper.RFCExecutionResult{System.Collections.Generic.Dictionary{System.String,System.Object}},System.String)">
 <summary>
 Safely retrieves a value from the RFC result dictionary
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.GetTableData(ElectroWay.SAPHelper.RFCExecutionResult{System.Collections.Generic.Dictionary{System.String,System.Object}},System.String)">
 <summary>
 Retrieves a list of dictionaries from a table-type result
 </summary>
</member>
<member name="M:ElectroWay.SAPHelper.PrintResultDetails(ElectroWay.SAPHelper.RFCExecutionResult{System.Collections.Generic.Dictionary{System.String,System.Object}})">
 <summary>
 Prints detailed information about the RFC result for debugging
 </summary>
</member>
<member name="M:ElectroWay.frmVehicleWt.SerialPort1_DataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)">
 <summary> 
 async read on secondary thread 
 </summary> 
</member>
<member name="M:ElectroWay.frmVehicleWt.DoUpdate(System.Object,System.EventArgs)">
 <summary> 
 update received string in UI 
 </summary> 
 <remarks></remarks> 
</member>
<member name="M:ElectroWay.frmWM.SerialPort1_DataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)">
 <summary> 
 async read on secondary thread 
 </summary> 
</member>
<member name="M:ElectroWay.frmWM.DoUpdate(System.Object,System.EventArgs)">
 <summary> 
 update received string in UI 
 </summary> 
 <remarks></remarks> 
</member>
</members>
</doc>
