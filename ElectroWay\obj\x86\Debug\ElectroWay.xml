﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
ElectroWay
</name>
</assembly>
<members>
<member name="T:ElectroWay.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:ElectroWay.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:ElectroWay.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="M:ElectroWay.frmVehicleWt.SerialPort1_DataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)">
 <summary> 
 async read on secondary thread 
 </summary> 
</member>
<member name="M:ElectroWay.frmVehicleWt.DoUpdate(System.Object,System.EventArgs)">
 <summary> 
 update received string in UI 
 </summary> 
 <remarks></remarks> 
</member>
<member name="M:ElectroWay.frmWM.SerialPort1_DataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)">
 <summary> 
 async read on secondary thread 
 </summary> 
</member>
<member name="M:ElectroWay.frmWM.DoUpdate(System.Object,System.EventArgs)">
 <summary> 
 update received string in UI 
 </summary> 
 <remarks></remarks> 
</member>
</members>
</doc>
