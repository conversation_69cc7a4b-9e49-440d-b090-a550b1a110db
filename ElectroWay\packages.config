﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle.Cryptography" version="2.2.1" targetFramework="net472" />
  <package id="DocumentFormat.OpenXml" version="3.1.0" targetFramework="net472" />
  <package id="DocumentFormat.OpenXml.Framework" version="3.1.0" targetFramework="net472" />
  <package id="itext" version="8.0.5" targetFramework="net472" />
  <package id="itext.bouncy-castle-adapter" version="8.0.5" targetFramework="net472" />
  <package id="itext.commons" version="8.0.5" targetFramework="net472" />
  <package id="itext7" version="8.0.5" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="5.0.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="5.0.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="5.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
</packages>