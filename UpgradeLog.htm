﻿<!DOCTYPE html>
<!-- saved from url=(0014)about:internet -->
 <html xmlns:msxsl="urn:schemas-microsoft-com:xslt"><head><meta content="en-us" http-equiv="Content-Language" /><meta content="text/html; charset=utf-16" http-equiv="Content-Type" /><title _locID="ConversionReport0">
          Migration Report
        </title><style> 
                    /* Body style, for the entire document */
                    body
                    {
                        background: #F3F3F4;
                        color: #1E1E1F;
                        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                        padding: 0;
                        margin: 0;
                    }

                    /* Header1 style, used for the main title */
                    h1
                    {
                        padding: 10px 0px 10px 10px;
                        font-size: 21pt;
                        background-color: #E2E2E2;
                        border-bottom: 1px #C1C1C2 solid; 
                        color: #201F20;
                        margin: 0;
                        font-weight: normal;
                    }

                    /* Header2 style, used for "Overview" and other sections */
                    h2
                    {
                        font-size: 18pt;
                        font-weight: normal;
                        padding: 15px 0 5px 0;
                        margin: 0;
                    }

                    /* Header3 style, used for sub-sections, such as project name */
                    h3
                    {
                        font-weight: normal;
                        font-size: 15pt;
                        margin: 0;
                        padding: 15px 0 5px 0;
                        background-color: transparent;
                    }

                    /* Color all hyperlinks one color */
                    a
                    {
                        color: #1382CE;
                    }

                    /* Table styles */ 
                    table
                    {
                        border-spacing: 0 0;
                        border-collapse: collapse;
                        font-size: 10pt;
                    }

                    table th
                    {
                        background: #E7E7E8;
                        text-align: left;
                        text-decoration: none;
                        font-weight: normal;
                        padding: 3px 6px 3px 6px;
                    }

                    table td
                    {
                        vertical-align: top;
                        padding: 3px 6px 5px 5px;
                        margin: 0px;
                        border: 1px solid #E7E7E8;
                        background: #F7F7F8;
                    }

                    /* Local link is a style for hyperlinks that link to file:/// content, there are lots so color them as 'normal' text until the user mouse overs */
                    .localLink
                    {
                        color: #1E1E1F;
                        background: #EEEEED;
                        text-decoration: none;
                    }

                    .localLink:hover
                    {
                        color: #1382CE;
                        background: #FFFF99;
                        text-decoration: none;
                    }

                    /* Center text, used in the over views cells that contain message level counts */ 
                    .textCentered
                    {
                        text-align: center;
                    }

                    /* The message cells in message tables should take up all avaliable space */
                    .messageCell
                    {
                        width: 100%;
                    }

                    /* Padding around the content after the h1 */ 
                    #content 
                    {
	                    padding: 0px 12px 12px 12px; 
                    }

                    /* The overview table expands to width, with a max width of 97% */ 
                    #overview table
                    {
                        width: auto;
                        max-width: 75%; 
                    }

                    /* The messages tables are always 97% width */
                    #messages table
                    {
                        width: 97%;
                    }

                    /* All Icons */
                    .IconSuccessEncoded, .IconInfoEncoded, .IconWarningEncoded, .IconErrorEncoded
                    {
                        min-width:18px;
                        min-height:18px; 
                        background-repeat:no-repeat;
                        background-position:center;
                    }

                    /* Success icon encoded */
                    .IconSuccessEncoded
                    {
                        /* Note: Do not delete the comment below. It is used to verify the correctness of the encoded image resource below before the product is released */
                        /* [---XsltValidateInternal-Base64EncodedImage:IconSuccess#Begin#background-image: url(data:image/png;base64,#Separator#);#End#] */
                        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABcElEQVR4Xq2TsUsCURzHv15g8ZJcBWlyiYYgCIWcb9DFRRwMW5TA2c0/QEFwFkxxUQdxVlBwCYWOi6IhWgQhBLHJUCkhLr/BW8S7gvrAg+N+v8/v+x68Z8MGy+XSCyABQAXgBgHGALoASkIIDWSLeLBetdHryMjd5IxQPWT4rn1c/P7+xxp72Cs9m5SZ0Bq2vPnbPFafK2zDvmNHypdC0BPkLlQhxJsCAhQoZwdZU5mwxh720qGo8MzTxTTKZDPCx2HoVzp6lz0Q9tKhyx0kGs8Ny+TkWRKk8lCROwEduhyg9l/6lunOPSfmH3NUH6uQ0KHLAe7JYvJjevm+DAMGJHToKtigE+vwvIidxLamb8IBY9e+C5LiXREkfho3TSd06HJA13/oh6T51MTsfQbHrsMynQ5dDihFjiK8JJAU9AKIWTp76dCVN7HWHrajmUEGvyF9nkbAE6gLIS7kTUyuf2gscLoJrElZo/Mvj+nPz/kLTmfnEwP3tB0AAAAASUVORK5CYII=);
                    }

                    /* Information icon encoded */
                    .IconInfoEncoded
                    {
                        /* Note: Do not delete the comment below. It is used to verify the correctness of the encoded image resource below before the product is released */
                        /* [---XsltValidateInternal-Base64EncodedImage:IconInformation#Begin#background-image: url(data:image/png;base64,#Separator#);#End#] */
                        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABHElEQVR4Xs2TsUoDQRRF7wwoziokjZUKadInhdhukR9YP8DMX1hYW+QvdsXa/QHBbcXC7W0CamWTQnclFutceIQJwwaWNLlwm5k5d94M76mmaeCrrmsLYOocY12FcxZFUeozCqKqqgYA8uevv1H6VuPxcwlfk5N92KHBxfFeCSAxxswlYAW/Xr989x/mv9gkhtyMDhcAxgzRsp7flj8B/HF1RsMXq+NZMkopaHe7lbKxQUEIGbKsYNoGn969060hZBkQex/W8oRQwsQaW2o3Ago2SVcJUzAgY3N0lTCZZm+zPS8HB51gMmS1DEYyOz9acKO1D8JWTlafKIMxdhvlfdyT94Vv5h7P8Ky7nQzACmhvKq3zk3PjW9asz9D/1oigecsioooAAAAASUVORK5CYII=);
                    }

                    /* Warning icon encoded */
                    .IconWarningEncoded
                    {
                        /* Note: Do not delete the comment below. It is used to verify the correctness of the encoded image resource below before the product is released */
                        /* [---XsltValidateInternal-Base64EncodedImage:IconWarning#Begin#background-image: url(data:image/png;base64,#Separator#);#End#] */
                        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAx0lEQVR4XpWSMQ7CMAxFf4xAyBMLCxMrO8dhaBcuwdCJS3RJBw7SA/QGTCxdWJgiQYWKXJWKIXHIlyw5lqr34tQgEOdcBsCOx5yZK3hCCKdYXneQkh4pEfqzLfu+wVDSyyzFoJjfz9NB+pAF+eizx2Vruts0k15mPgvS6GYvpVtQhB61IB/dk6AF6fS4Ben0uIX5odtFe8Q/eW1KvFeH4e8khT6+gm5B+t3juyDt7n0jpe+CANTd+oTUjN/U3yVaABnSUjFz/gFq44JaVSCXeQAAAABJRU5ErkJggg==);
                    }

                    /* Error icon encoded */
                    .IconErrorEncoded
                    {
                        /* Note: Do not delete the comment below. It is used to verify the correctness of the encoded image resource below before the product is released */
                        /* [---XsltValidateInternal-Base64EncodedImage:IconError#Begin#background-image: url(data:image/png;base64,#Separator#);#End#] */
                        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABQElEQVR4XqWTvUoEQRCE6wYPZUA80AfwAQz23uCMjA7MDRQEIzPBVEyNTQUFIw00vcQTTMzuAh/AxEQQT8HF/3G/oGGnEUGuoNnd6qoZuqltyKEsyzVJq5I6rnUp6SjGeGhESikzzlc1eL7opfuVbrqbU1Zw9NCgtQMaZpY0eNnaaL2fHusvTK5vKu7sjSS1Y4y3QUA6K3e3Mau5UFDyMP7tYF9o8cAHZv68vipoIJg971PZIZ5HiwdvYGGvFVFHmGmZ2MxwmQYPXubPl9Up0tfoMQGetXd6mRbvhBw+boZ6WF7Mbv1+GsHRk0fQmPAH1GfmZirbCfDJ61tw3Px8/8pZsPAG4jlVhcPgZ7adwNWBB68lkRQWFiTgFlbnLY3DGGM7izIJIyT/jjIvEJw6fdJTc6krDzh6aMwMP9bvDH4ADSsa9uSWVJkAAAAASUVORK5CYII=);
                    }
                 </style><script type="text/javascript" language="javascript"> 
          
            // Startup 
            // Hook up the the loaded event for the document/window, to linkify the document content
            var startupFunction = function() { linkifyElement("messages"); };
            
            if(window.attachEvent)
            {
              window.attachEvent('onload', startupFunction);
            }
            else if (window.addEventListener) 
            {
              window.addEventListener('load', startupFunction, false);
            }
            else 
            {
              document.addEventListener('load', startupFunction, false);
            } 
            
            // Toggles the visibility of table rows with the specified name 
            function toggleTableRowsByName(name)
            {
               var allRows = document.getElementsByTagName('tr');
               for (i=0; i < allRows.length; i++)
               {
                  var currentName = allRows[i].getAttribute('name');
                  if(!!currentName && currentName.indexOf(name) == 0)
                  {
                      var isVisible = allRows[i].style.display == ''; 
                      isVisible ? allRows[i].style.display = 'none' : allRows[i].style.display = '';
                  }
               }
            }
            
            function scrollToFirstVisibleRow(name) 
            {
               var allRows = document.getElementsByTagName('tr');
               for (i=0; i < allRows.length; i++)
               {
                  var currentName = allRows[i].getAttribute('name');
                  var isVisible = allRows[i].style.display == ''; 
                  if(!!currentName && currentName.indexOf(name) == 0 && isVisible)
                  {
                     allRows[i].scrollIntoView(true); 
                     return true; 
                  }
               }
               
               return false;
            }
            
            // Linkifies the specified text content, replaces candidate links with html links 
            function linkify(text)
            {
                 if(!text || 0 === text.length)
                 {
                     return text; 
                 }

                 // Find http, https and ftp links and replace them with hyper links 
                 var urlLink = /(http|https|ftp)\:\/\/[a-zA-Z0-9\-\.]+(:[a-zA-Z0-9]*)?\/?([a-zA-Z0-9\-\._\?\,\/\\\+&%\$#\=~;\{\}])*/gi;
                 
                 return text.replace(urlLink, '<a href="$&">$&</a>') ;
            }
            
            // Linkifies the specified element by ID
            function linkifyElement(id)
            {
                var element = document.getElementById(id);
                if(!!element)
                {
                  element.innerHTML = linkify(element.innerHTML); 
                }
            }
            
            function ToggleMessageVisibility(projectName)
            {
              if(!projectName || 0 === projectName.length)
              {
                return; 
              }
              
              toggleTableRowsByName("MessageRowClass" + projectName);
              toggleTableRowsByName('MessageRowHeaderShow' + projectName);
              toggleTableRowsByName('MessageRowHeaderHide' + projectName); 
            }
            
            function ScrollToFirstVisibleMessage(projectName)
            {
              if(!projectName || 0 === projectName.length)
              {
                return; 
              }
              
              // First try the 'Show messages' row
              if(!scrollToFirstVisibleRow('MessageRowHeaderShow' + projectName))
              {
                // Failed to find a visible row for 'Show messages', try an actual message row 
                scrollToFirstVisibleRow('MessageRowClass' + projectName); 
              }
            }
           </script></head><body><h1 _locID="ConversionReport">
          Migration Report - ElectroWay</h1><div id="content"><h2 _locID="OverviewTitle">Overview</h2><div id="overview"><table><tr><th></th><th _locID="ProjectTableHeader">Project</th><th _locID="PathTableHeader">Path</th><th _locID="ErrorsTableHeader">Errors</th><th _locID="WarningsTableHeader">Warnings</th><th _locID="MessagesTableHeader">Messages</th></tr><tr><td class="IconWarningEncoded" /><td><strong><a href="#Solution"><span _locID="OverviewSolutionSpan">Solution</span></a></strong></td><td>ElectroWay.sln</td><td class="textCentered"><a>0</a></td><td class="textCentered"><a href="#SolutionWarning">1</a></td><td class="textCentered"><a href="#" onclick="ScrollToFirstVisibleMessage('Solution'); return false;">2</a></td></tr><tr><td class="IconSuccessEncoded" /><td><strong><a href="#ElectroWay">ElectroWay</a></strong></td><td>ElectroWay\ElectroWay.vbproj</td><td class="textCentered"><a>0</a></td><td class="textCentered"><a>0</a></td><td class="textCentered"><a href="#" onclick="ScrollToFirstVisibleMessage('ElectroWay'); return false;">206</a></td></tr></table></div><h2 _locID="SolutionAndProjectsTitle">Solution and projects</h2><div id="messages"><a name="Solution" /><h3 _locID="ProjectDisplayNameHeader">Solution</h3><table><tr id="SolutionHeaderRow"><th></th><th class="messageCell" _locID="MessageTableHeader">Message</th></tr><tr name="WarningRowClassSolution"><td class="IconWarningEncoded"><a name="SolutionWarning" /></td><td class="messageCell"><strong>ElectroWay.sln:
        </strong><span>Visual Studio needs to make non-functional changes to this project in order to enable the project to open in released versions of Visual Studio newer than Visual Studio 2010 SP1 without impacting project behavior.</span></td></tr><tr name="MessageRowHeaderShowSolution"><td class="IconInfoEncoded" /><td class="messageCell"><a _locID="ShowAdditionalMessages" href="#" name="SolutionMessage" onclick="ToggleMessageVisibility('Solution'); return false;">
          Show 2 additional messages
        </a></td></tr><tr name="MessageRowClassSolution" style="display: none"><td class="IconInfoEncoded"><a name="SolutionMessage" /></td><td class="messageCell"><strong>ElectroWay.sln:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay.sln</span></td></tr><tr name="MessageRowClassSolution" style="display: none"><td class="IconInfoEncoded"><a name="SolutionMessage" /></td><td class="messageCell"><strong>ElectroWay.sln:
        </strong><span>Solution migrated successfully</span></td></tr><tr style="display: none" name="MessageRowHeaderHideSolution"><td class="IconInfoEncoded" /><td class="messageCell"><a _locID="HideAdditionalMessages" href="#" name="SolutionMessage" onclick="ToggleMessageVisibility('Solution'); return false;">
          Hide 2 additional messages
        </a></td></tr></table><a name="ElectroWay" /><h3>ElectroWay</h3><table><tr id="ElectroWayHeaderRow"><th></th><th class="messageCell" _locID="MessageTableHeader">Message</th></tr><tr name="MessageRowHeaderShowElectroWay"><td class="IconInfoEncoded" /><td class="messageCell"><a _locID="ShowAdditionalMessages" href="#" name="ElectroWayMessage" onclick="ToggleMessageVisibility('ElectroWay'); return false;">
          Show 206 additional messages
        </a></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\ElectroWay.vbproj:
        </strong><span>Project file successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\ElectroWay.vbproj</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\ElectroWay.vbproj.user:
        </strong><span>Project user file successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\ElectroWay.vbproj.user</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Class1.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Class1.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Form1.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Form1.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Form1.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Form1.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCancelVehicle.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCancelVehicle.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCancelVehicle.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCancelVehicle.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmChangeGroupRef.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmChangeGroupRef.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmChangeGroupRef.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmChangeGroupRef.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmClearSecondWeighment.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmClearSecondWeighment.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmClearSecondWeighment.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmClearSecondWeighment.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCompanymaster.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCompanymaster.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCompanymaster.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCompanymaster.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\FrmContMaterialReturnDetails.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\FrmContMaterialReturnDetails.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\FrmContMaterialReturnDetails.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\FrmContMaterialReturnDetails.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMatApproval.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMatApproval.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMatApproval.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMatApproval.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterial.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterial.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterial.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterial.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterialDetails_47KOutPass.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterialDetails_47KOutPass.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterialDetails_47KOutPass.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterialDetails_47KOutPass.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterialDetails_OutPass.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterialDetails_OutPass.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterialDetails_OutPass.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterialDetails_OutPass.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCustomer.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCustomer.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCustomer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCustomer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDetailsReportNew.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDetailsReportNew.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDetailsReportNew.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDetailsReportNew.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDocumentDetails.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDocumentDetails.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDocumentDetails.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDocumentDetails.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\ECL_RptGateEntrySlip1.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\ECL_RptGateEntrySlip1.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmCheckPostReport.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmCheckPostReport.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmCheckPostReport.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmCheckPostReport.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmDetailsReport.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmDetailsReport.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmDetailsReport.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmDetailsReport.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDriver.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDriver.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDriver.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDriver.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGateEntry.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGateEntry.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGateEntry.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGateEntry.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGetMasterFromSAP.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGetMasterFromSAP.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGetMasterFromSAP.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGetMasterFromSAP.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGouping.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGouping.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGouping.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGouping.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGrosstareNetWtUpdation.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGrosstareNetWtUpdation.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGrosstareNetWtUpdation.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGrosstareNetWtUpdation.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGroupingCancel.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGroupingCancel.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGroupingCancel.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGroupingCancel.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmHelp.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmHelp.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmHelp.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmHelp.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmLocationMaster.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmLocationMaster.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmLocationMaster.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmLocationMaster.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmLogin.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmLogin.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmLogin.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmLogin.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmManualWeighmentAuthorisation.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmManualWeighmentAuthorisation.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmManualWeighmentAuthorisation.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmManualWeighmentAuthorisation.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmMaterial.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmMaterial.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmMaterial.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmMaterial.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmNode.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmNode.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmNode.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmNode.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPassword.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPassword.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPassword.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPassword.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPathSettings.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPathSettings.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPathSettings.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPathSettings.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPlantmaster.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPlantmaster.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPlantmaster.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPlantmaster.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmReferenceMaster.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmReferenceMaster.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmReferenceMaster.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmReferenceMaster.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRequiredDate.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRequiredDate.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRequiredDate.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRequiredDate.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRe_Grouping.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRe_Grouping.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRe_Grouping.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRe_Grouping.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRouteMaster.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRouteMaster.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRouteMaster.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRouteMaster.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmSelectMaterial.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmSelectMaterial.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmSelectMaterial.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmSelectMaterial.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmServerConfig.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmServerConfig.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmServerConfig.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmServerConfig.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmSplitting.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmSplitting.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmSplitting.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmSplitting.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTareWthelp.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTareWthelp.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTareWthelp.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTareWthelp.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransaction.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransaction.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransaction.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransaction.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransactionMaster.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransactionMaster.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransactionMaster.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransactionMaster.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransferToPlant.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransferToPlant.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransferToPlant.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransferToPlant.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransporter1.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransporter1.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransporter1.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransporter1.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUnloadingDOUpdation.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUnloadingDOUpdation.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUnloadingDOUpdation.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUnloadingDOUpdation.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdate.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdate.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdate.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdate.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdateChallanTransporter.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdateChallanTransporter.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdateChallanTransporter.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdateChallanTransporter.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdateLineItems.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdateLineItems.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdateLineItems.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdateLineItems.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUser.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUser.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUser.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUser.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicle.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicle.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicle.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicle.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleStatus.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleStatus.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleStatus.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleStatus.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleTransporter.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleTransporter.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleTransporter.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleTransporter.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleWt.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleWt.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleWt.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleWt.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVendor.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVendor.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVendor.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVendor.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmWM.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmWM.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmWM.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmWM.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\MDIForm1.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\MDIForm1.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\MDIForm1.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\MDIForm1.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Module1.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Module1.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\My Project\AssemblyInfo.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\My Project\AssemblyInfo.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\My Project\Application.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\My Project\Application.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\My Project\Resources.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\My Project\Resources.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\My Project\Settings.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\My Project\Settings.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmReport.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmReport.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmReport.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmReport.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmSlipPrint.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmSlipPrint.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmSlipPrint.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmSlipPrint.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmSlipPrintDirect.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmSlipPrintDirect.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmSlipPrintDirect.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmSlipPrintDirect.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\GateEntrySlip.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\GateEntrySlip.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\GateEntrySlip.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\GateEntrySlip.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\GateEntry_Slip.Designer.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\GateEntry_Slip.Designer.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\GateEntry_Slip.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\GateEntry_Slip.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptGateEntrySlip.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptGateEntrySlip.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptGateEntrySlip1.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptGateEntrySlip1.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptGateEntrySlip2.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptGateEntrySlip2.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptGateEntrySlip3.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptGateEntrySlip3.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptWeighmentSlip.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptWeighmentSlip.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\VIEW_Cont_Mat_Ret_ID.vb:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\VIEW_Cont_Mat_Ret_ID.vb</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\My Project\Application.myapp:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\My Project\Application.myapp</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\My Project\Settings.settings:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\My Project\Settings.settings</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\ESLLOGO.ico:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\ESLLOGO.ico</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Icon1.ico:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Icon1.ico</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Form1.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Form1.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCancelVehicle.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCancelVehicle.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmChangeGroupRef.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmChangeGroupRef.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmClearSecondWeighment.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmClearSecondWeighment.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCompanymaster.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCompanymaster.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\FrmContMaterialReturnDetails.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\FrmContMaterialReturnDetails.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMatApproval.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMatApproval.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterial.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterial.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterialDetails_47KOutPass.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterialDetails_47KOutPass.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmContractorMaterialDetails_OutPass.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmContractorMaterialDetails_OutPass.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmCustomer.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmCustomer.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDetailsReportNew.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDetailsReportNew.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDocumentDetails.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDocumentDetails.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\ECL_RptGateEntrySlip1.rpt:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\ECL_RptGateEntrySlip1.rpt</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmCheckPostReport.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmCheckPostReport.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmDetailsReport.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmDetailsReport.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmDriver.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmDriver.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGateEntry.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGateEntry.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGetMasterFromSAP.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGetMasterFromSAP.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGouping.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGouping.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGrosstareNetWtUpdation.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGrosstareNetWtUpdation.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmGroupingCancel.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmGroupingCancel.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmHelp.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmHelp.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmLocationMaster.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmLocationMaster.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmLogin.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmLogin.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmManualWeighmentAuthorisation.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmManualWeighmentAuthorisation.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmMaterial.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmMaterial.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmNode.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmNode.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPassword.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPassword.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPathSettings.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPathSettings.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmPlantmaster.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmPlantmaster.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmReferenceMaster.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmReferenceMaster.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRequiredDate.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRequiredDate.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRe_Grouping.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRe_Grouping.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmRouteMaster.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmRouteMaster.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmSelectMaterial.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmSelectMaterial.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmServerConfig.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmServerConfig.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmSplitting.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmSplitting.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTareWthelp.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTareWthelp.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransaction.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransaction.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransactionMaster.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransactionMaster.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransferToPlant.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransferToPlant.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmTransporter1.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmTransporter1.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUnloadingDOUpdation.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUnloadingDOUpdation.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdate.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdate.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdateChallanTransporter.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdateChallanTransporter.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUpdateLineItems.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUpdateLineItems.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmUser.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmUser.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicle.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicle.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleStatus.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleStatus.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleTransporter.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleTransporter.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVehicleWt.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVehicleWt.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmVendor.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmVendor.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\frmWM.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\frmWM.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\MDIForm1.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\MDIForm1.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\My Project\Resources.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\My Project\Resources.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmReport.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmReport.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmSlipPrint.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmSlipPrint.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\frmSlipPrintDirect.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\frmSlipPrintDirect.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\GateEntrySlip.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\GateEntrySlip.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\GateEntry_Slip.resx:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\GateEntry_Slip.resx</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptGateEntrySlip.rpt:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptGateEntrySlip.rpt</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptGateEntrySlip1.rpt:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptGateEntrySlip1.rpt</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptGateEntrySlip2.rpt:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptGateEntrySlip2.rpt</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptGateEntrySlip3.rpt:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptGateEntrySlip3.rpt</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\RptWeighmentSlip.rpt:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\RptWeighmentSlip.rpt</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\Reports\VIEW_Cont_Mat_Ret_ID.rpt:
        </strong><span>File successfully backed up as C:\Users\<USER>\source\repos\electroway_hana - rise\Backup\ElectroWay\Reports\VIEW_Cont_Mat_Ret_ID.rpt</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\ElectroWay.vbproj:
        </strong><span>Project migrated successfully</span></td></tr><tr name="MessageRowClassElectroWay" style="display: none"><td class="IconInfoEncoded"><a name="ElectroWayMessage" /></td><td class="messageCell"><strong>ElectroWay\ElectroWay.vbproj:
        </strong><span>Scan complete: Migration not required for project files.</span></td></tr><tr style="display: none" name="MessageRowHeaderHideElectroWay"><td class="IconInfoEncoded" /><td class="messageCell"><a _locID="HideAdditionalMessages" href="#" name="ElectroWayMessage" onclick="ToggleMessageVisibility('ElectroWay'); return false;">
          Hide 206 additional messages
        </a></td></tr></table></div></div></body></html>