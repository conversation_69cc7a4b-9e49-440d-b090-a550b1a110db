﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Extensions.DependencyInjection.Abstractions</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Extensions.DependencyInjection.ActivatorUtilities">
      <summary>Helper code for the various activator services.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateFactory(System.Type,System.Type[])">
      <summary>Create a delegate that will instantiate a type with constructor arguments provided directly
            and/or from an <see cref="T:System.IServiceProvider" />.</summary>
      <param name="instanceType">The type to activate</param>
      <param name="argumentTypes">
            The types of objects, in order, that will be passed to the returned function as its second parameter</param>
      <returns>
            A factory that will instantiate instanceType using an <see cref="T:System.IServiceProvider" />
            and an argument array containing objects matching the types defined in argumentTypes
       .</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance(System.IServiceProvider,System.Type,System.Object[])">
      <summary>Instantiate a type with constructor arguments provided directly and/or from an <see cref="T:System.IServiceProvider" />.</summary>
      <param name="provider">The service provider used to resolve dependencies</param>
      <param name="instanceType">The type to activate</param>
      <param name="parameters">Constructor arguments not provided by the <paramref name="provider" />.</param>
      <returns>An activated object of type instanceType</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance``1(System.IServiceProvider,System.Object[])">
      <summary>Instantiate a type with constructor arguments provided directly and/or from an <see cref="T:System.IServiceProvider" />.</summary>
      <param name="provider">The service provider used to resolve dependencies</param>
      <param name="parameters">Constructor arguments not provided by the <paramref name="provider" />.</param>
      <typeparam name="T">The type to activate</typeparam>
      <returns>An activated object of type T</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetServiceOrCreateInstance(System.IServiceProvider,System.Type)">
      <summary>Retrieve an instance of the given type from the service provider. If one is not found then instantiate it directly.</summary>
      <param name="provider">The service provider</param>
      <param name="type">The type of the service</param>
      <returns>The resolved service or created instance</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.GetServiceOrCreateInstance``1(System.IServiceProvider)">
      <summary>Retrieve an instance of the given type from the service provider. If one is not found then instantiate it directly.</summary>
      <param name="provider">The service provider used to resolve dependencies</param>
      <typeparam name="T">The type of the service</typeparam>
      <returns>The resolved service or created instance</returns>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.ActivatorUtilitiesConstructorAttribute">
      <summary>Marks the constructor to be used when activating type using <see cref="T:Microsoft.Extensions.DependencyInjection.ActivatorUtilities" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilitiesConstructorAttribute.#ctor" />
    <member name="T:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions">
      <summary>Extension methods for adding and removing services to an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.Add(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.DependencyInjection.ServiceDescriptor)">
      <summary>Adds the specified <paramref name="descriptor" /> to the <paramref name="collection" />.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="descriptor">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> to add.</param>
      <returns>A reference to the current instance of <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.Add(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyInjection.ServiceDescriptor})">
      <summary>Adds a sequence of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> to the <paramref name="collection" />.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="descriptors">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />s to add.</param>
      <returns>A reference to the current instance of <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.RemoveAll(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
      <summary>Removes all services of type <paramref name="serviceType" /> in <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="serviceType">The service type to remove.</param>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> for chaining.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.RemoveAll``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Removes all services of type <typeparamref name="T" /> in <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <typeparam name="T" />
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> for chaining.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.Replace(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.DependencyInjection.ServiceDescriptor)">
      <summary>Removes the first service in <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> with the same service type
            as <paramref name="descriptor" /> and adds <paramref name="descriptor" /> to the collection.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="descriptor">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> to replace with.</param>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> for chaining.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAdd(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.DependencyInjection.ServiceDescriptor)">
      <summary>Adds the specified <paramref name="descriptor" /> to the <paramref name="collection" /> if the
            service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="descriptor">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> to add.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAdd(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyInjection.ServiceDescriptor})">
      <summary>Adds the specified <paramref name="descriptors" /> to the <paramref name="collection" /> if the
            service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="descriptors">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />s to add.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddEnumerable(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.DependencyInjection.ServiceDescriptor)">
      <summary>Adds a <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> if an existing descriptor with the same
            <see cref="P:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.ServiceType" /> and an implementation that does not already exist in <paramref name="services" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="descriptor">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddEnumerable(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Collections.Generic.IEnumerable{Microsoft.Extensions.DependencyInjection.ServiceDescriptor})">
      <summary>Adds the specified <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />s if an existing descriptor with the same
            <see cref="P:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.ServiceType" /> and an implementation that does not already exist
            in <paramref name="services" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="descriptors">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />s.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddScoped(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> service
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddScoped(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> service
            using the factory specified in <paramref name="implementationFactory" />
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddScoped(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Type)">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> service
            with the <paramref name="implementationType" /> implementation
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
      <param name="implementationType">The implementation type of the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddScoped``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> service
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddScoped``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``0})">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> service
            using the factory specified in <paramref name="implementationFactory" />
            to the <paramref name="services" /> if the service type hasn't already been registered.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddScoped``2(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> service
            implementation type specified in <typeparamref name="TImplementation" />
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddSingleton(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> service
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddSingleton(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> service
            using the factory specified in <paramref name="implementationFactory" />
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddSingleton(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Type)">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> service
            with the <paramref name="implementationType" /> implementation
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
      <param name="implementationType">The implementation type of the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddSingleton``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> service
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddSingleton``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,``0)">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> service
            with an instance specified in <paramref name="instance" />
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="instance">The instance of the service to add.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddSingleton``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``0})">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> service
            using the factory specified in <paramref name="implementationFactory" />
            to the <paramref name="services" /> if the service type hasn't already been registered.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddSingleton``2(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> service
            implementation type specified in <typeparamref name="TImplementation" />
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddTransient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> service
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddTransient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> service
            using the factory specified in <paramref name="implementationFactory" />
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddTransient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Type)">
      <summary>Adds the specified <paramref name="service" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> service
            with the <paramref name="implementationType" /> implementation
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="service">The type of the service to register.</param>
      <param name="implementationType">The implementation type of the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddTransient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> service
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddTransient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``0})">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> service
            using the factory specified in <paramref name="implementationFactory" />
            to the <paramref name="services" /> if the service type hasn't already been registered.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.Extensions.ServiceCollectionDescriptorExtensions.TryAddTransient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds the specified <typeparamref name="TService" /> as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> service
            implementation type specified in <typeparamref name="TImplementation" />
            to the <paramref name="collection" /> if the service type hasn't already been registered.</summary>
      <param name="collection">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.IServiceCollection">
      <summary>Specifies the contract for a collection of service descriptors.</summary>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.IServiceProviderFactory`1">
      <summary>Provides an extension point for creating a container specific builder and an <see cref="T:System.IServiceProvider" />.</summary>
      <typeparam name="TContainerBuilder" />
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.IServiceProviderFactory`1.CreateBuilder(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Creates a container builder from an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The collection of services</param>
      <returns>A container builder that can be used to create an <see cref="T:System.IServiceProvider" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.IServiceProviderFactory`1.CreateServiceProvider(`0)">
      <summary>Creates an <see cref="T:System.IServiceProvider" /> from the container builder.</summary>
      <param name="containerBuilder">The container builder</param>
      <returns>An <see cref="T:System.IServiceProvider" /></returns>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.IServiceScope">
      <summary>The <see cref="M:System.IDisposable.Dispose" /> method ends the scope lifetime. Once Dispose
            is called, any scoped services that have been resolved from
            <see cref="P:Microsoft.Extensions.DependencyInjection.IServiceScope.ServiceProvider" /> will be
            disposed.</summary>
    </member>
    <member name="P:Microsoft.Extensions.DependencyInjection.IServiceScope.ServiceProvider">
      <summary>The <see cref="T:System.IServiceProvider" /> used to resolve dependencies from the scope.</summary>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.IServiceScopeFactory">
      <summary>A factory for creating instances of <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceScope" />, which is used to create
            services within a scope.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.IServiceScopeFactory.CreateScope">
      <summary>Create an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceScope" /> which
            contains an <see cref="T:System.IServiceProvider" /> used to resolve dependencies from a
            newly created scope.</summary>
      <returns>
            An <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceScope" /> controlling the
            lifetime of the scope. Once this is disposed, any scoped services that have been resolved
            from the <see cref="P:Microsoft.Extensions.DependencyInjection.IServiceScope.ServiceProvider" />
            will also be disposed.
          .</returns>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.ISupportRequiredService">
      <summary>Optional contract used by <see cref="M:Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService``1(System.IServiceProvider)" />
            to resolve services if supported by <see cref="T:System.IServiceProvider" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ISupportRequiredService.GetRequiredService(System.Type)">
      <summary>Gets service of type <paramref name="serviceType" /> from the <see cref="T:System.IServiceProvider" /> implementing
            this interface.</summary>
      <param name="serviceType">An object that specifies the type of service object to get.</param>
      <returns>A service object of type <paramref name="serviceType" />.
            Throws an exception if the <see cref="T:System.IServiceProvider" /> cannot create the object.</returns>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.ObjectFactory">
      <summary>The result of <see cref="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateFactory(System.Type,System.Type[])" />.</summary>
      <param name="serviceProvider">The <see cref="T:System.IServiceProvider" /> to get service arguments from.</param>
      <param name="arguments">Additional constructor arguments.</param>
      <returns>The instantiated type.</returns>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions">
      <summary>Extension methods for adding services to an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddScoped(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
      <summary>Adds a scoped service of the type specified in <paramref name="serviceType" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register and the implementation to use.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddScoped(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Adds a scoped service of the type specified in <paramref name="serviceType" /> with a
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddScoped(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Type)">
      <summary>Adds a scoped service of the type specified in <paramref name="serviceType" /> with an
            implementation of the type specified in <paramref name="implementationType" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register.</param>
      <param name="implementationType">The implementation type of the service.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddScoped``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds a scoped service of the type specified in <typeparamref name="TService" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddScoped``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``0})">
      <summary>Adds a scoped service of the type specified in <typeparamref name="TService" /> with a
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddScoped``2(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds a scoped service of the type specified in <typeparamref name="TService" /> with an
            implementation type specified in <typeparamref name="TImplementation" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddScoped``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``1})">
      <summary>Adds a scoped service of the type specified in <typeparamref name="TService" /> with an
            implementation type specified in <typeparamref name="TImplementation" /> using the
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
      <summary>Adds a singleton service of the type specified in <paramref name="serviceType" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register and the implementation to use.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Adds a singleton service of the type specified in <paramref name="serviceType" /> with a
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Object)">
      <summary>Adds a singleton service of the type specified in <paramref name="serviceType" /> with an
            instance specified in <paramref name="implementationInstance" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register.</param>
      <param name="implementationInstance">The instance of the service.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Type)">
      <summary>Adds a singleton service of the type specified in <paramref name="serviceType" /> with an
            implementation of the type specified in <paramref name="implementationType" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register.</param>
      <param name="implementationType">The implementation type of the service.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds a singleton service of the type specified in <typeparamref name="TService" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,``0)">
      <summary>Adds a singleton service of the type specified in <typeparamref name="TService" /> with an
            instance specified in <paramref name="implementationInstance" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="implementationInstance">The instance of the service.</param>
      <typeparam name="TService" />
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``0})">
      <summary>Adds a singleton service of the type specified in <typeparamref name="TService" /> with a
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton``2(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds a singleton service of the type specified in <typeparamref name="TService" /> with an
            implementation type specified in <typeparamref name="TImplementation" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddSingleton``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``1})">
      <summary>Adds a singleton service of the type specified in <typeparamref name="TService" /> with an
            implementation type specified in <typeparamref name="TImplementation" /> using the
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddTransient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
      <summary>Adds a transient service of the type specified in <paramref name="serviceType" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register and the implementation to use.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddTransient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Adds a transient service of the type specified in <paramref name="serviceType" /> with a
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddTransient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type,System.Type)">
      <summary>Adds a transient service of the type specified in <paramref name="serviceType" /> with an
            implementation of the type specified in <paramref name="implementationType" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="serviceType">The type of the service to register.</param>
      <param name="implementationType">The implementation type of the service.</param>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddTransient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds a transient service of the type specified in <typeparamref name="TService" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddTransient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``0})">
      <summary>Adds a transient service of the type specified in <typeparamref name="TService" /> with a
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddTransient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds a transient service of the type specified in <typeparamref name="TService" /> with an
            implementation type specified in <typeparamref name="TImplementation" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceCollectionServiceExtensions.AddTransient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.IServiceProvider,``1})">
      <summary>Adds a transient service of the type specified in <typeparamref name="TService" /> with an
            implementation type specified in <typeparamref name="TImplementation" /> using the
            factory specified in <paramref name="implementationFactory" /> to the
            specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the service to.</param>
      <param name="implementationFactory">The factory that creates the service.</param>
      <typeparam name="TService">The type of the service to add.</typeparam>
      <typeparam name="TImplementation">The type of the implementation to use.</typeparam>
      <returns>A reference to this instance after the operation has completed.</returns>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor">
      <summary>Describes a service with its service type, implementation, and lifetime.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.#ctor(System.Type,System.Func{System.IServiceProvider,System.Object},Microsoft.Extensions.DependencyInjection.ServiceLifetime)">
      <summary>Initializes a new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified <paramref name="factory" />.</summary>
      <param name="serviceType">The <see cref="T:System.Type" /> of the service.</param>
      <param name="factory">A factory used for creating service instances.</param>
      <param name="lifetime">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceLifetime" /> of the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.#ctor(System.Type,System.Object)">
      <summary>Initializes a new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified <paramref name="instance" />
            as a <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" />.</summary>
      <param name="serviceType">The <see cref="T:System.Type" /> of the service.</param>
      <param name="instance">The instance implementing the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.#ctor(System.Type,System.Type,Microsoft.Extensions.DependencyInjection.ServiceLifetime)">
      <summary>Initializes a new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified <paramref name="implementationType" />.</summary>
      <param name="serviceType">The <see cref="T:System.Type" /> of the service.</param>
      <param name="implementationType">The <see cref="T:System.Type" /> implementing the service.</param>
      <param name="lifetime">The <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceLifetime" /> of the service.</param>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Describe(System.Type,System.Func{System.IServiceProvider,System.Object},Microsoft.Extensions.DependencyInjection.ServiceLifetime)">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="serviceType" />, <paramref name="implementationFactory" />,
            and <paramref name="lifetime" />.</summary>
      <param name="serviceType">The type of the service.</param>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <param name="lifetime">The lifetime of the service.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Describe(System.Type,System.Type,Microsoft.Extensions.DependencyInjection.ServiceLifetime)">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="serviceType" />, <paramref name="implementationType" />,
            and <paramref name="lifetime" />.</summary>
      <param name="serviceType">The type of the service.</param>
      <param name="implementationType">The type of the implementation.</param>
      <param name="lifetime">The lifetime of the service.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Scoped(System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="service" />, <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> lifetime.</summary>
      <param name="service">The type of the service.</param>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Scoped(System.Type,System.Type)">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="service" /> and <paramref name="implementationType" />
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> lifetime.</summary>
      <param name="service">The type of the service.</param>
      <param name="implementationType">The type of the implementation.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Scoped``1(System.Func{System.IServiceProvider,``0})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> lifetime.</summary>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <typeparam name="TService">The type of the service.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Scoped``2">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <typeparamref name="TImplementation" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> lifetime.</summary>
      <typeparam name="TService">The type of the service.</typeparam>
      <typeparam name="TImplementation">The type of the implementation.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Scoped``2(System.Func{System.IServiceProvider,``1})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <typeparamref name="TImplementation" />,
            <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> lifetime.</summary>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <typeparam name="TService">The type of the service.</typeparam>
      <typeparam name="TImplementation">The type of the implementation.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Singleton(System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="serviceType" />, <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> lifetime.</summary>
      <param name="serviceType">The type of the service.</param>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Singleton(System.Type,System.Object)">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="serviceType" />, <paramref name="implementationInstance" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> lifetime.</summary>
      <param name="serviceType">The type of the service.</param>
      <param name="implementationInstance">The instance of the implementation.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Singleton(System.Type,System.Type)">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="service" /> and <paramref name="implementationType" />
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> lifetime.</summary>
      <param name="service">The type of the service.</param>
      <param name="implementationType">The type of the implementation.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Singleton``1(``0)">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <paramref name="implementationInstance" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped" /> lifetime.</summary>
      <param name="implementationInstance">The instance of the implementation.</param>
      <typeparam name="TService">The type of the service.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Singleton``1(System.Func{System.IServiceProvider,``0})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> lifetime.</summary>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <typeparam name="TService">The type of the service.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Singleton``2">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <typeparamref name="TImplementation" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> lifetime.</summary>
      <typeparam name="TService">The type of the service.</typeparam>
      <typeparam name="TImplementation">The type of the implementation.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Singleton``2(System.Func{System.IServiceProvider,``1})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <typeparamref name="TImplementation" />,
            <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton" /> lifetime.</summary>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <typeparam name="TService">The type of the service.</typeparam>
      <typeparam name="TImplementation">The type of the implementation.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.ToString" />
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Transient(System.Type,System.Func{System.IServiceProvider,System.Object})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="service" />, <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> lifetime.</summary>
      <param name="service">The type of the service.</param>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Transient(System.Type,System.Type)">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <paramref name="service" /> and <paramref name="implementationType" />
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> lifetime.</summary>
      <param name="service">The type of the service.</param>
      <param name="implementationType">The type of the implementation.</param>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Transient``1(System.Func{System.IServiceProvider,``0})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> lifetime.</summary>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <typeparam name="TService">The type of the service.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Transient``2">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <typeparamref name="TImplementation" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> lifetime.</summary>
      <typeparam name="TService">The type of the service.</typeparam>
      <typeparam name="TImplementation">The type of the implementation.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Transient``2(System.Func{System.IServiceProvider,``1})">
      <summary>Creates an instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" /> with the specified
            <typeparamref name="TService" />, <typeparamref name="TImplementation" />,
            <paramref name="implementationFactory" />,
            and the <see cref="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient" /> lifetime.</summary>
      <param name="implementationFactory">A factory to create new instances of the service implementation.</param>
      <typeparam name="TService">The type of the service.</typeparam>
      <typeparam name="TImplementation">The type of the implementation.</typeparam>
      <returns>A new instance of <see cref="T:Microsoft.Extensions.DependencyInjection.ServiceDescriptor" />.</returns>
    </member>
    <member name="P:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.ImplementationFactory" />
    <member name="P:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.ImplementationInstance" />
    <member name="P:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.ImplementationType" />
    <member name="P:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.Lifetime" />
    <member name="P:Microsoft.Extensions.DependencyInjection.ServiceDescriptor.ServiceType" />
    <member name="T:Microsoft.Extensions.DependencyInjection.ServiceLifetime">
      <summary>Specifies the lifetime of a service in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.</summary>
    </member>
    <member name="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Scoped">
      <summary>Specifies that a new instance of the service will be created for each scope.</summary>
    </member>
    <member name="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Singleton">
      <summary>Specifies that a single instance of the service will be created.</summary>
    </member>
    <member name="F:Microsoft.Extensions.DependencyInjection.ServiceLifetime.Transient">
      <summary>Specifies that a new instance of the service will be created every time it is requested.</summary>
    </member>
    <member name="T:Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions">
      <summary>Extension methods for getting services from an <see cref="T:System.IServiceProvider" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.CreateScope(System.IServiceProvider)">
      <summary>Creates a new <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceScope" /> that can be used to resolve scoped services.</summary>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> to create the scope from.</param>
      <returns>A <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceScope" /> that can be used to resolve scoped services.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(System.IServiceProvider,System.Type)">
      <summary>Get service of type <paramref name="serviceType" /> from the <see cref="T:System.IServiceProvider" />.</summary>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> to retrieve the service object from.</param>
      <param name="serviceType">An object that specifies the type of service object to get.</param>
      <exception cref="T:System.InvalidOperationException">There is no service of type <paramref name="serviceType" />.</exception>
      <returns>A service object of type <paramref name="serviceType" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService``1(System.IServiceProvider)">
      <summary>Get service of type <typeparamref name="T" /> from the <see cref="T:System.IServiceProvider" />.</summary>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> to retrieve the service object from.</param>
      <typeparam name="T">The type of service object to get.</typeparam>
      <exception cref="T:System.InvalidOperationException">There is no service of type <typeparamref name="T" />.</exception>
      <returns>A service object of type <typeparamref name="T" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService``1(System.IServiceProvider)">
      <summary>Get service of type <typeparamref name="T" /> from the <see cref="T:System.IServiceProvider" />.</summary>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> to retrieve the service object from.</param>
      <typeparam name="T">The type of service object to get.</typeparam>
      <returns>A service object of type <typeparamref name="T" /> or null if there is no such service.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetServices(System.IServiceProvider,System.Type)">
      <summary>Get an enumeration of services of type <paramref name="serviceType" /> from the <see cref="T:System.IServiceProvider" />.</summary>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> to retrieve the services from.</param>
      <param name="serviceType">An object that specifies the type of service object to get.</param>
      <returns>An enumeration of services of type <paramref name="serviceType" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetServices``1(System.IServiceProvider)">
      <summary>Get an enumeration of services of type <typeparamref name="T" /> from the <see cref="T:System.IServiceProvider" />.</summary>
      <param name="provider">The <see cref="T:System.IServiceProvider" /> to retrieve the services from.</param>
      <typeparam name="T">The type of service object to get.</typeparam>
      <returns>An enumeration of services of type <typeparamref name="T" />.</returns>
    </member>
  </members>
</doc>