﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Extensions.Options</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions">
      <summary>Extension methods for adding options services to the DI container.</summary>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.AddOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Adds services required for using options.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.AddOptions``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Gets an options builder that forwards <c>Configure</c> calls for the same named <typeparamref name="TOptions" /> to the underlying service collection.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <typeparam name="TOptions">The options type to be configured.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" /> so that configure calls can be chained in it.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.AddOptions``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
      <summary>Gets an options builder that forwards <c>Configure</c> calls for the same named <typeparamref name="TOptions" /> to the underlying service collection.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="name">The name of the options instance.</param>
      <typeparam name="TOptions">The options type to be configured.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" /> so that <c>Configure</c> calls can be chained in it.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
      <summary>Registers an action used to configure a particular type of options. These are run before <see cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TOptions">The options type to be configured.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{``0})">
      <summary>Registers an action used to configure a particular type of options. These are run before <see cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="name">The name of the options instance.</param>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TOptions">The options type to be configured.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.ConfigureAll``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
      <summary>Registers an action used to configure all instances of a particular type of options.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TOptions">The options type to be configured.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.ConfigureOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Object)">
      <summary>Registers an object that will have all of its <c>I[Post]ConfigureOptions</c> registered.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="configureInstance">The instance that will configure options.</param>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.ConfigureOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
      <summary>Registers a type that will have all of its <c>I[Post]ConfigureOptions</c> registered.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="configureType">The type that will configure options.</param>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.ConfigureOptions``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
      <summary>Registers a type that will have all of its <c>I[Post]ConfigureOptions</c> registered.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <typeparam name="TConfigureOptions">The type that will configure options.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
      <summary>Registers an action used to initialize a particular type of options. These are run after <see cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TOptions">The options type to be configured.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{``0})">
      <summary>Registers an action used to configure a particular type of options. These are run after <see cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="name">The name of the options instance.</param>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TOptions">The options type to be configure.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigureAll``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
      <summary>Registers an action used to post configure all instances of a particular type of options. These are run after <see cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})" />.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add the services to.</param>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TOptions">The options type to be configured.</typeparam>
      <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> so that additional calls can be chained.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`1">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IConfigureNamedOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`1.#ctor(System.String,System.Action{`0})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`1.Configure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance with the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`1.Configure(System.String,`0)">
      <summary>Invokes the registered configure <see cref="P:Microsoft.Extensions.Options.ConfigureNamedOptions`1.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`1.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`1.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`2">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IConfigureNamedOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep">Dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`2.#ctor(System.String,`1,System.Action{`0,`1})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency">A dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`2.Configure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance with the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`2.Configure(System.String,`0)">
      <summary>Invokes the registered configure <see cref="P:Microsoft.Extensions.Options.ConfigureNamedOptions`2.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`2.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`2.Dependency">
      <summary>The dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`2.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`3">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IConfigureNamedOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`3.#ctor(System.String,`1,`2,System.Action{`0,`1,`2})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency">A dependency.</param>
      <param name="dependency2">A second dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Configure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance with the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Configure(System.String,`0)">
      <summary>Invokes the registered configure <see cref="P:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`4">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IConfigureNamedOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`4.#ctor(System.String,`1,`2,`3,System.Action{`0,`1,`2,`3})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency">A dependency.</param>
      <param name="dependency2">A second dependency.</param>
      <param name="dependency3">A third dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Configure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance with the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Configure(System.String,`0)">
      <summary>Invokes the registered configure <see cref="P:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`5">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IConfigureNamedOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
      <typeparam name="TDep4">Fourth dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`5.#ctor(System.String,`1,`2,`3,`4,System.Action{`0,`1,`2,`3,`4})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency1">A dependency.</param>
      <param name="dependency2">A second dependency.</param>
      <param name="dependency3">A third dependency.</param>
      <param name="dependency4">A fourth dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Configure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance with the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Configure(System.String,`0)">
      <summary>Invokes the registered configure <see cref="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Dependency4">
      <summary>The fourth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`6">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IConfigureNamedOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
      <typeparam name="TDep4">Fourth dependency type.</typeparam>
      <typeparam name="TDep5">Fifth dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`6.#ctor(System.String,`1,`2,`3,`4,`5,System.Action{`0,`1,`2,`3,`4,`5})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency1">A dependency.</param>
      <param name="dependency2">A second dependency.</param>
      <param name="dependency3">A third dependency.</param>
      <param name="dependency4">A fourth dependency.</param>
      <param name="dependency5">A fifth dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Configure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance with the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Configure(System.String,`0)">
      <summary>Invokes the registered configure <see cref="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Dependency4">
      <summary>The fourth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Dependency5">
      <summary>The fifth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ConfigureOptions`1">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IConfigureOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureOptions`1.#ctor(System.Action{`0})">
      <summary>Constructor.</summary>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ConfigureOptions`1.Configure(`0)">
      <summary>Invokes the registered configure <see cref="P:Microsoft.Extensions.Options.ConfigureOptions`1.Action" />.</summary>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.ConfigureOptions`1.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.IConfigureNamedOptions`1">
      <summary>Represents something that configures the <typeparamref name="TOptions" /> type.</summary>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IConfigureNamedOptions`1.Configure(System.String,`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="T:Microsoft.Extensions.Options.IConfigureOptions`1">
      <summary>Represents something that configures the <typeparamref name="TOptions" /> type.
            Note: These are run before all <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1" />.</summary>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IConfigureOptions`1.Configure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance.</summary>
      <param name="options">The options instance to configure.</param>
    </member>
    <member name="T:Microsoft.Extensions.Options.IOptions`1">
      <summary>Used to retrieve configured <typeparamref name="TOptions" /> instances.</summary>
      <typeparam name="TOptions">The type of options being requested.</typeparam>
    </member>
    <member name="P:Microsoft.Extensions.Options.IOptions`1.Value">
      <summary>Gets the default configured <typeparamref name="TOptions" /> instance.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.IOptionsChangeTokenSource`1">
      <summary>Used to fetch <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> used for tracking options changes.</summary>
      <typeparam name="TOptions">Options type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsChangeTokenSource`1.GetChangeToken">
      <summary>Returns a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> which can be used to register a change notification callback.</summary>
      <returns>Change token.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.IOptionsChangeTokenSource`1.Name">
      <summary>The name of the option instance being changed.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.IOptionsFactory`1">
      <summary>Used to create <typeparamref name="TOptions" /> instances.</summary>
      <typeparam name="TOptions">The type of options being requested.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsFactory`1.Create(System.String)">
      <summary>Returns a configured <typeparamref name="TOptions" /> instance with the given name.</summary>
      <param name="name" />
    </member>
    <member name="T:Microsoft.Extensions.Options.IOptionsMonitor`1">
      <summary>Used for notifications when <typeparamref name="TOptions" /> instances change.</summary>
      <typeparam name="TOptions">The options type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsMonitor`1.Get(System.String)">
      <summary>Returns a configured <typeparamref name="TOptions" /> instance with the given name.</summary>
      <param name="name" />
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsMonitor`1.OnChange(System.Action{`0,System.String})">
      <summary>Registers a listener to be called whenever a named <typeparamref name="TOptions" /> changes.</summary>
      <param name="listener">The action to be invoked when <typeparamref name="TOptions" /> has changed.</param>
      <returns>An <see cref="T:System.IDisposable" /> which should be disposed to stop listening for changes.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.IOptionsMonitor`1.CurrentValue">
      <summary>Returns the current <typeparamref name="TOptions" /> instance with the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.IOptionsMonitorCache`1">
      <summary>Used by <see cref="T:Microsoft.Extensions.Options.IOptionsMonitor`1" /> to cache <typeparamref name="TOptions" /> instances.</summary>
      <typeparam name="TOptions">The type of options being requested.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsMonitorCache`1.Clear">
      <summary>Clears all options instances from the cache.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsMonitorCache`1.GetOrAdd(System.String,System.Func{`0})">
      <summary>Gets a named options instance, or adds a new instance created with <paramref name="createOptions" />.</summary>
      <param name="name">The name of the options instance.</param>
      <param name="createOptions">The func used to create the new instance.</param>
      <returns>The options instance.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsMonitorCache`1.TryAdd(System.String,`0)">
      <summary>Tries to adds a new option to the cache, will return false if the name already exists.</summary>
      <param name="name">The name of the options instance.</param>
      <param name="options">The options instance.</param>
      <returns>Whether anything was added.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsMonitorCache`1.TryRemove(System.String)">
      <summary>Try to remove an options instance.</summary>
      <param name="name">The name of the options instance.</param>
      <returns>Whether anything was removed.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Options.IOptionsSnapshot`1">
      <summary>Used to access the value of <typeparamref name="TOptions" /> for the lifetime of a request.</summary>
      <typeparam name="TOptions">Options type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IOptionsSnapshot`1.Get(System.String)">
      <summary>Returns a configured <typeparamref name="TOptions" /> instance with the given name.</summary>
      <param name="name" />
    </member>
    <member name="T:Microsoft.Extensions.Options.IPostConfigureOptions`1">
      <summary>Represents something that configures the <typeparamref name="TOptions" /> type.
            Note: These are run after all <see cref="T:Microsoft.Extensions.Options.IConfigureOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IPostConfigureOptions`1.PostConfigure(System.String,`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="T:Microsoft.Extensions.Options.IValidateOptions`1">
      <summary>Interface used to validate options.</summary>
      <typeparam name="TOptions">The options type to validate.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.IValidateOptions`1.Validate(System.String,`0)">
      <summary>Validates a specific named options instance (or all when name is null).</summary>
      <param name="name">The name of the options instance being validated.</param>
      <param name="options">The options instance.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Options.ValidateOptionsResult" /> result.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Options.Options">
      <summary>Helper class.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Options.Options.DefaultName">
      <summary>The default name used for options instances: "".</summary>
    </member>
    <member name="M:Microsoft.Extensions.Options.Options.Create``1(``0)">
      <summary>Creates a wrapper around an instance of <typeparamref name="TOptions" /> to return itself as an <see cref="T:Microsoft.Extensions.Options.IOptions`1" />.</summary>
      <param name="options">Options object.</param>
      <typeparam name="TOptions">Options type.</typeparam>
      <returns>Wrapped options object.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Options.OptionsBuilder`1">
      <summary>Used to configure <typeparamref name="TOptions" /> instances.</summary>
      <typeparam name="TOptions">The type of options being requested.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.#ctor(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
      <summary>Constructor.</summary>
      <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> for the options being configured.</param>
      <param name="name">The default name of the <typeparamref name="TOptions" /> instance, if null <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" /> is used.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})">
      <summary>Registers an action used to configure a particular type of options. These are run before all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure``1(System.Action{`0,``0})">
      <summary>Registers an action used to configure a particular type of options. These are run before all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep">A dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure``2(System.Action{`0,``0,``1})">
      <summary>Registers an action used to configure a particular type of options. These are run before all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep1">The first dependency used by the action.</typeparam>
      <typeparam name="TDep2">The second dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure``3(System.Action{`0,``0,``1,``2})">
      <summary>Registers an action used to configure a particular type of options. These are run before all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep1">The first dependency used by the action.</typeparam>
      <typeparam name="TDep2">The second dependency used by the action.</typeparam>
      <typeparam name="TDep3">The third dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure``4(System.Action{`0,``0,``1,``2,``3})">
      <summary>Registers an action used to configure a particular type of options. These are run before all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep1">The first dependency used by the action.</typeparam>
      <typeparam name="TDep2">The second dependency used by the action.</typeparam>
      <typeparam name="TDep3">The third dependency used by the action.</typeparam>
      <typeparam name="TDep4">The fourth dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure``5(System.Action{`0,``0,``1,``2,``3,``4})">
      <summary>Registers an action used to configure a particular type of options. These are run before all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep1">The first dependency used by the action.</typeparam>
      <typeparam name="TDep2">The second dependency used by the action.</typeparam>
      <typeparam name="TDep3">The third dependency used by the action.</typeparam>
      <typeparam name="TDep4">The fourth dependency used by the action.</typeparam>
      <typeparam name="TDep5">The fifth dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})">
      <summary>Registers an action used to configure a particular type of options. These are run after all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure``1(System.Action{`0,``0})">
      <summary>Registers an action used to post configure a particular type of options. These are run after all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep">The dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure``2(System.Action{`0,``0,``1})">
      <summary>Registers an action used to post configure a particular type of options. These are run after all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep1">The first dependency used by the action.</typeparam>
      <typeparam name="TDep2">The second dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure``3(System.Action{`0,``0,``1,``2})">
      <summary>Registers an action used to post configure a particular type of options. These are run after all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep1">The first dependency used by the action.</typeparam>
      <typeparam name="TDep2">The second dependency used by the action.</typeparam>
      <typeparam name="TDep3">The third dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure``4(System.Action{`0,``0,``1,``2,``3})">
      <summary>Registers an action used to post configure a particular type of options. These are run after all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep1">The first dependency used by the action.</typeparam>
      <typeparam name="TDep2">The second dependency used by the action.</typeparam>
      <typeparam name="TDep3">The third dependency used by the action.</typeparam>
      <typeparam name="TDep4">The fourth dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure``5(System.Action{`0,``0,``1,``2,``3,``4})">
      <summary>Registers an action used to post configure a particular type of options. These are run after all <see cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})" />.</summary>
      <param name="configureOptions">The action used to configure the options.</param>
      <typeparam name="TDep1">The first dependency used by the action.</typeparam>
      <typeparam name="TDep2">The second dependency used by the action.</typeparam>
      <typeparam name="TDep3">The third dependency used by the action.</typeparam>
      <typeparam name="TDep4">The fourth dependency used by the action.</typeparam>
      <typeparam name="TDep5">The fifth dependency used by the action.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate(System.Func{`0,System.Boolean})">
      <summary>Register a validation action for an options type using a default failure message.</summary>
      <param name="validation">The validation function.</param>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate(System.Func{`0,System.Boolean},System.String)">
      <summary>Register a validation action for an options type.</summary>
      <param name="validation">The validation function.</param>
      <param name="failureMessage">The failure message to use when validation fails.</param>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``1(System.Func{`0,``0,System.Boolean})">
      <summary>Register a validation action for an options type using a default failure message.</summary>
      <param name="validation">The validation function.</param>
      <typeparam name="TDep">The dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``1(System.Func{`0,``0,System.Boolean},System.String)">
      <summary>Register a validation action for an options type.</summary>
      <param name="validation">The validation function.</param>
      <param name="failureMessage">The failure message to use when validation fails.</param>
      <typeparam name="TDep">The dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``2(System.Func{`0,``0,``1,System.Boolean})">
      <summary>Register a validation action for an options type using a default failure message.</summary>
      <param name="validation">The validation function.</param>
      <typeparam name="TDep1">The first dependency used by the validation function.</typeparam>
      <typeparam name="TDep2">The second dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``2(System.Func{`0,``0,``1,System.Boolean},System.String)">
      <summary>Register a validation action for an options type.</summary>
      <param name="validation">The validation function.</param>
      <param name="failureMessage">The failure message to use when validation fails.</param>
      <typeparam name="TDep1">The first dependency used by the validation function.</typeparam>
      <typeparam name="TDep2">The second dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``3(System.Func{`0,``0,``1,``2,System.Boolean})">
      <summary>Register a validation action for an options type using a default failure message.</summary>
      <param name="validation">The validation function.</param>
      <typeparam name="TDep1">The first dependency used by the validation function.</typeparam>
      <typeparam name="TDep2">The second dependency used by the validation function.</typeparam>
      <typeparam name="TDep3">The third dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``3(System.Func{`0,``0,``1,``2,System.Boolean},System.String)">
      <summary>Register a validation action for an options type.</summary>
      <param name="validation">The validation function.</param>
      <param name="failureMessage">The failure message to use when validation fails.</param>
      <typeparam name="TDep1">The first dependency used by the validation function.</typeparam>
      <typeparam name="TDep2">The second dependency used by the validation function.</typeparam>
      <typeparam name="TDep3">The third dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``4(System.Func{`0,``0,``1,``2,``3,System.Boolean})">
      <summary>Register a validation action for an options type using a default failure message.</summary>
      <param name="validation">The validation function.</param>
      <typeparam name="TDep1">The first dependency used by the validation function.</typeparam>
      <typeparam name="TDep2">The second dependency used by the validation function.</typeparam>
      <typeparam name="TDep3">The third dependency used by the validation function.</typeparam>
      <typeparam name="TDep4">The fourth dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``4(System.Func{`0,``0,``1,``2,``3,System.Boolean},System.String)">
      <summary>Register a validation action for an options type.</summary>
      <param name="validation">The validation function.</param>
      <param name="failureMessage">The failure message to use when validation fails.</param>
      <typeparam name="TDep1">The first dependency used by the validation function.</typeparam>
      <typeparam name="TDep2">The second dependency used by the validation function.</typeparam>
      <typeparam name="TDep3">The third dependency used by the validation function.</typeparam>
      <typeparam name="TDep4">The fourth dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``5(System.Func{`0,``0,``1,``2,``3,``4,System.Boolean})">
      <summary>Register a validation action for an options type using a default failure message.</summary>
      <param name="validation">The validation function.</param>
      <typeparam name="TDep1">The first dependency used by the validation function.</typeparam>
      <typeparam name="TDep2">The second dependency used by the validation function.</typeparam>
      <typeparam name="TDep3">The third dependency used by the validation function.</typeparam>
      <typeparam name="TDep4">The fourth dependency used by the validation function.</typeparam>
      <typeparam name="TDep5">The fifth dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Validate``5(System.Func{`0,``0,``1,``2,``3,``4,System.Boolean},System.String)">
      <summary>Register a validation action for an options type.</summary>
      <param name="validation">The validation function.</param>
      <param name="failureMessage">The failure message to use when validation fails.</param>
      <typeparam name="TDep1">The first dependency used by the validation function.</typeparam>
      <typeparam name="TDep2">The second dependency used by the validation function.</typeparam>
      <typeparam name="TDep3">The third dependency used by the validation function.</typeparam>
      <typeparam name="TDep4">The fourth dependency used by the validation function.</typeparam>
      <typeparam name="TDep5">The fifth dependency used by the validation function.</typeparam>
      <returns>The current <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1" />.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsBuilder`1.Name">
      <summary>The default name of the <typeparamref name="TOptions" /> instance.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsBuilder`1.Services">
      <summary>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> for the options being configured.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.OptionsCache`1">
      <summary>Used to cache <typeparamref name="TOptions" /> instances.</summary>
      <typeparam name="TOptions">The type of options being requested.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsCache`1.#ctor" />
    <member name="M:Microsoft.Extensions.Options.OptionsCache`1.Clear">
      <summary>Clears all options instances from the cache.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsCache`1.GetOrAdd(System.String,System.Func{`0})">
      <summary>Gets a named options instance, or adds a new instance created with <paramref name="createOptions" />.</summary>
      <param name="name">The name of the options instance.</param>
      <param name="createOptions">The func used to create the new instance.</param>
      <returns>The options instance.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsCache`1.TryAdd(System.String,`0)">
      <summary>Tries to adds a new option to the cache, will return false if the name already exists.</summary>
      <param name="name">The name of the options instance.</param>
      <param name="options">The options instance.</param>
      <returns>Whether anything was added.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsCache`1.TryRemove(System.String)">
      <summary>Try to remove an options instance.</summary>
      <param name="name">The name of the options instance.</param>
      <returns>Whether anything was removed.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Options.OptionsFactory`1">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IOptionsFactory`1" />.</summary>
      <typeparam name="TOptions">The type of options being requested.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsFactory`1.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IConfigureOptions{`0}},System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IPostConfigureOptions{`0}})">
      <summary>Initializes a new instance with the specified options configurations.</summary>
      <param name="setups">The configuration actions to run.</param>
      <param name="postConfigures">The initialization actions to run.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsFactory`1.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IConfigureOptions{`0}},System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IPostConfigureOptions{`0}},System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IValidateOptions{`0}})">
      <summary>Initializes a new instance with the specified options configurations.</summary>
      <param name="setups">The configuration actions to run.</param>
      <param name="postConfigures">The initialization actions to run.</param>
      <param name="validations">The validations to run.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsFactory`1.Create(System.String)">
      <summary>Returns a configured <typeparamref name="TOptions" /> instance with the given <paramref name="name" />.</summary>
      <param name="name" />
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsFactory`1.CreateInstance(System.String)">
      <param name="name" />
    </member>
    <member name="T:Microsoft.Extensions.Options.OptionsManager`1">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IOptions`1" /> and <see cref="T:Microsoft.Extensions.Options.IOptionsSnapshot`1" />.</summary>
      <typeparam name="TOptions">Options type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsManager`1.#ctor(Microsoft.Extensions.Options.IOptionsFactory{`0})">
      <summary>Initializes a new instance with the specified options configurations.</summary>
      <param name="factory">The factory to use to create options.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsManager`1.Get(System.String)">
      <summary>Returns a configured <typeparamref name="TOptions" /> instance with the given <paramref name="name" />.</summary>
      <param name="name" />
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsManager`1.Value">
      <summary>The default configured <typeparamref name="TOptions" /> instance, equivalent to Get(Options.DefaultName).</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.OptionsMonitor`1">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IOptionsMonitor`1" />.</summary>
      <typeparam name="TOptions">Options type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsMonitor`1.#ctor(Microsoft.Extensions.Options.IOptionsFactory{`0},System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IOptionsChangeTokenSource{`0}},Microsoft.Extensions.Options.IOptionsMonitorCache{`0})">
      <summary>Constructor.</summary>
      <param name="factory">The factory to use to create options.</param>
      <param name="sources">The sources used to listen for changes to the options instance.</param>
      <param name="cache">The cache used to store options.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsMonitor`1.Dispose">
      <summary>Removes all change registration subscriptions.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsMonitor`1.Get(System.String)">
      <summary>Returns a configured <typeparamref name="TOptions" /> instance with the given <paramref name="name" />.</summary>
      <param name="name" />
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsMonitor`1.OnChange(System.Action{`0,System.String})">
      <summary>Registers a listener to be called whenever <typeparamref name="TOptions" /> changes.</summary>
      <param name="listener">The action to be invoked when <typeparamref name="TOptions" /> has changed.</param>
      <returns>An <see cref="T:System.IDisposable" /> which should be disposed to stop listening for changes.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsMonitor`1.CurrentValue">
      <summary>The present value of the options.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.OptionsMonitorExtensions">
      <summary>Extension methods for <see cref="T:Microsoft.Extensions.Options.IOptionsMonitor`1" />.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsMonitorExtensions.OnChange``1(Microsoft.Extensions.Options.IOptionsMonitor{``0},System.Action{``0})">
      <summary>Registers a listener to be called whenever <typeparamref name="TOptions" /> changes.</summary>
      <param name="monitor">The IOptionsMonitor.</param>
      <param name="listener">The action to be invoked when <typeparamref name="TOptions" /> has changed.</param>
      <typeparam name="TOptions" />
      <returns>An <see cref="T:System.IDisposable" /> which should be disposed to stop listening for changes.</returns>
    </member>
    <member name="T:Microsoft.Extensions.Options.OptionsValidationException">
      <summary>Thrown when options validation fails.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsValidationException.#ctor(System.String,System.Type,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Constructor.</summary>
      <param name="optionsName">The name of the options instance that failed.</param>
      <param name="optionsType">The options type that failed.</param>
      <param name="failureMessages">The validation failure messages.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsValidationException.Failures">
      <summary>The validation failures.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsValidationException.Message">
      <summary>The message is a semicolon separated list of the <see cref="P:Microsoft.Extensions.Options.OptionsValidationException.Failures" />.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsValidationException.OptionsName">
      <summary>The name of the options instance that failed.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsValidationException.OptionsType">
      <summary>The type of the options that failed.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.OptionsWrapper`1">
      <summary>
        <see cref="T:Microsoft.Extensions.Options.IOptions`1" /> wrapper that returns the options instance.</summary>
      <typeparam name="TOptions">Options type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.OptionsWrapper`1.#ctor(`0)">
      <summary>Intializes the wrapper with the options instance to return.</summary>
      <param name="options">The options instance to return.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.OptionsWrapper`1.Value">
      <summary>The options instance.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`1">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`1.#ctor(System.String,System.Action{`0})">
      <summary>Creates a new instance of <see cref="T:Microsoft.Extensions.Options.PostConfigureOptions`1" />.</summary>
      <param name="name">The name of the options.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`1.PostConfigure(System.String,`0)">
      <summary>Invokes the registered initialization <see cref="P:Microsoft.Extensions.Options.PostConfigureOptions`1.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the action to invoke.</param>
      <param name="options">The options to use in initialization.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`1.Action">
      <summary>The initialization action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`1.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`2">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep">Dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`2.#ctor(System.String,`1,System.Action{`0,`1})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency">A dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`2.PostConfigure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance using the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`2.PostConfigure(System.String,`0)">
      <summary>Invokes the registered initialization <see cref="P:Microsoft.Extensions.Options.PostConfigureOptions`2.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`2.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`2.Dependency">
      <summary>The dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`2.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`3">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`3.#ctor(System.String,`1,`2,System.Action{`0,`1,`2})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency">A dependency.</param>
      <param name="dependency2">A second dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`3.PostConfigure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance using the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`3.PostConfigure(System.String,`0)">
      <summary>Invokes the registered initialization <see cref="P:Microsoft.Extensions.Options.PostConfigureOptions`3.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`3.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`3.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`3.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`3.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`4">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`4.#ctor(System.String,`1,`2,`3,System.Action{`0,`1,`2,`3})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency">A dependency.</param>
      <param name="dependency2">A second dependency.</param>
      <param name="dependency3">A third dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`4.PostConfigure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance using the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`4.PostConfigure(System.String,`0)">
      <summary>Invokes the registered initialization <see cref="P:Microsoft.Extensions.Options.PostConfigureOptions`4.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`4.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`4.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`4.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`4.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`4.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`5">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
      <typeparam name="TDep4">Fourth dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`5.#ctor(System.String,`1,`2,`3,`4,System.Action{`0,`1,`2,`3,`4})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency1">A dependency.</param>
      <param name="dependency2">A second dependency.</param>
      <param name="dependency3">A third dependency.</param>
      <param name="dependency4">A fourth dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`5.PostConfigure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance using the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`5.PostConfigure(System.String,`0)">
      <summary>Invokes the registered initialization <see cref="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Dependency4">
      <summary>The fourth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`6">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1" />.</summary>
      <typeparam name="TOptions">Options type being configured.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
      <typeparam name="TDep4">Fourth dependency type.</typeparam>
      <typeparam name="TDep5">Fifth dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`6.#ctor(System.String,`1,`2,`3,`4,`5,System.Action{`0,`1,`2,`3,`4,`5})">
      <summary>Constructor.</summary>
      <param name="name">The name of the options.</param>
      <param name="dependency1">A dependency.</param>
      <param name="dependency2">A second dependency.</param>
      <param name="dependency3">A third dependency.</param>
      <param name="dependency4">A fourth dependency.</param>
      <param name="dependency5">A fifth dependency.</param>
      <param name="action">The action to register.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`6.PostConfigure(`0)">
      <summary>Invoked to configure a <typeparamref name="TOptions" /> instance using the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName" />.</summary>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`6.PostConfigure(System.String,`0)">
      <summary>Invokes the registered initialization <see cref="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Action" /> if the <paramref name="name" /> matches.</summary>
      <param name="name">The name of the options instance being configured.</param>
      <param name="options">The options instance to configured.</param>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Action">
      <summary>The configuration action.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Dependency4">
      <summary>The fourth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Dependency5">
      <summary>The fifth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Name">
      <summary>The options name.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ValidateOptions`1">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IValidateOptions`1" /></summary>
      <typeparam name="TOptions">The options type to validate.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`1.#ctor(System.String,System.Func{`0,System.Boolean},System.String)">
      <summary>Constructor.</summary>
      <param name="name">Options name.</param>
      <param name="validation">Validation function.</param>
      <param name="failureMessage">Validation failure message.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`1.Validate(System.String,`0)">
      <summary>Validates a specific named options instance (or all when <paramref name="name" /> is null).</summary>
      <param name="name">The name of the options instance being validated.</param>
      <param name="options">The options instance.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Options.ValidateOptionsResult" /> result.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`1.FailureMessage">
      <summary>The error to return when validation fails.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`1.Name">
      <summary>The options name.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`1.Validation">
      <summary>The validation function.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ValidateOptions`2">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IValidateOptions`1" /></summary>
      <typeparam name="TOptions">The options type to validate.</typeparam>
      <typeparam name="TDep">Dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`2.#ctor(System.String,`1,System.Func{`0,`1,System.Boolean},System.String)">
      <summary>Constructor.</summary>
      <param name="name">Options name.</param>
      <param name="dependency">The dependency.</param>
      <param name="validation">Validation function.</param>
      <param name="failureMessage">Validation failure message.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`2.Validate(System.String,`0)">
      <summary>Validates a specific named options instance (or all when <paramref name="name" /> is null).</summary>
      <param name="name">The name of the options instance being validated.</param>
      <param name="options">The options instance.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Options.ValidateOptionsResult" /> result.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`2.Dependency">
      <summary>The dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`2.FailureMessage">
      <summary>The error to return when validation fails.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`2.Name">
      <summary>The options name.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`2.Validation">
      <summary>The validation function.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ValidateOptions`3">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IValidateOptions`1" /></summary>
      <typeparam name="TOptions">The options type to validate.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`3.#ctor(System.String,`1,`2,System.Func{`0,`1,`2,System.Boolean},System.String)">
      <summary>Constructor.</summary>
      <param name="name">Options name.</param>
      <param name="dependency1">The first dependency.</param>
      <param name="dependency2">The second dependency.</param>
      <param name="validation">Validation function.</param>
      <param name="failureMessage">Validation failure message.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`3.Validate(System.String,`0)">
      <summary>Validates a specific named options instance (or all when <paramref name="name" /> is null).</summary>
      <param name="name">The name of the options instance being validated.</param>
      <param name="options">The options instance.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Options.ValidateOptionsResult" /> result.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`3.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`3.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`3.FailureMessage">
      <summary>The error to return when validation fails.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`3.Name">
      <summary>The options name.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`3.Validation">
      <summary>The validation function.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ValidateOptions`4">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IValidateOptions`1" /></summary>
      <typeparam name="TOptions">The options type to validate.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`4.#ctor(System.String,`1,`2,`3,System.Func{`0,`1,`2,`3,System.Boolean},System.String)">
      <summary>Constructor.</summary>
      <param name="name">Options name.</param>
      <param name="dependency1">The first dependency.</param>
      <param name="dependency2">The second dependency.</param>
      <param name="dependency3">The third dependency.</param>
      <param name="validation">Validation function.</param>
      <param name="failureMessage">Validation failure message.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`4.Validate(System.String,`0)">
      <summary>Validates a specific named options instance (or all when <paramref name="name" /> is null).</summary>
      <param name="name">The name of the options instance being validated.</param>
      <param name="options">The options instance.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Options.ValidateOptionsResult" /> result.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`4.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`4.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`4.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`4.FailureMessage">
      <summary>The error to return when validation fails.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`4.Name">
      <summary>The options name.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`4.Validation">
      <summary>The validation function.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ValidateOptions`5">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IValidateOptions`1" /></summary>
      <typeparam name="TOptions">The options type to validate.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
      <typeparam name="TDep4">Fourth dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`5.#ctor(System.String,`1,`2,`3,`4,System.Func{`0,`1,`2,`3,`4,System.Boolean},System.String)">
      <summary>Constructor.</summary>
      <param name="name">Options name.</param>
      <param name="dependency1">The first dependency.</param>
      <param name="dependency2">The second dependency.</param>
      <param name="dependency3">The third dependency.</param>
      <param name="dependency4">The fourth dependency.</param>
      <param name="validation">Validation function.</param>
      <param name="failureMessage">Validation failure message.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`5.Validate(System.String,`0)">
      <summary>Validates a specific named options instance (or all when <paramref name="name" /> is null).</summary>
      <param name="name">The name of the options instance being validated.</param>
      <param name="options">The options instance.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Options.ValidateOptionsResult" /> result.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`5.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`5.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`5.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`5.Dependency4">
      <summary>The fourth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`5.FailureMessage">
      <summary>The error to return when validation fails.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`5.Name">
      <summary>The options name.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`5.Validation">
      <summary>The validation function.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ValidateOptions`6">
      <summary>Implementation of <see cref="T:Microsoft.Extensions.Options.IValidateOptions`1" /></summary>
      <typeparam name="TOptions">The options type to validate.</typeparam>
      <typeparam name="TDep1">First dependency type.</typeparam>
      <typeparam name="TDep2">Second dependency type.</typeparam>
      <typeparam name="TDep3">Third dependency type.</typeparam>
      <typeparam name="TDep4">Fourth dependency type.</typeparam>
      <typeparam name="TDep5">Fifth dependency type.</typeparam>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`6.#ctor(System.String,`1,`2,`3,`4,`5,System.Func{`0,`1,`2,`3,`4,`5,System.Boolean},System.String)">
      <summary>Constructor.</summary>
      <param name="name">Options name.</param>
      <param name="dependency1">The first dependency.</param>
      <param name="dependency2">The second dependency.</param>
      <param name="dependency3">The third dependency.</param>
      <param name="dependency4">The fourth dependency.</param>
      <param name="dependency5">The fifth dependency.</param>
      <param name="validation">Validation function.</param>
      <param name="failureMessage">Validation failure message.</param>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptions`6.Validate(System.String,`0)">
      <summary>Validates a specific named options instance (or all when <paramref name="name" /> is null).</summary>
      <param name="name">The name of the options instance being validated.</param>
      <param name="options">The options instance.</param>
      <returns>The <see cref="T:Microsoft.Extensions.Options.ValidateOptionsResult" /> result.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`6.Dependency1">
      <summary>The first dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`6.Dependency2">
      <summary>The second dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`6.Dependency3">
      <summary>The third dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`6.Dependency4">
      <summary>The fourth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`6.Dependency5">
      <summary>The fifth dependency.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`6.FailureMessage">
      <summary>The error to return when validation fails.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`6.Name">
      <summary>The options name.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptions`6.Validation">
      <summary>The validation function.</summary>
    </member>
    <member name="T:Microsoft.Extensions.Options.ValidateOptionsResult">
      <summary>Represents the result of an options validation.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Options.ValidateOptionsResult.Skip">
      <summary>Result when validation was skipped due to name not matching.</summary>
    </member>
    <member name="F:Microsoft.Extensions.Options.ValidateOptionsResult.Success">
      <summary>Validation was successful.</summary>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptionsResult.#ctor" />
    <member name="M:Microsoft.Extensions.Options.ValidateOptionsResult.Fail(System.Collections.Generic.IEnumerable{System.String})">
      <summary>Returns a failure result.</summary>
      <param name="failures">The reasons for the failure.</param>
      <returns>The failure result.</returns>
    </member>
    <member name="M:Microsoft.Extensions.Options.ValidateOptionsResult.Fail(System.String)">
      <summary>Returns a failure result.</summary>
      <param name="failureMessage">The reason for the failure.</param>
      <returns>The failure result.</returns>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptionsResult.Failed">
      <summary>True if validation failed.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptionsResult.FailureMessage">
      <summary>Used to describe why validation failed.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptionsResult.Failures">
      <summary>Full list of failures (can be multiple).</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptionsResult.Skipped">
      <summary>True if validation was not run.</summary>
    </member>
    <member name="P:Microsoft.Extensions.Options.ValidateOptionsResult.Succeeded">
      <summary>True if validation was successful.</summary>
    </member>
  </members>
</doc>