<?xml version="1.0"?>
<doc>
    <assembly>
        <name>itext.pdfa</name>
    </assembly>
    <members>
        <member name="T:iText.Pdfa.Checker.PdfA1Checker">
            <summary>
            PdfA1<PERSON><PERSON><PERSON> defines the requirements of the PDF/A-1 standard and contains
            method implementations from the abstract
            <see cref="T:iText.Pdfa.Checker.PdfAChecker"/>
            class.
            </summary>
            <remarks>
            PdfA1Checker defines the requirements of the PDF/A-1 standard and contains
            method implementations from the abstract
            <see cref="T:iText.Pdfa.Checker.PdfAChecker"/>
            class.
            <para />
            The specification implemented by this class is ISO 19005-1
            </remarks>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.#ctor(iText.Kernel.Pdf.PdfAConformanceLevel)">
            <summary>Creates a PdfA1Checker with the required conformance level</summary>
            <param name="conformanceLevel">
            the required conformance level, <c>a</c> or
            <c>b</c>
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckColor(iText.Kernel.Colors.Color,iText.Kernel.Pdf.PdfDictionary,System.Nullable{System.Boolean},iText.Kernel.Pdf.PdfStream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckColor(iText.Kernel.Pdf.Canvas.CanvasGraphicsState,iText.Kernel.Colors.Color,iText.Kernel.Pdf.PdfDictionary,System.Nullable{System.Boolean},iText.Kernel.Pdf.PdfStream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckColorSpace(iText.Kernel.Pdf.Colorspace.PdfColorSpace,iText.Kernel.Pdf.PdfObject,iText.Kernel.Pdf.PdfDictionary,System.Boolean,System.Nullable{System.Boolean})">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckColorsUsages">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckPageColorsUsages(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckCrypto(iText.Kernel.Pdf.PdfObject)">
            <summary><inheritDoc/></summary>
            <param name="crypto">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckSignatureType(System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckText(System.String,iText.Kernel.Font.PdfFont)">
            <summary><inheritDoc/></summary>
            <param name="text">
            
            <inheritDoc/>
            </param>
            <param name="font">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.GetMaxRealValue">
            <summary>Retrieve maximum allowed real value.</summary>
            <returns>maximum allowed real number</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.GetMaxIntegerValue">
            <summary>Retrieve maximal allowed integer value.</summary>
            <returns>maximal allowed integer number</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.GetMinIntegerValue">
            <summary>Retrieve minimal allowed integer value.</summary>
            <returns>minimal allowed integer number</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.GetMaxNameLength">
            <summary>Retrieve maximum allowed length of the name object.</summary>
            <returns>maximum allowed length of the name</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.GetMaxStringLength">
            <summary>Returns maximum allowed bytes length of the string in a PDF document.</summary>
            <returns>maximum string length</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckAnnotation(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.GetForbiddenAnnotations">
            <summary>Gets forbidden annotation types.</summary>
            <returns>a set of forbidden annotation types</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.CheckCatalog(iText.Kernel.Pdf.PdfCatalog)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA1Checker.GetFormFields(iText.Kernel.Pdf.PdfArray)">
            <summary>
            Gets a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of fields with kids from a
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            objects.
            </summary>
            <param name="array">
            the
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of form fields
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            objects
            </param>
            <returns>
            the
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            of form fields
            </returns>
        </member>
        <member name="T:iText.Pdfa.Checker.PdfA2Checker">
            <summary>
            PdfA2Checker defines the requirements of the PDF/A-2 standard and contains a
            number of methods that override the implementations of its superclass
            <see cref="T:iText.Pdfa.Checker.PdfA1Checker"/>.
            </summary>
            <remarks>
            PdfA2Checker defines the requirements of the PDF/A-2 standard and contains a
            number of methods that override the implementations of its superclass
            <see cref="T:iText.Pdfa.Checker.PdfA1Checker"/>.
            <para />
            The specification implemented by this class is ISO 19005-2
            </remarks>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.#ctor(iText.Kernel.Pdf.PdfAConformanceLevel)">
            <summary>Creates a PdfA2Checker with the required conformance level</summary>
            <param name="conformanceLevel">
            the required conformance level, <c>a</c> or
            <c>u</c> or <c>b</c>
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckColor(iText.Kernel.Colors.Color,iText.Kernel.Pdf.PdfDictionary,System.Nullable{System.Boolean},iText.Kernel.Pdf.PdfStream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckColor(iText.Kernel.Pdf.Canvas.CanvasGraphicsState,iText.Kernel.Colors.Color,iText.Kernel.Pdf.PdfDictionary,System.Nullable{System.Boolean},iText.Kernel.Pdf.PdfStream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckColorSpace(iText.Kernel.Pdf.Colorspace.PdfColorSpace,iText.Kernel.Pdf.PdfObject,iText.Kernel.Pdf.PdfDictionary,System.Boolean,System.Nullable{System.Boolean})">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckAnnotation(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.GetAppearanceLessAnnotations">
            <summary>Gets annotation types which are allowed not to have appearance stream.</summary>
            <returns>set of annotation names.</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckAnnotationAgainstActions(iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            Checked annotation against actions, exception will be thrown if either
            <c>A</c>
            or
            <c>AA</c>
            actions aren't allowed for specific type of annotation.
            </summary>
            <param name="annotDic">an annotation PDF dictionary</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.GetForbiddenAnnotations">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckCatalogAAConformance(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks if the catalog is compliant with the PDF/A-2 standard.</summary>
            <param name="dict">the catalog dictionary</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckPageAAConformance(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks if the page is compliant with the PDF/A-2 standard.</summary>
            <param name="dict">the page dictionary</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckPageColorsUsages(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckFormXObject(iText.Kernel.Pdf.PdfStream)">
            <summary>
            For pdf/a-2+ checkers use the
            <c>checkFormXObject(PdfStream form, PdfStream contentStream)</c>
            method
            </summary>
            <param name="form">
            the
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckFormXObject(iText.Kernel.Pdf.PdfStream,iText.Kernel.Pdf.PdfStream)">
            <summary>
            Verify the conformity of the Form XObject with appropriate
            specification.
            </summary>
            <remarks>
            Verify the conformity of the Form XObject with appropriate
            specification. Throws PdfAConformanceException if any discrepancy was found
            </remarks>
            <param name="form">
            the
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            to be checked
            </param>
            <param name="contentStream">
            the
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            current content stream
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckTransparencyGroup(iText.Kernel.Pdf.PdfStream,iText.Kernel.Pdf.PdfStream)">
            <summary>
            Verify the conformity of the transparency group XObject with appropriate
            specification.
            </summary>
            <remarks>
            Verify the conformity of the transparency group XObject with appropriate
            specification. Throws PdfAConformanceException if any discrepancy was found
            </remarks>
            <param name="form">
            the
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            transparency group XObject.
            </param>
            <param name="contentStream">
            the
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            current content stream
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckContentConfigurationDictAgainstAsKey(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Check optional content configuration dictionary against AS key.</summary>
            <param name="config">a content configuration dictionary</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.GetTransparencyErrorMessage">
            <summary>Retrieve transparency error message valid for the pdf/a standard being used.</summary>
            <returns>error message.</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA2Checker.CheckBlendMode(iText.Kernel.Pdf.PdfName)">
            <summary>Check if blendMode is compatible with pdf/a standard being used.</summary>
            <param name="blendMode">blend mode name to check.</param>
        </member>
        <member name="T:iText.Pdfa.Checker.PdfA3Checker">
            <summary>
            PdfA3Checker defines the requirements of the PDF/A-3 standard and contains a
            number of methods that override the implementations of its superclass
            <see cref="T:iText.Pdfa.Checker.PdfA2Checker"/>.
            </summary>
            <remarks>
            PdfA3Checker defines the requirements of the PDF/A-3 standard and contains a
            number of methods that override the implementations of its superclass
            <see cref="T:iText.Pdfa.Checker.PdfA2Checker"/>.
            The specification implemented by this class is ISO 19005-3
            </remarks>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA3Checker.#ctor(iText.Kernel.Pdf.PdfAConformanceLevel)">
            <summary>Creates a PdfA3Checker with the required conformance level</summary>
            <param name="conformanceLevel">
            the required conformance level, <c>a</c> or
            <c>u</c> or <c>b</c>
            </param>
        </member>
        <member name="T:iText.Pdfa.Checker.PdfA4Checker">
            <summary>
            PdfA4Checker defines the requirements of the PDF/A-4 standard and contains a
            number of methods that override the implementations of its superclass
            <see cref="T:iText.Pdfa.Checker.PdfA3Checker"/>.
            </summary>
            <remarks>
            PdfA4Checker defines the requirements of the PDF/A-4 standard and contains a
            number of methods that override the implementations of its superclass
            <see cref="T:iText.Pdfa.Checker.PdfA3Checker"/>.
            <para />
            The specification implemented by this class is ISO 19005-4
            </remarks>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.#ctor(iText.Kernel.Pdf.PdfAConformanceLevel)">
            <summary>Creates a PdfA4Checker with the required conformance level</summary>
            <param name="conformanceLevel">the required conformance level</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckColorSpace(iText.Kernel.Pdf.Colorspace.PdfColorSpace,iText.Kernel.Pdf.PdfObject,iText.Kernel.Pdf.PdfDictionary,System.Boolean,System.Nullable{System.Boolean})">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckPageColorsUsages(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckTrailer(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckCatalog(iText.Kernel.Pdf.PdfCatalog)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckPageObject(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckCatalogValidEntries(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckFileSpec(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckPageTransparency(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckCatalogAAConformance(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Check the conformity of the AA dictionary on catalog level.</summary>
            <param name="dict">the catalog dictionary</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckPageAAConformance(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Check the conformity of the AA dictionary on catalog level.</summary>
            <param name="dict">the catalog dictionary</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckPdfNumber(iText.Kernel.Pdf.PdfNumber)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckCanvasStack(System.Char)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckSignatureType(System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.GetMaxStringLength">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckNumberOfDeviceNComponents(iText.Kernel.Pdf.Colorspace.PdfSpecialCs.DeviceN)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckFormXObject(iText.Kernel.Pdf.PdfStream,iText.Kernel.Pdf.PdfStream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckAnnotation(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.GetForbiddenAnnotations">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.GetAppearanceLessAnnotations">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckWidgetAAConformance(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Check the conformity of the AA dictionary on widget level.</summary>
            <param name="dict">the widget dictionary</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckMetaData(iText.Kernel.Pdf.PdfDictionary)">
            <param name="catalog">
            the catalog
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckOutputIntents(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckAnnotationAgainstActions(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.GetForbiddenActions">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckContentConfigurationDictAgainstAsKey(iText.Kernel.Pdf.PdfDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.GetTransparencyErrorMessage">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.CheckBlendMode(iText.Kernel.Pdf.PdfName)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfA4Checker.GetMaxNameLength">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Pdfa.Checker.PdfAChecker">
            <summary>
            An abstract class that will run through all necessary checks defined in the
            different PDF/A standards and levels.
            </summary>
            <remarks>
            An abstract class that will run through all necessary checks defined in the
            different PDF/A standards and levels. A number of common checks are executed
            in this class, while standard-dependent specifications are implemented in the
            available subclasses. The standard that is followed is the series of ISO
            19005 specifications, currently generations 1 through 3. The ZUGFeRD standard
            is derived from ISO 19005-3.
            While it is possible to subclass this method and implement its abstract
            methods in client code, this is not encouraged and will have little effect.
            It is not possible to plug custom implementations into iText, because iText
            should always refuse to create non-compliant PDF/A, which would be possible
            with client code implementations. Any future generations of the PDF/A
            standard and its derivates will get their own implementation in the
            iText - pdfa project.
            </remarks>
        </member>
        <member name="F:iText.Pdfa.Checker.PdfAChecker.ICC_COLOR_SPACE_RGB">
            <summary>
            The Red-Green-Blue color profile as defined by the International Color
            Consortium.
            </summary>
        </member>
        <member name="F:iText.Pdfa.Checker.PdfAChecker.ICC_COLOR_SPACE_CMYK">
            <summary>
            The Cyan-Magenta-Yellow-Key (black) color profile as defined by the
            International Color Consortium.
            </summary>
        </member>
        <member name="F:iText.Pdfa.Checker.PdfAChecker.ICC_COLOR_SPACE_GRAY">
            <summary>
            The Grayscale color profile as defined by the International Color
            Consortium.
            </summary>
        </member>
        <member name="F:iText.Pdfa.Checker.PdfAChecker.ICC_DEVICE_CLASS_OUTPUT_PROFILE">
            <summary>The Output device class</summary>
        </member>
        <member name="F:iText.Pdfa.Checker.PdfAChecker.ICC_DEVICE_CLASS_MONITOR_PROFILE">
            <summary>The Monitor device class</summary>
        </member>
        <member name="F:iText.Pdfa.Checker.PdfAChecker.maxGsStackDepth">
            <summary>
            The maximum Graphics State stack depth in PDF/A documents, i.e. the
            maximum number of graphics state operators with code <c>q</c> that
            may be opened (i.e. not yet closed by a corresponding <c>Q</c>) at
            any point in a content stream sequence.
            </summary>
            <remarks>
            The maximum Graphics State stack depth in PDF/A documents, i.e. the
            maximum number of graphics state operators with code <c>q</c> that
            may be opened (i.e. not yet closed by a corresponding <c>Q</c>) at
            any point in a content stream sequence.
            Defined as 28 by PDF/A-1 section 6.1.12, by referring to the PDF spec
            Appendix C table 1 "architectural limits".
            </remarks>
        </member>
        <member name="F:iText.Pdfa.Checker.PdfAChecker.checkedObjects">
            <summary>Contains some objects that are already checked.</summary>
            <remarks>
            Contains some objects that are already checked.
            NOTE: Not all objects that were checked are stored in that set. This set is used for avoiding double checks for
            actions, signatures, xObjects and page objects; and for letting those objects to be manually flushed.
            Use this mechanism carefully: objects that are able to be changed (or at least if object's properties
            that shall be checked are able to be changed) shouldn't be marked as checked if they are not to be
            flushed immediately.
            </remarks>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.#ctor(iText.Kernel.Pdf.PdfAConformanceLevel)">
            <summary>Creates a PdfAChecker with the required conformance level.</summary>
            <param name="conformanceLevel">the required conformance level</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckDocument(iText.Kernel.Pdf.PdfCatalog)">
            <summary>
            This method checks a number of document-wide requirements of the PDF/A
            standard.
            </summary>
            <remarks>
            This method checks a number of document-wide requirements of the PDF/A
            standard. The algorithms of some of these checks vary with the PDF/A
            level and thus are implemented in subclasses; others are implemented
            as private methods in this class.
            </remarks>
            <param name="catalog">The catalog being checked</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckSinglePage(iText.Kernel.Pdf.PdfPage)">
            <summary>
            This method checks all requirements that must be fulfilled by a page in a
            PDF/A document.
            </summary>
            <param name="page">the page that must be checked</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPdfObject(iText.Kernel.Pdf.PdfObject)">
            <summary>
            This method checks the requirements that must be fulfilled by a COS
            object in a PDF/A document.
            </summary>
            <param name="obj">the COS object that must be checked</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.GetConformanceLevel">
            <summary>
            Gets the
            <see cref="T:iText.Kernel.Pdf.PdfAConformanceLevel"/>
            for this file.
            </summary>
            <returns>the defined conformance level for this document.</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.IsFullCheckMode">
            <summary>In full check mode all objects will be tested for ISO conformance.</summary>
            <remarks>
            In full check mode all objects will be tested for ISO conformance. If full check mode is
            switched off objects which were not modified might be skipped to speed up the validation
            of the document
            </remarks>
            <returns>true if full check mode is switched on</returns>
            <seealso cref="M:iText.Kernel.Pdf.PdfObject.IsModified"/>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.SetFullCheckMode(System.Boolean)">
            <summary>In full check mode all objects will be tested for ISO conformance.</summary>
            <remarks>
            In full check mode all objects will be tested for ISO conformance. If full check mode is
            switched off objects which were not modified might be skipped to speed up the validation
            of the document
            </remarks>
            <param name="fullCheckMode">is a new value for full check mode switcher</param>
            <seealso cref="M:iText.Kernel.Pdf.PdfObject.IsModified"/>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.ObjectIsChecked(iText.Kernel.Pdf.PdfObject)">
            <summary>
            Remembers which objects have already been checked, in order to avoid
            redundant checks.
            </summary>
            <param name="object">the object to check</param>
            <returns>whether or not the object has already been checked</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckTagStructureElement(iText.Kernel.Pdf.PdfObject)">
            <summary>
            This method checks compliance of the tag structure elements, such as struct elements
            or parent tree entries.
            </summary>
            <param name="obj">an object that represents tag structure element.</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckSignature(iText.Kernel.Pdf.PdfDictionary)">
            <summary>This method checks compliance of the signature dictionary</summary>
            <param name="signatureDict">
            a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            containing the signature.
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckSignatureType(System.Boolean)">
            <summary>This method checks compliance of the signature type</summary>
            <param name="isCAdES">true is CAdES sig type is used, false otherwise.</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckCanvasStack(System.Char)">
            <summary>
            This method checks compliance with the graphics state architectural
            limitation, explained by
            <see cref="F:iText.Pdfa.Checker.PdfAChecker.maxGsStackDepth"/>.
            </summary>
            <param name="stackOperation">the operation to check the graphics state counter for</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckInlineImage(iText.Kernel.Pdf.PdfStream,iText.Kernel.Pdf.PdfDictionary)">
            <summary>
            This method checks compliance with the inline image restrictions in the
            PDF/A specs, specifically filter parameters.
            </summary>
            <param name="inlineImage">
            a
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            containing the inline image
            </param>
            <param name="currentColorSpaces">
            a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            containing the color spaces used in the document
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckColor(iText.Kernel.Colors.Color,iText.Kernel.Pdf.PdfDictionary,System.Nullable{System.Boolean},iText.Kernel.Pdf.PdfStream)">
            <summary>
            This method checks compliance with the color restrictions imposed by the
            available color spaces in the document.
            </summary>
            <remarks>
            This method checks compliance with the color restrictions imposed by the
            available color spaces in the document.
            This method will be abstract in update 7.2
            </remarks>
            <param name="color">the color to check</param>
            <param name="currentColorSpaces">
            a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            containing the color spaces used in the document
            </param>
            <param name="fill">whether the color is used for fill or stroke operations</param>
            <param name="contentStream">current content stream</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckColor(iText.Kernel.Pdf.Canvas.CanvasGraphicsState,iText.Kernel.Colors.Color,iText.Kernel.Pdf.PdfDictionary,System.Nullable{System.Boolean},iText.Kernel.Pdf.PdfStream)">
            <summary>
            This method checks compliance with the color restrictions imposed by the
            available color spaces in the document.
            </summary>
            <param name="gState">canvas graphics state</param>
            <param name="color">the color to check</param>
            <param name="currentColorSpaces">
            a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            containing the color spaces used in the document
            </param>
            <param name="fill">whether the color is used for fill or stroke operations</param>
            <param name="contentStream">current content stream</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckColorSpace(iText.Kernel.Pdf.Colorspace.PdfColorSpace,iText.Kernel.Pdf.PdfObject,iText.Kernel.Pdf.PdfDictionary,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            This method performs a range of checks on the given color space, depending
            on the type and properties of that color space.
            </summary>
            <param name="colorSpace">the color space to check</param>
            <param name="pdfObject">the pdf object to check color space in</param>
            <param name="currentColorSpaces">
            a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            containing the color spaces used in the document
            </param>
            <param name="checkAlternate">whether or not to also check the parent color space</param>
            <param name="fill">whether the color space is used for fill or stroke operations</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckColorSpace(iText.Kernel.Pdf.Colorspace.PdfColorSpace,iText.Kernel.Pdf.PdfDictionary,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            This method performs a range of checks on the given color space, depending
            on the type and properties of that color space.
            </summary>
            <param name="colorSpace">the color space to check</param>
            <param name="currentColorSpaces">
            a
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            containing the color spaces used in the document
            </param>
            <param name="checkAlternate">whether or not to also check the parent color space</param>
            <param name="fill">whether the color space is used for fill or stroke operations</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.SetPdfAOutputIntentColorSpace(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Set Pdf A output intent color space.</summary>
            <param name="catalog">Catalog dictionary to retrieve the color space from.</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckRenderingIntent(iText.Kernel.Pdf.PdfName)">
            <summary>
            Checks whether the rendering intent of the document is within the allowed
            range of intents.
            </summary>
            <remarks>
            Checks whether the rendering intent of the document is within the allowed
            range of intents. This is defined in ISO 19005-1 section 6.2.9, and
            unchanged in newer generations of the PDF/A specification.
            </remarks>
            <param name="intent">the intent to be analyzed</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckFontGlyphs(iText.Kernel.Font.PdfFont,iText.Kernel.Pdf.PdfStream)">
            <summary>Performs a check of the each font glyph as a Form XObject.</summary>
            <remarks>
            Performs a check of the each font glyph as a Form XObject. See ISO 19005-2 Annex A.5.
            This only applies to type 3 fonts.
            This method will be abstract in update 7.2
            </remarks>
            <param name="font">
            
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            to be checked
            </param>
            <param name="contentStream">stream containing checked font</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckExtGState(iText.Kernel.Pdf.Canvas.CanvasGraphicsState,iText.Kernel.Pdf.PdfStream)">
            <summary>
            Performs a number of checks on the graphics state, among others ISO
            19005-1 section 6.2.8 and 6.4 and ISO 19005-2 section 6.2.5 and 6.2.10.
            </summary>
            <remarks>
            Performs a number of checks on the graphics state, among others ISO
            19005-1 section 6.2.8 and 6.4 and ISO 19005-2 section 6.2.5 and 6.2.10.
            This method will be abstract in the update 7.2
            </remarks>
            <param name="extGState">the graphics state to be checked</param>
            <param name="contentStream">current content stream</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckFont(iText.Kernel.Font.PdfFont)">
            <summary>Performs a number of checks on the font.</summary>
            <remarks>
            Performs a number of checks on the font. See ISO 19005-1 section 6.3,
            ISO 19005-2 and ISO 19005-3 section 6.2.11.
            Be aware that not all constraints defined in the ISO are checked in this method,
            for most of them we consider that iText always creates valid fonts.
            </remarks>
            <param name="pdfFont">font to be checked</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckXrefTable(iText.Kernel.Pdf.PdfXrefTable)">
            <summary>Verify the conformity of the cross-reference table.</summary>
            <param name="xrefTable">is the Xref table</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckCrypto(iText.Kernel.Pdf.PdfObject)">
            <summary>Verify the conformity of encryption usage.</summary>
            <param name="crypto">Encryption object to verify.</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckText(System.String,iText.Kernel.Font.PdfFont)">
            <summary>Verify the conformity of the text written by the specified font.</summary>
            <param name="text">Text to verify.</param>
            <param name="font">Font to verify the text against.</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckContentStream(iText.Kernel.Pdf.PdfStream)">
            <summary>Attest content stream conformance with appropriate specification.</summary>
            <remarks>
            Attest content stream conformance with appropriate specification.
            Throws PdfAConformanceException if any discrepancy was found
            </remarks>
            <param name="contentStream">is a content stream to validate</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckContentStreamObject(iText.Kernel.Pdf.PdfObject)">
            <summary>
            Verify the conformity of the operand of content stream with appropriate
            specification.
            </summary>
            <remarks>
            Verify the conformity of the operand of content stream with appropriate
            specification. Throws PdfAConformanceException if any discrepancy was found
            </remarks>
            <param name="object">is an operand of content stream to validate</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.GetMaxNumberOfIndirectObjects">
            <summary>Retrieve maximum allowed number of indirect objects in conforming document.</summary>
            <returns>maximum allowed number of indirect objects</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.GetForbiddenActions">
            <summary>Retrieve forbidden actions in conforming document.</summary>
            <returns>
            set of
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            with forbidden actions
            </returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.GetAllowedNamedActions">
            <summary>Retrieve allowed actions in conforming document.</summary>
            <returns>
            set of
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            with allowed named actions
            </returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckAction(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks if the action is allowed.</summary>
            <param name="action">to be checked</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckAnnotation(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the annotation dictionary.</summary>
            <param name="annotDic">
            the annotation
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to be checked
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckCatalogValidEntries(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks if entries in catalog dictionary are valid.</summary>
            <param name="catalogDict">
            the catalog
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to be checked
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckColorsUsages">
            <summary>Verify the conformity of used color spaces.</summary>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPageColorsUsages(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of used color spaces on the page.</summary>
            <param name="pageDict">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            contains contents for colors to be checked.
            </param>
            <param name="pageResources">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            contains resources for colors to be checked.
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckImage(iText.Kernel.Pdf.PdfStream,iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the given image.</summary>
            <param name="image">the image to check</param>
            <param name="currentColorSpaces">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            containing the color spaces used in the document
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckFileSpec(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the file specification dictionary.</summary>
            <param name="fileSpec">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            containing file specification to be checked
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckForm(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the form dictionary.</summary>
            <param name="form">
            the form
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to be checked
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckFormXObject(iText.Kernel.Pdf.PdfStream)">
            <summary>Verify the conformity of the form XObject dictionary.</summary>
            <param name="form">
            the
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckLogicalStructure(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Performs a number of checks on the logical structure of the document.</summary>
            <param name="catalog">
            the catalog
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckMetaData(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Performs a number of checks on the metadata of the document.</summary>
            <param name="catalog">
            the catalog
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckNonSymbolicTrueTypeFont(iText.Kernel.Font.PdfTrueTypeFont)">
            <summary>Verify the conformity of the non-symbolic TrueType font.</summary>
            <param name="trueTypeFont">
            the
            <see cref="T:iText.Kernel.Font.PdfTrueTypeFont"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckOutputIntents(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the output intents array in the catalog dictionary.</summary>
            <param name="catalogOrPageDict">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            of a catalog or page to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPageObject(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the page dictionary.</summary>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to check
            </param>
            <param name="pageResources">the page's resources dictionary</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPageSize(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks the allowable size of the page.</summary>
            <param name="page">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            of page which size being checked
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPdfArray(iText.Kernel.Pdf.PdfArray)">
            <summary>Verify the conformity of the PDF array.</summary>
            <param name="array">
            the
            <see cref="T:iText.Kernel.Pdf.PdfArray"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPdfDictionary(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the PDF dictionary.</summary>
            <param name="dictionary">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPdfName(iText.Kernel.Pdf.PdfName)">
            <summary>Verify the conformity of the PDF name.</summary>
            <param name="name">
            the
            <see cref="T:iText.Kernel.Pdf.PdfName"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPdfNumber(iText.Kernel.Pdf.PdfNumber)">
            <summary>Verify the conformity of the PDF number.</summary>
            <param name="number">
            the
            <see cref="T:iText.Kernel.Pdf.PdfNumber"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPdfStream(iText.Kernel.Pdf.PdfStream)">
            <summary>Verify the conformity of the PDF stream.</summary>
            <param name="stream">
            the
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPdfString(iText.Kernel.Pdf.PdfString)">
            <summary>Verify the conformity of the PDF string.</summary>
            <param name="string">
            the
            <see cref="T:iText.Kernel.Pdf.PdfString"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckSymbolicTrueTypeFont(iText.Kernel.Font.PdfTrueTypeFont)">
            <summary>Verify the conformity of the symbolic TrueType font.</summary>
            <param name="trueTypeFont">
            the
            <see cref="T:iText.Kernel.Font.PdfTrueTypeFont"/>
            to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckTrailer(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the trailer dictionary.</summary>
            <param name="trailer">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            of trailer to check
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckCatalog(iText.Kernel.Pdf.PdfCatalog)">
            <summary>Verify the conformity of the pdf catalog.</summary>
            <param name="catalog">
            the
            <see cref="T:iText.Kernel.Pdf.PdfCatalog"/>
            of trailer to check.
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckPageTransparency(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the page transparency.</summary>
            <param name="pageDict">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            contains contents for transparency to be checked
            </param>
            <param name="pageResources">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            contains resources for transparency to be checked
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckResources(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Verify the conformity of the resources dictionary.</summary>
            <param name="resources">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to be checked
            </param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckResources(iText.Kernel.Pdf.PdfDictionary,iText.Kernel.Pdf.PdfObject)">
            <summary>Verify the conformity of the resources dictionary.</summary>
            <param name="resources">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to be checked
            </param>
            <param name="pdfObject">the pdf object to check resources for</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckFlag(System.Int32,System.Int32)">
            <summary>Checks if the specified flag is set.</summary>
            <param name="flags">a set of flags specifying various characteristics of the PDF object</param>
            <param name="flag">to be checked</param>
            <returns>true if the specified flag is set</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckStructure(iText.Kernel.Pdf.PdfAConformanceLevel)">
            <summary>Checks conformance level of PDF/A standard.</summary>
            <param name="conformanceLevel">
            the
            <see cref="T:iText.Kernel.Pdf.PdfAConformanceLevel"/>
            to be checked
            </param>
            <returns>true if the specified conformanceLevel is <c>a</c> for PDF/A-1, PDF/A-2 or PDF/A-3</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.IsContainsTransparencyGroup(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks whether the specified dictionary has a transparency group.</summary>
            <param name="dictionary">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to check
            </param>
            <returns>
            true if and only if the specified dictionary has a
            <see cref="F:iText.Kernel.Pdf.PdfName.Group"/>
            key and its value is
            a dictionary with
            <see cref="F:iText.Kernel.Pdf.PdfName.Transparency"/>
            subtype
            </returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.IsAlreadyChecked(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks whether the specified dictionary was already checked.</summary>
            <param name="dictionary">
            the
            <see cref="T:iText.Kernel.Pdf.PdfDictionary"/>
            to check
            </param>
            <returns>true if the specified dictionary was checked</returns>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckResourcesOfAppearanceStreams(iText.Kernel.Pdf.PdfDictionary)">
            <summary>Checks resources of the appearance streams.</summary>
            <param name="appearanceStreamsDict">the dictionary with appearance streams to check.</param>
        </member>
        <member name="M:iText.Pdfa.Checker.PdfAChecker.CheckAppearanceStream(iText.Kernel.Pdf.PdfStream)">
            <summary>Check single annotation appearance stream.</summary>
            <param name="appearanceStream">
            the
            <see cref="T:iText.Kernel.Pdf.PdfStream"/>
            to check
            </param>
        </member>
        <member name="T:iText.Pdfa.Exceptions.PdfAConformanceException">
            <summary>Exception that is thrown when the PDF Document doesn't adhere to the PDF/A specification.</summary>
        </member>
        <member name="M:iText.Pdfa.Exceptions.PdfAConformanceException.#ctor(System.String)">
            <summary>Creates a PdfAConformanceException.</summary>
            <param name="message">the error message</param>
        </member>
        <member name="M:iText.Pdfa.Exceptions.PdfAConformanceException.#ctor(System.String,System.Object)">
            <summary>Creates a PdfAConformanceException.</summary>
            <param name="message">the error message</param>
            <param name="object">an object</param>
        </member>
        <member name="T:iText.Pdfa.Exceptions.PdfaExceptionMessageConstant">
            <summary>Class that bundles all the error message templates as constants.</summary>
        </member>
        <member name="T:iText.Pdfa.Logs.PdfAConformanceLogMessageConstant">
            <summary>Class containing pdfa conformance constants to be used in logging.</summary>
        </member>
        <member name="T:iText.Pdfa.Logs.PdfALogMessageConstant">
            <summary>Class containing constants to be used in logging.</summary>
        </member>
        <member name="T:iText.Pdfa.PdfAAgnosticPdfDocument">
            <summary>
            This class extends
            <see cref="T:iText.Pdfa.PdfADocument"/>
            and serves as
            <see cref="T:iText.Pdfa.PdfADocument"/>
            for
            PDF/A compliant documents and as
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            for non PDF/A documents.
            </summary>
            <remarks>
            This class extends
            <see cref="T:iText.Pdfa.PdfADocument"/>
            and serves as
            <see cref="T:iText.Pdfa.PdfADocument"/>
            for
            PDF/A compliant documents and as
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            for non PDF/A documents.
            <para />
            This class can throw various exceptions like
            <see cref="T:iText.Kernel.Exceptions.PdfException"/>
            as well as
            <see cref="T:iText.Pdfa.Exceptions.PdfAConformanceException"/>
            for PDF/A documents.
            </remarks>
        </member>
        <member name="M:iText.Pdfa.PdfAAgnosticPdfDocument.#ctor(iText.Kernel.Pdf.PdfReader,iText.Kernel.Pdf.PdfWriter)">
            <summary>Opens a PDF/A document in stamping mode.</summary>
            <param name="reader">
            the
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            </param>
            <param name="writer">
            the
            <see cref="T:iText.Kernel.Pdf.PdfWriter"/>
            object to write to
            </param>
        </member>
        <member name="M:iText.Pdfa.PdfAAgnosticPdfDocument.#ctor(iText.Kernel.Pdf.PdfReader,iText.Kernel.Pdf.PdfWriter,iText.Kernel.Pdf.StampingProperties)">
            <summary>Opens a PDF/A document in stamping mode.</summary>
            <param name="reader">
            the
            <see cref="T:iText.Kernel.Pdf.PdfReader"/>
            </param>
            <param name="writer">
            the
            <see cref="T:iText.Kernel.Pdf.PdfWriter"/>
            object to write to
            </param>
            <param name="properties">
            
            <see cref="T:iText.Kernel.Pdf.StampingProperties"/>
            of the stamping process
            </param>
        </member>
        <member name="T:iText.Pdfa.PdfADocument">
            <summary>
            This class extends
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            and is in charge of creating files
            that comply with the PDF/A standard.
            </summary>
            <remarks>
            This class extends
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            and is in charge of creating files
            that comply with the PDF/A standard.
            Client code is still responsible for making sure the file is actually PDF/A
            compliant: multiple steps must be undertaken (depending on the
            <see cref="T:iText.Kernel.Pdf.PdfAConformanceLevel"/>
            ) to ensure that the PDF/A standard is followed.
            This class will throw exceptions, mostly
            <see cref="T:iText.Pdfa.Exceptions.PdfAConformanceException"/>
            ,
            and thus refuse to output a PDF/A file if at any point the document does not
            adhere to the PDF/A guidelines specified by the
            <see cref="T:iText.Kernel.Pdf.PdfAConformanceLevel"/>.
            </remarks>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.#ctor(iText.Kernel.Pdf.PdfWriter,iText.Kernel.Pdf.PdfAConformanceLevel,iText.Kernel.Pdf.PdfOutputIntent)">
            <summary>Constructs a new PdfADocument for writing purposes, i.e. from scratch.</summary>
            <remarks>
            Constructs a new PdfADocument for writing purposes, i.e. from scratch. A
            PDF/A file has a conformance level, and must have an explicit output
            intent.
            </remarks>
            <param name="writer">
            the
            <see cref="T:iText.Kernel.Pdf.PdfWriter"/>
            object to write to
            </param>
            <param name="conformanceLevel">the generation and strictness level of the PDF/A that must be followed.</param>
            <param name="outputIntent">
            a
            <see cref="T:iText.Kernel.Pdf.PdfOutputIntent"/>
            </param>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.#ctor(iText.Kernel.Pdf.PdfWriter,iText.Kernel.Pdf.PdfAConformanceLevel,iText.Kernel.Pdf.PdfOutputIntent,iText.Kernel.Pdf.DocumentProperties)">
            <summary>Constructs a new PdfADocument for writing purposes, i.e. from scratch.</summary>
            <remarks>
            Constructs a new PdfADocument for writing purposes, i.e. from scratch. A
            PDF/A file has a conformance level, and must have an explicit output
            intent.
            </remarks>
            <param name="writer">
            the
            <see cref="T:iText.Kernel.Pdf.PdfWriter"/>
            object to write to
            </param>
            <param name="conformanceLevel">the generation and strictness level of the PDF/A that must be followed.</param>
            <param name="outputIntent">
            a
            <see cref="T:iText.Kernel.Pdf.PdfOutputIntent"/>
            </param>
            <param name="properties">
            a
            <see cref="T:iText.Kernel.Pdf.DocumentProperties"/>
            </param>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.GetDefaultFont">
            <summary>No default font for PDF/A documents.</summary>
            <returns>
            
            <see langword="null"/>.
            </returns>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.#ctor(iText.Kernel.Pdf.PdfReader,iText.Kernel.Pdf.PdfWriter)">
            <summary>Opens a PDF/A document in the stamping mode.</summary>
            <param name="reader">PDF reader.</param>
            <param name="writer">PDF writer.</param>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.#ctor(iText.Kernel.Pdf.PdfReader,iText.Kernel.Pdf.PdfWriter,iText.Kernel.Pdf.StampingProperties)">
            <summary>Opens a PDF/A document in stamping mode.</summary>
            <param name="reader">PDF reader.</param>
            <param name="writer">PDF writer.</param>
            <param name="properties">properties of the stamping process</param>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.GetConformanceLevel">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.AddOutputIntent(iText.Kernel.Pdf.PdfOutputIntent)">
            <summary><inheritDoc/></summary>
            <param name="outputIntent">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.CheckIsoConformance(System.Object,iText.Kernel.Pdf.IsoKey,iText.Kernel.Pdf.PdfResources,iText.Kernel.Pdf.PdfStream,System.Object)">
            <param name="obj">an object to conform.</param>
            <param name="key">type of object to conform.</param>
            <param name="resources">
            
            <see cref="T:iText.Kernel.Pdf.PdfResources"/>
            associated with an object to check.
            </param>
            <param name="contentStream">current content stream.</param>
            <param name="extra">extra data required for the check.</param>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.SetChecker(iText.Kernel.Pdf.PdfAConformanceLevel)">
            <summary>
            Sets the checker that defines the requirements of the PDF/A standard
            depending on conformance level.
            </summary>
            <param name="conformanceLevel">
            
            <see cref="T:iText.Kernel.Pdf.PdfAConformanceLevel"/>
            </param>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.InitTagStructureContext">
            <summary>Initializes tagStructureContext to track necessary information of document's tag structure.</summary>
        </member>
        <member name="M:iText.Pdfa.PdfADocument.FlushInfoDictionary(System.Boolean)">
            <summary><inheritDoc/></summary>
            <param name="appendMode">
            
            <inheritDoc/>
            </param>
        </member>
        <member name="T:iText.Pdfa.PdfAXMPUtil">
            <summary>Utilities to construct an XMP for a PDF/A file.</summary>
        </member>
    </members>
</doc>
