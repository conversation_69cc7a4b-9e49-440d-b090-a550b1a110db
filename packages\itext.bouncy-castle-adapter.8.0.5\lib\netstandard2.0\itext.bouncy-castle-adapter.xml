<?xml version="1.0"?>
<doc>
    <assembly>
        <name>itext.bouncy-castle-adapter</name>
    </assembly>
    <members>
        <member name="T:iText.Bouncycastle.Asn1.Asn1EncodableBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableBC.#ctor(Org.BouncyCastle.Asn1.Asn1Encodable)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>.
            </summary>
            <param name="encodable">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableBC.GetEncodable">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableBC.ToASN1Primitive">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableBC.IsNull">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1EncodableVector"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.#ctor">
            <summary>
            Creates new wrapper instance for new
            <see cref="T:Org.BouncyCastle.Asn1.Asn1EncodableVector"/>
            object.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.#ctor(Org.BouncyCastle.Asn1.Asn1EncodableVector)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1EncodableVector"/>.
            </summary>
            <param name="encodableVector">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1EncodableVector"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.GetEncodableVector">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1EncodableVector"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.Add(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.Add(iText.Commons.Bouncycastle.Asn1.Cms.IAttribute)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.Add(iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.AddOptional(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.AddOptional(iText.Commons.Bouncycastle.Asn1.Cms.IAttribute)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.AddOptional(iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1EncodableVectorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.ASN1EncodingBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.ASN1EncodingBC.#ctor(Org.BouncyCastle.Asn1.Asn1Encodable)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>.
            </summary>
            <param name="asn1Encoding">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.ASN1EncodingBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.ASN1EncodingBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.ASN1EncodingBC.GetASN1Encoding">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.ASN1EncodingBC.GetDer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.ASN1EncodingBC.GetBer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.ASN1EncodingBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.ASN1EncodingBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.ASN1EncodingBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Asn1InputStreamBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1InputStream"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.#ctor(Org.BouncyCastle.Asn1.Asn1InputStream)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1InputStream"/>.
            </summary>
            <param name="asn1InputStream">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1InputStream"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.#ctor(System.Byte[])">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1InputStream"/>.
            </summary>
            <param name="bytes">
            byte array to create
            <see cref="T:Org.BouncyCastle.Asn1.Asn1InputStream"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.#ctor(System.IO.Stream)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1InputStream"/>.
            </summary>
            <param name="stream">
            InputStream to create
            <see cref="T:Org.BouncyCastle.Asn1.Asn1InputStream"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.GetAsn1InputStream">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1InputStream"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.ReadObject">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.Dispose">
            <summary>
            Delegates
            <c>close</c>
            method call to the wrapped stream.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1InputStreamBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Asn1ObjectBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Object"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1ObjectBC.#ctor(Org.BouncyCastle.Asn1.Asn1Object)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Object"/>.
            </summary>
            <param name="primitive">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Object"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1ObjectBC.#ctor(System.Byte[])">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Object"/>.
            </summary>
            <param name="array">
            byte array to create
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Object"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1ObjectBC.GetPrimitive">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Object"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1ObjectBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1ObjectBC.GetEncoded(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Asn1OctetStringBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OctetString"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1OctetStringBC.#ctor(Org.BouncyCastle.Asn1.Asn1OctetString)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OctetString"/>.
            </summary>
            <param name="string">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OctetString"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1OctetStringBC.#ctor(iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject,System.Boolean)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OctetString"/>.
            </summary>
            <param name="taggedObject">
            ASN1TaggedObject wrapper to create
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OctetString"/>
            </param>
            <param name="b">
            boolean to create
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OctetString"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1OctetStringBC.GetAsn1OctetString">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OctetString"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1OctetStringBC.GetOctets">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1OctetStringBC.GetDerEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Asn1SequenceBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Sequence"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SequenceBC.#ctor(Org.BouncyCastle.Asn1.Asn1Sequence)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Sequence"/>.
            </summary>
            <param name="sequence">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Sequence"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SequenceBC.#ctor(System.Object)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Sequence"/>.
            </summary>
            <param name="obj">
            to get
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Sequence"/>
            instance to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SequenceBC.GetAsn1Sequence">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Sequence"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SequenceBC.GetObjectAt(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SequenceBC.GetObjects">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SequenceBC.Size">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SequenceBC.ToArray">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Asn1SetBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Set"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SetBC.#ctor(Org.BouncyCastle.Asn1.Asn1Set)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Set"/>.
            </summary>
            <param name="set">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Set"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SetBC.#ctor(Org.BouncyCastle.Asn1.Asn1TaggedObject,System.Boolean)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Set"/>.
            </summary>
            <param name="taggedObject">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1TaggedObject"/>
            to create
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Set"/>
            to be wrapped
            </param>
            <param name="b">
            boolean to create
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Set"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SetBC.GetAsn1Set">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Set"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SetBC.GetObjects">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SetBC.Size">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SetBC.GetObjectAt(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1SetBC.ToArray">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Asn1TaggedObjectBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1TaggedObject"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1TaggedObjectBC.#ctor(Org.BouncyCastle.Asn1.Asn1TaggedObject)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1TaggedObject"/>.
            </summary>
            <param name="taggedObject">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1TaggedObject"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1TaggedObjectBC.GetAsn1TaggedObject">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1TaggedObject"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1TaggedObjectBC.GetObject">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Asn1TaggedObjectBC.GetTagNo">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cmp.PkiFailureInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cmp.PkiFailureInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cmp.PkiFailureInfoBC.#ctor(Org.BouncyCastle.Asn1.Cmp.PkiFailureInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cmp.PkiFailureInfo"/>.
            </summary>
            <param name="pkiFailureInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cmp.PkiFailureInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cmp.PkiFailureInfoBC.GetPkiFailureInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cmp.PkiFailureInfo"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cmp.PkiFailureInfoBC.IntValue">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.AttributeBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.Attribute"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeBC.#ctor(Org.BouncyCastle.Asn1.Cms.Attribute)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.Attribute"/>.
            </summary>
            <param name="attribute">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.Attribute"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeBC.GetAttribute">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.Attribute"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeBC.GetAttrValues">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.AttributeTableBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.AttributeTable"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeTableBC.#ctor(Org.BouncyCastle.Asn1.Cms.AttributeTable)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.AttributeTable"/>.
            </summary>
            <param name="attributeTable">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.AttributeTable"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeTableBC.#ctor(Org.BouncyCastle.Asn1.Asn1Set)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.AttributeTable"/>.
            </summary>
            <param name="set">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Set"/>
            to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.AttributeTable"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeTableBC.GetAttributeTable">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.AttributeTable"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeTableBC.Get(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeTableBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeTableBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.AttributeTableBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.ContentInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.ContentInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.ContentInfoBC.#ctor(Org.BouncyCastle.Asn1.Cms.ContentInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.ContentInfo"/>.
            </summary>
            <param name="contentInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.ContentInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.ContentInfoBC.#ctor(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.ContentInfo"/>.
            </summary>
            <param name="objectIdentifier">ASN1ObjectIdentifier wrapper</param>
            <param name="encodable">ASN1Encodable wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.ContentInfoBC.GetContentInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.ContentInfo"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.EncryptedContentInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EncryptedContentInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.EncryptedContentInfoBC.#ctor(Org.BouncyCastle.Asn1.Cms.EncryptedContentInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EncryptedContentInfo"/>.
            </summary>
            <param name="encryptedContentInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EncryptedContentInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.EncryptedContentInfoBC.#ctor(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EncryptedContentInfo"/>.
            </summary>
            <param name="data">ASN1ObjectIdentifier wrapper</param>
            <param name="algorithmIdentifier">AlgorithmIdentifier wrapper</param>
            <param name="octetString">ASN1OctetString wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.EncryptedContentInfoBC.GetEncryptedContentInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EncryptedContentInfo"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.EnvelopedDataBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.EnvelopedDataBC.#ctor(Org.BouncyCastle.Asn1.Cms.EnvelopedData)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>.
            </summary>
            <param name="envelopedData">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.EnvelopedDataBC.#ctor(iText.Commons.Bouncycastle.Asn1.Cms.IOriginatorInfo,iText.Commons.Bouncycastle.Asn1.IAsn1Set,iText.Commons.Bouncycastle.Asn1.Cms.IEncryptedContentInfo,iText.Commons.Bouncycastle.Asn1.IAsn1Set)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>.
            </summary>
            <param name="originatorInfo">
            OriginatorInfo wrapper to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>
            </param>
            <param name="set">
            ASN1Set wrapper to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>
            </param>
            <param name="encryptedContentInfo">
            EncryptedContentInfo wrapper to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>
            </param>
            <param name="set1">
            ASN1Set wrapper to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.EnvelopedDataBC.GetEnvelopedData">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.EnvelopedData"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.IssuerAndSerialNumberBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.IssuerAndSerialNumber"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.IssuerAndSerialNumberBC.#ctor(Org.BouncyCastle.Asn1.Cms.IssuerAndSerialNumber)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.IssuerAndSerialNumber"/>.
            </summary>
            <param name="issuerAndSerialNumber">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.IssuerAndSerialNumber"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.IssuerAndSerialNumberBC.#ctor(iText.Commons.Bouncycastle.Asn1.X500.IX500Name,Org.BouncyCastle.Math.BigInteger)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.IssuerAndSerialNumber"/>.
            </summary>
            <param name="issuer">
            X500Name wrapper to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.IssuerAndSerialNumber"/>
            </param>
            <param name="value">
            BigInteger to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.IssuerAndSerialNumber"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.IssuerAndSerialNumberBC.GetIssuerAndSerialNumber">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.IssuerAndSerialNumber"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.KeyTransRecipientInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.KeyTransRecipientInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.KeyTransRecipientInfoBC.#ctor(Org.BouncyCastle.Asn1.Cms.KeyTransRecipientInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.KeyTransRecipientInfo"/>.
            </summary>
            <param name="keyTransRecipientInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.KeyTransRecipientInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.KeyTransRecipientInfoBC.#ctor(iText.Commons.Bouncycastle.Asn1.Cms.IRecipientIdentifier,iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.KeyTransRecipientInfo"/>.
            </summary>
            <param name="recipientIdentifier">RecipientIdentifier wrapper</param>
            <param name="algorithmIdentifier">AlgorithmIdentifier wrapper</param>
            <param name="octetString">ASN1OctetString wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.KeyTransRecipientInfoBC.GetKeyTransRecipientInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.KeyTransRecipientInfo"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.OriginatorInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.OriginatorInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.OriginatorInfoBC.#ctor(Org.BouncyCastle.Asn1.Cms.OriginatorInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.OriginatorInfo"/>.
            </summary>
            <param name="originatorInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.OriginatorInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.OriginatorInfoBC.GetOriginatorInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.OriginatorInfo"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.RecipientIdentifierBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientIdentifier"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.RecipientIdentifierBC.#ctor(Org.BouncyCastle.Asn1.Cms.RecipientIdentifier)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientIdentifier"/>.
            </summary>
            <param name="recipientIdentifier">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientIdentifier"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.RecipientIdentifierBC.#ctor(iText.Commons.Bouncycastle.Asn1.Cms.IIssuerAndSerialNumber)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientIdentifier"/>.
            </summary>
            <param name="issuerAndSerialNumber">
            IssuerAndSerialNumber wrapper to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientIdentifier"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.RecipientIdentifierBC.GetRecipientIdentifier">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientIdentifier"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Cms.RecipientInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.RecipientInfoBC.#ctor(Org.BouncyCastle.Asn1.Cms.RecipientInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientInfo"/>.
            </summary>
            <param name="recipientInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.RecipientInfoBC.#ctor(iText.Commons.Bouncycastle.Asn1.Cms.IKeyTransRecipientInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientInfo"/>.
            </summary>
            <param name="keyTransRecipientInfo">
            KeyTransRecipientInfo to create
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientInfo"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Cms.RecipientInfoBC.GetRecipientInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Cms.RecipientInfo"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerBitStringBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerBitString"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerBitStringBC.#ctor(Org.BouncyCastle.Asn1.DerBitString)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerBitString"/>.
            </summary>
            <param name="asn1BitString">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerBitString"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerBitStringBC.GetDerBitString">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerBitString"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerBitStringBC.GetString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerBitStringBC.IntValue">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerEnumeratedBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerEnumerated"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerEnumeratedBC.#ctor(Org.BouncyCastle.Asn1.DerEnumerated)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerEnumerated"/>.
            </summary>
            <param name="asn1Enumerated">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerEnumerated"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerEnumeratedBC.#ctor(System.Int32)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerEnumerated"/>.
            </summary>
            <param name="i">
            int value to create
            <see cref="T:Org.BouncyCastle.Asn1.DerEnumerated"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerEnumeratedBC.GetDerEnumerated">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerEnumerated"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerEnumeratedBC.IntValueExact">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerIA5StringBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerIA5String"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIA5StringBC.#ctor(Org.BouncyCastle.Asn1.DerIA5String)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerIA5String"/>.
            </summary>
            <param name="deria5String">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerIA5String"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIA5StringBC.#ctor(System.String)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerIA5String"/>.
            </summary>
            <param name="str">
            string to create
            <see cref="T:Org.BouncyCastle.Asn1.DerIA5String"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIA5StringBC.GetDerIA5String">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerIA5String"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIA5StringBC.GetString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerIntegerBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerInteger"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIntegerBC.#ctor(Org.BouncyCastle.Asn1.DerInteger)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerInteger"/>.
            </summary>
            <param name="i">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerInteger"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIntegerBC.#ctor(System.Int32)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerInteger"/>.
            </summary>
            <param name="i">
            int value to create
            <see cref="T:Org.BouncyCastle.Asn1.DerInteger"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIntegerBC.#ctor(iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerInteger"/>.
            </summary>
            <param name="i">
            BigInteger value to create
            <see cref="T:Org.BouncyCastle.Asn1.DerInteger"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIntegerBC.GetDerInteger">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerInteger"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerIntegerBC.GetValue">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerNullBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerNull"/>.
            </summary>
        </member>
        <member name="F:iText.Bouncycastle.Asn1.DerNullBC.INSTANCE">
            <summary>
            Wrapper for
            <see cref="T:Org.BouncyCastle.Asn1.DerNull"/>
            INSTANCE.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerNullBC.#ctor(Org.BouncyCastle.Asn1.DerNull)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerNull"/>.
            </summary>
            <param name="derNull">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerNull"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerNullBC.GetDerNull">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerNull"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerObjectIdentifierBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerObjectIdentifier"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerObjectIdentifierBC.#ctor(System.String)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerObjectIdentifier"/>.
            </summary>
            <param name="identifier">
            string to create
            <see cref="T:Org.BouncyCastle.Asn1.DerObjectIdentifier"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerObjectIdentifierBC.#ctor(Org.BouncyCastle.Asn1.DerObjectIdentifier)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerObjectIdentifier"/>.
            </summary>
            <param name="identifier">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerObjectIdentifier"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerObjectIdentifierBC.GetDerObjectIdentifier">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerObjectIdentifier"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerObjectIdentifierBC.GetId">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerOctetStringBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerOctetString"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOctetStringBC.#ctor(System.Byte[])">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerOctetString"/>.
            </summary>
            <param name="bytes">
            byte array to create
            <see cref="T:Org.BouncyCastle.Asn1.DerOctetString"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOctetStringBC.#ctor(Org.BouncyCastle.Asn1.DerOctetString)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerOctetString"/>.
            </summary>
            <param name="octetString">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerOctetString"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOctetStringBC.GetDerOctetString">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerOctetString"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerOutputStreamBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OutputStream"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOutputStreamBC.#ctor(System.IO.Stream)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OutputStream"/>.
            </summary>
            <param name="stream">
            OutputStream to create
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OutputStream"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOutputStreamBC.#ctor(Org.BouncyCastle.Asn1.Asn1OutputStream)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OutputStream"/>.
            </summary>
            <param name="stream">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OutputStream"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOutputStreamBC.GetDerOutputStream">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1OutputStream"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOutputStreamBC.WriteObject(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOutputStreamBC.Dispose">
            <summary>
            Delegates
            <c>close</c>
            method call to the wrapped stream.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOutputStreamBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOutputStreamBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerOutputStreamBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerSequenceBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerSequence"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerSequenceBC.#ctor(Org.BouncyCastle.Asn1.DerSequence)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerSequence"/>.
            </summary>
            <param name="derSequence">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerSequence"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerSequenceBC.#ctor(Org.BouncyCastle.Asn1.Asn1EncodableVector)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerSequence"/>.
            </summary>
            <param name="vector">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1EncodableVector"/>
            to create
            <see cref="T:Org.BouncyCastle.Asn1.DerSequence"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerSequenceBC.#ctor(Org.BouncyCastle.Asn1.Asn1Encodable)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerSequence"/>.
            </summary>
            <param name="encodable">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>
            to create
            <see cref="T:Org.BouncyCastle.Asn1.DerSequence"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerSequenceBC.GetDerSequence">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerSequence"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerSetBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerSet"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerSetBC.#ctor(Org.BouncyCastle.Asn1.DerSet)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerSet"/>.
            </summary>
            <param name="derSet">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerSet"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerSetBC.#ctor(Org.BouncyCastle.Asn1.Asn1EncodableVector)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerSet"/>.
            </summary>
            <param name="vector">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1EncodableVector"/>
            to create
            <see cref="T:Org.BouncyCastle.Asn1.DerSet"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerSetBC.#ctor(Org.BouncyCastle.Asn1.Asn1Encodable)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerSet"/>.
            </summary>
            <param name="encodable">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>
            to create
            <see cref="T:Org.BouncyCastle.Asn1.DerSet"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerSetBC.GetDerSet">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerSet"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerStringBaseBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerStringBase"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerStringBaseBC.#ctor(Org.BouncyCastle.Asn1.DerStringBase)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerStringBase"/>.
            </summary>
            <param name="asn1String">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerStringBase"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerStringBaseBC.GetDerStringBase">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerStringBase"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerStringBaseBC.GetString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerStringBaseBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerStringBaseBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerStringBaseBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerTaggedObjectBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerTaggedObjectBC.#ctor(Org.BouncyCastle.Asn1.DerTaggedObject)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>.
            </summary>
            <param name="derTaggedObject">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerTaggedObjectBC.#ctor(System.Int32,Org.BouncyCastle.Asn1.Asn1Encodable)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>.
            </summary>
            <param name="i">
            int value to create
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>
            to be wrapped
            </param>
            <param name="encodable">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>
            to create
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerTaggedObjectBC.#ctor(System.Boolean,System.Int32,Org.BouncyCastle.Asn1.Asn1Encodable)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>.
            </summary>
            <param name="b">
            boolean to create
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>
            to be wrapped
            </param>
            <param name="i">
            int value to create
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>
            to be wrapped
            </param>
            <param name="encodable">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1Encodable"/>
            to create
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerTaggedObjectBC.GetDerTaggedObject">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerTaggedObject"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.DerUtcTimeBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.DerUtcTime"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerUtcTimeBC.#ctor(Org.BouncyCastle.Asn1.DerUtcTime)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.DerUtcTime"/>.
            </summary>
            <param name="asn1UTCTime">
            
            <see cref="T:Org.BouncyCastle.Asn1.DerUtcTime"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.DerUtcTimeBC.GetDerUtcTime">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.DerUtcTime"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Esf.OtherHashAlgAndValueBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.OtherHashAlgAndValue"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.OtherHashAlgAndValueBC.#ctor(Org.BouncyCastle.Asn1.Esf.OtherHashAlgAndValue)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.OtherHashAlgAndValue"/>.
            </summary>
            <param name="otherHashAlgAndValue">
            
            <see cref="T:Org.BouncyCastle.Asn1.Esf.OtherHashAlgAndValue"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.OtherHashAlgAndValueBC.#ctor(iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.OtherHashAlgAndValue"/>.
            </summary>
            <param name="algorithmIdentifier">AlgorithmIdentifier wrapper</param>
            <param name="octetString">ASN1OctetString wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.OtherHashAlgAndValueBC.GetOtherHashAlgAndValue">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Esf.OtherHashAlgAndValue"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyId"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdBC.#ctor(Org.BouncyCastle.Asn1.Esf.SignaturePolicyId)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyId"/>.
            </summary>
            <param name="signaturePolicyId">
            
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyId"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdBC.#ctor(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.Esf.IOtherHashAlgAndValue,Org.BouncyCastle.Asn1.Esf.SigPolicyQualifierInfo[])">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyId"/>.
            </summary>
            <param name="objectIdentifier">ASN1ObjectIdentifier wrapper</param>
            <param name="algAndValue">OtherHashAlgAndValue wrapper</param>
            <param name="policyQualifiers">SigPolicyQualifierInfo array</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdBC.#ctor(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.Esf.IOtherHashAlgAndValue)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyId"/>.
            </summary>
            <param name="objectIdentifier">ASN1ObjectIdentifier wrapper</param>
            <param name="algAndValue">OtherHashAlgAndValue wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdBC.GetSignaturePolicyId">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyId"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdentifierBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyIdentifier"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdentifierBC.#ctor(Org.BouncyCastle.Asn1.Esf.SignaturePolicyIdentifier)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyIdentifier"/>.
            </summary>
            <param name="signaturePolicyIdentifier">
            
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyIdentifier"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdentifierBC.#ctor(iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyId)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyIdentifier"/>.
            </summary>
            <param name="signaturePolicyId">SignaturePolicyId wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SignaturePolicyIdentifierBC.GetSignaturePolicyIdentifier">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SignaturePolicyIdentifier"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Esf.SigPolicyQualifierInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SigPolicyQualifierInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SigPolicyQualifierInfoBC.#ctor(Org.BouncyCastle.Asn1.Esf.SigPolicyQualifierInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SigPolicyQualifierInfo"/>.
            </summary>
            <param name="qualifierInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SigPolicyQualifierInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SigPolicyQualifierInfoBC.#ctor(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IDerIA5String)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SigPolicyQualifierInfo"/>.
            </summary>
            <param name="objectIdentifier">ASN1ObjectIdentifier wrapper</param>
            <param name="string">DERIA5String wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Esf.SigPolicyQualifierInfoBC.GetSigPolicyQualifierInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Esf.SigPolicyQualifierInfo"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ess.ESSCertIDBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ess.EssCertID"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.ESSCertIDBC.#ctor(Org.BouncyCastle.Asn1.Ess.EssCertID)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ess.EssCertID"/>.
            </summary>
            <param name="essCertID">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ess.EssCertID"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.ESSCertIDBC.GetEssCertID">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ess.EssCertID"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.ESSCertIDBC.GetCertHash">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ess.ESSCertIDv2BC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ess.EssCertIDv2"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.ESSCertIDv2BC.#ctor(Org.BouncyCastle.Asn1.Ess.EssCertIDv2)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ess.EssCertIDv2"/>.
            </summary>
            <param name="essCertIDv2">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ess.EssCertIDv2"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.ESSCertIDv2BC.GetEssCertIDv2">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ess.EssCertIDv2"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.ESSCertIDv2BC.GetHashAlgorithm">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.ESSCertIDv2BC.GetCertHash">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ess.SigningCertificateBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ess.SigningCertificate"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.SigningCertificateBC.#ctor(Org.BouncyCastle.Asn1.Ess.SigningCertificate)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ess.SigningCertificate"/>.
            </summary>
            <param name="signingCertificate">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ess.SigningCertificate"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.SigningCertificateBC.GetSigningCertificate">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ess.SigningCertificate"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.SigningCertificateBC.GetCerts">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ess.SigningCertificateV2BC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ess.SigningCertificateV2"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.SigningCertificateV2BC.#ctor(Org.BouncyCastle.Asn1.Ess.SigningCertificateV2)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ess.SigningCertificateV2"/>.
            </summary>
            <param name="signingCertificateV2">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ess.SigningCertificateV2"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.SigningCertificateV2BC.GetSigningCertificateV2">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ess.SigningCertificateV2"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ess.SigningCertificateV2BC.GetCerts">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.IAsn1GeneralizedTimeBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1GeneralizedTime"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.IAsn1GeneralizedTimeBC.#ctor(Org.BouncyCastle.Asn1.Asn1GeneralizedTime)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Asn1GeneralizedTime"/>.
            </summary>
            <param name="asn1GeneralizedTime">
            
            <see cref="T:Org.BouncyCastle.Asn1.Asn1GeneralizedTime"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.IAsn1GeneralizedTimeBC.GetAsn1GeneralizedTime">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Asn1GeneralizedTime"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.IAsn1GeneralizedTimeBC.GetDate">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.BasicOcspResponse"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.BasicOcspResponse)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.BasicOcspResponse"/>.
            </summary>
            <param name="basicOCSPResponse">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.BasicOcspResponse"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.GetBasicOcspResponse">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.BasicOcspResponse"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.GetProducedAtDate">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.GetCerts">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.GetOcspCerts">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.GetResponses">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.GetProducedAt">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.BasicOcspResponseBC.GetExtensionParsedValue(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspObjectIdentifiers"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.OcspObjectIdentifiers)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspObjectIdentifiers"/>.
            </summary>
            <param name="ocspObjectIdentifiers">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspObjectIdentifiers"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.GetOcspObjectIdentifiers">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspObjectIdentifiers"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.GetIdPkixOcspBasic">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.GetIdPkixOcspNonce">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.GetIdPkixOcspNoCheck">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.GetIdPkixOcspArchiveCutoff">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspObjectIdentifiersBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponse"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.OcspResponse)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponse"/>.
            </summary>
            <param name="ocspResponse">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponse"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC.#ctor(iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponseStatus,iText.Commons.Bouncycastle.Asn1.Ocsp.IResponseBytes)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponse"/>.
            </summary>
            <param name="respStatus">OCSPResponseStatus wrapper</param>
            <param name="responseBytes">ResponseBytes wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC.GetOcspResponse">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponse"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC.GetStatus">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC.GetResponseObject">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseBC.GetSuccessful">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ocsp.OcspResponseStatusBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponseStatus"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseStatusBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.OcspResponseStatus)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponseStatus"/>.
            </summary>
            <param name="ocspResponseStatus">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponseStatus"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseStatusBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.Ocsp.OcspResponseStatusBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseStatusBC.GetOcspResponseStatus">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.OcspResponseStatus"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.OcspResponseStatusBC.GetSuccessful">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Ocsp.ResponseBytesBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.ResponseBytes"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.ResponseBytesBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.ResponseBytes)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.ResponseBytes"/>.
            </summary>
            <param name="responseBytes">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.ResponseBytes"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.ResponseBytesBC.#ctor(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IDerOctetString)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.ResponseBytes"/>.
            </summary>
            <param name="asn1ObjectIdentifier">ASN1ObjectIdentifier wrapper</param>
            <param name="derOctetString">DEROctetString wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Ocsp.ResponseBytesBC.GetResponseBytes">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.ResponseBytes"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Pkcs.PkcsObjectIdentifiers"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.#ctor(Org.BouncyCastle.Asn1.Pkcs.PkcsObjectIdentifiers)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Pkcs.PkcsObjectIdentifiers"/>.
            </summary>
            <param name="pkcsObjectIdentifiers">
            
            <see cref="T:Org.BouncyCastle.Asn1.Pkcs.PkcsObjectIdentifiers"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.GetPkcsObjectIdentifiers">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Pkcs.PkcsObjectIdentifiers"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.GetIdAaSignatureTimeStampToken">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.GetIdAaEtsSigPolicyId">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.GetIdSpqEtsUri">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.GetEnvelopedData">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.GetData">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.PkcsObjectIdentifiersBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Pcks.RsassaPssParametersBC">
            <summary>
            BC wrapper implementation for
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Pkcs.IRsassaPssParameters"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.RsassaPssParametersBC.#ctor(Org.BouncyCastle.Asn1.Pkcs.RsassaPssParameters)">
            <summary>
            Creates new wrapper instance for
            <see cref="!:Org.BouncyCastle.Asn1.Pkcs.RSASSAPSSparams"/>.
            </summary>
            <param name="params">
            
            <see cref="!:Org.BouncyCastle.Asn1.Pkcs.RSASSAPSSparams"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.RsassaPssParametersBC.GetRsassaPssParameters">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Pkcs.RsassaPssParameters"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.RsassaPssParametersBC.GetHashAlgorithm">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.RsassaPssParametersBC.GetMaskGenAlgorithm">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.RsassaPssParametersBC.GetSaltLength">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Pcks.RsassaPssParametersBC.GetTrailerField">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Tsp.MessageImprintBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Tsp.MessageImprint"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Tsp.MessageImprintBC.#ctor(Org.BouncyCastle.Asn1.Tsp.MessageImprint)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Tsp.MessageImprint"/>.
            </summary>
            <param name="messageImprint">
            
            <see cref="T:Org.BouncyCastle.Asn1.Tsp.MessageImprint"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Tsp.MessageImprintBC.GetMessageImprint">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Tsp.MessageImprint"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Tsp.MessageImprintBC.GetHashedMessage">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Tsp.MessageImprintBC.GetHashAlgorithm">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Tsp.TstInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Tsp.TstInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Tsp.TstInfoBC.#ctor(Org.BouncyCastle.Asn1.Tsp.TstInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Tsp.TstInfo"/>.
            </summary>
            <param name="tstInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.Tsp.TstInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Tsp.TstInfoBC.GetTstInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Tsp.TstInfo"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Tsp.TstInfoBC.GetMessageImprint">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Tsp.TstInfoBC.GetGenTime">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.Util.Asn1DumpBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Utilities.Asn1Dump"/>.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Bouncycastle.Asn1.Util.Asn1DumpBC.#ctor" -->
        <member name="M:iText.Bouncycastle.Asn1.Util.Asn1DumpBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.Util.Asn1DumpBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Util.Asn1DumpBC.DumpAsString(System.Object,System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.Util.Asn1DumpBC.DumpAsString(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.AlgorithmIdentifierBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.AlgorithmIdentifier"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.AlgorithmIdentifierBC.#ctor(Org.BouncyCastle.Asn1.X509.AlgorithmIdentifier)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.AlgorithmIdentifier"/>.
            </summary>
            <param name="algorithmIdentifier">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.AlgorithmIdentifier"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.AlgorithmIdentifierBC.GetAlgorithmIdentifier">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.AlgorithmIdentifier"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.AlgorithmIdentifierBC.GetAlgorithm">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.AlgorithmIdentifierBC.GetParameters">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.AuthorityKeyIdentifierBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.AuthorityKeyIdentifier"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.AuthorityKeyIdentifierBC.#ctor(Org.BouncyCastle.Asn1.X509.AuthorityKeyIdentifier)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.AuthorityKeyIdentifier"/>.
            </summary>
            <param name="authorityKeyIdentifier">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.AuthorityKeyIdentifier"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.AuthorityKeyIdentifierBC.GetAuthorityKeyIdentifier">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.AuthorityKeyIdentifier"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.BasicConstraintsBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.BasicConstraints"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.BasicConstraintsBC.#ctor(Org.BouncyCastle.Asn1.X509.BasicConstraints)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.BasicConstraints"/>.
            </summary>
            <param name="basicConstraints">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.BasicConstraints"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.BasicConstraintsBC.GetBasicConstraints">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.BasicConstraints"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.CrlDistPointBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.CrlDistPoint"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.CrlDistPointBC.#ctor(Org.BouncyCastle.Asn1.X509.CrlDistPoint)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.CrlDistPoint"/>.
            </summary>
            <param name="crlDistPoint">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.CrlDistPoint"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.CrlDistPointBC.GetCrlDistPoint">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.CrlDistPoint"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.CrlDistPointBC.GetDistributionPoints">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.CrlReasonBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.CrlReason"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.CrlReasonBC.#ctor(Org.BouncyCastle.Asn1.X509.CrlReason)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.CrlReason"/>.
            </summary>
            <param name="reason">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.CrlReason"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.CrlReasonBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.X509.CrlReasonBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.CrlReasonBC.GetCrlReason">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.CrlReason"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.CrlReasonBC.GetKeyCompromise">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.CrlReasonBC.GetRemoveFromCRL">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.DistributionPointBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.DistributionPoint"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointBC.#ctor(Org.BouncyCastle.Asn1.X509.DistributionPoint)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.DistributionPoint"/>.
            </summary>
            <param name="distributionPoint">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.DistributionPoint"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointBC.GetPoint">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.DistributionPoint"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointBC.GetDistributionPoint">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointBC.GetCRLIssuer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointBC.GetReasons">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.DistributionPointNameBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.DistributionPointName"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointNameBC.#ctor(Org.BouncyCastle.Asn1.X509.DistributionPointName)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.DistributionPointName"/>.
            </summary>
            <param name="distributionPointName">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.DistributionPointName"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointNameBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.X509.DistributionPointNameBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointNameBC.GetDistributionPointName">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.DistributionPointName"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointNameBC.GetType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointNameBC.GetName">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.DistributionPointNameBC.GetFullName">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.ExtendedKeyUsageBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.ExtendedKeyUsage"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.ExtendedKeyUsageBC.#ctor(Org.BouncyCastle.Asn1.X509.ExtendedKeyUsage)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.ExtendedKeyUsage"/>.
            </summary>
            <param name="extendedKeyUsage">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.ExtendedKeyUsage"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.ExtendedKeyUsageBC.#ctor(iText.Commons.Bouncycastle.Asn1.X509.IKeyPurposeID)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.ExtendedKeyUsage"/>.
            </summary>
            <param name="purposeId">KeyPurposeId wrapper</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.ExtendedKeyUsageBC.#ctor(iText.Commons.Bouncycastle.Asn1.X509.IKeyPurposeID[])">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.ExtendedKeyUsage"/>.
            </summary>
            <param name="purposeIds">KeyPurposeId wrappers array</param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.ExtendedKeyUsageBC.GetExtendedKeyUsage">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.ExtendedKeyUsage"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.GeneralNameBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.GeneralName"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.GeneralNameBC.#ctor(Org.BouncyCastle.Asn1.X509.GeneralName)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.GeneralName"/>.
            </summary>
            <param name="generalName">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.GeneralName"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.GeneralNameBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.X509.GeneralNameBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.GeneralNameBC.GetGeneralName">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.GeneralName"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.GeneralNameBC.GetTagNo">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.GeneralNameBC.GetUniformResourceIdentifier">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.GeneralNamesBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.GeneralNames"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.GeneralNamesBC.#ctor(Org.BouncyCastle.Asn1.X509.GeneralNames)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.GeneralNames"/>.
            </summary>
            <param name="generalNames">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.GeneralNames"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.GeneralNamesBC.GetGeneralNames">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.GeneralNames"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.GeneralNamesBC.GetNames">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.IssuingDistributionPoint"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC.#ctor(Org.BouncyCastle.Asn1.X509.IssuingDistributionPoint)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.IssuingDistributionPoint"/>.
            </summary>
            <param name="issuingDistPoint">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.IssuingDistributionPoint"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC.GetIssuingDistributionPoint">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.IssuingDistributionPoint"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC.GetDistributionPoint">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC.OnlyContainsUserCerts">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC.OnlyContainsCACerts">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC.IsIndirectCRL">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC.OnlyContainsAttributeCerts">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.IssuingDistributionPointBC.GetOnlySomeReasons">
            <summary><inheritDoc/></summary>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.KeyPurposeIDBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.KeyPurposeID"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyPurposeIDBC.#ctor(Org.BouncyCastle.Asn1.X509.KeyPurposeID)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.KeyPurposeID"/>.
            </summary>
            <param name="keyPurposeId">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.KeyPurposeID"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyPurposeIDBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.X509.KeyPurposeIDBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyPurposeIDBC.GetKeyPurposeID">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.KeyPurposeID"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyPurposeIDBC.GetIdKpOCSPSigning">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.KeyUsageBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.KeyUsage"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyUsageBC.#ctor(Org.BouncyCastle.Asn1.X509.KeyUsage)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.KeyUsage"/>.
            </summary>
            <param name="keyUsage">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.KeyUsage"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyUsageBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.X509.KeyUsageBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyUsageBC.GetKeyUsage">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.KeyUsage"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyUsageBC.GetDigitalSignature">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.KeyUsageBC.GetNonRepudiation">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.ReasonFlagsBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.ReasonFlags"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.ReasonFlagsBC.#ctor(Org.BouncyCastle.Asn1.X509.ReasonFlags)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.ReasonFlags"/>.
            </summary>
            <param name="reasonFlags">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.ReasonFlags"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.ReasonFlagsBC.GetReasonFlags">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.ReasonFlags"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.SubjectKeyIdentifierBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.SubjectKeyIdentifier"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.SubjectKeyIdentifierBC.#ctor(Org.BouncyCastle.Asn1.X509.SubjectKeyIdentifier)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.SubjectKeyIdentifier"/>.
            </summary>
            <param name="keyIdentifier">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.SubjectKeyIdentifier"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.SubjectKeyIdentifierBC.GetSubjectKeyIdentifier">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.SubjectKeyIdentifier"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.SubjectPublicKeyInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.SubjectPublicKeyInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.SubjectPublicKeyInfoBC.#ctor(Org.BouncyCastle.Asn1.X509.SubjectPublicKeyInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.SubjectPublicKeyInfo"/>.
            </summary>
            <param name="subjectPublicKeyInfo">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.SubjectPublicKeyInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.SubjectPublicKeyInfoBC.GetSubjectPublicKeyInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.SubjectPublicKeyInfo"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.SubjectPublicKeyInfoBC.GetAlgorithm">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.TbsCertificateStructureBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.TbsCertificateStructure"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.TbsCertificateStructureBC.#ctor(Org.BouncyCastle.Asn1.X509.TbsCertificateStructure)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.TbsCertificateStructure"/>.
            </summary>
            <param name="tbsCertificate">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.TbsCertificateStructure"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.TbsCertificateStructureBC.GetTbsCertificateStructure">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.TbsCertificateStructure"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.TbsCertificateStructureBC.GetSubjectPublicKeyInfo">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.TbsCertificateStructureBC.GetIssuer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.TbsCertificateStructureBC.GetSerialNumber">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.TimeBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.Time"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.TimeBC.#ctor(Org.BouncyCastle.Asn1.X509.Time)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.Time"/>.
            </summary>
            <param name="time">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.Time"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.TimeBC.GetTime">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.Time"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.X509ExtensionBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Extension"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionBC.#ctor(Org.BouncyCastle.Asn1.X509.X509Extension)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.Asn1.X509.X509Extension"/>.
            </summary>
            <param name="extension">
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Extension"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionBC.GetX509Extension">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped <see cref="T:Org.BouncyCastle.Asn1.X509.X509Extension"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Extensions"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.#ctor(Org.BouncyCastle.Asn1.X509.X509Extensions)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Extensions"/>.
            </summary>
            <param name="extension">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Extensions"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetX509Extensions">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Extensions"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetCRlDistributionPoints">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetIssuingDistributionPoint">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetAuthorityInfoAccess">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetBasicConstraints">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetKeyUsage">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetExtendedKeyUsage">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetAuthorityKeyIdentifier">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetSubjectKeyIdentifier">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509ExtensionsBC.GetExpiredCertsOnCRL">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Asn1.X509.X509NameBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Name"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509NameBC.#ctor(Org.BouncyCastle.Asn1.X509.X509Name)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Name"/>.
            </summary>
            <param name="x500Name">
            
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Name"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Asn1.X509.X509NameBC.GetX509Name">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.X509.X509Name"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.BouncyCastleFactory">
            <summary>
            This class implements
            <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleFactory"/>
            and creates bouncy-castle classes instances.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.#ctor">
            <summary>
            Creates
            <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleFactory"/>
            for bouncy-castle FIPS module.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.GetAlgorithmOid(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.GetDigestAlgorithmOid(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.GetAlgorithmName(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1ObjectIdentifier(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1ObjectIdentifier(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1ObjectIdentifierInstance(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1InputStream(System.IO.Stream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1InputStream(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1OctetString(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1OctetString(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1OctetString(iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject,System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1OctetString(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Sequence(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Sequence(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Sequence(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1SequenceInstance(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERSequence(iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERSequence(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1TaggedObject(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Integer(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Integer(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Integer(iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Set(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Set(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Set(iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject,System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateNullASN1Set">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1OutputStream(System.IO.Stream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1OutputStream(System.IO.Stream,System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDEROctetString(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDEROctetString(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1EncodableVector">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERNull">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERTaggedObject(System.Int32,iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERTaggedObject(System.Boolean,System.Int32,iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERSet(iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERSet(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERSet(iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyIdentifier)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERSet(iText.Commons.Bouncycastle.Asn1.Cms.IRecipientInfo)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Enumerated(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Enumerated(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Encoding">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateAttributeTable(iText.Commons.Bouncycastle.Asn1.IAsn1Set)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreatePKCSObjectIdentifiers">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateAttribute(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1Set)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateContentInfo(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateContentInfo(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSigningCertificate(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSigningCertificateV2(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBasicOCSPResponse(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBasicOCSPResponse(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBasicOCSPResponse(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPObjectIdentifiers">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateAlgorithmIdentifier(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateAlgorithmIdentifier(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateRSASSAPSSParams(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateRSASSAPSSParamsWithMGF1(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.GetProviderName">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCertificateID">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateExtensions(System.Collections.IDictionary)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateExtensions">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPReqBuilder">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSigPolicyQualifierInfo(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IDerIA5String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1String(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Primitive(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPResponse(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPResponse">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPResponse(iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponseStatus,iText.Commons.Bouncycastle.Asn1.Ocsp.IResponseBytes)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPResponse(System.Int32,System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateResponseBytes(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IDerOctetString)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPResponseStatus(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPResponseStatus">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCertificateStatus">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateRevokedStatus(iText.Commons.Bouncycastle.Cert.Ocsp.ICertStatus)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateRevokedStatus(System.DateTime,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Primitive(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERIA5String(iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject,System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDERIA5String(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCRLDistPoint(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateIssuingDistributionPoint(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateIssuingDistributionPoint(iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName,System.Boolean,System.Boolean,iText.Commons.Bouncycastle.Asn1.X509.IReasonFlags,System.Boolean,System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateReasonFlags(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDistributionPointName">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateDistributionPointName(iText.Commons.Bouncycastle.Asn1.X509.IGeneralNames)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateGeneralNames(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateGeneralName">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOtherHashAlgAndValue(iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSignaturePolicyId(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.Esf.IOtherHashAlgAndValue)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSignaturePolicyId(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.Esf.IOtherHashAlgAndValue,iText.Commons.Bouncycastle.Asn1.Esf.ISigPolicyQualifierInfo[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSignaturePolicyIdentifier(iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyId)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateEnvelopedData(iText.Commons.Bouncycastle.Asn1.Cms.IOriginatorInfo,iText.Commons.Bouncycastle.Asn1.IAsn1Set,iText.Commons.Bouncycastle.Asn1.Cms.IEncryptedContentInfo,iText.Commons.Bouncycastle.Asn1.IAsn1Set)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateRecipientInfo(iText.Commons.Bouncycastle.Asn1.Cms.IKeyTransRecipientInfo)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateEncryptedContentInfo(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateTBSCertificate(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateIssuerAndSerialNumber(iText.Commons.Bouncycastle.Asn1.X500.IX500Name,iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateRecipientIdentifier(iText.Commons.Bouncycastle.Asn1.Cms.IIssuerAndSerialNumber)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateKeyTransRecipientInfo(iText.Commons.Bouncycastle.Asn1.Cms.IRecipientIdentifier,iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateNullOriginatorInfo">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCMSEnvelopedData(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateTimeStampRequestGenerator">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateTimeStampResponse(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateAbstractOCSPException(System.Exception)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateUnknownStatus">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1Dump">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1BitString(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1GeneralizedTime(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1GeneralizedTime(System.DateTime)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateASN1UTCTime(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateTimeStampResponseGenerator(iText.Commons.Bouncycastle.Tsp.ITimeStampTokenGenerator,System.Collections.IList)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateTimeStampRequest(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateTimeStampTokenGenerator(iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String,System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX500Name(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX500Name(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateRespID(iText.Commons.Bouncycastle.Asn1.X500.IX500Name)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBasicOCSPRespBuilder(iText.Commons.Bouncycastle.Cert.Ocsp.IRespID)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPReq(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX509v2CRLBuilder(iText.Commons.Bouncycastle.Asn1.X500.IX500Name,System.DateTime)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateJcaX509v3CertificateBuilder(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime,System.DateTime,iText.Commons.Bouncycastle.Asn1.X500.IX500Name,iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBasicConstraints(System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBasicConstraints(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateKeyUsage">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateKeyUsage(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateKeyPurposeId">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateExtendedKeyUsage(iText.Commons.Bouncycastle.Asn1.X509.IKeyPurposeID)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateExtendedKeyUsage(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSubjectPublicKeyInfo(iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCRLReason">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateTSTInfo(iText.Commons.Bouncycastle.Asn1.Cms.IContentInfo)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSingleResp(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX509Certificate(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX509Certificate(System.IO.Stream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX509Crl(System.IO.Stream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX509Crls(System.IO.Stream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateIDigest(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCertificateID(System.String,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX500NameInstance(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateOCSPReq(iText.Commons.Bouncycastle.Cert.Ocsp.ICertID,System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateISigner">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateX509CertificateParser">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateGeneralSecurityException(System.String,System.Exception)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateGeneralSecurityException(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateGeneralSecurityException">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.GetBouncyCastleFactoryTestUtil">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBigInteger">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBigInteger(System.Int32,System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateBigInteger(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCipher(System.Boolean,System.Byte[],System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCipherCbCnoPad(System.Boolean,System.Byte[],System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCipherCbCnoPad(System.Boolean,System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateNullCrl">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateTimeStampToken(iText.Commons.Bouncycastle.Asn1.Cms.IContentInfo)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateRsa2048KeyPairGenerator">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateContentSigner(System.String,iText.Commons.Bouncycastle.Crypto.IPrivateKey)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateAuthorityKeyIdentifier(iText.Commons.Bouncycastle.Asn1.X509.ISubjectPublicKeyInfo)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateSubjectKeyIdentifier(iText.Commons.Bouncycastle.Asn1.X509.ISubjectPublicKeyInfo)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.IsNullExtension(iText.Commons.Bouncycastle.Asn1.X509.IX509Extension)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.IsNull(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateExtension(System.Boolean,iText.Commons.Bouncycastle.Asn1.IDerOctetString)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateCipherBytes(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.Byte[],iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.IsInApprovedOnlyMode">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.IsEncryptionFeatureSupported(System.Int32,System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreatePEMParser(System.IO.TextReader,System.Char[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.GetBouncyCastleUtil">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleFactory.CreateEndDate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.BouncyCastleUtil.ReadPkcs7Certs(System.IO.Stream)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Ocsp.BasicOcspRespGenerator"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.#ctor(Org.BouncyCastle.Ocsp.BasicOcspRespGenerator)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.BasicOcspRespGenerator"/>.
            </summary>
            <param name="basicOCSPRespBuilder">
            
            <see cref="T:Org.BouncyCastle.Ocsp.BasicOcspRespGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.#ctor(iText.Commons.Bouncycastle.Cert.Ocsp.IRespID)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.BasicOcspRespGenerator"/>.
            </summary>
            <param name="respID">
            RespID wrapper to create
            <see cref="T:Org.BouncyCastle.Ocsp.BasicOcspRespGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.GetBasicOcspRespGenerator">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Ocsp.BasicOcspRespGenerator"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.SetResponseExtensions(iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.AddResponse(iText.Commons.Bouncycastle.Cert.Ocsp.ICertID,iText.Commons.Bouncycastle.Cert.Ocsp.ICertStatus,System.DateTime,System.DateTime,iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.Build(iText.Commons.Bouncycastle.Operator.IContentSigner,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.DateTime)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.BasicOcspRespGeneratorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.CertIDBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertID"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.CertID)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertID"/>.
            </summary>
            <param name="certificateID">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertID"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.#ctor(System.String,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertID"/>.
            </summary>
            <param name="hashAlgorithm">
            hash algorithm to create
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertID"/>
            </param>
            <param name="issuerCert">
            X509Certificate wrapper to create
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertID"/>
            </param>
            <param name="serialNumber">
            serial number to create
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertID"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Cert.Ocsp.CertIDBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.GetCertID">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertID"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.GetHashAlgOID">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.GetHashSha1">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.MatchesIssuer(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.GetSerialNumber">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertIDBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.CertStatusBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertStatusBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.CertStatus)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </summary>
            <param name="certificateStatus">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertStatusBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Cert.Ocsp.CertStatusBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertStatusBC.GetCertStatus">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertStatusBC.GetGood">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertStatusBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertStatusBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.CertStatusBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.OcspExceptionBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Ocsp.OcspException"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspExceptionBC.#ctor(Org.BouncyCastle.Ocsp.OcspException)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.OcspException"/>.
            </summary>
            <param name="exception">
            
            <see cref="T:Org.BouncyCastle.Ocsp.OcspException"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspExceptionBC.GetException">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Ocsp.OcspException"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Cert.Ocsp.OcspExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.OcspReqBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReq"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.#ctor(Org.BouncyCastle.Ocsp.OcspReq)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReq"/>.
            </summary>
            <param name="ocspReq">
            
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReq"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.#ctor(iText.Commons.Bouncycastle.Cert.Ocsp.ICertID,System.Byte[])">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReq"/>.
            </summary>
            <param name="certId">
            CertID wrapper
            </param>
            <param name="documentId">
            byte array
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.GetOcspReq">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReq"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.GetRequestList">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.GetExtension(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OcspReqBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReqGenerator"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC.#ctor(Org.BouncyCastle.Ocsp.OcspReqGenerator)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReqGenerator"/>.
            </summary>
            <param name="reqBuilder">
            
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReqGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC.GetReqBuilder">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Ocsp.OcspReqGenerator"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC.SetRequestExtensions(iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC.AddRequest(iText.Commons.Bouncycastle.Cert.Ocsp.ICertID)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC.Build">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.OCSPReqBuilderBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.ReqBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Ocsp.Req"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.ReqBC.#ctor(Org.BouncyCastle.Ocsp.Req)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.Req"/>.
            </summary>
            <param name="req">
            
            <see cref="T:Org.BouncyCastle.Ocsp.Req"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.ReqBC.GetReq">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Ocsp.Req"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.ReqBC.GetCertID">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.ReqBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.ReqBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.ReqBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.RespIDBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Ocsp.RespID"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RespIDBC.#ctor(Org.BouncyCastle.Ocsp.RespID)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.RespID"/>.
            </summary>
            <param name="respID">
            
            <see cref="T:Org.BouncyCastle.Ocsp.RespID"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RespIDBC.#ctor(iText.Commons.Bouncycastle.Asn1.X500.IX500Name)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Ocsp.RespID"/>.
            </summary>
            <param name="x500Name">
            X500Name wrapper to create
            <see cref="T:Org.BouncyCastle.Ocsp.RespID"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RespIDBC.GetRespID">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Ocsp.RespID"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RespIDBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RespIDBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RespIDBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.RevokedStatusBC">
            <summary>
            Wrapper class for revoked
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RevokedStatusBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.CertStatus)">
            <summary>
            Creates new wrapper instance for revoked
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </summary>
            <param name="certificateStatus">
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RevokedStatusBC.#ctor(System.DateTime,System.Int32)">
            <summary>
            Creates new wrapper instance for revoked
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </summary>
            <param name="date">date to create RevokedInfo</param>
            <param name="i">CrlReason int value</param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RevokedStatusBC.GetRevokedStatus">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.RevokedStatusBC.GetRevocationTime">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.SingleResponse"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.SingleResponse)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.SingleResponse"/>.
            </summary>
            <param name="singleResp">
            
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.SingleResponse"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.#ctor(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.SingleResponse"/>.
            </summary>
            <param name="basicResp">
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            wrapper to get
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.SingleResponse"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.GetSingleResponse">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.SingleResponse"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.GetCertID">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.GetCertStatus">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.GetNextUpdate">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.GetThisUpdate">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.SingleResponseBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cert.Ocsp.UnknownStatusBC">
            <summary>
            Wrapper class for unknown
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.UnknownStatusBC.#ctor(Org.BouncyCastle.Asn1.Ocsp.CertStatus)">
            <summary>
            Creates new wrapper instance for unknown
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </summary>
            <param name="certificateStatus">
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.UnknownStatusBC.#ctor">
            <summary>
            Creates new wrapper instance for unknown
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.Ocsp.UnknownStatusBC.GetUnknownStatus">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Asn1.Ocsp.CertStatus"/>.
            </returns>
        </member>
        <member name="T:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.X509.X509V2CrlGenerator"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.#ctor(Org.BouncyCastle.X509.X509V2CrlGenerator)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.X509.X509V2CrlGenerator"/>.
            </summary>
            <param name="builder">
            
            <see cref="T:Org.BouncyCastle.X509.X509V2CrlGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.#ctor(iText.Commons.Bouncycastle.Asn1.X500.IX500Name,System.DateTime)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.X509.X509V2CrlGenerator"/>.
            </summary>
            <param name="x500Name">
            X500Name wrapper to create
            <see cref="T:Org.BouncyCastle.X509.X509V2CrlGenerator"/>
            </param>
            <param name="date">
            Date to create
            <see cref="T:Org.BouncyCastle.X509.X509V2CrlGenerator"/>
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.GetBuilder">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.X509.X509V2CrlGenerator"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.AddCRLEntry(iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.AddExtension(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Boolean,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.SetNextUpdate(System.DateTime)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.Build(iText.Commons.Bouncycastle.Operator.IContentSigner)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cert.X509V2CrlGeneratorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cms.CmsEnvelopedDataBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Cms.CmsEnvelopedData"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsEnvelopedDataBC.#ctor(Org.BouncyCastle.Cms.CmsEnvelopedData)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Cms.CmsEnvelopedData"/>.
            </summary>
            <param name="cmsEnvelopedData">
            
            <see cref="T:Org.BouncyCastle.Cms.CmsEnvelopedData"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsEnvelopedDataBC.GetCmsEnvelopedData">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Cms.CmsEnvelopedData"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsEnvelopedDataBC.GetRecipientInfos">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsEnvelopedDataBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsEnvelopedDataBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsEnvelopedDataBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cms.CmsExceptionBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Cms.CmsException"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsExceptionBC.#ctor(Org.BouncyCastle.Cms.CmsException)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Cms.CmsException"/>.
            </summary>
            <param name="exception">
            
            <see cref="T:Org.BouncyCastle.Cms.CmsException"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsExceptionBC.GetCmsException">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Cms.CmsException"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.CmsExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Cms.CmsExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cms.RecipientIDBC">
            <summary>
            Wrapper class for
            <see cref="!:Org.BouncyCastle.Cms.RecipientId"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientIDBC.#ctor(Org.BouncyCastle.Cms.RecipientID)">
            <summary>
            Creates new wrapper instance for
            <see cref="!:Org.BouncyCastle.Cms.RecipientId"/>.
            </summary>
            <param name="recipientId">
            
            <see cref="!:Org.BouncyCastle.Cms.RecipientId"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientIDBC.GetRecipientId">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="!:Org.BouncyCastle.Cms.RecipientId"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientIDBC.Match(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientIDBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientIDBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientIDBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cms.RecipientInformationBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Cms.RecipientInformation"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationBC.#ctor(Org.BouncyCastle.Cms.RecipientInformation)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Cms.RecipientInformation"/>.
            </summary>
            <param name="recipientInformation">
            
            <see cref="T:Org.BouncyCastle.Cms.RecipientInformation"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationBC.GetRecipientInformation">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Cms.RecipientInformation"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationBC.GetContent(iText.Commons.Bouncycastle.Crypto.IPrivateKey)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationBC.GetRID">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cms.RecipientInformationStoreBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Cms.RecipientInformationStore"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationStoreBC.#ctor(Org.BouncyCastle.Cms.RecipientInformationStore)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Cms.RecipientInformationStore"/>.
            </summary>
            <param name="recipientInformationStore">
            
            <see cref="T:Org.BouncyCastle.Cms.RecipientInformationStore"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationStoreBC.GetRecipientInformationStore">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Cms.RecipientInformationStore"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationStoreBC.GetRecipients">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationStoreBC.Get(iText.Commons.Bouncycastle.Cms.IRecipientID)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationStoreBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationStoreBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.RecipientInformationStoreBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Cms.SignerInfoGeneratorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Cms.SignerInfoGenerator"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.SignerInfoGeneratorBC.#ctor(Org.BouncyCastle.Cms.SignerInfoGenerator)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Cms.SignerInfoGenerator"/>.
            </summary>
            <param name="signerInfoGenerator">
            
            <see cref="T:Org.BouncyCastle.Cms.SignerInfoGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Cms.SignerInfoGeneratorBC.GetSignerInfoGenerator">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Cms.SignerInfoGenerator"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Cms.SignerInfoGeneratorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Cms.SignerInfoGeneratorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Cms.SignerInfoGeneratorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Crypto.AsymmetricCipherKeyPairBC">
            <summary>
            Wrapper class for AsymmetricCipherKeyPair.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.AsymmetricCipherKeyPairBC.#ctor(Org.BouncyCastle.Crypto.AsymmetricCipherKeyPair)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Crypto.AsymmetricCipherKeyPair"/>.
            </summary>
            <param name="keyPair">
            <see cref="T:Org.BouncyCastle.Crypto.AsymmetricCipherKeyPair"/> to be wrapped
            </param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Bouncycastle.Crypto.AsymmetricCipherKeyPairBC.GetKeyPair" -->
        <member name="M:iText.Bouncycastle.Crypto.AsymmetricCipherKeyPairBC.GetPrivateKey">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.AsymmetricCipherKeyPairBC.GetPublicKey">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.AsymmetricCipherKeyPairBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.AsymmetricCipherKeyPairBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.AsymmetricCipherKeyPairBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Crypto.CipherBC">
            <summary>
            Wrapper class for IBufferedCipher.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherBC.#ctor(Org.BouncyCastle.Crypto.IBufferedCipher)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Crypto.IBufferedCipher"/>.
            </summary>
            <param name="cipher">
            <see cref="T:Org.BouncyCastle.Crypto.IBufferedCipher"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherBC.#ctor(System.Boolean,System.Byte[],System.Byte[])">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Crypto.IBufferedCipher"/>.
            </summary>
            <param name="forEncryption">boolean value</param>
            <param name="key">byte array</param>
            <param name="iv">init vector</param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Bouncycastle.Crypto.CipherBC.GetICipher" -->
        <member name="M:iText.Bouncycastle.Crypto.CipherBC.Update(System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherBC.DoFinal">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherCBCnoPadBC.#ctor(System.Boolean,System.Byte[],System.Byte[])">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Crypto.IBlockCipher"/>.
            </summary>
            <param name="forEncryption">
            Defines whether this wrapper will be used for encryption or decryption.
            </param>
            <param name="key">
            Key bytes to be used during block cipher creation.
            </param>
            <param name="initVector">
            Initialization vector to be used during block cipher creation.
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherCBCnoPadBC.ProcessBlock(System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherCBCnoPadBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherCBCnoPadBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.CipherCBCnoPadBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Crypto.DigestBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Crypto.IDigest"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.#ctor(Org.BouncyCastle.Crypto.IDigest)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Crypto.IDigest"/>.
            </summary>
            <param name="iDigest">
            
            <see cref="T:Org.BouncyCastle.Crypto.IDigest"/>
            to be wrapped
            </param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Bouncycastle.Crypto.DigestBC.GetIDigest" -->
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.Digest(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.Digest">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.Update(System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.GetDigestLength">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.Update(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.Reset">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.GetAlgorithmName">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.DigestBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Crypto.Generators.RsaKeyPairGeneratorBC">
            <summary>
            Wrapper class for RsaKeyPairGenerator.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.Generators.RsaKeyPairGeneratorBC.#ctor">
            <summary>
            Creates new wrapper instance for
            <see cref="!:RsaKeyPairGeneratorr"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.Generators.RsaKeyPairGeneratorBC.#ctor(Org.BouncyCastle.Crypto.Generators.RsaKeyPairGenerator)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Crypto.Generators.RsaKeyPairGenerator"/>.
            </summary>
            <param name="generator">
            <see cref="T:Org.BouncyCastle.Crypto.Generators.RsaKeyPairGenerator"/> to be wrapped
            </param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Bouncycastle.Crypto.Generators.RsaKeyPairGeneratorBC.GetGenerator" -->
        <member name="M:iText.Bouncycastle.Crypto.Generators.RsaKeyPairGeneratorBC.GenerateKeyPair">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.Generators.RsaKeyPairGeneratorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.Generators.RsaKeyPairGeneratorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.Generators.RsaKeyPairGeneratorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Crypto.PrivateKeyBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Crypto.ICipherParameters"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PrivateKeyBC.#ctor(Org.BouncyCastle.Crypto.ICipherParameters)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.Crypto.ICipherParameters"/>.
            </summary>
            <param name="privateKey">
            <see cref="T:Org.BouncyCastle.Crypto.ICipherParameters"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PrivateKeyBC.GetPrivateKey">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped <see cref="T:Org.BouncyCastle.Crypto.ICipherParameters"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PrivateKeyBC.GetAlgorithm">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PrivateKeyBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PrivateKeyBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PrivateKeyBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Crypto.PublicKeyBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Crypto.AsymmetricKeyParameter"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PublicKeyBC.#ctor(Org.BouncyCastle.Crypto.AsymmetricKeyParameter)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.Crypto.AsymmetricKeyParameter"/>.
            </summary>
            <param name="publicKey">
            <see cref="T:Org.BouncyCastle.Crypto.AsymmetricKeyParameter"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PublicKeyBC.GetPublicKey">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped <see cref="T:Org.BouncyCastle.Crypto.AsymmetricKeyParameter"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PublicKeyBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PublicKeyBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.PublicKeyBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Crypto.SignerBC">
            <summary>
            Wrapper class for <see cref="T:Org.BouncyCastle.Crypto.ISigner"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.#ctor(Org.BouncyCastle.Crypto.ISigner)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.Crypto.ISigner"/>.
            </summary>
            <param name="iSigner">
            
            <see cref="T:Org.BouncyCastle.Crypto.ISigner"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.GetISigner">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped <see cref="T:Org.BouncyCastle.Crypto.ISigner"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.InitVerify(iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.InitSign(iText.Commons.Bouncycastle.Crypto.IPrivateKey)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.InitRsaPssSigner(System.String,System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.Update(System.Byte[],System.Int32,System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.Update(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.VerifySignature(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.GenerateSignature">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.UpdateVerifier(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.SetDigestAlgorithm(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Crypto.SignerBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Math.BigIntegerBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Math.BigInteger"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.#ctor(Org.BouncyCastle.Math.BigInteger)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.Math.BigInteger"/>.
            </summary>
            <param name="bigInteger">
            <see cref="T:Org.BouncyCastle.Math.BigInteger"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.GetInstance">
            <summary>Gets wrapper instance.</summary>
            <returns>
            
            <see cref="T:iText.Bouncycastle.Math.BigIntegerBC"/>
            instance.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.GetBigInteger">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped <see cref="T:Org.BouncyCastle.Math.BigInteger"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.GetIntValue">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.ToString(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.ValueOf(System.Int64)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.Remainder(iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Math.BigIntegerBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Openssl.PEMParserBC">
            <summary>
            Wrapper class for
            <see cref="!:Org.BouncyCastle.Openssl.PEMParser"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Openssl.PEMParserBC.#ctor(Org.BouncyCastle.OpenSsl.PemReader)">
            <summary>
            Creates new wrapper instance for
            <see cref="!:Org.BouncyCastle.OpenSsl.OpenSslPemReader"/>.
            </summary>
            <param name="parser">
            
            <see cref="!:Org.BouncyCastle.OpenSsl.OpenSslPemReader"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Openssl.PEMParserBC.GetParser">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="!:Org.BouncyCastle.OpenSsl.OpenSslPemReader"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Openssl.PEMParserBC.ReadObject">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Openssl.PEMParserBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Openssl.PEMParserBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Openssl.PEMParserBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Operator.ContentSignerBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Crypto.Operators.Asn1SignatureFactory"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Operator.ContentSignerBC.#ctor(Org.BouncyCastle.Crypto.Operators.Asn1SignatureFactory)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Crypto.Operators.Asn1SignatureFactory"/>.
            </summary>
            <param name="contentSigner">
            
            <see cref="T:Org.BouncyCastle.Crypto.Operators.Asn1SignatureFactory"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Operator.ContentSignerBC.GetContentSigner">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Crypto.Operators.Asn1SignatureFactory"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Operator.ContentSignerBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Operator.ContentSignerBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Operator.ContentSignerBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Operator.DigestCalculatorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Crypto.IDigestFactory"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Operator.DigestCalculatorBC.#ctor(Org.BouncyCastle.Crypto.IDigestFactory)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Crypto.IDigestFactory"/>.
            </summary>
            <param name="digestCalculator">
            
            <see cref="T:Org.BouncyCastle.Crypto.IDigestFactory"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Operator.DigestCalculatorBC.GetDigestCalculator">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Crypto.IDigestFactory"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Operator.DigestCalculatorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Operator.DigestCalculatorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Operator.DigestCalculatorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Security.CertificateExpiredExceptionBC">
            <summary>Wrapper class for <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateExpiredException"/>.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateExpiredExceptionBC.#ctor(Org.BouncyCastle.Security.Certificates.CertificateExpiredException)">
            <summary>
            Creates new wrapper for <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateExpiredException"/>.
            </summary>
            <param name="exception">
            <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateExpiredException"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateExpiredExceptionBC.GetException">
            <summary>Get actual org.bouncycastle object being wrapped.</summary>
            <returns>wrapped <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateExpiredException"/>.</returns>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateExpiredExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateExpiredExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateExpiredExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Security.CertificateExpiredExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Security.CertificateNotYetValidExceptionBC">
            <summary>Wrapper class for <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateNotYetValidException"/>.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateNotYetValidExceptionBC.#ctor(Org.BouncyCastle.Security.Certificates.CertificateNotYetValidException)">
            <summary>
            Creates new wrapper for <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateNotYetValidException"/>.
            </summary>
            <param name="exception">
            <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateNotYetValidException"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateNotYetValidExceptionBC.GetException">
            <summary>Get actual org.bouncycastle object being wrapped.</summary>
            <returns>wrapped <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateNotYetValidException"/>.</returns>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateNotYetValidExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateNotYetValidExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateNotYetValidExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Security.CertificateNotYetValidExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Security.CertificateParsingExceptionBC">
            <summary>Wrapper class for <see cref="!:ParsingExceptionBCFips"/>.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateParsingExceptionBC.#ctor(Org.BouncyCastle.Security.Certificates.CertificateParsingException)">
            <summary>
            Creates new wrapper for <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateParsingException"/>.
            </summary>
            <param name="exception">
            <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateParsingException"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateParsingExceptionBC.GetException">
            <summary>Get actual org.bouncycastle object being wrapped.</summary>
            <returns>wrapped <see cref="T:Org.BouncyCastle.Security.Certificates.CertificateParsingException"/>.</returns>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateParsingExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateParsingExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.CertificateParsingExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Security.CertificateParsingExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Security.CrlExceptionBC">
            <summary>Wrapper class for <see cref="T:Org.BouncyCastle.Security.Certificates.CrlException"/>.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.CrlExceptionBC.#ctor(Org.BouncyCastle.Security.Certificates.CrlException)">
            <summary>
            Creates new wrapper for <see cref="T:Org.BouncyCastle.Security.Certificates.CrlException"/>.
            </summary>
            <param name="exception">
            <see cref="T:Org.BouncyCastle.Security.Certificates.CrlException"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Security.CrlExceptionBC.GetException">
            <summary>Get actual org.bouncycastle object being wrapped.</summary>
            <returns>wrapped <see cref="T:Org.BouncyCastle.Security.Certificates.CrlException"/>.</returns>
        </member>
        <member name="M:iText.Bouncycastle.Security.CrlExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Security.CrlExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.CrlExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Security.CrlExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Security.GeneralSecurityExceptionBC">
            <summary>Wrapper class for <see cref="T:Org.BouncyCastle.Security.GeneralSecurityException"/>.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.GeneralSecurityExceptionBC.#ctor(Org.BouncyCastle.Security.GeneralSecurityException)">
            <summary>
            Creates new wrapper for <see cref="T:Org.BouncyCastle.Security.GeneralSecurityException"/>.
            </summary>
            <param name="exception">
            <see cref="T:Org.BouncyCastle.Security.GeneralSecurityException"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Security.GeneralSecurityExceptionBC.#ctor(System.String,System.Exception)">
            <summary>
            Creates new wrapper for <see cref="T:Org.BouncyCastle.Security.GeneralSecurityException"/>
            using another exception.
            </summary>
            <param name="exceptionMessage">
            Another exception message to be used during instance creation
            </param>
            <param name="exception">
            Another exception to be used during instance creation
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Security.GeneralSecurityExceptionBC.GetException">
            <summary>Get actual org.bouncycastle object being wrapped.</summary>
            <returns>wrapped <see cref="T:Org.BouncyCastle.Security.GeneralSecurityException"/>.</returns>
        </member>
        <member name="M:iText.Bouncycastle.Security.GeneralSecurityExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Security.GeneralSecurityExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.GeneralSecurityExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Security.GeneralSecurityExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Security.SecurityUtilityExceptionBC">
            <summary>Wrapper class for <see cref="T:Org.BouncyCastle.Security.SecurityUtilityException"/>.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.SecurityUtilityExceptionBC.#ctor(Org.BouncyCastle.Security.SecurityUtilityException)">
            <summary>
            Creates new wrapper for <see cref="T:Org.BouncyCastle.Security.SecurityUtilityException"/>.
            </summary>
            <param name="exception">
            <see cref="T:Org.BouncyCastle.Security.SecurityUtilityException"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Security.SecurityUtilityExceptionBC.GetException">
            <summary>Get actual org.bouncycastle object being wrapped.</summary>
            <returns>wrapped <see cref="T:Org.BouncyCastle.Security.SecurityUtilityException"/>.</returns>
        </member>
        <member name="M:iText.Bouncycastle.Security.SecurityUtilityExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Security.SecurityUtilityExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Security.SecurityUtilityExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Security.SecurityUtilityExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Tsp.TimeStampRequestBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampRequest"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestBC.#ctor(Org.BouncyCastle.Tsp.TimeStampRequest)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampRequest"/>.
            </summary>
            <param name="timeStampRequest">
            
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampRequest"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestBC.GetTimeStampRequest">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampRequest"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestBC.GetNonce">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampRequestGenerator"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC.#ctor(Org.BouncyCastle.Tsp.TimeStampRequestGenerator)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampRequestGenerator"/>.
            </summary>
            <param name="requestGenerator">
            
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampRequestGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC.GetRequestGenerator">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampRequestGenerator"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC.SetCertReq(System.Boolean)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC.SetReqPolicy(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC.Generate(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Byte[],iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampRequestGeneratorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Tsp.TimeStampResponseBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponse"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.#ctor(Org.BouncyCastle.Tsp.TimeStampResponse)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponse"/>.
            </summary>
            <param name="timeStampResponse">
            
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponse"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.GetTimeStampResponse">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponse"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.Validate(iText.Commons.Bouncycastle.Tsp.ITimeStampRequest)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.GetFailInfo">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.GetTimeStampToken">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.GetStatusString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Tsp.TimeStampResponseGeneratorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponseGenerator"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseGeneratorBC.#ctor(Org.BouncyCastle.Tsp.TimeStampResponseGenerator)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponseGenerator"/>.
            </summary>
            <param name="timeStampResponseGenerator">
            
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponseGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseGeneratorBC.#ctor(iText.Commons.Bouncycastle.Tsp.ITimeStampTokenGenerator,System.Collections.IList)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponseGenerator"/>.
            </summary>
            <param name="tokenGenerator">TimeStampTokenGenerator wrapper</param>
            <param name="algorithms">set of algorithm strings</param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseGeneratorBC.GetTimeStampResponseGenerator">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampResponseGenerator"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseGeneratorBC.Generate(iText.Commons.Bouncycastle.Tsp.ITimeStampRequest,iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseGeneratorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseGeneratorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampResponseGeneratorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Tsp.TimeStampTokenBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampToken"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenBC.#ctor(Org.BouncyCastle.Tsp.TimeStampToken)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampToken"/>.
            </summary>
            <param name="timeStampToken">
            
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampToken"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenBC.GetTimeStampToken">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampToken"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenBC.GetTimeStampInfo">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenBC.Validate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenGenerator"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.#ctor(Org.BouncyCastle.Tsp.TimeStampTokenGenerator)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenGenerator"/>.
            </summary>
            <param name="timeStampTokenGenerator">
            
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.#ctor(iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String,System.String)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenGenerator"/>.
            </summary>
            <param name="pk">AsymmetricKeyParameter wrapper</param>
            <param name="cert">X509Certificate wrapper</param>
            <param name="allowedDigest">allowed digest</param>
            <param name="policyOid">policy OID</param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.GetTimeStampTokenGenerator">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenGenerator"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.SetAccuracySeconds(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.SetCertificates(System.Collections.Generic.IList{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.Generate(iText.Commons.Bouncycastle.Tsp.ITimeStampRequest,iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenGeneratorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenInfo"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.#ctor(Org.BouncyCastle.Tsp.TimeStampTokenInfo)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenInfo"/>.
            </summary>
            <param name="timeStampTokenInfo">
            
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenInfo"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.GetTimeStampTokenInfo">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Tsp.TimeStampTokenInfo"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.GetHashAlgorithm">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.ToASN1Structure">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.GetGenTime">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TimeStampTokenInfoBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.Tsp.TSPExceptionBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.Tsp.TspException"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TSPExceptionBC.#ctor(Org.BouncyCastle.Tsp.TspException)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.Tsp.TspException"/>.
            </summary>
            <param name="tspException">
            
            <see cref="T:Org.BouncyCastle.Tsp.TspException"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TSPExceptionBC.GetTSPException">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.Tsp.TspException"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TSPExceptionBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TSPExceptionBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.Tsp.TSPExceptionBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="P:iText.Bouncycastle.Tsp.TSPExceptionBC.Message">
            <summary>
            Delegates
            <c>getMessage</c>
            method call to the wrapped exception.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.X509.X509CertificateBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.X509.X509Certificate"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.#ctor(Org.BouncyCastle.X509.X509Certificate)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.X509.X509Certificate"/>.
            </summary>
            <param name="certificate">
            
            <see cref="T:Org.BouncyCastle.X509.X509Certificate"/> to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetCertificate">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped <see cref="T:Org.BouncyCastle.X509.X509Certificate"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetIssuerDN">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetSerialNumber">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetPublicKey">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetSigAlgOID">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetSigAlgParams">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetTbsCertificate">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetExtensionValue(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.Verify(iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetCriticalExtensionOids">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.CheckValidity(System.DateTime)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetSubjectDN">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetEndDateTime">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetNotBefore">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetNotAfter">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetExtendedKeyUsage">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetKeyUsage">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetBasicConstraints">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.X509.X509CertificateParserBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.X509.X509CertificateParser"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateParserBC.#ctor(Org.BouncyCastle.X509.X509CertificateParser)">
            <summary>
            Creates new wrapper instance for
            <see cref="T:Org.BouncyCastle.X509.X509CertificateParser"/>.
            </summary>
            <param name="certificateParser">
            <see cref="T:Org.BouncyCastle.X509.X509CertificateParser"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateParserBC.GetCertificateParser">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped
            <see cref="T:Org.BouncyCastle.X509.X509CertificateParser"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateParserBC.ReadAllCerts(System.Byte[])">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateParserBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateParserBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CertificateParserBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="F:iText.Bouncycastle.X509.X509CrlBC.x509Crl">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.X509.X509Crl"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.#ctor(Org.BouncyCastle.X509.X509Crl)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.X509.X509Crl"/>.
            </summary>
            <param name="x509Crl">
            <see cref="T:Org.BouncyCastle.X509.X509Crl"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.GetX509Crl">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped <see cref="T:Org.BouncyCastle.X509.X509Crl"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.IsRevoked(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.GetIssuerDN">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.GetThisUpdate">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.GetNextUpdate">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.Verify(iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.GetEncoded">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.GetExtensionValue(System.String)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.GetRevokedCertificate(iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509CrlBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
        <member name="T:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC">
            <summary>
            Wrapper class for
            <see cref="T:Org.BouncyCastle.X509.X509V3CertificateGenerator"/>.
            </summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC.#ctor(Org.BouncyCastle.X509.X509V3CertificateGenerator)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.X509.X509V3CertificateGenerator"/>.
            </summary>
            <param name="certificateBuilder">
            <see cref="T:Org.BouncyCastle.X509.X509V3CertificateGenerator"/>
            to be wrapped
            </param>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC.#ctor(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime,System.DateTime,iText.Commons.Bouncycastle.Asn1.X500.IX500Name,iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary>
            Creates new wrapper instance for <see cref="T:Org.BouncyCastle.X509.X509V3CertificateGenerator"/>.
            </summary>
            <param name="signingCert">to get issuerDN to set</param>
            <param name="number">certificate serial number to set</param>
            <param name="startDate">to set</param>
            <param name="endDate">to set</param>
            <param name="subjectDn">to set</param>
            <param name="publicKey">to set</param>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC.GetCertificateBuilder">
            <summary>Gets actual org.bouncycastle object being wrapped.</summary>
            <returns>
            wrapped <see cref="T:Org.BouncyCastle.X509.X509V3CertificateGenerator"/>.
            </returns>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC.AddExtension(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Boolean,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC.Build(iText.Commons.Bouncycastle.Operator.IContentSigner)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC.Equals(System.Object)">
            <summary>Indicates whether some other object is "equal to" this one.</summary>
            <remarks>Indicates whether some other object is "equal to" this one. Compares wrapped objects.</remarks>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC.GetHashCode">
            <summary>Returns a hash code value based on the wrapped object.</summary>
        </member>
        <member name="M:iText.Bouncycastle.X509.X509V3CertificateGeneratorBC.ToString">
            <summary>
            Delegates
            <c>toString</c>
            method call to the wrapped object.
            </summary>
        </member>
    </members>
</doc>
