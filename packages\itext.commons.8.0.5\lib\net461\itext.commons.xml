<?xml version="1.0"?>
<doc>
    <assembly>
        <name>itext.commons</name>
    </assembly>
    <members>
        <member name="T:iText.Commons.Actions.AbstractContextBasedEventHandler">
            <summary>Base class for events handling depending on the context.</summary>
        </member>
        <member name="M:iText.Commons.Actions.AbstractContextBasedEventHandler.#ctor(iText.Commons.Actions.Contexts.IContext)">
            <summary>
            Creates a new instance of the handler with the defined fallback for events within unknown
            contexts.
            </summary>
            <param name="onUnknownContext">is a fallback for events within unknown context</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractContextBasedEventHandler.OnEvent(iText.Commons.Actions.IEvent)">
            <summary>
            Performs context validation and if event is allowed to be processed passes it to
            <see cref="M:iText.Commons.Actions.AbstractContextBasedEventHandler.OnAcceptedEvent(iText.Commons.Actions.AbstractContextBasedITextEvent)"/>.
            </summary>
            <param name="event">to handle</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractContextBasedEventHandler.OnAcceptedEvent(iText.Commons.Actions.AbstractContextBasedITextEvent)">
            <summary>Handles the accepted event.</summary>
            <param name="event">to handle</param>
        </member>
        <member name="T:iText.Commons.Actions.AbstractContextBasedITextEvent">
            <summary>Represents a context-based event.</summary>
            <remarks>
            Represents a context-based event. See also
            <see cref="T:iText.Commons.Actions.AbstractContextBasedEventHandler"/>.
            Only for internal usage.
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.AbstractContextBasedITextEvent.#ctor(iText.Commons.Actions.Data.ProductData,iText.Commons.Actions.Contexts.IMetaInfo)">
            <summary>Creates an event containing auxiliary meta data.</summary>
            <param name="productData">is a description of the product which has generated an event</param>
            <param name="metaInfo">is an auxiliary meta info</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractContextBasedITextEvent.GetClassFromContext">
            <summary>Obtains the current event context class.</summary>
            <returns>context class</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractContextBasedITextEvent.SetMetaInfo(iText.Commons.Actions.Contexts.IMetaInfo)">
            <summary>Sets meta info.</summary>
            <param name="metaInfo">meta info</param>
            <returns>true if meta info has been set, false otherwise</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractContextBasedITextEvent.GetMetaInfo">
            <summary>Obtains stored meta info associated with the event.</summary>
            <returns>meta info</returns>
        </member>
        <member name="T:iText.Commons.Actions.AbstractEventWrapper">
            <summary>Base class to wrap events.</summary>
        </member>
        <member name="M:iText.Commons.Actions.AbstractEventWrapper.#ctor(iText.Commons.Actions.AbstractProductProcessITextEvent,iText.Commons.Actions.Confirmations.EventConfirmationType)">
            <summary>Creates a wrapper for the event.</summary>
            <param name="event">
            is a
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>
            to wrap
            </param>
            <param name="confirmationType">event confirmation type</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractEventWrapper.#ctor(iText.Commons.Actions.Sequence.SequenceId,iText.Commons.Actions.AbstractProductProcessITextEvent,iText.Commons.Actions.Confirmations.EventConfirmationType)">
            <summary>
            Creates a wrapper of event associated with
            <see cref="T:iText.Commons.Actions.Sequence.SequenceId"/>.
            </summary>
            <param name="updatedSequenceId">
            is a
            <see cref="T:iText.Commons.Actions.Sequence.SequenceId"/>
            for the document. May be different with
            sequence id of original event
            </param>
            <param name="event">
            is a
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>
            to wrap
            </param>
            <param name="confirmationType">event confirmation type</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractEventWrapper.GetEvent">
            <summary>Obtains the wrapped event.</summary>
            <returns>wrapped event</returns>
        </member>
        <member name="T:iText.Commons.Actions.AbstractITextConfigurationEvent">
            <summary>Abstract class which represents system configuration events.</summary>
            <remarks>Abstract class which represents system configuration events. Only for internal usage.</remarks>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextConfigurationEvent.AddProcessor(iText.Commons.Actions.Processors.ITextProductEventProcessor)">
            <summary>
            Adds a new
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>
            for a product.
            </summary>
            <param name="processor">is a new processor</param>
            <returns>a replaced processor for the product</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextConfigurationEvent.RemoveProcessor(System.String)">
            <summary>Removes a processor registered for a product.</summary>
            <param name="productName">is a product for which processor is removed</param>
            <returns>removed processor</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextConfigurationEvent.GetActiveProcessor(System.String)">
            <summary>Gets a processor registered for a product.</summary>
            <remarks>
            Gets a processor registered for a product.
            <para />
            If processor isn't registered and product supports AGPL mode
            <see cref="T:iText.Commons.Actions.Processors.DefaultITextProductEventProcessor"/>
            will be obtained otherwise null will be returned.
            </remarks>
            <param name="productName">is a product for which processor is obtained</param>
            <returns>processor for the product</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextConfigurationEvent.GetProcessors">
            <summary>Gets an unmodifiable map of registered processors.</summary>
            <returns>all processors</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextConfigurationEvent.GetEvents(iText.Commons.Actions.Sequence.SequenceId)">
            <summary>Gets events registered for provided identifier.</summary>
            <param name="id">is the identifier</param>
            <returns>the list of event for identifier</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextConfigurationEvent.AddEvent(iText.Commons.Actions.Sequence.SequenceId,iText.Commons.Actions.AbstractProductProcessITextEvent)">
            <summary>Registers a new event for provided identifier.</summary>
            <param name="id">is the identifier</param>
            <param name="event">is the event to register</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextConfigurationEvent.RegisterInternalNamespace(System.String)">
            <summary>Registers internal namespace.</summary>
            <param name="namespace">is the namespace to register</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextConfigurationEvent.DoAction">
            <summary>Method defines the logic of action processing.</summary>
        </member>
        <member name="T:iText.Commons.Actions.AbstractITextEvent">
            <summary>Abstract class which defines events only for internal usage.</summary>
        </member>
        <member name="M:iText.Commons.Actions.AbstractITextEvent.#ctor">
            <summary>Creates an instance of abstract iText event.</summary>
            <remarks>Creates an instance of abstract iText event. Only for internal usage.</remarks>
        </member>
        <member name="T:iText.Commons.Actions.AbstractProductITextEvent">
            <summary>
            Abstract class which defines general product events by encapsulating
            <see cref="T:iText.Commons.Actions.Data.ProductData"/>
            of the product which generated event.
            </summary>
            <remarks>
            Abstract class which defines general product events by encapsulating
            <see cref="T:iText.Commons.Actions.Data.ProductData"/>
            of the product which generated event. Only for internal usage.
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.AbstractProductITextEvent.#ctor(iText.Commons.Actions.Data.ProductData)">
            <summary>
            Creates instance of abstract product iText event based
            on passed product data.
            </summary>
            <remarks>
            Creates instance of abstract product iText event based
            on passed product data. Only for internal usage.
            </remarks>
            <param name="productData">is a description of the product which has generated an event</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractProductITextEvent.GetProductData">
            <summary>Gets a product data which generated the event.</summary>
            <returns>information about the product</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractProductITextEvent.GetProductName">
            <summary>Gets a name of product which generated the event.</summary>
            <returns>product name</returns>
        </member>
        <member name="T:iText.Commons.Actions.AbstractProductProcessITextEvent">
            <summary>Abstract class which defines product process event.</summary>
            <remarks>Abstract class which defines product process event. Only for internal usage.</remarks>
        </member>
        <member name="M:iText.Commons.Actions.AbstractProductProcessITextEvent.#ctor(iText.Commons.Actions.Sequence.SequenceId,iText.Commons.Actions.Data.ProductData,iText.Commons.Actions.Contexts.IMetaInfo,iText.Commons.Actions.Confirmations.EventConfirmationType)">
            <summary>
            Creates an event associated with
            <see cref="T:iText.Commons.Actions.Sequence.SequenceId"/>.
            </summary>
            <remarks>
            Creates an event associated with
            <see cref="T:iText.Commons.Actions.Sequence.SequenceId"/>
            . It may contain auxiliary meta data.
            </remarks>
            <param name="sequenceId">is a general identifier for the event</param>
            <param name="productData">is a description of the product which has generated an event</param>
            <param name="metaInfo">is an auxiliary meta info</param>
            <param name="confirmationType">
            defines when the event should be confirmed to notify that the
            associated process has finished successfully
            </param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractProductProcessITextEvent.#ctor(iText.Commons.Actions.Data.ProductData,iText.Commons.Actions.Contexts.IMetaInfo,iText.Commons.Actions.Confirmations.EventConfirmationType)">
            <summary>Creates an event which is not associated with any object.</summary>
            <remarks>Creates an event which is not associated with any object. It may contain auxiliary meta data.</remarks>
            <param name="productData">is a description of the product which has generated an event</param>
            <param name="metaInfo">is an auxiliary meta info</param>
            <param name="confirmationType">
            defines when the event should be confirmed to notify that the
            associated process has finished successfully
            </param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractProductProcessITextEvent.GetSequenceId">
            <summary>Retrieves an identifier of event source.</summary>
            <returns>an identifier of event source</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractProductProcessITextEvent.GetEventType">
            <summary>Returns an event type.</summary>
            <returns>event type</returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractProductProcessITextEvent.GetConfirmationType">
            <summary>
            Retrieves an
            <see cref="T:iText.Commons.Actions.Confirmations.EventConfirmationType">event confirmation type</see>.
            </summary>
            <returns>
            value of event confirmation type which defines when the event should be confirmed
            to notify that the associated process has finished successfully
            </returns>
        </member>
        <member name="T:iText.Commons.Actions.AbstractStatisticsAggregator">
            <summary>Abstract class for statistics aggregation.</summary>
            <remarks>Abstract class for statistics aggregation. Note that aggregator class must be thread safe.</remarks>
        </member>
        <member name="M:iText.Commons.Actions.AbstractStatisticsAggregator.Aggregate(iText.Commons.Actions.AbstractStatisticsEvent)">
            <summary>Aggregates data from the provided event.</summary>
            <param name="event">
            
            <see cref="T:iText.Commons.Actions.AbstractStatisticsEvent"/>
            instance
            </param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractStatisticsAggregator.RetrieveAggregation">
            <summary>Retrieves aggregated data.</summary>
            <returns>
            aggregated data as
            <see cref="T:System.Object"/>
            </returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractStatisticsAggregator.Merge(iText.Commons.Actions.AbstractStatisticsAggregator)">
            <summary>Merges data from the provided aggregator into this aggregator.</summary>
            <param name="aggregator">from which data will be taken.</param>
        </member>
        <member name="T:iText.Commons.Actions.AbstractStatisticsEvent">
            <summary>Abstract class which defines statistics event.</summary>
            <remarks>Abstract class which defines statistics event. Only for internal usage.</remarks>
        </member>
        <member name="M:iText.Commons.Actions.AbstractStatisticsEvent.#ctor(iText.Commons.Actions.Data.ProductData)">
            <summary>Creates instance of abstract statistics iText event based on passed product data.</summary>
            <remarks>Creates instance of abstract statistics iText event based on passed product data. Only for internal usage.
                </remarks>
            <param name="productData">is a description of the product which has generated an event</param>
        </member>
        <member name="M:iText.Commons.Actions.AbstractStatisticsEvent.CreateStatisticsAggregatorFromName(System.String)">
            <summary>Creates statistics aggregator based on provided statistics name.</summary>
            <remarks>
            Creates statistics aggregator based on provided statistics name.
            By default prints log warning and returns <c>null</c>.
            </remarks>
            <param name="statisticsName">
            name of statistics based on which aggregator will be created.
            Shall be one of those returned from
            <see cref="M:iText.Commons.Actions.AbstractStatisticsEvent.GetStatisticsNames"/>
            </param>
            <returns>
            new instance of
            <see cref="T:iText.Commons.Actions.AbstractStatisticsAggregator"/>
            </returns>
        </member>
        <member name="M:iText.Commons.Actions.AbstractStatisticsEvent.GetStatisticsNames">
            <summary>Gets all statistics names related to this event.</summary>
            <returns>
            
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            of statistics names
            </returns>
        </member>
        <member name="T:iText.Commons.Actions.Confirmations.ConfirmedEventWrapper">
            <summary>
            A wrapper for a
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>
            storing additional data about the event.
            </summary>
            <remarks>
            A wrapper for a
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>
            storing additional data about the event.
            If wrapped event is immutable then the instance of the wrapper is immutable too.
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.Confirmations.ConfirmedEventWrapper.#ctor(iText.Commons.Actions.AbstractProductProcessITextEvent,System.String,System.String)">
            <summary>Creates a wrapper for the event with additional data about the event.</summary>
            <param name="event">
            is a
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>
            to wrap
            </param>
            <param name="productUsageType">is a product usage marker</param>
            <param name="producerLine">
            is a producer line defined by the
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>
            which registered the event
            </param>
        </member>
        <member name="M:iText.Commons.Actions.Confirmations.ConfirmedEventWrapper.GetProductUsageType">
            <summary>Obtains the license type for the product which generated the event.</summary>
            <returns>product usage type</returns>
        </member>
        <member name="M:iText.Commons.Actions.Confirmations.ConfirmedEventWrapper.GetProducerLine">
            <summary>
            Gets producer line defined by the
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>
            which registered the
            event.
            </summary>
            <returns>producer line</returns>
        </member>
        <member name="T:iText.Commons.Actions.Confirmations.ConfirmEvent">
            <summary>
            Used to confirm that process associated with some
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>
            ended successfully.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Confirmations.ConfirmEvent.#ctor(iText.Commons.Actions.Sequence.SequenceId,iText.Commons.Actions.AbstractProductProcessITextEvent)">
            <summary>Creates an instance of confirmation event.</summary>
            <param name="updatedSequenceId">
            is a
            <see cref="T:iText.Commons.Actions.Sequence.SequenceId"/>
            for the document. May be different with
            sequence id of original event
            </param>
            <param name="confirmedEvent">is an event to confirm</param>
        </member>
        <member name="M:iText.Commons.Actions.Confirmations.ConfirmEvent.#ctor(iText.Commons.Actions.AbstractProductProcessITextEvent)">
            <summary>Creates an instance of confirmation event.</summary>
            <param name="confirmedEvent">is an event to confirm</param>
        </member>
        <member name="M:iText.Commons.Actions.Confirmations.ConfirmEvent.GetConfirmedEvent">
            <summary>
            Returns the
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>
            associated with confirmed process.
            </summary>
            <returns>confirmed event</returns>
        </member>
        <member name="T:iText.Commons.Actions.Confirmations.EventConfirmationType">
            <summary>
            Defines the strategy of
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>
            confirming.
            </summary>
        </member>
        <member name="F:iText.Commons.Actions.Confirmations.EventConfirmationType.ON_DEMAND">
            <summary>
            The successful execution of the process associated with the event should be confirmed by the
            second invocation of the
            <see cref="M:iText.Commons.Actions.EventManager.OnEvent(iText.Commons.Actions.IEvent)"/>
            method.
            </summary>
        </member>
        <member name="F:iText.Commons.Actions.Confirmations.EventConfirmationType.ON_CLOSE">
            <summary>
            The successful execution of the process associated with the event will be confirmed during
            the end of processing.
            </summary>
        </member>
        <member name="F:iText.Commons.Actions.Confirmations.EventConfirmationType.UNCONFIRMABLE">
            <summary>The process associated with the event shouldn't be confirmed.</summary>
        </member>
        <member name="T:iText.Commons.Actions.Contexts.AbstractContextManagerConfigurationEvent">
            <summary>Class is recommended for internal usage.</summary>
            <remarks>Class is recommended for internal usage. Represents system configuration events.</remarks>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.AbstractContextManagerConfigurationEvent.#ctor">
            <summary>Creates an instance of context manager configuration event.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.AbstractContextManagerConfigurationEvent.RegisterGenericContext(System.Collections.Generic.ICollection{System.String},System.Collections.Generic.ICollection{System.String})">
            <summary>Registers generic context for products and namespaces which are associated with them.</summary>
            <param name="namespaces">namespaces of the products to be registered</param>
            <param name="products">the products to be registered</param>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.AbstractContextManagerConfigurationEvent.UnregisterContext(System.Collections.Generic.ICollection{System.String})">
            <summary>Unregisters certain namespaces.</summary>
            <param name="namespaces">the namespaces to be unregistered</param>
        </member>
        <member name="T:iText.Commons.Actions.Contexts.ContextManager">
            <summary>The class that retrieves context of its invocation.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.ContextManager.GetInstance">
            <summary>Gets the singleton instance of this class.</summary>
            <returns>
            the
            <see cref="T:iText.Commons.Actions.Contexts.ContextManager"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.ContextManager.GetContext(System.Type)">
            <summary>Gets the context associated with the passed class object.</summary>
            <remarks>
            Gets the context associated with the passed class object.
            The context is determined by class namespace.
            </remarks>
            <param name="clazz">the class for which the context will be determined.</param>
            <returns>
            the
            <see cref="T:iText.Commons.Actions.Contexts.IContext"/>
            associated with the class, or
            <see langword="null"/>
            if the class is unknown.
            </returns>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.ContextManager.GetContext(System.String)">
            <summary>Gets the context associated with the passed class object.</summary>
            <remarks>
            Gets the context associated with the passed class object.
            The context is determined by class namespace.
            </remarks>
            <param name="className">the class name with the namespace for which the context will be determined.</param>
            <returns>
            the
            <see cref="T:iText.Commons.Actions.Contexts.IContext"/>
            associated with the class, or
            <see langword="null"/>
            if the class is unknown.
            </returns>
        </member>
        <member name="T:iText.Commons.Actions.Contexts.GenericContext">
            <summary>
            Generic context that allows
            <see cref="T:iText.Commons.Actions.AbstractContextBasedITextEvent"/>
            based on the whitelist of supported events.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.GenericContext.#ctor(System.Collections.Generic.ICollection{System.String})">
            <summary>Creates a Generic Context instance with support of provided namespaces and products.</summary>
            <param name="supportedProducts">is a collection of supported products</param>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.GenericContext.IsAllowed(iText.Commons.Actions.AbstractContextBasedITextEvent)">
            <summary>Checks if the source product of the event is supported by the context.</summary>
            <param name="event">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Commons.Actions.Contexts.IContext">
            <summary>The class that determines weather event should be processed or not.</summary>
            <remarks>
            The class that determines weather event should be processed or not.
            Is calculated by the
            <see cref="T:iText.Commons.Actions.Contexts.ContextManager"/>
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.IContext.IsAllowed(iText.Commons.Actions.AbstractContextBasedITextEvent)">
            <summary>Determines weather event should be processed or not.</summary>
            <param name="event">is an event to test</param>
            <returns>true if event is allowed by the actual context and false otherwise</returns>
        </member>
        <member name="T:iText.Commons.Actions.Contexts.IMetaInfo">
            <summary>The meta info that can holds information about instance that throws the event.</summary>
        </member>
        <member name="T:iText.Commons.Actions.Contexts.UnknownContext">
            <summary>
            The fallback
            <see cref="T:iText.Commons.Actions.Contexts.IContext"/>.
            </summary>
        </member>
        <member name="F:iText.Commons.Actions.Contexts.UnknownContext.RESTRICTIVE">
            <summary>
            The
            <see cref="T:iText.Commons.Actions.Contexts.IContext"/>
            that forbids all events.
            </summary>
        </member>
        <member name="F:iText.Commons.Actions.Contexts.UnknownContext.PERMISSIVE">
            <summary>
            The
            <see cref="T:iText.Commons.Actions.Contexts.IContext"/>
            that allows all events.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.UnknownContext.#ctor(System.Boolean)">
            <summary>
            Creates a fallback
            <see cref="T:iText.Commons.Actions.Contexts.IContext"/>.
            </summary>
            <param name="allowEvents">defines whether the context allows all events or not</param>
        </member>
        <member name="M:iText.Commons.Actions.Contexts.UnknownContext.IsAllowed(iText.Commons.Actions.AbstractContextBasedITextEvent)">
            <summary>Depending on its internal state allows or rejects all event.</summary>
            <remarks>
            Depending on its internal state allows or rejects all event.
            Behaviour is defined via constructor
            <see cref="M:iText.Commons.Actions.Contexts.UnknownContext.#ctor(System.Boolean)"/>
            </remarks>
            <param name="event">
            
            <inheritDoc/>
            </param>
            <returns>
            
            <inheritDoc/>
            </returns>
        </member>
        <member name="T:iText.Commons.Actions.Data.CommonsProductData">
            <summary>
            Stores an instance of
            <see cref="T:iText.Commons.Actions.Data.ProductData"/>
            related to iText commons module.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Data.CommonsProductData.GetInstance">
            <summary>
            Getter for an instance of
            <see cref="T:iText.Commons.Actions.Data.ProductData"/>
            related to iText commons module.
            </summary>
            <returns>iText commons product description</returns>
        </member>
        <member name="T:iText.Commons.Actions.Data.ProductData">
            <summary>Class is used to describe used product information.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Data.ProductData.#ctor(System.String,System.String,System.String,System.Int32,System.Int32)">
            <summary>Creates a new instance of product data.</summary>
            <param name="publicProductName">is a product name</param>
            <param name="productName">is a technical name of the product</param>
            <param name="version">is a version of the product</param>
            <param name="sinceCopyrightYear">is the first year of a product development</param>
            <param name="toCopyrightYear">is a last year of a product development</param>
        </member>
        <member name="M:iText.Commons.Actions.Data.ProductData.#ctor(System.String,System.String,System.String,System.String,System.Int32,System.Int32)">
            <summary>Creates a new instance of product data.</summary>
            <param name="publicProductName">is a product name</param>
            <param name="productName">is a technical name of the product</param>
            <param name="version">is a version of the product</param>
            <param name="minimalCompatibleLicenseKeyVersion">is a minimal compatible version of licensekey library</param>
            <param name="sinceCopyrightYear">is the first year of a product development</param>
            <param name="toCopyrightYear">is a last year of a product development</param>
        </member>
        <member name="M:iText.Commons.Actions.Data.ProductData.GetPublicProductName">
            <summary>Getter for a product name.</summary>
            <returns>product name</returns>
        </member>
        <member name="M:iText.Commons.Actions.Data.ProductData.GetProductName">
            <summary>Getter for a technical name of the product.</summary>
            <returns>the technical name of the product</returns>
        </member>
        <member name="M:iText.Commons.Actions.Data.ProductData.GetVersion">
            <summary>Getter for a version of the product.</summary>
            <returns>version of the product</returns>
        </member>
        <member name="M:iText.Commons.Actions.Data.ProductData.GetSinceCopyrightYear">
            <summary>Getter for the first year of copyright period.</summary>
            <returns>the first year of copyright</returns>
        </member>
        <member name="M:iText.Commons.Actions.Data.ProductData.GetToCopyrightYear">
            <summary>Getter for the last year of copyright period.</summary>
            <returns>the last year of copyright</returns>
        </member>
        <member name="M:iText.Commons.Actions.Data.ProductData.GetMinCompatibleLicensingModuleVersion">
            <summary>Getter for the minimal compatible licensekey version.</summary>
            <returns>minimal compatible version of licensekey library.</returns>
        </member>
        <member name="T:iText.Commons.Actions.EventManager">
            <summary>Entry point for event handling mechanism.</summary>
            <remarks>
            Entry point for event handling mechanism. Class is a singleton,
            see
            <see cref="M:iText.Commons.Actions.EventManager.GetInstance"/>.
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.EventManager.GetInstance">
            <summary>Allows an access to the instance of EventManager.</summary>
            <returns>the instance of the class</returns>
        </member>
        <member name="M:iText.Commons.Actions.EventManager.AcknowledgeAgplUsageDisableWarningMessage">
            <summary>Deliberately turns off the warning message about AGPL usage.</summary>
            <remarks>
            Deliberately turns off the warning message about AGPL usage.
            <para />
            <b> Important note. Calling of this method means that the terms of AGPL license are met. </b>
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.EventManager.OnEvent(iText.Commons.Actions.IEvent)">
            <summary>Handles the event.</summary>
            <param name="event">to handle</param>
        </member>
        <member name="M:iText.Commons.Actions.EventManager.Register(iText.Commons.Actions.IEventHandler)">
            <summary>
            Add new
            <see cref="T:iText.Commons.Actions.IEventHandler"/>
            to the event handling process.
            </summary>
            <param name="handler">is a handler to add</param>
        </member>
        <member name="M:iText.Commons.Actions.EventManager.IsRegistered(iText.Commons.Actions.IEventHandler)">
            <summary>Check if the handler was registered for event handling process.</summary>
            <param name="handler">is a handler to check</param>
            <returns>true if handler has been already registered and false otherwise</returns>
        </member>
        <member name="M:iText.Commons.Actions.EventManager.Unregister(iText.Commons.Actions.IEventHandler)">
            <summary>Removes handler from event handling process.</summary>
            <param name="handler">is a handle to remove</param>
            <returns>
            true if the handler had been registered previously and was removed. False if the
            handler was not found among registered handlers
            </returns>
        </member>
        <member name="T:iText.Commons.Actions.IEvent">
            <summary>Base marker interface for any generated event of any source.</summary>
        </member>
        <member name="T:iText.Commons.Actions.IEventHandler">
            <summary>The interface for an event handler.</summary>
        </member>
        <member name="M:iText.Commons.Actions.IEventHandler.OnEvent(iText.Commons.Actions.IEvent)">
            <summary>Handles the event.</summary>
            <param name="event">to handle</param>
        </member>
        <member name="T:iText.Commons.Actions.NamespaceConstant">
            <summary>Class that stores namespaces of iText open source products.</summary>
        </member>
        <member name="T:iText.Commons.Actions.Processors.AbstractITextProductEventProcessor">
            <summary>Abstract class with some standard functionality for product event processing.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Processors.AbstractITextProductEventProcessor.#ctor(System.String)">
            <summary>Creates a new instance of an abstract processor for the provided product.</summary>
            <param name="productName">the product which will be handled by this processor</param>
        </member>
        <member name="T:iText.Commons.Actions.Processors.DefaultITextProductEventProcessor">
            <summary>Defines a default strategy of product event processing.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Processors.DefaultITextProductEventProcessor.#ctor(System.String)">
            <summary>Creates an instance of product event processor.</summary>
            <param name="productName">is a product name</param>
        </member>
        <member name="T:iText.Commons.Actions.Processors.DefaultProductProcessorFactory">
            <summary>
            Default factory class to construct
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Processors.DefaultProductProcessorFactory.CreateProcessor(System.String)">
            <summary>Creates default product processor using a product name.</summary>
            <param name="productName">the product which will be handled by this processor</param>
            <returns>
            current
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>
            instance
            </returns>
        </member>
        <member name="T:iText.Commons.Actions.Processors.IProductProcessorFactory">
            <summary>
            Class represents a factory for
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>
            objects.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Processors.IProductProcessorFactory.CreateProcessor(System.String)">
            <summary>Creates product processor using a product name.</summary>
            <param name="productName">the product which will be handled by this processor</param>
            <returns>the processor instance</returns>
        </member>
        <member name="T:iText.Commons.Actions.Processors.ITextProductEventProcessor">
            <summary>Interface for product event processors.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Processors.ITextProductEventProcessor.OnEvent(iText.Commons.Actions.AbstractProductProcessITextEvent)">
            <summary>
            Handles the
            <see cref="T:iText.Commons.Actions.AbstractProductProcessITextEvent"/>.
            </summary>
            <param name="event">to handle</param>
        </member>
        <member name="M:iText.Commons.Actions.Processors.ITextProductEventProcessor.GetProductName">
            <summary>Gets the name of the product to which this processor corresponds.</summary>
            <returns>the product name</returns>
        </member>
        <member name="M:iText.Commons.Actions.Processors.ITextProductEventProcessor.GetUsageType">
            <summary>Gets the usage type of the product to which this processor corresponds.</summary>
            <returns>the usage type</returns>
        </member>
        <member name="M:iText.Commons.Actions.Processors.ITextProductEventProcessor.GetProducer">
            <summary>Gets the producer line for the product.</summary>
            <returns>the producer line</returns>
        </member>
        <member name="T:iText.Commons.Actions.Processors.UnderAgplITextProductEventProcessor">
            <summary>The class defines an under APGL strategy of product event processing.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Processors.UnderAgplITextProductEventProcessor.#ctor(System.String)">
            <summary>Creates a new instance of under AGPL processor for the provided product.</summary>
            <param name="productName">the product which will be handled by this processor</param>
        </member>
        <member name="T:iText.Commons.Actions.Processors.UnderAgplProductProcessorFactory">
            <summary>
            Factory class to construct
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>
            instance under AGPL license.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Processors.UnderAgplProductProcessorFactory.CreateProcessor(System.String)">
            <summary>Creates under AGPL product processor using a product name.</summary>
            <param name="productName">the product which will be handled by this processor</param>
            <returns>
            current
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>
            instance
            </returns>
        </member>
        <member name="T:iText.Commons.Actions.Producer.AbstractFormattedPlaceholderPopulator">
            <summary>Abstract populator for placeholders consuming a parameter which is a pattern string.</summary>
            <remarks>
            Abstract populator for placeholders consuming a parameter which is a pattern string. Any latin
            letter inside the pattern which is not quoted considered as a param defining the component of the
            outputted value.
            </remarks>
        </member>
        <member name="F:iText.Commons.Actions.Producer.AbstractFormattedPlaceholderPopulator.APOSTROPHE">
            <summary>Escaping character.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Producer.AbstractFormattedPlaceholderPopulator.AttachQuotedString(System.Int32,System.Text.StringBuilder,System.Char[])">
            <summary>Processes quoted string inside format array.</summary>
            <remarks>
            Processes quoted string inside format array. It is expected that provided index points to the
            apostrophe character so that since the <c>index + 1</c> position quoted string starts.
            <para />
            String may contain escaped apostrophes <c>\'</c> which processed as characters.
            Backslash is used for escaping so you need double backslash to print it <c>\\</c>. All
            the rest backslashes (not followed by apostrophe or one more backslash) are simply ignored.
            </remarks>
            <param name="index">is a index of apostrophe starting a new quoted string</param>
            <param name="builder">
            is a
            <see cref="T:System.Text.StringBuilder"/>
            building a resulting formatted string. It is
            updated by the method: quoted string is attached
            </param>
            <param name="formatArray">is a format representation</param>
            <returns>index of the character after the closing apostrophe</returns>
        </member>
        <member name="M:iText.Commons.Actions.Producer.AbstractFormattedPlaceholderPopulator.IsLetter(System.Char)">
            <summary>Checks if provided character is a latin letter.</summary>
            <param name="ch">is character to check</param>
            <returns><c>true</c> if character is a latin letter and <c>false</c> otherwise</returns>
        </member>
        <member name="T:iText.Commons.Actions.Producer.CopyrightSincePlaceholderPopulator">
            <summary>Class is used to populate <c>copyrightSince</c> placeholder.</summary>
            <remarks>
            Class is used to populate <c>copyrightSince</c> placeholder. The resulting string is a
            representation of the first year of copyright years range. Among all products involved into
            product creation the earliest <c>copyrightSince</c> year is picked as a resulting value.
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.Producer.CopyrightSincePlaceholderPopulator.Populate(System.Collections.Generic.IList{iText.Commons.Actions.Confirmations.ConfirmedEventWrapper},System.String)">
            <summary>
            Builds a replacement for a placeholder <c>copyrightSince</c> in accordance with the
            registered events.
            </summary>
            <param name="events">
            is a list of event involved into document processing. It is expected that it
            is not empty as such cases should be handled by
            <see cref="T:iText.Commons.Actions.Producer.ProducerBuilder"/>
            without
            calling any
            <see cref="T:iText.Commons.Actions.Producer.IPlaceholderPopulator"/>
            </param>
            <param name="parameter">
            is a parameter for the placeholder. It should be <c>null</c> as
            <c>copyrightSince</c> as the placeholder is not configurable
            </param>
            <returns>the earliest copyright year</returns>
        </member>
        <member name="T:iText.Commons.Actions.Producer.CopyrightToPlaceholderPopulator">
            <summary>Class is used to populate <c>copyrightTo</c> placeholder.</summary>
            <remarks>
            Class is used to populate <c>copyrightTo</c> placeholder. The resulting string is a
            representation of the last year of copyright years range. Among all products involved into
            product creation the latest <c>copyrightTo</c> year is picked as a resulting value
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.Producer.CopyrightToPlaceholderPopulator.Populate(System.Collections.Generic.IList{iText.Commons.Actions.Confirmations.ConfirmedEventWrapper},System.String)">
            <summary>
            Builds a replacement for a placeholder <c>copyrightTo</c> in accordance with the
            registered events.
            </summary>
            <param name="events">
            is a list of event involved into document processing. It is expected that it
            is not empty as such cases should be handled by
            <see cref="T:iText.Commons.Actions.Producer.ProducerBuilder"/>
            without
            calling any
            <see cref="T:iText.Commons.Actions.Producer.IPlaceholderPopulator"/>
            </param>
            <param name="parameter">
            is a parameter for the placeholder. It should be <c>null</c> as
            <c>copyrightTo</c> as the placeholder is not configurable
            </param>
            <returns>the latest copyright year</returns>
        </member>
        <member name="T:iText.Commons.Actions.Producer.CurrentDatePlaceholderPopulator">
            <summary>Class is used to populate <c>currentDate</c> placeholder.</summary>
            <remarks>
            Class is used to populate <c>currentDate</c> placeholder. Placeholder should be configured
            with parameter defining the format of date output. Within format strings, unquoted letters from
            <c>A</c> to <c>Z</c> and from <c>a</c> to <c>z</c> are process as pattern
            letters. Chain of equal pattern letters forms an appropriate component of
            <c>currentDate</c> format. There following components are supported:
            <para />
            <list type="bullet">
            <item><description><c>d</c> is for the day of the month, from 1 through 31
            </description></item>
            <item><description><c>dd</c> is for the day of the month, from 01 through 31
            </description></item>
            <item><description><c>M</c> defines the month from 1 to 12
            </description></item>
            <item><description><c>MM</c> defines the month from 01 to 12
            </description></item>
            <item><description><c>MMM</c> defines the abbreviated name of the month
            </description></item>
            <item><description><c>MMMM</c> defines the full name of month
            </description></item>
            <item><description><c>yy</c> means the year from 00 to 99
            </description></item>
            <item><description><c>yyyy</c> means the year in for digits format
            </description></item>
            <item><description><c>s</c> shows current second, from 0 through 59
            </description></item>
            <item><description><c>ss</c> shows current second, from 00 through 59
            </description></item>
            <item><description><c>m</c> is replaced with the current minute from 0 to 59
            </description></item>
            <item><description><c>mm</c> is replaced with the current minute from 00 to 59
            </description></item>
            <item><description><c>H</c> stands for the current hour, using a 24-hour clock from 0 to 23
            </description></item>
            <item><description><c>HH</c> stands for the current hour, using a 24-hour clock from 00 to 23
            </description></item>
            </list>
            <para />
            Text can be quoted using single quotes (') to avoid interpretation. All other characters are not
            interpreted and just copied into the output string. String may contain escaped apostrophes
            <c>\'</c> which processed as characters. Backslash is used for escaping so you need double
            backslash to print it <c>\\</c>. All the rest backslashes (not followed by apostrophe or
            one more backslash) are simply ignored.
            <para />
            The result of the processing is current date representing in accordance with the provided format.
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.Producer.CurrentDatePlaceholderPopulator.Populate(System.Collections.Generic.IList{iText.Commons.Actions.Confirmations.ConfirmedEventWrapper},System.String)">
            <summary>
            Builds a replacement for a placeholder <c>currentDate</c> in accordance with the
            provided format.
            </summary>
            <param name="events">
            is a list of event involved into document processing. It is not used during
            the placeholder replacement
            </param>
            <param name="parameter">defines output format in accordance with the description</param>
            <returns>date of producer line creation in accordance with defined format</returns>
        </member>
        <member name="T:iText.Commons.Actions.Producer.IPlaceholderPopulator">
            <summary>Interface for placeholder population.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Producer.IPlaceholderPopulator.Populate(System.Collections.Generic.IList{iText.Commons.Actions.Confirmations.ConfirmedEventWrapper},System.String)">
            <summary>
            Populates a placeholder based on the list of
            <see cref="T:iText.Commons.Actions.Confirmations.ConfirmedEventWrapper"/>
            and the array of parts of placeholder.
            </summary>
            <param name="events">is a list of event involved into document processing</param>
            <param name="parameter">is a parameter passed to a placeholder and separated via delimiter <c>:</c></param>
            <returns>value for placeholder replacement</returns>
        </member>
        <member name="T:iText.Commons.Actions.Producer.ProducerBuilder">
            <summary>Class is used for producer line building.</summary>
        </member>
        <member name="F:iText.Commons.Actions.Producer.ProducerBuilder.PATTERN_STRING">
            <summary>Pattern is used to search a placeholders.</summary>
            <remarks>
            Pattern is used to search a placeholders. Currently it searches substrings started with
            <c>${</c> and ended with <c>}</c> without <c>}</c> character inside.
            These substrings are interpreted as placeholders and the first group is the content of the
            placeholder.
            Note: The escape on '}' is necessary for regex dialect compatibility reasons.
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.Producer.ProducerBuilder.ModifyProducer``1(System.Collections.Generic.IList{``0},System.String)">
            <summary>Modifies an old producer line according to events registered for the document.</summary>
            <remarks>
            Modifies an old producer line according to events registered for the document.
            Events can be either wrapped with
            <see cref="T:iText.Commons.Actions.Confirmations.ConfirmedEventWrapper"/>
            or not.
            Format of the new producer line will be defined by the first event in the list.
            Placeholder will be replaced and merged all together.
            </remarks>
            <param name="events">list of events registered for the document</param>
            <param name="oldProducer">
            old producer line. If <c>null</c> or empty, will be replaced
            with a new one. Otherwise new line will be attached with
            <c>modified using</c> prefix. If old producer line already contains
            <c>modified using itext</c> substring with the current version of itext at the end,
            no changes will be made
            </param>
            <returns>modified producer line</returns>
        </member>
        <member name="M:iText.Commons.Actions.Producer.ProducerBuilder.DoAction">
            <summary>Configuration events for util internal purposes are not expected to be sent.</summary>
        </member>
        <member name="T:iText.Commons.Actions.Producer.UsedProductsPlaceholderPopulator">
            <summary>Class is used to populate <c>usedProducts</c> placeholder.</summary>
            <remarks>
            Class is used to populate <c>usedProducts</c> placeholder. Placeholder should be configured
            with parameter defining the format of output. Within format strings, unquoted letters from
            <c>A</c> to <c>Z</c> and from <c>a</c> to <c>z</c> are process as pattern
            letters representing appropriate component of <c>usedProducts</c> format. There are three
            letters which are allowed in the outputformat:
            <para />
            <list type="bullet">
            <item><description><c>P</c> stands for product name
            </description></item>
            <item><description><c>V</c> stands for version of the product
            </description></item>
            <item><description><c>T</c> is for usage type of the product
            </description></item>
            </list>
            <para />
            Text can be quoted using single quotes (') to avoid interpretation. All other characters are not
            interpreted and just copied into the output string. String may contain escaped apostrophes
            <c>\'</c> which processed as characters. Backslash is used for escaping so you need double
            backslash to print it <c>\\</c>. All the rest backslashes (not followed by apostrophe or
            one more backslash) are simply ignored.
            <para />
            The result of the processing is the list of all products mentioned among events as a
            comma-separated list. The order of the elements is defined by the order of products mentioning in
            the <c>events</c>. Equal strings are skipped even if they were generated for different
            products (i. e. format <c>P</c> stands for product name only: if several version of the
            same product are used, it will be the only mentioning of that product).
            </remarks>
        </member>
        <member name="M:iText.Commons.Actions.Producer.UsedProductsPlaceholderPopulator.Populate(System.Collections.Generic.IList{iText.Commons.Actions.Confirmations.ConfirmedEventWrapper},System.String)">
            <summary>
            Builds a replacement for a placeholder <c>usedProducts</c> in accordance with the
            registered events and provided format.
            </summary>
            <param name="events">is a list of event involved into document processing</param>
            <param name="parameter">defines output format in accordance with the for description</param>
            <returns>populated comma-separated list of used products in accordance with the format</returns>
        </member>
        <member name="T:iText.Commons.Actions.ProductEventHandler">
            <summary>Handles events based oh their origin.</summary>
        </member>
        <member name="M:iText.Commons.Actions.ProductEventHandler.OnAcceptedEvent(iText.Commons.Actions.AbstractContextBasedITextEvent)">
            <summary>
            Pass the event to the appropriate
            <see cref="T:iText.Commons.Actions.Processors.ITextProductEventProcessor"/>.
            </summary>
            <param name="event">to handle</param>
        </member>
        <member name="T:iText.Commons.Actions.ProductNameConstant">
            <summary>Class which contains open source product name constants.</summary>
        </member>
        <member name="F:iText.Commons.Actions.ProductNameConstant.ITEXT_CORE">
            <summary>itext-core constant.</summary>
        </member>
        <member name="F:iText.Commons.Actions.ProductNameConstant.ITEXT_CORE_SIGN">
            <summary>itext-core sign module constant.</summary>
        </member>
        <member name="F:iText.Commons.Actions.ProductNameConstant.PDF_HTML">
            <summary>pdfhtml constant.</summary>
        </member>
        <member name="F:iText.Commons.Actions.ProductNameConstant.PDF_SWEEP">
            <summary>pdfsweep constant.</summary>
        </member>
        <member name="F:iText.Commons.Actions.ProductNameConstant.PDF_OCR_TESSERACT4">
            <summary>pdfocr-tesseract4 constant.</summary>
        </member>
        <member name="F:iText.Commons.Actions.ProductNameConstant.PRODUCT_NAMES">
            <summary>set of product names.</summary>
        </member>
        <member name="T:iText.Commons.Actions.ProductProcessorFactoryKeeper">
            <summary>Helper class which allow to change used product processor factory instance.</summary>
        </member>
        <member name="M:iText.Commons.Actions.ProductProcessorFactoryKeeper.SetProductProcessorFactory(iText.Commons.Actions.Processors.IProductProcessorFactory)">
            <summary>Sets product processor factory instance.</summary>
            <param name="productProcessorFactory">the instance to be set</param>
        </member>
        <member name="M:iText.Commons.Actions.ProductProcessorFactoryKeeper.RestoreDefaultProductProcessorFactory">
            <summary>Restores default factory.</summary>
        </member>
        <member name="M:iText.Commons.Actions.ProductProcessorFactoryKeeper.GetProductProcessorFactory">
            <summary>Gets reporting product processor factory instance.</summary>
            <returns>the product processor factory instance</returns>
        </member>
        <member name="T:iText.Commons.Actions.Sequence.AbstractIdentifiableElement">
            <summary>
            The class allows to make any element identifiable so that it is possible to store some metadata
            for it.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Sequence.AbstractIdentifiableElement.GetSequenceId">
            <summary>Obtains an identifier if it was set.</summary>
            <returns>identifier</returns>
        </member>
        <member name="M:iText.Commons.Actions.Sequence.AbstractIdentifiableElement.SetSequenceId(iText.Commons.Actions.Sequence.SequenceId)">
            <summary>Sets an identifier.</summary>
            <param name="sequenceId">is a new identifier for the element</param>
        </member>
        <member name="T:iText.Commons.Actions.Sequence.SequenceId">
            <summary>The class represents unique numeric identifier with autoincrement strategy of generation.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Sequence.SequenceId.#ctor">
            <summary>Creates a new instance of identifier.</summary>
        </member>
        <member name="M:iText.Commons.Actions.Sequence.SequenceId.GetId">
            <summary>Obtains an id.</summary>
            <returns>id</returns>
        </member>
        <member name="T:iText.Commons.Actions.Sequence.SequenceIdManager">
            <summary>
            Util class which is responsible for marking of
            <see cref="T:iText.Commons.Actions.Sequence.AbstractIdentifiableElement"/>
            with
            appropriate
            <see cref="T:iText.Commons.Actions.Sequence.SequenceId"/>.
            </summary>
        </member>
        <member name="M:iText.Commons.Actions.Sequence.SequenceIdManager.SetSequenceId(iText.Commons.Actions.Sequence.AbstractIdentifiableElement,iText.Commons.Actions.Sequence.SequenceId)">
            <summary>
            Provides an
            <see cref="T:iText.Commons.Actions.Sequence.AbstractIdentifiableElement"/>
            with a
            <see cref="T:iText.Commons.Actions.Sequence.SequenceId"/>.
            </summary>
            <remarks>
            Provides an
            <see cref="T:iText.Commons.Actions.Sequence.AbstractIdentifiableElement"/>
            with a
            <see cref="T:iText.Commons.Actions.Sequence.SequenceId"/>
            . Note that it is
            forbidden to override already existing identifier. If try to provide a new one then exception
            will be thrown.
            </remarks>
            <param name="element">is an identifiable element</param>
            <param name="sequenceId">is an identifier to set</param>
        </member>
        <member name="M:iText.Commons.Actions.Sequence.SequenceIdManager.GetSequenceId(iText.Commons.Actions.Sequence.AbstractIdentifiableElement)">
            <summary>Gets an identifier of the element.</summary>
            <remarks>Gets an identifier of the element. If it was not provided will return <c>null</c>.</remarks>
            <param name="element">is an identifiable element</param>
            <returns>the identifier of the element if presented and <c>null</c> otherwise</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cmp.IPkiFailureInfo">
            <summary>
            This interface represents the wrapper for PKIFailureInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Cmp.IPkiFailureInfo.IntValue">
            <summary>
            Calls actual
            <c>intValue</c>
            method for the wrapped PKIFailureInfo object.
            </summary>
            <returns>integer value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IAttribute">
            <summary>
            This interface represents the wrapper for Attribute that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Cms.IAttribute.GetAttrValues">
            <summary>
            Calls actual
            <c>getAttrValues</c>
            method for the wrapped Attribute object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Set"/>
            wrapped attribute values.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IAttributeTable">
            <summary>
            This interface represents the wrapper for AttributeTable that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Cms.IAttributeTable.Get(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier)">
            <summary>
            Calls actual
            <c>get</c>
            method for the wrapped AttributeTable object.
            </summary>
            <param name="oid">ASN1ObjectIdentifier wrapper</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Cms.IAttribute"/>
            wrapper for the received Attribute object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IContentInfo">
            <summary>
            This interface represents the wrapper for ContentInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IEncryptedContentInfo">
            <summary>
            This interface represents the wrapper for EncryptedContentInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IEnvelopedData">
            <summary>
            This interface represents the wrapper for EnvelopedData that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IIssuerAndSerialNumber">
            <summary>
            This interface represents the wrapper for IssuerAndSerialNumber that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IKeyTransRecipientInfo">
            <summary>
            This interface represents the wrapper for KeyTransRecipientInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IOriginatorInfo">
            <summary>
            This interface represents the wrapper for OriginatorInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IRecipientIdentifier">
            <summary>
            This interface represents the wrapper for RecipientIdentifier that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Cms.IRecipientInfo">
            <summary>
            This interface represents the wrapper for RecipientInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Esf.IOtherHashAlgAndValue">
            <summary>
            This interface represents the wrapper for OtherHashAlgAndValue that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyId">
            <summary>
            This interface represents the wrapper for SignaturePolicyId that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyIdentifier">
            <summary>
            This interface represents the wrapper for SignaturePolicyIdentifier that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Esf.ISigPolicyQualifierInfo">
            <summary>
            This interface represents the wrapper for SigPolicyQualifierInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ess.IEssCertID">
            <summary>
            This interface represents the wrapper for ESSCertID that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ess.IEssCertID.GetCertHash">
            <summary>
            Calls actual
            <c>getCertHash</c>
            method for the wrapped ESSCertID object.
            </summary>
            <returns>certificate hash byte array.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ess.IEssCertIDv2">
            <summary>
            This interface represents the wrapper for ESSCertIDv2 that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ess.IEssCertIDv2.GetHashAlgorithm">
            <summary>
            Calls actual
            <c>getHashAlgorithm</c>
            method for the wrapped ESSCertIDv2 object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier"/>
            hash algorithm wrapper.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ess.IEssCertIDv2.GetCertHash">
            <summary>
            Calls actual
            <c>getCertHash</c>
            method for the wrapped ESSCertIDv2 object.
            </summary>
            <returns>certificate hash byte array.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ess.ISigningCertificate">
            <summary>
            This interface represents the wrapper for SigningCertificate that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ess.ISigningCertificate.GetCerts">
            <summary>
            Calls actual
            <c>getCerts</c>
            method for the wrapped SigningCertificate object.
            </summary>
            <returns>
            array of wrapped certificates
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ess.IEssCertID"/>.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ess.ISigningCertificateV2">
            <summary>
            This interface represents the wrapper for SigningCertificateV2 that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ess.ISigningCertificateV2.GetCerts">
            <summary>
            Calls actual
            <c>getCerts</c>
            method for the wrapped SigningCertificateV2 object.
            </summary>
            <returns>
            array of wrapped certificates
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ess.IEssCertIDv2"/>.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable">
            <summary>
            This interface represents the wrapper for ASN1Encodable that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable.ToASN1Primitive">
            <summary>
            Calls actual
            <c>toASN1Primitive</c>
            method for the wrapped ASN1Encodable object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            wrapped ASN1Primitive object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable.IsNull">
            <summary>Checks if wrapped object is null.</summary>
            <returns>
            true if
            <see langword="null"/>
            is wrapped, false otherwise.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector">
            <summary>
            This interface represents the wrapper for ASN1EncodableVector that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector.Add(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>
            Calls actual
            <c>add</c>
            method for the wrapped ASN1EncodableVector object.
            </summary>
            <param name="primitive">ASN1Primitive wrapper.</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector.Add(iText.Commons.Bouncycastle.Asn1.Cms.IAttribute)">
            <summary>
            Calls actual
            <c>add</c>
            method for the wrapped ASN1EncodableVector object.
            </summary>
            <param name="attribute">Attribute wrapper.</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector.Add(iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier)">
            <summary>
            Calls actual
            <c>add</c>
            method for the wrapped ASN1EncodableVector object.
            </summary>
            <param name="element">AlgorithmIdentifier wrapper.</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector.AddOptional(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>
            Calls actual
            <c>add</c>
            method for the wrapped ASN1EncodableVector object if the primitive is not null.
            </summary>
            <param name="primitive">ASN1Primitive wrapper.</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector.AddOptional(iText.Commons.Bouncycastle.Asn1.Cms.IAttribute)">
            <summary>
            Calls actual
            <c>add</c>
            method for the wrapped ASN1EncodableVector object if the attribute is not null.
            </summary>
            <param name="attribute">Attribute wrapper.</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector.AddOptional(iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier)">
            <summary>
            Calls actual
            <c>add</c>
            method for the wrapped ASN1EncodableVector object if the element is not null.
            </summary>
            <param name="element">AlgorithmIdentifier wrapper.</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1Encoding">
            <summary>
            This interface represents the wrapper for ASN1Encoding that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Encoding.GetDer">
            <summary>
            Gets
            <c>DER</c>
            constant for the wrapped ASN1Encoding.
            </summary>
            <returns>ASN1Encoding.DER value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Encoding.GetBer">
            <summary>
            Gets
            <c>BER</c>
            constant for the wrapped ASN1Encoding.
            </summary>
            <returns>ASN1Encoding.BER value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1InputStream">
            <summary>
            This interface represents the wrapper for ASN1InputStream that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1InputStream.ReadObject">
            <summary>
            Calls actual
            <c>readObject</c>
            method for the wrapped ASN1InputStream object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            wrapped ASN1Primitive object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1InputStream.Dispose">
            <summary>
            Delegates
            <c>close</c>
            method call to the wrapped stream.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object">
            <summary>
            This interface represents the wrapper for ASN1Primitive that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Object.GetEncoded">
            <summary>
            Calls actual
            <c>getEncoded</c>
            method for the wrapped ASN1Primitive object.
            </summary>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Object.GetEncoded(System.String)">
            <summary>
            Calls actual
            <c>getEncoded</c>
            method for the wrapped ASN1Primitive object.
            </summary>
            <param name="encoding">encoding value</param>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1OctetString">
            <summary>
            This interface represents the wrapper for ASN1OctetString that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1OctetString.GetOctets">
            <summary>
            Calls actual
            <c>getOctets</c>
            method for the wrapped ASN1OctetString object.
            </summary>
            <returns>octets byte array.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1OctetString.GetDerEncoded">
            <summary>
            Calls actual
            <c>GetDerEncoded</c>
            method for the wrapped ASN1OctetString object.
            </summary>
            <returns>DER-encoded byte array.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1Sequence">
            <summary>
            This interface represents the wrapper for ASN1Sequence that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Sequence.GetObjectAt(System.Int32)">
            <summary>
            Calls actual
            <c>getObjectAt</c>
            method for the wrapped ASN1Sequence object.
            </summary>
            <param name="i">index</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable"/>
            wrapped ASN1Encodable object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Sequence.GetObjects">
            <summary>
            Calls actual
            <c>getObjects</c>
            method for the wrapped ASN1Sequence object.
            </summary>
            <returns>received objects.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Sequence.Size">
            <summary>
            Calls actual
            <c>size</c>
            method for the wrapped ASN1Sequence object.
            </summary>
            <returns>sequence size.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Sequence.ToArray">
            <summary>
            Calls actual
            <c>toArray</c>
            method for the wrapped ASN1Sequence object.
            </summary>
            <returns>array of wrapped ASN1Encodable objects.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1Set">
            <summary>
            This interface represents the wrapper for ASN1Set that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Set.GetObjects">
            <summary>
            Calls actual
            <c>getObjects</c>
            method for the wrapped ASN1Set object.
            </summary>
            <returns>received objects.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Set.Size">
            <summary>
            Calls actual
            <c>size</c>
            method for the wrapped ASN1Set object.
            </summary>
            <returns>set size.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Set.GetObjectAt(System.Int32)">
            <summary>
            Calls actual
            <c>getObjectAt</c>
            method for the wrapped ASN1Set object.
            </summary>
            <param name="index">index</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable"/>
            wrapped ASN1Encodable object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1Set.ToArray">
            <summary>
            Calls actual
            <c>toArray</c>
            method for the wrapped ASN1Set object.
            </summary>
            <returns>array of wrapped ASN1Encodable objects.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject">
            <summary>
            This interface represents the wrapper for ASN1TaggedObject that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject.GetObject">
            <summary>
            Calls actual
            <c>getObject</c>
            method for the wrapped ASN1TaggedObject object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Object"/>
            wrapped ASN1Primitive object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject.GetTagNo">
            <summary>
            Calls actual
            <c>getTagNo</c>
            method for the wrapped ASN1TaggedObject object.
            </summary>
            <returns>tagNo value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerBitString">
            <summary>
            This interface represents the wrapper for ASN1BitString that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IDerBitString.IntValue">
            <summary>
            Calls actual
            <c>intValue</c>
            method for the wrapped ASN1BitString object.
            </summary>
            <returns>int value of the wrapped ASN1BitString.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerEnumerated">
            <summary>
            This interface represents the wrapper for ASN1Enumerated that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IDerEnumerated.IntValueExact">
            <summary>
            Calls actual
            <c>intValueExact()</c>
            method for the wrapped ASN1Enumerated object.
            </summary>
            <returns>integer value of the wrapped ASN1Enumerated object.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerGeneralizedTime">
            <summary>
            This interface represents the wrapper for ASN1GeneralizedTime that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IDerGeneralizedTime.GetDate">
            <summary>
            Calls actual
            <c>getDate</c>
            method for the wrapped ASN1GeneralizedTime object.
            </summary>
            <returns>
            
            <see cref="T:System.DateTime"/>
            date stored in the wrapped ASN1GeneralizedTime.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerIA5String">
            <summary>
            This interface represents the wrapper for DERIA5String that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerInteger">
            <summary>
            This interface represents the wrapper for ASN1Integer that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IDerInteger.GetValue">
            <summary>
            Calls actual
            <c>getValue</c>
            method for the wrapped ASN1Integer object.
            </summary>
            <returns>BigInteger value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerNull">
            <summary>
            This interface represents the wrapper for DERNull that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier">
            <summary>
            This interface represents the wrapper for ASN1ObjectIdentifier that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier.GetId">
            <summary>
            Calls actual
            <c>getId</c>
            method for the wrapped ASN1ObjectIdentifier object.
            </summary>
            <returns>string ID.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerOctetString">
            <summary>
            This interface represents the wrapper for DEROctetString that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerOutputStream">
            <summary>
            This interface represents the wrapper for ASN1OutputStream that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IDerOutputStream.WriteObject(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>
            Calls actual
            <c>writeObject</c>
            method for the wrapped ASN1OutputStream object.
            </summary>
            <param name="primitive">wrapped ASN1Primitive object.</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IDerOutputStream.Dispose">
            <summary>
            Delegates
            <c>close</c>
            method call to the wrapped stream.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerSequence">
            <summary>
            This interface represents the wrapper for DERSequence that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerSet">
            <summary>
            This interface represents the wrapper for DERSet that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerStringBase">
            <summary>
            This interface represents the wrapper for ASN1String that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.IDerStringBase.GetString">
            <summary>
            Calls actual
            <c>getString</c>
            method for the wrapped ASN1String object.
            </summary>
            <returns>the resulting string.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerTaggedObject">
            <summary>
            This interface represents the wrapper for DERTaggedObject that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.IDerUtcTime">
            <summary>
            This interface represents the wrapper for ASN1UTCTime that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse">
            <summary>
            This interface represents the wrapper for BasicOCSPResponse that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse.GetProducedAtDate">
            <summary>
            Gets TbsResponseData for the wrapped BasicOCSPResponse object
            and calls actual
            <c>getProducedAt</c>
            method, then gets DateTime.
            </summary>
            <returns>produced at date.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse.Verify(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>
            Verifies given certificate for the wrapped BasicOCSPResponse object.
            </summary>
            <returns>boolean value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse.GetCerts">
            <summary>
            Gets actual
            <c>Certs</c>
            field for the wrapped BasicOCSPResponse object.
            </summary>
            <returns>list of wrapped certificates.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse.GetOcspCerts">
            <summary>
            Gets actual
            <c>Certs</c>
            field for the wrapped BasicOCSPResponse object.
            </summary>
            <returns>list of wrapped certificates.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse.GetEncoded">
            <summary>
            Calls actual
            <c>GetEncoded</c>
            method for the wrapped BasicOCSPResponse object.
            </summary>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse.GetResponses">
            <summary>
            Calls actual
            <c>GetResponses</c>
            method for the wrapped BasicOCSPResponse object.
            </summary>
            <returns>wrapped SingleResp list.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse.GetProducedAt">
            <summary>
            Calls actual
            <c>GetProducedAt</c>
            method for the wrapped BasicOCSPResponse object.
            </summary>
            <returns>date BasicOCSPResponse was produced at.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse.GetExtensionParsedValue(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier)">
            <summary>
            Calls actual
            <c>GetExtensionParsedValue</c>
            method for the wrapped BasicOCSPResponse object.
            </summary>
            <returns>Parsed extension value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspObjectIdentifiers">
            <summary>
            This interface represents the wrapper for OCSPObjectIdentifiers that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspObjectIdentifiers.GetIdPkixOcspBasic">
            <summary>
            Gets
            <c>id_pkix_ocsp_basic</c>
            constant for the wrapped OCSPObjectIdentifiers.
            </summary>
            <returns>OCSPObjectIdentifiers.id_pkix_ocsp_basic wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspObjectIdentifiers.GetIdPkixOcspNonce">
            <summary>
            Gets
            <c>id_pkix_ocsp_nonce</c>
            constant for the wrapped OCSPObjectIdentifiers.
            </summary>
            <returns>OCSPObjectIdentifiers.id_pkix_ocsp_nonce wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspObjectIdentifiers.GetIdPkixOcspNoCheck">
            <summary>
            Gets
            <c>id_pkix_ocsp_nocheck</c>
            constant for the wrapped OCSPObjectIdentifiers.
            </summary>
            <returns>OCSPObjectIdentifiers.id_pkix_ocsp_nocheck wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspObjectIdentifiers.GetIdPkixOcspArchiveCutoff">
            <summary>
            Gets
            <c>id_pkix_ocsp_archive_cutoff</c>
            constant for the wrapped OCSPObjectIdentifiers.
            </summary>
            <returns>OCSPObjectIdentifiers.id_pkix_ocsp_archive_cutoff wrapper.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponse">
            <summary>
            This interface represents the wrapper for OCSPResponse that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponse.GetEncoded">
            <summary>
            Calls actual
            <c>GetEncoded</c>
            method for the wrapped OCSPResponse object.
            </summary>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponse.GetStatus">
            <summary>
            Calls actual
            <c>GetStatus</c>
            method for the wrapped OCSPResponse object.
            </summary>
            <returns>status value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponse.GetResponseObject">
            <summary>
            Calls actual
            <c>GetResponseObject</c>
            method for the wrapped OCSPResponse object.
            </summary>
            <returns>response object.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponse.GetSuccessful">
            <summary>
            Gets
            <c>SUCCESSFUL</c>
            constant for the wrapped OCSPResp.
            </summary>
            <returns>OCSPResp.SUCCESSFUL value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponseStatus">
            <summary>
            This interface represents the wrapper for OCSPResponseStatus that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponseStatus.GetSuccessful">
            <summary>
            Gets
            <c>SUCCESSFUL</c>
            constant for the wrapped OCSPResponseStatus.
            </summary>
            <returns>OCSPResponseStatus.SUCCESSFUL value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IResponseBytes">
            <summary>
            This interface represents the wrapper for ResponseBytes that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Pkcs.IPkcsObjectIdentifiers">
            <summary>
            This interface represents the wrapper for PKCSObjectIdentifiers that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IPkcsObjectIdentifiers.GetIdAaSignatureTimeStampToken">
            <summary>
            Gets
            <c>id_aa_signatureTimeStampToken</c>
            constant for the wrapped PKCSObjectIdentifiers.
            </summary>
            <returns>PKCSObjectIdentifiers.id_aa_signatureTimeStampToken wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IPkcsObjectIdentifiers.GetIdAaEtsSigPolicyId">
            <summary>
            Gets
            <c>id_aa_ets_sigPolicyId</c>
            constant for the wrapped PKCSObjectIdentifiers.
            </summary>
            <returns>PKCSObjectIdentifiers.id_aa_ets_sigPolicyId wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IPkcsObjectIdentifiers.GetIdSpqEtsUri">
            <summary>
            Gets
            <c>id_spq_ets_uri</c>
            constant for the wrapped PKCSObjectIdentifiers.
            </summary>
            <returns>PKCSObjectIdentifiers.id_spq_ets_uri wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IPkcsObjectIdentifiers.GetEnvelopedData">
            <summary>
            Gets
            <c>envelopedData</c>
            constant for the wrapped PKCSObjectIdentifiers.
            </summary>
            <returns>PKCSObjectIdentifiers.envelopedData wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IPkcsObjectIdentifiers.GetData">
            <summary>
            Gets
            <c>data</c>
            constant for the wrapped PKCSObjectIdentifiers.
            </summary>
            <returns>PKCSObjectIdentifiers.data wrapper.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Pkcs.IRsassaPssParameters">
            <summary>Wrapper interface for BouncyCastle's representation of RSASSA-PSS parameters in ASN.1.</summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IRsassaPssParameters.GetHashAlgorithm">
            <summary>
            Return the
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier"/>
            describing the digest algorithm to be used in the signature.
            </summary>
            <returns>
            an
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier"/>
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IRsassaPssParameters.GetMaskGenAlgorithm">
            <summary>
            Return the
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier"/>
            describing the mask generation function to be used in the signature.
            </summary>
            <returns>
            an
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier"/>
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IRsassaPssParameters.GetSaltLength">
            <summary>Return the salt length parameter.</summary>
            <remarks>
            Return the salt length parameter. This is a
            <see cref="!:iText.Commons.Bouncycastle.Math.IASN1Integer"/>
            for API consistency reasons, but typical
            values will be small.
            </remarks>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Pkcs.IRsassaPssParameters.GetTrailerField">
            <summary>Return the trailer field parameter.</summary>
            <remarks>
            Return the trailer field parameter. This is a
            <see cref="!:iText.Commons.Bouncycastle.Math.IASN1Integer"/>
            for API consistency reasons, but typical
            values will be small.
            </remarks>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Tsp.IMessageImprint">
            <summary>
            This interface represents the wrapper for MessageImprint that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Tsp.IMessageImprint.GetHashedMessage">
            <summary>
            Calls actual
            <c>getHashedMessage</c>
            method for the wrapped MessageImprint object.
            </summary>
            <returns>hashed message byte array.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Tsp.IMessageImprint.GetHashAlgorithm">
            <summary>
            Calls actual
            <c>getHashAlgorithm</c>
            method for the wrapped MessageImprint object.
            </summary>
            <returns>algorithm identifier wrapper.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Tsp.ITstInfo">
            <summary>
            This interface represents the wrapper for TSTInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Tsp.ITstInfo.GetMessageImprint">
            <summary>
            Calls actual
            <c>getMessageImprint</c>
            method for the wrapped TSTInfo object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Tsp.IMessageImprint"/>
            wrapper for the received MessageImprint object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Tsp.ITstInfo.GetGenTime">
            <summary>
            Calls actual
            <c>getGenTime</c>
            method for the wrapped TSTInfo object and gets date.
            </summary>
            <returns>
            the received
            <see cref="T:System.DateTime"/>
            object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.Util.IAsn1Dump">
            <summary>
            This interface represents the wrapper for ASN1Dump that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Util.IAsn1Dump.DumpAsString(System.Object,System.Boolean)">
            <summary>
            Calls actual
            <c>dumpAsString</c>
            method for the wrapped ASN1Dump object.
            </summary>
            <param name="obj">the ASN1Primitive (or its wrapper) to be dumped out</param>
            <param name="b">if true, dump out the contents of octet and bit strings</param>
            <returns>the resulting string.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.Util.IAsn1Dump.DumpAsString(System.Object)">
            <summary>
            Calls actual
            <c>dumpAsString</c>
            method for the wrapped ASN1Dump object.
            </summary>
            <param name="obj">the ASN1Primitive (or its wrapper) to be dumped out</param>
            <returns>the resulting string.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X500.IX500Name">
            <summary>
            This interface represents the wrapper for X500Name that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier">
            <summary>
            This interface represents the wrapper for AlgorithmIdentifier that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier.GetAlgorithm">
            <summary>
            Calls actual
            <c>getAlgorithm</c>
            method for the wrapped AlgorithmIdentifier object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier"/>
            wrapped algorithm ASN1ObjectIdentifier.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier.GetParameters">
            <summary>
            Calls actual
            <c>getParameters</c>
            method for the wrapped AlgorithmIdentifier object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable"/>
            wrapped algorithm parameters.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IAuthorityKeyIdentifier">
            <summary>
            This interface represents the wrapper for AuthorityKeyIdentifier that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IBasicConstraints">
            <summary>
            This interface represents the wrapper for BasicConstraints that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.ICrlDistPoint">
            <summary>
            This interface represents the wrapper for CRLDistPoint that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.ICrlDistPoint.GetDistributionPoints">
            <summary>
            Calls actual
            <c>getDistributionPoints</c>
            method for the wrapped CRLDistPoint object.
            </summary>
            <returns>
            array of the wrapped distribution points
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPoint"/>.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.ICrlReason">
            <summary>
            This interface represents the wrapper for CRLReason that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.ICrlReason.GetKeyCompromise">
            <summary>
            Gets
            <c>keyCompromise</c>
            constant for the wrapped CRLReason.
            </summary>
            <returns>CRLReason.keyCompromise value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.ICrlReason.GetRemoveFromCRL">
            <summary>
            Gets
            <c>removeFromCRL</c>
            constant for the wrapped CRLReason.
            </summary>
            <returns>CRLReason.removeFromCRL value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPoint">
            <summary>
            This interface represents the wrapper for DistributionPoint that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPoint.GetDistributionPoint">
            <summary>
            Calls actual
            <c>getDistributionPoints</c>
            method for the wrapped DistributionPoint object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName"/>
            wrapped distribution point.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPoint.GetCRLIssuer">
            <summary>
            Calls actual
            <c>getCRLIssuer</c>
            method for the wrapped DistributionPoint object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IGeneralNames"/>
            wrapped CRL issuer.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPoint.GetReasons">
            <summary>
            Calls actual
            <c>getReasons</c>
            method for the wrapped DistributionPoint object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IReasonFlags"/>
            wrapped reason codes.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName">
            <summary>
            This interface represents the wrapper for DistributionPointName that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName.GetType">
            <summary>
            Calls actual
            <c>getType</c>
            method for the wrapped DistributionPointName object.
            </summary>
            <returns>type value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName.GetName">
            <summary>
            Calls actual
            <c>getName</c>
            method for the wrapped DistributionPointName object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IAsn1Encodable"/>
            ASN1Encodable wrapper.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName.GetFullName">
            <summary>
            Gets
            <c>FULL_NAME</c>
            constant for the wrapped DistributionPointName.
            </summary>
            <returns>DistributionPointName.FULL_NAME value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IExtendedKeyUsage">
            <summary>
            This interface represents the wrapper for ExtendedKeyUsage that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IGeneralName">
            <summary>
            This interface represents the wrapper for GeneralName that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IGeneralName.GetTagNo">
            <summary>
            Calls actual
            <c>getTagNo</c>
            method for the wrapped GeneralName object.
            </summary>
            <returns>tagNo value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IGeneralName.GetUniformResourceIdentifier">
            <summary>
            Gets
            <c>uniformResourceIdentifier</c>
            constant for the wrapped GeneralName.
            </summary>
            <returns>GeneralName.uniformResourceIdentifier value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IGeneralNames">
            <summary>
            This interface represents the wrapper for GeneralNames that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IGeneralNames.GetNames">
            <summary>
            Calls actual
            <c>getNames</c>
            method for the wrapped GeneralNames object.
            </summary>
            <returns>
            array of wrapped names
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IGeneralName"/>.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IIssuingDistributionPoint">
            <summary>
            This interface represents the wrapper for IssuingDistributionPoint that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IIssuingDistributionPoint.GetDistributionPoint">
            <summary>
            Calls actual
            <c>getDistributionPoint</c>
            method for the wrapped IssuingDistributionPoint object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName"/>
            wrapped distribution point name.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IIssuingDistributionPoint.OnlyContainsUserCerts">
            <summary>
            Calls actual
            <c>onlyContainsUserCerts</c>
            method for the wrapped IssuingDistributionPoint object.
            </summary>
            <returns>true if onlyContainsUserCerts was set, false otherwise.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IIssuingDistributionPoint.OnlyContainsCACerts">
            <summary>
            Calls actual
            <c>onlyContainsCACerts</c>
            method for the wrapped IssuingDistributionPoint object.
            </summary>
            <returns>true if onlyContainsCACerts was set, false otherwise.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IIssuingDistributionPoint.IsIndirectCRL">
            <summary>
            Calls actual
            <c>isIndirectCRL</c>
            method for the wrapped IssuingDistributionPoint object.
            </summary>
            <returns>boolean value identifying if CRL is indirect.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IIssuingDistributionPoint.OnlyContainsAttributeCerts">
            <summary>
            Calls actual
            <c>onlyContainsAttributeCerts</c>
            method for the wrapped IssuingDistributionPoint object.
            </summary>
            <returns>true if onlyContainsAttributeCerts was set, false otherwise.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IIssuingDistributionPoint.GetOnlySomeReasons">
            <summary>
            Calls actual
            <c>getOnlySomeReasons</c>
            method for the wrapped IssuingDistributionPoint object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IReasonFlags"/>
            wrapped reason flags.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IKeyPurposeID">
            <summary>
            This interface represents the wrapper for KeyPurposeId that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IKeyPurposeID.GetIdKpOCSPSigning">
            <summary>
            Gets
            <c>id_kp_OCSPSigning</c>
            constant for the wrapped KeyPurposeId.
            </summary>
            <returns>KeyPurposeId.id_kp_OCSPSigning value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IKeyUsage">
            <summary>
            This interface represents the wrapper for KeyUsage that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IKeyUsage.GetDigitalSignature">
            <summary>
            Gets
            <c>digitalSignature</c>
            constant for the wrapped KeyUsage.
            </summary>
            <returns>KeyUsage.digitalSignature value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IKeyUsage.GetNonRepudiation">
            <summary>
            Gets
            <c>nonRepudiation</c>
            constant for the wrapped KeyUsage.
            </summary>
            <returns>KeyUsage.nonRepudiation value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IReasonFlags">
            <summary>
            This interface represents the wrapper for ReasonFlags that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.ISubjectKeyIdentifier">
            <summary>
            This interface represents the wrapper for SubjectKeyIdentifier that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.ISubjectPublicKeyInfo">
            <summary>
            This interface represents the wrapper for SubjectPublicKeyInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.ISubjectPublicKeyInfo.GetAlgorithm">
            <summary>
            Calls actual
            <c>getAlgorithm</c>
            method for the wrapped SubjectPublicKeyInfo object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier"/>
            wrapped AlgorithmIdentifier.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.ITbsCertificateStructure">
            <summary>
            This interface represents the wrapper for TBSCertificate that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.ITbsCertificateStructure.GetSubjectPublicKeyInfo">
            <summary>
            Calls actual
            <c>getSubjectPublicKeyInfo</c>
            method for the wrapped TBSCertificate object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.ISubjectPublicKeyInfo"/>
            wrapped SubjectPublicKeyInfo.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.ITbsCertificateStructure.GetIssuer">
            <summary>
            Calls actual
            <c>getIssuer</c>
            method for the wrapped TBSCertificate object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X500.IX500Name"/>
            wrapped X500Name.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.ITbsCertificateStructure.GetSerialNumber">
            <summary>
            Calls actual
            <c>getSerialNumber</c>
            method for the wrapped TBSCertificate object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IDerInteger"/>
            wrapped ASN1Integer.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.ITime">
            <summary>
            This interface represents the wrapper for Time that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IX509Extension">
            <summary>
            This interface represents the wrapper for X509Extension that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions">
            <summary>
            This interface represents the wrapper for X509Extensions that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetCRlDistributionPoints">
            <summary>
            Gets
            <c>cRLDistributionPoints</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.cRLDistributionPoints wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetIssuingDistributionPoint">
            <summary>
            Gets
            <c>issuingDistributionPoint</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.issuingDistributionPoint wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetAuthorityInfoAccess">
            <summary>
            Gets
            <c>authorityInfoAccess</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.authorityInfoAccess wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetBasicConstraints">
            <summary>
            Gets
            <c>basicConstraints</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.basicConstraints wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetKeyUsage">
            <summary>
            Gets
            <c>keyUsage</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.keyUsage wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetExtendedKeyUsage">
            <summary>
            Gets
            <c>extendedKeyUsage</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.extendedKeyUsage wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetAuthorityKeyIdentifier">
            <summary>
            Gets
            <c>authorityKeyIdentifier</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.authorityKeyIdentifier wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetSubjectKeyIdentifier">
            <summary>
            Gets
            <c>subjectKeyIdentifier</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.subjectKeyIdentifier wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions.GetExpiredCertsOnCRL">
            <summary>
            Gets
            <c>expiredCertsOnCRL</c>
            constant for the wrapped Extension.
            </summary>
            <returns>Extension.expiredCertsOnCRL wrapper.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.CRLReason">
            <summary>
            The CRLReason enumeration specifies the reason that a certificate is revoked, as defined in RFC 5280: Internet X.509 Public Key Infrastructure Certificate and CRL Profile .
            </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.UNSPECIFIED">
            <summary>
            This reason indicates that it is unspecified as to why the
            certificate has been revoked.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:iText.Commons.Bouncycastle.Cert.CRLReason.KEY_COMPROMISE" -->
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.CA_COMPROMISE">
             <summary>
            This reason indicates that it is known or suspected that the
            certificate subject's private key has been compromised. It applies
            to certificate authority (CA) certificates only.
             </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.AFFILIATION_CHANGED">
             <summary>
            This reason indicates that the subject's name or other information
            has changed.
             </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.SUPERSEDED">
             <summary>
            This reason indicates that the certificate has been superseded.
             </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.CESSATION_OF_OPERATION">
             <summary>
            This reason indicates that the certificate is no longer needed.
             </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.CERTIFICATE_HOLD">
             <summary>
            This reason indicates that the certificate has been put on hold.
             </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.UNUSED">
             <summary>
            Unused reason.
             </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.REMOVE_FROM_CRL">
             <summary>
            This reason indicates that the certificate was previously on hold
            and should be removed from the CRL. It is for use with delta CRLs.
             </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.PRIVILEGE_WITHDRAWN">
             <summary>
            This reason indicates that the privileges granted to the subject of
            the certificate have been withdrawn.
             </summary>
        </member>
        <member name="F:iText.Commons.Bouncycastle.Cert.CRLReason.AA_COMPROMISE">
             <summary>
            This reason indicates that it is known or suspected that the
            certificate subject's private key has been compromised. It applies
            to authority attribute (AA) certificates only.
             </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.IX509Certificate">
            <summary>
            This interface represents the wrapper for X509Certificate that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetIssuerDN">
            <summary>
            Calls actual
            <c>GetIssuerDN</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>Issuer DN wrapper .</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetSerialNumber">
            <summary>
            Calls actual
            <c>GetSerialNumber</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>Serial number.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetPublicKey">
            <summary>
            Calls actual
            <c>GetPublicKey</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>Public key wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetSigAlgOID">
            <summary>
            Get the Signature Algorithms Object ID.
            </summary>
            <returns>A string containg a '.' separated object id.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetSigAlgParams">
            <summary>
            Get the signature algorithms parameters. (EG DSA Parameters)
            </summary>
            <returns>A byte array containing the Der encoded version of the parameters or null if there are none.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetEncoded">
            <summary>
            Calls actual
            <c>GetEncoded</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>byte array.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetTbsCertificate">
            <summary>
            Calls actual
            <c>GetTbsCertificate</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>byte array.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetExtensionValue(System.String)">
            <summary>
            Calls actual
            <c>GetExtensionValue</c>
            method for the wrapped X509Certificate object.
            </summary>
            <param name="oid">string oid value</param>
            <returns>extension value wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.Verify(iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary>
            Calls actual
            <c>Verify</c>
            method for the wrapped X509Certificate object.
            </summary>
            <param name="issuerPublicKey">public key wrapper</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetCriticalExtensionOids">
            <summary>
            Calls actual
            <c>GetCriticalExtensionOids</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>critical extension oids set.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.CheckValidity(System.DateTime)">
            <summary>
            Calls actual
            <c>CheckValidity</c>
            method for the wrapped X509Certificate object.
            </summary>
            <param name="time">DateTime object</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetSubjectDN">
            <summary>
            Returns actual
            <c>SubjectDN</c>
            property for the wrapped X509Certificate object.
            </summary>
            <returns>X500Name wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetEndDateTime">
            <summary>
            Returns actual
            <c>EndDate</c>
            property of the certificate structure
            for the wrapped X509Certificate object.
            </summary>
            <returns>String time value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetNotBefore">
            <summary>
            Returns actual
            <c>NotBefore</c>
            property for the wrapped X509Certificate object.
            </summary>
            <returns>DateTime value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetNotAfter">
            <summary>
            Returns actual
            <c>NotAfter</c>
            property for the wrapped X509Certificate object.
            </summary>
            <returns>DateTime value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetExtendedKeyUsage">
            <summary>
            Calls actual
            <c>GetExtendedKeyUsage</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>List of object identifiers represented as Strings.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetKeyUsage">
            <summary>
            Calls actual
            <c>GetKeyUsage</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>bool array representing certificate key usage</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Certificate.GetBasicConstraints">
            <summary>
            Call actual
            <c>GetBasicConstraints</c>
            method for the wrapped X509Certificate object.
            </summary>
            <returns>int value representing basic constraints extension</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.IX509Crl">
            <summary>
            This interface represents the wrapper for X509Crl that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Crl.IsRevoked(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>
            Calls actual
            <c>IsRevoked</c>
            method for the wrapped X509Crl object.
            </summary>
            <param name="cert">x509 certificate wrapper</param>
            <returns>boolean value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Crl.GetIssuerDN">
            <summary>
            Calls actual
            <c>GetIssuerDN</c>
            method for the wrapped X509Crl object.
            </summary>
            <returns>X500Name wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Crl.GetThisUpdate">
            <summary>
            Calls actual
            <c>GetThisUpdate</c>
            method for the wrapped X509Crl object.
            </summary>
            <returns>DateTime value of this update.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Crl.GetNextUpdate">
            <summary>
            Calls actual
            <c>GetNextUpdate</c>
            method for the wrapped X509Crl object.
            </summary>
            <returns>DateTime value of the next update.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Crl.Verify(iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary>
            Calls actual
            <c>Verify</c>
            method for the wrapped X509Crl object.
            </summary>
            <param name="publicKey">public key to verify</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Crl.GetEncoded">
            <summary>
            Calls actual
            <c>GetEncoded</c>
            method for the wrapped X509Crl object.
            </summary>
            <returns>encoded array</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Crl.GetExtensionValue(System.String)">
            <summary>
            Calls actual
            <c>GetExtensionValue</c>
            method for the wrapped X509Crl object.
            </summary>
            <returns>the DER-encoded octet string of the extension value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509Crl.GetRevokedCertificate(iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary>
            Calls actual
            <c>GetRevokedCertificate</c>
            method for the wrapped X509Crl object.
            </summary>
            <returns></returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.IX509V2CrlGenerator">
            <summary>
            This interface represents the wrapper for X509v2CRLBuilder that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509V2CrlGenerator.AddCRLEntry(iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime,System.Int32)">
            <summary>
            Calls actual
            <c>addCRLEntry</c>
            method for the wrapped X509v2CRLBuilder object.
            </summary>
            <param name="bigInteger">serial number of revoked certificate</param>
            <param name="date">date of certificate revocation</param>
            <param name="i">the reason code, as indicated in CRLReason, i.e CRLReason.keyCompromise, or 0 if not to be used
                </param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509V2CrlGenerator"/>
            the current wrapper object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509V2CrlGenerator.AddExtension(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Boolean,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>
            Calls actual
            <c>addExtension</c>
            method for the wrapped X509v2CRLBuilder object.
            </summary>
            <param name="objectIdentifier">extension object identifier</param>
            <param name="isCritical">specifies if extension is critical or not</param>
            <param name="extension">encoded extension value</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509V2CrlGenerator"/>
            the current wrapper object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509V2CrlGenerator.SetNextUpdate(System.DateTime)">
            <summary>
            Calls actual
            <c>setNextUpdate</c>
            method for the wrapped X509v2CRLBuilder object.
            </summary>
            <param name="nextUpdate">date of next CRL update</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509V2CrlGenerator"/>
            the current wrapper object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509V2CrlGenerator.Build(iText.Commons.Bouncycastle.Operator.IContentSigner)">
            <summary>
            Calls actual
            <c>build</c>
            method for the wrapped X509v2CRLBuilder object.
            </summary>
            <param name="signer">ContentSigner wrapper</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Crl"/>
            the wrapper for built X509CRLHolder object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.IX509V3CertificateGenerator">
            <summary>
            This interface represents the wrapper for JcaX509v3CertificateBuilder that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509V3CertificateGenerator.Build(iText.Commons.Bouncycastle.Operator.IContentSigner)">
            <summary>
            Calls actual
            <c>build</c>
            method for the wrapped JcaX509v3CertificateBuilder object.
            </summary>
            <param name="contentSigner">ContentSigner wrapper</param>
            <returns>{IX509CertificateHolder} wrapper for built X509CertificateHolder object.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.IX509V3CertificateGenerator.AddExtension(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Boolean,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>
            Calls actual
            <c>addExtension</c>
            method for the wrapped JcaX509v3CertificateBuilder object.
            </summary>
            <param name="extensionOID">wrapper for the OID defining the extension type</param>
            <param name="critical">true if the extension is critical, false otherwise</param>
            <param name="extensionValue">wrapped ASN.1 structure that forms the extension's value</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509V3CertificateGenerator"/>
            this wrapper object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.AbstractOcspException">
            <summary>
            This class represents the wrapper for OCSPException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.IBasicOcspRespGenerator">
            <summary>
            This interface represents the wrapper for BasicOCSPRespBuilder that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IBasicOcspRespGenerator.SetResponseExtensions(iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions)">
            <summary>
            Calls actual
            <c>setResponseExtensions</c>
            method for the wrapped BasicOCSPRespBuilder object.
            </summary>
            <param name="extensions">response extensions wrapper</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.IBasicOcspRespGenerator"/>
            this wrapper object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IBasicOcspRespGenerator.AddResponse(iText.Commons.Bouncycastle.Cert.Ocsp.ICertID,iText.Commons.Bouncycastle.Cert.Ocsp.ICertStatus,System.DateTime,System.DateTime,iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions)">
            <summary>
            Calls actual
            <c>addResponse</c>
            method for the wrapped BasicOCSPRespBuilder object.
            </summary>
            <param name="certID">wrapped certificate ID details</param>
            <param name="certificateStatus">wrapped status of the certificate - wrapped null if okay</param>
            <param name="time">date this response was valid on</param>
            <param name="time1">date when next update should be requested</param>
            <param name="extensions">optional wrapped extensions</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.IBasicOcspRespGenerator"/>
            this wrapper object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IBasicOcspRespGenerator.Build(iText.Commons.Bouncycastle.Operator.IContentSigner,iText.Commons.Bouncycastle.Cert.IX509Certificate[],System.DateTime)">
            <summary>
            Calls actual
            <c>build</c>
            method for the wrapped BasicOCSPRespBuilder object.
            </summary>
            <param name="signer">ContentSigner wrapper</param>
            <param name="chain">list of wrapped X509CertificateHolder objects</param>
            <param name="time">produced at</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse"/>
            wrapper for built BasicOCSPResp object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.ICertID">
            <summary>
            This interface represents the wrapper for CertificateID that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ICertID.GetHashAlgOID">
            <summary>
            Calls actual
            <c>getHashAlgOID</c>
            method for the wrapped CertificateID object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier"/>
            hash algorithm OID wrapper.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ICertID.GetHashSha1">
            <summary>
            Gets
            <c>getHashSha1</c>
            constant for the wrapped CertificateID.
            </summary>
            <returns>CertificateID.HASH_SHA1 value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ICertID.MatchesIssuer(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>
            Calls actual
            <c>matchesIssuer</c>
            method for the wrapped CertificateID object.
            </summary>
            <param name="certificate">X509Certificate wrapper</param>
            <returns>boolean value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ICertID.GetSerialNumber">
            <summary>
            Calls actual
            <c>getSerialNumber</c>
            method for the wrapped CertificateID object.
            </summary>
            <returns>serial number value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.ICertStatus">
            <summary>
            This interface represents the wrapper for CertificateStatus that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ICertStatus.GetGood">
            <summary>
            Gets
            <c>GOOD</c>
            constant for the wrapped CertificateStatus.
            </summary>
            <returns>CertificateStatus.GOOD wrapper.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspReqGenerator">
            <summary>
            This interface represents the wrapper for OCSPReqBuilder that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspReqGenerator.SetRequestExtensions(iText.Commons.Bouncycastle.Asn1.X509.IX509Extensions)">
            <summary>
            Calls actual
            <c>setRequestExtensions</c>
            method for the wrapped OCSPReqBuilder object.
            </summary>
            <param name="extensions">wrapper for extensions to set</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspReqGenerator"/>
            this wrapper object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspReqGenerator.AddRequest(iText.Commons.Bouncycastle.Cert.Ocsp.ICertID)">
            <summary>
            Calls actual
            <c>addRequest</c>
            method for the wrapped OCSPReqBuilder object.
            </summary>
            <param name="certificateID">CertificateID wrapper</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspReqGenerator"/>
            this wrapper object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspReqGenerator.Build">
            <summary>
            Calls actual
            <c>build</c>
            method for the wrapped OCSPReqBuilder object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspRequest"/>
            wrapper for built OCSPReq object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspRequest">
            <summary>
            This interface represents the wrapper for OCSPReq that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspRequest.GetEncoded">
            <summary>
            Calls actual
            <c>getEncoded</c>
            method for the wrapped OCSPReq object.
            </summary>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspRequest.GetRequestList">
            <summary>
            Calls actual
            <c>GetRequestList</c>
            method for the wrapped OCSPReq object.
            </summary>
            <returns>the array of the wrapped Req objects.</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Commons.Bouncycastle.Cert.Ocsp.IOcspRequest.GetExtension(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier)" -->
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.IReq">
            <summary>
            This interface represents the wrapper for Req that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IReq.GetCertID">
            <summary>
            Calls actual
            <c>getCertID</c>
            method for the wrapped Req object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.ICertID"/>
            the wrapper for the received CertificateID.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.IRespID">
            <summary>
            This interface represents the wrapper for RespID that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.IRevokedCertStatus">
            <summary>
            This interface represents the wrapper for RevokedStatus that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.IRevokedCertStatus.GetRevocationTime">
            <summary>
            Gets RevocationTime for the wrapped revoked CertStatus object.
            </summary>
            <returns>certificate revocation time.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse">
            <summary>
            This interface represents the wrapper for SingleResp that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse.GetCertID">
            <summary>
            Calls actual
            <c>getCertID</c>
            method for the wrapped SingleResp object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.ICertID"/>
            the wrapper for the received CertificateID.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse.GetCertStatus">
            <summary>
            Calls actual
            <c>getCertStatus</c>
            method for the wrapped SingleResp object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.Ocsp.ICertStatus"/>
            the wrapper for the received CertificateStatus.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse.GetNextUpdate">
            <summary>
            Calls actual
            <c>getNextUpdate</c>
            method for the wrapped SingleResp object.
            </summary>
            <returns>date of next update.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cert.Ocsp.ISingleResponse.GetThisUpdate">
            <summary>
            Calls actual
            <c>getThisUpdate</c>
            method for the wrapped SingleResp object.
            </summary>
            <returns>date of this update.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cert.Ocsp.IUnknownCertStatus">
            <summary>
            This interface represents the wrapper for UnknownStatus that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cms.AbstractCmsException">
            <summary>
            This class represents the wrapper for CMSException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cms.ICmsEnvelopedData">
            <summary>
            This interface represents the wrapper for CMSEnvelopedData that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cms.ICmsEnvelopedData.GetRecipientInfos">
            <summary>
            Calls actual
            <c>getRecipientInfos</c>
            method for the wrapped CMSEnvelopedData object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cms.IRecipientInformationStore"/>
            the wrapper for the received RecipientInformationStore object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cms.IRecipientID">
            <summary>
            This interface represents the wrapper for RecipientId that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cms.IRecipientID.Match(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>
            Calls actual
            <c>match</c>
            method for the wrapped RecipientId object.
            </summary>
            <param name="holder">X509CertificateHolder wrapper</param>
            <returns>boolean value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cms.IRecipientInformation">
            <summary>
            This interface represents the wrapper for RecipientInformation that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cms.IRecipientInformation.GetContent(iText.Commons.Bouncycastle.Crypto.IPrivateKey)">
            <summary>
            Calls actual
            <c>getContent</c>
            method for the wrapped RecipientInformation object.
            </summary>
            <param name="key">wrapper for recipient object to use to recover content encryption key</param>
            <returns>the content inside the EnvelopedData this RecipientInformation is associated with.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cms.IRecipientInformation.GetRID">
            <summary>
            Calls actual
            <c>getRID</c>
            method for the wrapped RecipientInformation object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cms.IRecipientID"/>
            the wrapper for received RecipientId object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cms.IRecipientInformationStore">
            <summary>
            This interface represents the wrapper for RecipientInformationStore that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cms.IRecipientInformationStore.GetRecipients">
            <summary>
            Calls actual
            <c>getRecipients</c>
            method for the wrapped RecipientInformationStore object.
            </summary>
            <returns>a collection of wrapped recipients.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Cms.IRecipientInformationStore.Get(iText.Commons.Bouncycastle.Cms.IRecipientID)">
            <summary>
            Calls actual
            <c>get</c>
            method for the wrapped RecipientInformationStore object.
            </summary>
            <param name="var1">RecipientId wrapper</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Cms.IRecipientInformation"/>
            the wrapper for received RecipientInformation object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Cms.ISignerInfoGenerator">
            <summary>
            This interface represents the wrapper for SignerInfoGenerator that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Crypto.Generators.IRsaKeyPairGenerator">
            <summary>
            This interface represents the wrapper for RsaKeyPairGenerator that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.Generators.IRsaKeyPairGenerator.GenerateKeyPair">
            <summary>
            Calls actual <c>GenerateKeyPair</c> method
            for the wrapped RsaKeyPairGenerator object.
            </summary>
            <returns>
            Asymmetric key pair wrapper.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Crypto.IAsymmetricCipherKeyPair">
            <summary>
            This interface represents the wrapper for AsymmetricCipherKeyPair that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IAsymmetricCipherKeyPair.GetPrivateKey">
            <summary>
            Gets actual private key for the wrapped AsymmetricCipherKeyPair object.
            </summary>
            <returns>Wrapped private key.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IAsymmetricCipherKeyPair.GetPublicKey">
            <summary>
            Gets actual public key for the wrapped AsymmetricCipherKeyPair object.
            </summary>
            <returns>Wrapped public key.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Crypto.ICipher">
            <summary>
            This interface represents the wrapper for ICipher that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ICipher.Update(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Continues processing another data part
            in wrapped ICipher object.
            </summary>
            <param name="inp">byte array</param>
            <param name="inpOff">offset</param>
            <param name="inpLen">length</param>
            <returns>
            byte array.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ICipher.DoFinal">
            <summary>
            Process the last block in the buffer
            of the wrapped ICipher object.
            </summary>
            <returns>
            byte array.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ICipherCBCnoPad.ProcessBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Processes data block using created cipher.
            </summary>
            <param name="inp">Input data bytes</param>
            <param name="inpOff">Input data offset</param>
            <param name="inpLen">Input data length</param>
            <returns>Processed bytes</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Crypto.IDigest">
            <summary>
            This interface represents the wrapper for IDigest that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IDigest.Digest(System.Byte[])">
            <summary>
            Calls actual
            <c>Digest</c>
            method for the wrapped IDigest object.
            </summary>
            <param name="enc2">byte array</param>
            <returns>
            byte array.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IDigest.Digest">
            <summary>
            Calls actual
            <c>Digest</c>
            method for the wrapped IDigest object.
            Leaves the digest reset.
            </summary>
            <returns>
            byte array.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IDigest.GetDigestLength">
            <summary>
            Gets byte length of wrapped digest algorithm.
            </summary>
            <returns>digest length</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IDigest.Update(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Calls actual
            <c>Update</c>
            method for the wrapped IDigest object.
            </summary>
            <param name="buf">byte array buffer</param>
            <param name="off">offset</param>
            <param name="len">buffer length</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IDigest.Update(System.Byte[])">
            <summary>
            Calls actual
            <c>Update</c>
            method for the wrapped IDigest object.
            </summary>
            <param name="buf">byte array buffer</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IDigest.Reset">
            <summary>
            Calls actual
            <c>Reset</c>
            method for the wrapped IDigest object.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IDigest.GetAlgorithmName">
            <summary>
            Gets actual
            <c>AlgorithmName</c>
            for the wrapped IDigest object.
            </summary>
            <returns>algorithm name.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Crypto.IPrivateKey">
            <summary>
            This interface represents the wrapper for PrivateKey that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.IPrivateKey.GetAlgorithm">
            <summary>
            Gets private key algorithm.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Crypto.IPublicKey">
            <summary>
            This interface represents the wrapper for PublicKey that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Crypto.ISigner">
            <summary>
            This interface represents the wrapper for ISigner that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.InitVerify(iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary>
            Calls actual
            <c>InitVerify</c>
            method for the wrapped ISigner object.
            </summary>
            <param name="publicKey">public key</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.InitSign(iText.Commons.Bouncycastle.Crypto.IPrivateKey)">
            <summary>
            Calls actual
            <c>InitVerify</c>
            method for the wrapped ISigner object.
            </summary>
            <param name="publicKey">public key</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.InitRsaPssSigner(System.String,System.Int32,System.Int32)">
            <summary>
            Creates actual signer object to create RSASSA-PSS signature
            </summary>
            <param name="digestAlgoName">digect algorithm</param>
            <param name="saltLen">salt length</param>
            <param name="trailerField">trailer field</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.Update(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Calls actual
            <c>Update</c>
            method for the wrapped ISigner object.
            </summary>
            <param name="buf">byte array buffer</param>
            <param name="off">offset</param>
            <param name="len">buffer length</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.Update(System.Byte[])">
            <summary>
            Calls actual
            <c>Update</c>
            method for the wrapped ISigner object.
            </summary>
            <param name="buf">byte array buffer</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.VerifySignature(System.Byte[])">
            <summary>
            Calls actual
            <c>VerifySignature</c>
            method for the wrapped ISigner object.
            </summary>
            <param name="digest">byte array</param>
            <returns>boolean value.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.GenerateSignature">
            <summary>
            Calls actual
            <c>GenerateSignature</c>
            method for the wrapped ISigner object.
            </summary>
            <returns>byte array.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.UpdateVerifier(System.Byte[])">
            <summary>
            Calls actual
            <c>UpdateVerifier</c>
            method for the wrapped ISigner object.
            </summary>
            <param name="digest">byte array</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Crypto.ISigner.SetDigestAlgorithm(System.String)">
            <summary>
            Sets hash and encryption algorithms
            for the wrapped ISigner object.
            </summary>
            <param name="algorithm">digest algorithm</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.IBouncyCastleFactory">
            <summary>
            <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleFactory"/>
            contains methods required for bouncy-classes objects creation.
            </summary>
            <remarks>
            <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleFactory"/>
            contains methods required for bouncy-classes objects creation. Implementation will be
            selected depending on a bouncy-castle dependency specified by the user.
            </remarks>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.GetAlgorithmOid(System.String)">
            <summary>
            Get signing algorithm oid from its name.
            </summary>
            <param name="name">name of the algorithm</param>
            <returns>algorithm oid</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.GetDigestAlgorithmOid(System.String)">
            <summary>
            Get hash algorithm oid from its name.
            </summary>
            <param name="name">name of the algorithm</param>
            <returns>algorithm oid</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.GetAlgorithmName(System.String)">
            <summary>
            Get signing algorithm name from its oid.
            </summary>
            <param name="oid">oid of the algorithm</param>
            <returns>algorithm name</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1ObjectIdentifier(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 encodable wrapper to the ASN1 object identifier wrapper.</summary>
            <param name="encodable">wrapper to be cast</param>
            <returns>casted wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1ObjectIdentifier(System.String)">
            <summary>
            Create ASN1 Object identifier wrapper from
            <see cref="T:System.String"/>.
            </summary>
            <param name="str">
            
            <see cref="T:System.String"/>
            to create object identifier from
            </param>
            <returns>created object identifier</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1ObjectIdentifierInstance(System.Object)">
            <summary>
            Create ASN1 Object identifier wrapper from
            <see cref="T:System.Object"/>
            using
            <c>getInstance</c>
            method call.
            </summary>
            <param name="object">
            
            <see cref="T:System.Object"/>
            to create object identifier from
            </param>
            <returns>created object identifier</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1InputStream(System.IO.Stream)">
            <summary>
            Create ASN1 Input stream wrapper from
            <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="stream">
            
            <see cref="T:System.IO.Stream"/>
            to create ASN1 Input stream from
            </param>
            <returns>created ASN1 Input stream</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1InputStream(System.Byte[])">
            <summary>
            Create ASN1 Input stream wrapper from
            <c>byte[]</c>.
            </summary>
            <param name="bytes">
            
            <c>byte[]</c>
            to create ASN1 Input stream from
            </param>
            <returns>created ASN1 Input stream</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1OctetString(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to the ASN1 Octet string wrapper.</summary>
            <param name="encodable">to be casted to ASN1 Octet string wrapper</param>
            <returns>casted ASN1 Octet string wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1OctetString(iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject,System.Boolean)">
            <summary>
            Create ASN1 Octet string wrapper from ASN1 Tagged object wrapper and
            <c>boolean</c>
            parameter.
            </summary>
            <param name="taggedObject">ASN1 Tagged object wrapper to create ASN1 Octet string wrapper from</param>
            <param name="b">boolean to create ASN1 Octet string wrapper</param>
            <returns>created ASN1 Octet string wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1OctetString(System.Byte[])">
            <summary>
            Create ASN1 Octet string wrapper from
            <c>byte[]</c>.
            </summary>
            <param name="bytes">
            
            <c>byte[]</c>
            to create ASN1 Octet string wrapper from
            </param>
            <returns>created ASN1 Octet string wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1OctetString(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>
            Create ASN1 Octet string wrapper from ASN1 Primitive wrapper.
            </summary>
            <param name="primitive">
            ASN1 Primitive wrapper to create ASN1 Octet string wrapper from
            </param>
            <returns>created ASN1 Octet string wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Sequence(System.Object)">
            <summary>
            Cast
            <see cref="T:System.Object"/>
            to ASN1 Sequence wrapper.
            </summary>
            <param name="object">
            
            <see cref="T:System.Object"/>
            to be cast. Must be instance of ASN1 Sequence
            </param>
            <returns>casted ASN1 Sequence wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Sequence(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 encodable wrapper to the ASN1 Sequence wrapper.</summary>
            <param name="encodable">to be casted to ASN1 Sequence wrapper</param>
            <returns>casted ASN1 Sequence wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Sequence(System.Byte[])">
            <summary>
            Create ASN1 Sequence wrapper from
            <c>byte[]</c>.
            </summary>
            <param name="array">
            
            <c>byte[]</c>
            to create ASN1 Sequence wrapper from
            </param>
            <returns>created ASN1 Sequence wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1SequenceInstance(System.Object)">
            <summary>
            Create ASN1 Sequence wrapper from
            <see cref="T:System.Object"/>
            using
            <c>getInstance</c>
            method call.
            </summary>
            <param name="object">
            
            <see cref="T:System.Object"/>
            to create ASN1 Sequence wrapper from
            </param>
            <returns>created ASN1 Sequence wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERSequence(iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector)">
            <summary>Create DER Sequence wrapper from ASN1 Encodable vector wrapper.</summary>
            <param name="encodableVector">ASN1 Encodable vector wrapper to create DER Sequence wrapper from</param>
            <returns>created DER Sequence wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERSequence(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>Create DER Sequence wrapper from ASN1 Primitive wrapper.</summary>
            <param name="primitive">ASN1 Primitive wrapper to create DER Sequence wrapper from</param>
            <returns>created DER Sequence wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1TaggedObject(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Create ASN1 Tagged object wrapper from ASN1 Encodable wrapper.</summary>
            <param name="encodable">ASN1 Encodable vector to create ASN1 Tagged object wrapper from</param>
            <returns>created ASN1 Tagged object wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Integer(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to ASN1 Integer wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be cast</param>
            <returns>casted ASN1 Integer</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Integer(System.Int32)">
            <summary>
            Create ASN1 Integer wrapper from
            <c>int</c>.
            </summary>
            <param name="i">
            
            <c>int</c>
            to create ASN1 Integer wrapper from
            </param>
            <returns>created ASN1 Integer wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Integer(iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary>
            Create ASN1 Integer wrapper from
            <see cref="T:iText.Commons.Bouncycastle.Math.IBigInteger"/>.
            </summary>
            <param name="i">
            
            <see cref="T:iText.Commons.Bouncycastle.Math.IBigInteger"/>
            to create ASN1 Integer wrapper from
            </param>
            <returns>created ASN1 Integer wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Set(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to ASN1 Set wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be cast</param>
            <returns>casted ASN1 Set</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Set(System.Object)">
            <summary>
            Create ASN1 Set wrapper from
            <see cref="T:System.Object"/>.
            </summary>
            <param name="encodable">
            
            <see cref="T:System.Object"/>
            to create ASN1 Set wrapper from. Must be instance of ASN1 Set
            </param>
            <returns>created ASN1 Set wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Set(iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject,System.Boolean)">
            <summary>
            Create ASN1 Set wrapper from ASN1 Tagged object wrapper and
            <c>boolean</c>
            parameter.
            </summary>
            <param name="taggedObject">ASN1 Tagged object wrapper to create ASN1 Set wrapper from</param>
            <param name="b">boolean to create ASN1 Set wrapper</param>
            <returns>created ASN1 Set wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateNullASN1Set">
            <summary>
            Create ASN1 Set wrapper which will store
            <see langword="null"/>.
            </summary>
            <returns>
            ASN1 Set wrapper with
            <see langword="null"/>
            value
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1OutputStream(System.IO.Stream)">
            <summary>
            Create ASN1 Output stream wrapper from
            <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="stream">
            
            <see cref="T:System.IO.Stream"/>
            to create ASN1 Output stream wrapper from
            </param>
            <returns>created ASN1 Output stream wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1OutputStream(System.IO.Stream,System.String)">
            <summary>
            Create ASN1 Output stream wrapper from
            <see cref="T:System.IO.Stream"/>
            and ASN1 Encoding.
            </summary>
            <param name="outputStream">
            
            <see cref="T:System.IO.Stream"/>
            to create ASN1 Output stream wrapper from
            </param>
            <param name="asn1Encoding">ASN1 Encoding to be used</param>
            <returns>created ASN1 Output stream wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDEROctetString(System.Byte[])">
            <summary>
            Create DER Octet string wrapper from
            <c>byte[]</c>.
            </summary>
            <param name="bytes">
            
            <c>byte[]</c>
            to create DER Octet string wrapper from
            </param>
            <returns>created DER Octet string wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDEROctetString(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to DER Octet string wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be casted</param>
            <returns>DER Octet string wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1EncodableVector">
            <summary>Create ASN1 Encodable wrapper without parameters.</summary>
            <returns>created ASN1 Encodable wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERNull">
            <summary>Create DER Null wrapper without parameters.</summary>
            <returns>created DER Null wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERTaggedObject(System.Int32,iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>
            Create DER Tagged object wrapper from
            <c>int</c>
            value and ASN1 Primitive wrapper.
            </summary>
            <param name="i">
            
            <c>int</c>
            value for DER Tagged object wrapper creation
            </param>
            <param name="primitive">ASN1 Primitive wrapper to create DER Tagged object wrapper from</param>
            <returns>created DER Tagged object wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERTaggedObject(System.Boolean,System.Int32,iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>
            Create DER Tagged object wrapper from
            <c>int</c>
            value,
            <c>boolean</c>
            value and ASN1 Primitive wrapper.
            </summary>
            <param name="b">
            
            <c>boolean</c>
            value for DER Tagged object wrapper creation
            </param>
            <param name="i">
            
            <c>int</c>
            value for DER Tagged object wrapper creation
            </param>
            <param name="primitive">ASN1 Primitive wrapper to create DER Tagged object wrapper from</param>
            <returns>created DER Tagged object wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERSet(iText.Commons.Bouncycastle.Asn1.IAsn1EncodableVector)">
            <summary>Create DER Set wrapper from ASN1 Encodable vector wrapper.</summary>
            <param name="encodableVector">ASN1 Encodable vector wrapper to create DER Set wrapper from</param>
            <returns>created DER Set wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERSet(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>Create DER Set wrapper from ASN1 Primitive wrapper.</summary>
            <param name="primitive">ASN1 Primitive wrapper to create DER Set wrapper from</param>
            <returns>created DER Set wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERSet(iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyIdentifier)">
            <summary>Create DER Set wrapper from signature policy identifier wrapper.</summary>
            <param name="identifier">signature policy identifier wrapper to create DER Set wrapper from</param>
            <returns>created DER Set wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERSet(iText.Commons.Bouncycastle.Asn1.Cms.IRecipientInfo)">
            <summary>Create DER Set wrapper from recipient info wrapper.</summary>
            <param name="recipientInfo">recipient info wrapper to create DER Set wrapper from</param>
            <returns>created DER Set wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Enumerated(System.Int32)">
            <summary>
            Create ASN1 Enumerated wrapper from
            <c>int</c>
            value.
            </summary>
            <param name="i">
            
            <c>int</c>
            to create ASN1 Enumerated wrapper from
            </param>
            <returns>created ASN1 Enumerated wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Enumerated(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>
            Create ASN1 Enumerated wrapper from
            <c>IASN1Encodable</c>
            value.
            </summary>
            <param name="object">
            
            <c>IASN1Encodable</c>
            to create ASN1 Enumerated wrapper from
            </param>
            <returns>created ASN1 Enumerated wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Encoding">
            <summary>Create ASN1 Encoding without parameters.</summary>
            <returns>created ASN1 Encoding</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateAttributeTable(iText.Commons.Bouncycastle.Asn1.IAsn1Set)">
            <summary>Create attribute table wrapper from ASN1 Set wrapper.</summary>
            <param name="unat">ASN1 Set wrapper to create attribute table wrapper from</param>
            <returns>created attribute table wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreatePKCSObjectIdentifiers">
            <summary>Create PKCS Object identifiers wrapper without parameters.</summary>
            <returns>created PKCS Object identifiers</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateAttribute(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1Set)">
            <summary>Create attribute wrapper from ASN1 Object identifier wrapper and ASN1 Set wrapper.</summary>
            <param name="attrType">ASN1 Object identifier wrapper to create attribute wrapper from</param>
            <param name="attrValues">ASN1 Object identifier wrapper to create attribute wrapper from</param>
            <returns>created attribute wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateContentInfo(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary>Create content info wrapper from ASN1 Sequence wrapper.</summary>
            <param name="sequence">ASN1 Sequence wrapper to create content info wrapper from</param>
            <returns>created content info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateContentInfo(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Create content info wrapper from ASN1 Object identifier wrapper and ASN1 Encodable wrapper.</summary>
            <param name="objectIdentifier">ASN1 Object identifier wrapper to create content info wrapper from</param>
            <param name="encodable">ASN1 Encodable wrapper to create content info wrapper from</param>
            <returns>created content info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTimeStampToken(iText.Commons.Bouncycastle.Asn1.Cms.IContentInfo)">
            <summary>Create timestamp token wrapper from content info wrapper.</summary>
            <param name="contentInfo">content info wrapper to create timestamp token wrapper from</param>
            <returns>created timestamp token wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSigningCertificate(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary>Create signing certificate wrapper from ASN1 Sequence wrapper.</summary>
            <param name="sequence">ASN1 Sequence wrapper to create signing certificate wrapper from</param>
            <returns>created signing certificate wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSigningCertificateV2(iText.Commons.Bouncycastle.Asn1.IAsn1Sequence)">
            <summary>Create signing certificate version 2 wrapper from ASN1 Sequence wrapper.</summary>
            <param name="sequence">ASN1 Sequence wrapper to create signing certificate version 2 wrapper from</param>
            <returns>created signing certificate version 2 wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBasicOCSPResponse(iText.Commons.Bouncycastle.Asn1.IAsn1Object)">
            <summary>Create basic OCSP Response wrapper from ASN1 Primitive wrapper.</summary>
            <param name="primitive">ASN1 Primitive wrapper to create basic OCSP response wrapper from</param>
            <returns>created basic OCSP response wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBasicOCSPResponse(System.Byte[])">
            <summary>Create basic OCSP Response wrapper from bytes array.</summary>
            <param name="bytes">bytes array to create basic OCSP response wrapper from</param>
            <returns>created basic OCSP response wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBasicOCSPResponse(System.Object)">
            <summary>
            Create basic OCSP Resp wrapper from
            <see cref="T:System.Object"/>.
            </summary>
            <param name="response">
            
            <see cref="T:System.Object"/>
            to create basic OCSP Resp wrapper from. Must be actual basic OCSP Resp instance
            </param>
            <returns>created basic OCSP Resp wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPObjectIdentifiers">
            <summary>Create OCSP Object identifiers wrapper without parameters.</summary>
            <returns>created OCSP Object identifiers wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateAlgorithmIdentifier(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier)">
            <summary>Create algorithm identifier wrapper from ASN1 Object identifier wrapper.</summary>
            <param name="algorithm">ASN1 Object identifier wrapper to create algorithm identifier wrapper from</param>
            <returns>created algorithm identifier wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateAlgorithmIdentifier(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Create algorithm identifier wrapper from ASN1 Object identifier wrapper and ASN1 Encodable wrapper.
                </summary>
            <param name="algorithm">ASN1 Object identifier wrapper to create algorithm identifier wrapper from</param>
            <param name="encodable">ASN1 Encodable wrapper to create algorithm identifier wrapper from</param>
            <returns>created algorithm identifier wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateRSASSAPSSParams(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Create a RSASSA-PSS params wrapper from an ASN1 Encodable wrapper.
                </summary>
            <param name="encodable"> ASN1 Encodable wrapper to create RSASSA-PSS params wrapper from</param>
            <returns>created RSASSA-PSS params wrapper</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateRSASSAPSSParamsWithMGF1(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Int32,System.Int32)" -->
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.GetProviderName">
            <summary>
            Get
            <see cref="T:System.String"/>
            which represents providers name for this factory.
            </summary>
            <returns>
            
            <see cref="T:System.String"/>
            which represents providers name
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCertificateID">
            <summary>Create certificate ID wrapper without parameters.</summary>
            <returns>created certificate ID wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateExtensions">
            <summary>Create extensions wrapper without parameters.</summary>
            <returns>created extensions wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateExtensions(System.Collections.IDictionary)">
            <summary>
            Create extensions wrapper from
            <see cref="T:System.Collections.IDictionary"/>.
            </summary>
            <returns>created extensions wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPReqBuilder">
            <summary>Create OCSP Req builder wrapper without parameters.</summary>
            <returns>created OCSP Req builder wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSigPolicyQualifierInfo(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IDerIA5String)">
            <summary>Create sig policy qualifier info wrapper from ASN1 Object identifier wrapper and DERIA5 String wrapper.
                </summary>
            <param name="objectIdentifier">ASN1 Object identifier wrapper to create sig policy qualifier info wrapper from
                </param>
            <param name="string">DERIA5 String wrapper to create sig policy qualifier info wrapper from</param>
            <returns>created sig policy qualifier info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1String(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to ASN1 String wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be cast</param>
            <returns>casted ASN1 String wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Primitive(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to ASN1 Primitive wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be cast</param>
            <returns>casted ASN1 Primitive wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Primitive(System.Byte[])">
            <summary>
            Create ASN1 Primitive wrapper from
            <c>byte[]</c>
            value.
            </summary>
            <param name="array">
            
            <c>byte[]</c>
            value to create ASN1 Primitive wrapper from
            </param>
            <returns>created ASN1 Primitive wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPResponse(System.Byte[])">
            <summary>
            Create OCSP Resp wrapper from
            <c>byte[]</c>
            value.
            </summary>
            <param name="bytes">
            
            <c>byte[]</c>
            value to create OCSP Resp wrapper from
            </param>
            <returns>created OCSP Resp wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPResponse">
            <summary>Create OCSP Resp wrapper without parameters.</summary>
            <returns>created OCSP Resp wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPResponse(iText.Commons.Bouncycastle.Asn1.Ocsp.IOcspResponseStatus,iText.Commons.Bouncycastle.Asn1.Ocsp.IResponseBytes)">
            <summary>Create OCSP Response wrapper from OCSP Response status wrapper and response bytes wrapper.</summary>
            <param name="respStatus">OCSP Response status wrapper to create OCSP Response wrapper from</param>
            <param name="responseBytes">response bytes wrapper to create OCSP Response wrapper from</param>
            <returns>created OCSP Response wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateResponseBytes(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.IDerOctetString)">
            <summary>Create response bytes wrapper from ASN1 Object identifier wrapper and DER Octet string wrapper.</summary>
            <param name="asn1ObjectIdentifier">ASN1 Object identifier wrapper to create response bytes wrapper from</param>
            <param name="derOctetString">DER Octet string wrapper to create response bytes wrapper from</param>
            <returns>created response bytes wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPResponse(System.Int32,System.Object)">
             <summary>
             Create OCSP Response wrapper from
             <c>int</c>
             value and
             <see cref="!:System.object"/>
             </summary>
             <param name="respStatus">
             
             <c>int</c>
             value to create OCSP Response wrapper from
             </param>
             <param name="ocspRespObject">
            
             <see cref="!:System.object"/>
             to create OCSP Response wrapper from
             </param>
             <returns>created OCSP Response wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPResponseStatus(System.Int32)">
            <summary>
            Create OCSP Response status wrapper from
            <c>int</c>
            value.
            </summary>
            <param name="status">
            
            <c>int</c>
            value to create OCSP Response status wrapper from
            </param>
            <returns>created OCSP Response status wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPResponseStatus">
            <summary>Create OCSP Response status wrapper without parameters.</summary>
            <returns>created OCSP Response status wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCertificateStatus">
            <summary>Create certificate status wrapper without parameters.</summary>
            <returns>created certificate status wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateRevokedStatus(iText.Commons.Bouncycastle.Cert.Ocsp.ICertStatus)">
            <summary>Create revoked status wrapper from certificate status wrapper.</summary>
            <param name="certificateStatus">certificate status wrapper to create revoked status wrapper from</param>
            <returns>created revoked status wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateRevokedStatus(System.DateTime,System.Int32)">
            <summary>
            Create revoked status wrapper from
            <see cref="T:System.DateTime"/>
            and
            <c>int</c>
            value.
            </summary>
            <param name="date">
            
            <see cref="T:System.DateTime"/>
            to create revoked status wrapper from
            </param>
            <param name="i">
            
            <c>int</c>
            value to create revoked status wrapper from
            </param>
            <returns>created revoked status wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERIA5String(iText.Commons.Bouncycastle.Asn1.IAsn1TaggedObject,System.Boolean)">
            <summary>
            Create DERIA5 String wrapper from ASN1 Tagged object wrapper and
            <c>boolean</c>
            value.
            </summary>
            <param name="taggedObject">ASN1 Tagged object wrapper to create DERIA5 String wrapper from</param>
            <param name="b">
            
            <c>boolean</c>
            value to create DERIA5 String wrapper from
            </param>
            <returns>created DERIA5 String wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDERIA5String(System.String)">
            <summary>
            Create DERIA5 String wrapper from
            <see cref="T:System.String"/>
            value.
            </summary>
            <param name="str">
            
            <see cref="T:System.String"/>
            value to create DERIA5 String wrapper from
            </param>
            <returns>created DERIA5 String wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCRLDistPoint(System.Object)">
            <summary>
            Create CRL Dist point wrapper from
            <see cref="T:System.Object"/>.
            </summary>
            <param name="object">
            
            <see cref="T:System.Object"/>
            to create CRL Dist point wrapper from
            </param>
            <returns>created CRL Dist point wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateIssuingDistributionPoint(System.Object)">
            <summary>
            Create Issuing Distribution Point wrapper from the object.
            </summary>
            <param name="point">
            <see cref="T:System.Object"/> to create Issuing Distribution Point wrapper from
            </param>
            <returns>created Issuing Distribution Point wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateIssuingDistributionPoint(iText.Commons.Bouncycastle.Asn1.X509.IDistributionPointName,System.Boolean,System.Boolean,iText.Commons.Bouncycastle.Asn1.X509.IReasonFlags,System.Boolean,System.Boolean)">
            <summary>
            Create Issuing Distribution Point wrapper with specified values.
            </summary>
            <param name="distributionPoint">
            one of names from the corresponding distributionPoint from the cRLDistributionPoints
            extension of every certificate that is within the scope of this CRL
            </param>
            <param name="onlyContainsUserCerts">
            true if the scope of the CRL only includes end entity public key certificates
            </param>
            <param name="onlyContainsCACerts">
            true if the scope of the CRL only includes CA certificates
            </param>
            <param name="onlySomeReasons">
            reason codes associated with a distribution point
            </param>
            <param name="indirectCRL">
            true if CRL includes certificates issued by authorities other than the CRL issuer,
            false if the scope of the CRL only includes certificates issued by the CRL issuer
            </param>
            <param name="onlyContainsAttributeCerts">
            true if the scope of the CRL only includes attribute certificates
            </param>
            <returns>created Issuing Distribution Point wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateReasonFlags(System.Int32)">
            <summary>
            Creates the wrapper for ReasonFlags.
            </summary>
            <param name="reasons">
            the bitwise OR of the Key Reason flags giving the allowed uses for the key
            </param>
            <returns>created ReasonFlags wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDistributionPointName">
            <summary>Create distribution point name wrapper without parameters.</summary>
            <returns>created distribution point name wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateDistributionPointName(iText.Commons.Bouncycastle.Asn1.X509.IGeneralNames)">
            <summary>Create distribution point name wrapper by passing general names.</summary>
            <param name="generalNames">
            general names to create distribution point name from
            </param>
            <returns>created distribution point name wrapper.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateGeneralNames(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to general names wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be cast</param>
            <returns>casted general names wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateGeneralName">
            <summary>Create general name wrapper without parameters.</summary>
            <returns>created general name wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOtherHashAlgAndValue(iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary>Create other hash alg and value wrapper from algorithm identifier wrapper and ASN1 Octet string wrapper.
                </summary>
            <param name="algorithmIdentifier">algorithm identifier wrapper to create other hash alg and value wrapper from
                </param>
            <param name="octetString">ASN1 Octet string wrapper to create other hash alg and value wrapper from</param>
            <returns>created other hash alg and value wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSignaturePolicyId(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.Esf.IOtherHashAlgAndValue)">
            <summary>Create signature policy id wrapper from ASN1 Object identifier wrapper and other hash alg and value wrapper.
                </summary>
            <param name="objectIdentifier">ASN1 Object identifier wrapper to create signature policy id wrapper from</param>
            <param name="algAndValue">other hash alg and value wrapper to create signature policy id wrapper from</param>
            <returns>created signature policy id wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSignaturePolicyId(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.Esf.IOtherHashAlgAndValue,iText.Commons.Bouncycastle.Asn1.Esf.ISigPolicyQualifierInfo[])">
            <summary>
            Create signature policy id wrapper from ASN1 Object identifier wrapper, other hash alg and value wrapper
            and sig policy qualifier info wrappers.
            </summary>
            <param name="objectIdentifier">ASN1 Object identifier wrapper to create signature policy id wrapper from</param>
            <param name="algAndValue">other hash alg and value wrapper to create signature policy id wrapper from</param>
            <param name="policyQualifiers">sig policy qualifier info wrappers to create signature policy id wrapper from
                </param>
            <returns>created signature policy id wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSignaturePolicyIdentifier(iText.Commons.Bouncycastle.Asn1.Esf.ISignaturePolicyId)">
            <summary>Create signature policy identifier wrapper from signature policy id wrapper.</summary>
            <param name="policyId">signature policy id wrapper to create signature policy identifier wrapper from</param>
            <returns>created signature policy identifier wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateEnvelopedData(iText.Commons.Bouncycastle.Asn1.Cms.IOriginatorInfo,iText.Commons.Bouncycastle.Asn1.IAsn1Set,iText.Commons.Bouncycastle.Asn1.Cms.IEncryptedContentInfo,iText.Commons.Bouncycastle.Asn1.IAsn1Set)">
            <summary>
            Create enveloped data wrapper from originator info wrapper, ASN1 Set wrapper,
            encrypted content info wrapper and another ASN1 Set wrapper.
            </summary>
            <param name="originatorInfo">originator info wrapper to create enveloped data wrapper from</param>
            <param name="set">ASN1 Set wrapper to create enveloped data wrapper from</param>
            <param name="encryptedContentInfo">encrypted content info wrapper to create enveloped data wrapper from</param>
            <param name="set1">ASN1 Set wrapper to create enveloped data wrapper from</param>
            <returns>created enveloped data wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateRecipientInfo(iText.Commons.Bouncycastle.Asn1.Cms.IKeyTransRecipientInfo)">
            <summary>Create recipient info wrapper from key trans recipient info wrapper.</summary>
            <param name="keyTransRecipientInfo">key trans recipient info wrapper to create recipient info wrapper from
                </param>
            <returns>created recipient info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateEncryptedContentInfo(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary>
            Create encrypted content info wrapper from ASN1 Object identifier wrapper,
            algorithm identifier wrapper and ASN1 Octet string wrapper.
            </summary>
            <param name="data">ASN1 Object identifier wrapper to create encrypted content info wrapper from</param>
            <param name="algorithmIdentifier">algorithm identifier wrapper to create encrypted content info wrapper from
                </param>
            <param name="octetString">ASN1 Octet string wrapper to create encrypted content info wrapper from</param>
            <returns>created encrypted content info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTBSCertificate(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Create TBS Certificate wrapper from ASN1 Encodable wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to create TBS Certificate wrapper from</param>
            <returns>created TBS Certificate wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTBSCertificate(System.Byte[])">
            <summary>Create TBS Certificate wrapper from ASN1 Encoded data.</summary>
            <param name="bytes">encoded TBS Certificate</param>
            <returns>created TBS Certificate wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateIssuerAndSerialNumber(iText.Commons.Bouncycastle.Asn1.X500.IX500Name,iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary>
            Create issuer and serial number wrapper from X500 Name wrapper and
            <see cref="T:iText.Commons.Bouncycastle.Math.IBigInteger"/>.
            </summary>
            <param name="issuer">X500 Name wrapper to create issuer and serial number wrapper from</param>
            <param name="value">
            
            <see cref="T:iText.Commons.Bouncycastle.Math.IBigInteger"/>
            to create issuer and serial number wrapper from
            </param>
            <returns>created issuer and serial number wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateRecipientIdentifier(iText.Commons.Bouncycastle.Asn1.Cms.IIssuerAndSerialNumber)">
            <summary>Create recipient identifier wrapper from issuer and serial number wrapper.</summary>
            <param name="issuerAndSerialNumber">issuer and serial number wrapper to create recipient identifier wrapper from
                </param>
            <returns>created recipient identifier wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateKeyTransRecipientInfo(iText.Commons.Bouncycastle.Asn1.Cms.IRecipientIdentifier,iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier,iText.Commons.Bouncycastle.Asn1.IAsn1OctetString)">
            <summary>
            Create key trans recipient info wrapper from recipient identifier wrapper,
            algorithm identifier wrapper and ASN1 Octet string wrapper.
            </summary>
            <param name="recipientIdentifier">recipient identifier wrapper to create key trans recipient info wrapper from
                </param>
            <param name="algorithmIdentifier">algorithm identifier wrapper to create key trans recipient info wrapper from
                </param>
            <param name="octetString">ASN1 Octet string wrapper to create key trans recipient info wrapper from</param>
            <returns>created key trans recipient info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateNullOriginatorInfo">
            <summary>
            Create originator info wrapper with
            <see langword="null"/>
            value.
            </summary>
            <returns>created originator info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCMSEnvelopedData(System.Byte[])">
            <summary>
            Create CMS enveloped data from
            <c>byte[]</c>
            value.
            </summary>
            <param name="valueBytes">
            
            <c>byte[]</c>
            value to create CMS enveloped data from
            </param>
            <returns>created CMS enveloped data</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTimeStampRequestGenerator">
            <summary>Create timestamp request generator wrapper without parameters.</summary>
            <returns>created timestamp request generator wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTimeStampResponse(System.Byte[])">
            <summary>
            Create timestamp response wrapper from
            <c>byte[]</c>
            value.
            </summary>
            <param name="respBytes">
            
            <c>byte[]</c>
            value to create timestamp response wrapper from
            </param>
            <returns>created timestamp response wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateAbstractOCSPException(System.Exception)">
            <summary>
            Create OCSP Exception wrapper from usual
            <see cref="T:System.Exception"/>.
            </summary>
            <param name="e">
            
            <see cref="T:System.Exception"/>
            to create OCSP Exception wrapper from
            </param>
            <returns>created OCSP Exception wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateUnknownStatus">
            <summary>Create unknown status wrapper without parameters.</summary>
            <returns>created unknown status wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1Dump">
            <summary>Create ASN1 Dump wrapper without parameters.</summary>
            <returns>created ASN1 Dump wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1BitString(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to ASN1 Bit string wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be cast</param>
            <returns>casted ASN1 Bit string wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1GeneralizedTime(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to ASN1 Generalized time wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be cast</param>
            <returns>casted ASN1 Generalized time wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1GeneralizedTime(System.DateTime)">
            <summary>Cast DateTime to ASN1 Generalized time wrapper.</summary>
            <param name="date">DateTime to be cast</param>
            <returns>ASN1 Generalized time wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateASN1UTCTime(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Cast ASN1 Encodable wrapper to ASN1 UTC Time wrapper.</summary>
            <param name="encodable">ASN1 Encodable wrapper to be cast</param>
            <returns>casted ASN1 UTC Time wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTimeStampTokenGenerator(iText.Commons.Bouncycastle.Crypto.IPrivateKey,iText.Commons.Bouncycastle.Cert.IX509Certificate,System.String,System.String)">
             <summary>
             Create timestamp response generator wrapper from private key wrapper, X509 Certificate wrapper, 
             <see cref="!:System.string"/> 
             allowed digest and
             <see cref="!:System.string"/>
             policy oid.
             </summary>
             <param name="pk">private key wrapper to create timestamp response generator wrapper from</param>
             <param name="cert">X509 Certificate wrapper to create timestamp response generator wrapper from</param>
             <param name="allowedDigest">
            
             <see cref="!:System.string"/>
             allowed digest to create timestamp response generator wrapper from
             </param>
             <param name="policyOid">
            
             <see cref="!:System.string"/>
             policy oid to create timestamp response generator wrapper from
             </param>
             <returns>created timestamp response generator wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTimeStampResponseGenerator(iText.Commons.Bouncycastle.Tsp.ITimeStampTokenGenerator,System.Collections.IList)">
            <summary>
            Create timestamp response generator wrapper from timestamp token generator wrapper and
            <see cref="T:System.Collections.IList"/>
            of algorithms.
            </summary>
            <param name="tokenGenerator">timestamp token generator wrapper to create timestamp response generator wrapper from
                </param>
            <param name="algorithms">
            
            <see cref="T:System.Collections.IList"/>
            of algorithms to create timestamp response generator wrapper from
            </param>
            <returns>created timestamp response generator wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTimeStampRequest(System.Byte[])">
            <summary>
            Create timestamp request wrapper from
            <c>byte[]</c>
            value.
            </summary>
            <param name="bytes">
            
            <c>byte[]</c>
            value to create timestamp request wrapper from
            </param>
            <returns>created timestamp request wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX500Name(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>
            Create X500 Name wrapper from
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>.
            </summary>
            <param name="certificate">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to create X500 Name wrapper from
            </param>
            <returns>created X500 Name wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX500Name(System.String)">
            <summary>
            Create X500 Name wrapper from
            <see cref="T:System.String"/>.
            </summary>
            <param name="s">
            
            <see cref="T:System.String"/>
            to create X500 Name wrapper from
            </param>
            <returns>created X500 Name wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateRespID(iText.Commons.Bouncycastle.Asn1.X500.IX500Name)">
            <summary>Create resp ID wrapper from X500 Name wrapper.</summary>
            <param name="x500Name">X500 Name wrapper to create resp ID wrapper from</param>
            <returns>created resp ID wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBasicOCSPRespBuilder(iText.Commons.Bouncycastle.Cert.Ocsp.IRespID)">
            <summary>Create basic OCSP Resp builder wrapper from resp ID wrapper.</summary>
            <param name="respID">resp ID wrapper to create basic OCSP Resp builder wrapper from</param>
            <returns>created basic OCSP Resp builder wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPReq(System.Byte[])">
            <summary>
            Create OCSP Req wrapper from
            <c>byte[]</c>.
            </summary>
            <param name="requestBytes">
            
            <c>byte[]</c>
            to create OCSP Req wrapper from
            </param>
            <returns>created OCSP Req wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX509v2CRLBuilder(iText.Commons.Bouncycastle.Asn1.X500.IX500Name,System.DateTime)">
            <summary>
            Create X509 Version 2 CRL Builder wrapper from X500 Name wrapper and
            <see cref="T:System.DateTime"/>.
            </summary>
            <param name="x500Name">X500 Name wrapper to create X509 Version 2 CRL Builder wrapper from</param>
            <param name="thisUpdate">
            
            <see cref="T:System.DateTime"/>
            to create X509 Version 2 CRL Builder wrapper from
            </param>
            <returns>created X509 Version 2 CRL Builder wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateJcaX509v3CertificateBuilder(iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime,System.DateTime,iText.Commons.Bouncycastle.Asn1.X500.IX500Name,iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary>
            Create Jca X509 Version 3 certificate builder wrapper from
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            ,
            <see cref="T:iText.Commons.Bouncycastle.Math.IBigInteger"/>
            , start
            <see cref="T:System.DateTime"/>
            , end
            <see cref="T:System.DateTime"/>
            , X500 Name wrapper and
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPublicKey"/>.
            </summary>
            <param name="signingCert">
            
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            to create Jca X509 Version 3 certificate builder wrapper from
            </param>
            <param name="certSerialNumber">
            
            <see cref="T:iText.Commons.Bouncycastle.Math.IBigInteger"/>
            to create Jca X509 Version 3 certificate builder wrapper from
            </param>
            <param name="startDate">
            start
            <see cref="T:System.DateTime"/>
            to create Jca X509 Version 3 certificate builder wrapper from
            </param>
            <param name="endDate">
            end
            <see cref="T:System.DateTime"/>
            to create Jca X509 Version 3 certificate builder wrapper from
            </param>
            <param name="subjectDnName">X500 Name wrapper to create Jca X509 Version 3 certificate builder wrapper from
                </param>
            <param name="publicKey">
            
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPublicKey"/>
            to create Jca X509 Version 3 certificate builder wrapper from
            </param>
            <returns>created Jca X509 Version 3 certificate builder wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBasicConstraints(System.Boolean)">
            <summary>
            Create basic constraints wrapper from
            <c>boolean</c>
            value.
            </summary>
            <param name="b">
            
            <c>boolean</c>
            value to create basic constraints wrapper from
            </param>
            <returns>created basic constraints wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBasicConstraints(System.Int32)">
             <summary>
             Create basic constraints wrapper from
             <c>int</c>
             value.
             </summary>
             <param name="pathLength"></param>
            
             <c>int</c>
             flag to create basic constraints wrapper from
             <returns>created basic constraints wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateKeyUsage">
            <summary>Create key usage wrapper without parameters.</summary>
            <returns>created key usage wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateKeyUsage(System.Int32)">
            <summary>
            Create key usage wrapper from
            <c>int</c>
            value.
            </summary>
            <param name="i">
            
            <c>int</c>
            value to create key usage wrapper from
            </param>
            <returns>created key usage wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateKeyPurposeId">
            <summary>Create key purpose id wrapper without parameters.</summary>
            <returns>created key purpose id wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateExtendedKeyUsage(iText.Commons.Bouncycastle.Asn1.X509.IKeyPurposeID)">
            <summary>Create extended key usage wrapper from key purpose id wrapper.</summary>
            <param name="purposeId">key purpose id wrapper to create extended key usage wrapper from</param>
            <returns>created extended key usage wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateExtendedKeyUsage(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier[])">
            <summary>
            Create extended key usage wrapper from an array of object identifier wrappers.
            </summary>
            <param name="purposeId">an array of object identifier wrappers</param>
            <returns>created extended key usage wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSubjectPublicKeyInfo(iText.Commons.Bouncycastle.Crypto.IPublicKey)">
            <summary>
            Create subject public key info wrapper from public key wrapper
            </summary>
            <param name="publicKey">
            
            <see cref="T:iText.Commons.Bouncycastle.Crypto.IPublicKey"/>
            to create subject public ket info wrapper from
            </param>
            <returns>created subject public ket info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCRLReason">
            <summary>Create CRL Reason wrapper without parameters.</summary>
            <returns>created CRL Reason wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateTSTInfo(iText.Commons.Bouncycastle.Asn1.Cms.IContentInfo)">
            <summary>Create TST Info wrapper from content info wrapper.</summary>
            <param name="contentInfo">content info wrapper to create TST Info wrapper from</param>
            <returns>created TST Info wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSingleResp(iText.Commons.Bouncycastle.Asn1.Ocsp.IBasicOcspResponse)">
            <summary>Create single resp wrapper from basic OCSP Response wrapper.</summary>
            <param name="basicResp">basic OCSP Response wrapper to create single resp wrapper from</param>
            <returns>created single resp wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX509Certificate(System.IO.Stream)">
             <summary>
             Create X509 Certificate wrapper from
             <see cref="T:System.IO.Stream"/>.
             </summary>
             <param name="s">
            
             <see cref="T:System.IO.Stream"/>
             to create X509 Certificate wrapper from
             </param>
             <returns>created X509 Certificate wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX509Crl(System.IO.Stream)">
             <summary>
             Create X509 Crl wrapper from
             <see cref="T:System.IO.Stream"/>.
             </summary>
             <param name="input">
            
             <see cref="T:System.IO.Stream"/>
             to create X509 Crl wrapper from
             </param>
             <returns>created X509 Crl wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX509Crls(System.IO.Stream)">
             <summary>
             Create collection of the X509 Crl wrappers from
             <see cref="T:System.IO.Stream"/>.
             </summary>
             <param name="input">
            
             <see cref="T:System.IO.Stream"/>
             to create X509 Crl wrapper from
             </param>
             <returns>created collection of the X509 Crl wrappers</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateIDigest(System.String)">
             <summary>
             Create digest wrapper from
             <see cref="!:System.string"/>
             hash algorithm.
             </summary>
             <param name="hashAlgorithm">
            
             <see cref="!:System.string"/>
             hash algorithm to create digest wrapper from
             </param>
             <returns>created digest wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCertificateID(System.String,iText.Commons.Bouncycastle.Cert.IX509Certificate,iText.Commons.Bouncycastle.Math.IBigInteger)">
             <summary>
             Create certificate ID wrapper from
             <see cref="!:System.string"/>
             hash algorithm, X509 Certificate wrapper and big integer wrapper.
             </summary>
             <param name="hashAlgorithm">
            
             <see cref="!:System.string"/>
             hash algorithm to create certificate ID wrapper from
             </param>
             <param name="issuerCert">X509 Certificate wrapper to create certificate ID wrapper from</param>
             <param name="serialNumber">big integer wrapper to create certificate ID wrapper from</param>
             <returns>created certificate ID wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX500NameInstance(iText.Commons.Bouncycastle.Asn1.IAsn1Encodable)">
            <summary>Create X500 Name wrapper from ASN1 Encodable wrapper using getInstance call.</summary>
            <param name="issuer">ASN1 Encodable wrapper to create X500 Name wrapper from</param>
            <returns>created X500 Name wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateOCSPReq(iText.Commons.Bouncycastle.Cert.Ocsp.ICertID,System.Byte[])">
             <summary>
             Create OCSP Req wrapper from certificate ID wrapper and
             <c>byte[]</c>
             document id.
             </summary>
             <param name="certId">certificate ID wrapper to create OCSP Req wrapper from</param>
             <param name="documentId">
            
             <c>byte[]</c>
             document id to create OCSP Req wrapper from
             </param>
             <returns>created OCSP Req wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateISigner">
            <summary>Create signer wrapper without parameters.</summary>
            <returns>created signer wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX509CertificateParser">
            <summary>Create X509 Certificate parser wrapper without parameters.</summary>
            <returns>created X509 Certificate parser wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateGeneralSecurityException(System.String,System.Exception)">
             <summary>
             Create general security exception wrapper from
             <see cref="!:System.string"/>
             exception message and
             <see cref="T:System.Exception"/>
             exception.
             </summary>
             <param name="exceptionMessage">
            
             <see cref="!:System.string"/>
             exception message to create general security exception wrapper from
             </param>
             <param name="exception">
            
             <see cref="T:System.Exception"/>
             exception to create general security exception wrapper from
             </param>
             <returns>created general security exception wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateGeneralSecurityException(System.String)">
             <summary>
             Create general security exception wrapper from
             <see cref="!:System.string"/>
             exception message.
             </summary>
             <param name="exceptionMessage">
            
             <see cref="!:System.string"/>
             exception message to create general security exception wrapper from
             </param>
             <returns>created general security exception wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateGeneralSecurityException">
            <summary>Create general security exception wrapper without parameters.</summary>
            <returns>created general security exception wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateX509Certificate(System.Object)">
            <summary>
            Cast
            <see cref="T:System.Object"/>
            element to
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>.
            </summary>
            <param name="element">
            
            <see cref="T:System.Object"/>
            to be cast
            </param>
            <returns>
            casted
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Certificate"/>
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.GetBouncyCastleFactoryTestUtil">
            <summary>
            Get
            <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleTestConstantsFactory"/>
            corresponding to this
            <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleFactory"/>.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleTestConstantsFactory"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBigInteger">
            <summary>Create big integer wrapper without parameters.</summary>
            <returns>created big integer wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBigInteger(System.Int32,System.Byte[])">
             <summary>
             Create big integer wrapper from
             <c>int</c>
             value and
             <c>byte[]</c>
             array.
             </summary>
             <param name="i">
            
             <c>int</c>
             value to create big integer wrapper from
             </param>
             <param name="array">
            
             <c>byte[]</c>
             array to create big integer wrapper from
             </param>
             <returns>created big integer wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateBigInteger(System.String)">
             <summary>
             Create big integer wrapper from
             <see cref="!:System.string"/>
             value.
             </summary>
             <param name="str">
            
             <see cref="!:System.string"/>
             value to create big integer wrapper from
             </param>
             <returns>created big integer wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCipher(System.Boolean,System.Byte[],System.Byte[])">
             <summary>
             Create cipher wrapper from
             <c>bool</c>
             value,
             <c>byte[]</c>
             key and
             <c>byte[]</c>
             value.
             </summary>
             <param name="forEncryption">
            
             <c>bool</c>
             value to create cipher wrapper from
             </param>
             <param name="key">
            
             <c>byte[]</c>
             key to create cipher wrapper from
             </param>
             <param name="iv">
            
             <c>byte[]</c>
             value to create cipher wrapper from
             </param>
             <returns>created cipher wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCipherCbCnoPad(System.Boolean,System.Byte[],System.Byte[])">
             <summary>
             Create cipher Cbc no pad wrapper from
             <c>bool</c>
             value,
             <c>byte[]</c>
             key and
             <c>byte[]</c>
             value.
             </summary>
             <param name="forEncryption">
            
             <c>bool</c>
             value to create cipher Cbc no pad wrapper from
             </param>
             <param name="key">
            
             <c>byte[]</c>
             key to create cipher Cbc no pad wrapper from
             </param>
             <param name="iv">
            
             <c>byte[]</c>
             value to create cipher Cbc no pad wrapper from
             </param>
             <returns>created cipher Cbc no pad wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCipherCbCnoPad(System.Boolean,System.Byte[])">
             <summary>
             Create cipher Cbc no pad wrapper from
             <c>bool</c>
             value and 
             <c>byte[]</c>
             key.
             </summary>
             <param name="forEncryption">
            
             <c>bool</c>
             value to create cipher Cbc no pad wrapper from
             </param>
             <param name="key">
            
             <c>byte[]</c>
             key to create cipher Cbc no pad wrapper from
             </param>
             <returns>created cipher Cbc no pad wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateNullCrl">
            <summary>
            Create
            <see langword="null"/>
            as
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Crl"/>
            object.
            </summary>
            <returns>
            
            <see langword="null"/>
            as
            <see cref="T:iText.Commons.Bouncycastle.Cert.IX509Crl"/>
            object
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateRsa2048KeyPairGenerator">
            <summary>Create Rsa 2048 key pair generator wrapper without parameters.</summary>
            <returns>created Rsa 2048 key pair generator wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreatePEMParser(System.IO.TextReader,System.Char[])">
             <summary>
             Create PEM Parser wrapper from
             <see cref="T:System.IO.TextReader"/>.
             </summary>
             <param name="reader">
             
             <see cref="T:System.IO.TextReader"/>
             to create PEM Parser wrapper from
             </param>
             <param name="password">
            
             <c>byte[]</c>
             password to read pem file
             </param>
             <returns>created PEM Parser wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateContentSigner(System.String,iText.Commons.Bouncycastle.Crypto.IPrivateKey)">
             <summary>
             Create content signer wrapper from
             <see cref="!:System.string"/>
             signature algorithm and private key wrapper.
             </summary>
             <param name="signatureAlgorithm">
            
             <see cref="!:System.string"/>
             signature algorithm to create content signer wrapper from
             </param>
             <param name="signingKey">private key wrapper to create content signer wrapper from</param>
             <returns>created content signer wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateAuthorityKeyIdentifier(iText.Commons.Bouncycastle.Asn1.X509.ISubjectPublicKeyInfo)">
            <summary>Create authority key identifier wrapper from subject public key info wrapper.</summary>
            <param name="issuerPublicKeyInfo">
            subject public key info wrapper to create authority key identifier wrapper from
            </param>
            <returns>created authority key identifier wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateSubjectKeyIdentifier(iText.Commons.Bouncycastle.Asn1.X509.ISubjectPublicKeyInfo)">
            <summary>
            Create subject key identifier from subject public key info wrapper.
            </summary>
            <param name="subjectPublicKeyInfo">
            subject public key info wrapper to create subject key identifier from
            </param>
            <returns>created subject key identifier</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateExtension(System.Boolean,iText.Commons.Bouncycastle.Asn1.IDerOctetString)">
             <summary>
             Create extension wrapper from
             <c>bool</c>
             value and DER Octet string wrapper.
             </summary>
             <param name="b">
            
             <c>bool</c>
             value to create extension wrapper from
             </param>
             <param name="octetString">DER Octet string wrapper to create extension wrapper from</param>
             <returns>created extension wrapper</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.IsNullExtension(iText.Commons.Bouncycastle.Asn1.X509.IX509Extension)">
            <summary>
            Checks if provided extension wrapper wraps
            <see langword="null"/>.
            </summary>
            <param name="extNonce">extension wrapper to check</param>
            <returns>
            
            <see langword="true"/>
            if provided extension wrapper wraps
            <see langword="null"/>
            ,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateCipherBytes(iText.Commons.Bouncycastle.Cert.IX509Certificate,System.Byte[],iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier)">
             <summary>
             Create
             <c>byte[]</c>
             cipher bytes from X509 Certificate wrapper,
             <c>byte[]</c>
             array and algorithm identifier wrapper.
             </summary>
             <param name="x509Certificate">X509 Certificate wrapper to create cipher bytes from</param>
             <param name="abyte0">
            
             <c>byte[]</c>
             array to create cipher bytes from
             </param>
             <param name="algorithmidentifier">algorithm identifier wrapper to create cipher bytes from</param>
             <returns>created cipher bytes</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.IsInApprovedOnlyMode">
             <summary>
             Check if this bouncy-castle corresponding to this factory is in approved mode.
             </summary>
             <returns>
            
             <c>true</c>
             if approved mode is enabled,
             <c>false</c>
             otherwise
             </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.IsEncryptionFeatureSupported(System.Int32,System.Boolean)">
            <summary>
            Checks whether an algorithm is supported for encryption by the chosen Bouncy Castle implementation,
            throws an exception when not supported.
            </summary>
            <param name="encryptionType"> the type of encryption
                        STANDARD_ENCRYPTION_40 = 2
                        STANDARD_ENCRYPTION_128 = 3
                        AES_128 = 4
                        AES_256 = 5</param>
            <param name="withCertificate"> true when used with a certificate, false otherwise</param>        
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.GetBouncyCastleUtil">
            <summary>
            Get Bouncy-Castle Util instance implementation related to this <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleFactory"/>
            </summary>
            <returns><see cref="T:iText.Commons.Bouncycastle.IBouncyCastleUtil"/> instance implementation</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleFactory.CreateEndDate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>
            Create string from the end date of the certificate.
            </summary>
            <param name="certificate">certificate to get end date</param>
            <returns>The end date of the certificate</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.IBouncyCastleTestConstantsFactory">
            <summary>
            <see cref="T:iText.Commons.Bouncycastle.IBouncyCastleTestConstantsFactory"/>
            contains methods for constants creation required in tests.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.IBouncyCastleTestConstantsFactory.GetCertificateInfoTestConst">
            <summary>Get constant for certificate info.</summary>
            <returns>constant for certificate info</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.IBouncyCastleUtil">
            <summary>
            This class contains util methods, which use bouncy-castle objects.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Commons.Bouncycastle.IBouncyCastleUtil.ReadPkcs7Certs(System.IO.Stream)" -->
        <member name="T:iText.Commons.Bouncycastle.Math.IBigInteger">
            <summary>
            This interface represents the wrapper for BigInteger that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Math.IBigInteger.GetIntValue">
            <summary>
            Gets integer value for the wrapped BigInteger.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Math.IBigInteger.ToString(System.Int32)">
            <summary>
            Calls toString with radix for the wrapped BigInteger.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Math.IBigInteger.ValueOf(System.Int64)">
            <summary>
            Calls ValueOf method for the wrapped BigInteger.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Math.IBigInteger.Remainder(iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary>
            Calls Remainder method for the wrapped BigInteger.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Openssl.IPemReader">
            <summary>
            This interface represents the wrapper for PEMParser that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Openssl.IPemReader.ReadObject">
            <summary>
            Calls actual
            <c>readObject</c>
            method for the wrapped PEMParser object.
            </summary>
            <returns>
            
            <see cref="T:System.Object"/>
            which represents read object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Operator.AbstractOperatorCreationException">
            <summary>
            This class represents the wrapper for OperatorCreationException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Operator.IContentSigner">
            <summary>
            This interface represents the wrapper for ContentSigner that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Operator.IDigestCalculator">
            <summary>
            This interface represents the wrapper for DigestCalculator that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Security.AbstractCertificateExpiredException">
            <summary>
            This class represents the wrapper for CertificateExpiredException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractCertificateExpiredException.#ctor">
            <summary>
            Base constructor for <see cref="!:CertificateExpiredException"/>.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractCertificateExpiredException.#ctor(System.String)">
            <summary>
            Creates new wrapper instance for <see cref="!:CertificateExpiredException"/>.
            The abstract class constructor gets executed from a derived class.
            </summary>
            <param name="message">Exception message</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Security.AbstractCertificateNotYetValidException">
            <summary>
            This class represents the wrapper for CertificateNotYetValidException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractCertificateNotYetValidException.#ctor">
            <summary>
            Base constructor for <see cref="!:CertificateNotYetValidException"/>.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractCertificateNotYetValidException.#ctor(System.String)">
            <summary>
            Creates new wrapper instance for <see cref="!:CertificateNotYetValidException"/>.
            The abstract class constructor gets executed from a derived class.
            </summary>
            <param name="message">Exception message</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Security.AbstractCertificateParsingException">
            <summary>
            This class represents the wrapper for CertificateParsingException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractCertificateParsingException.#ctor">
            <summary>
            Base constructor for <see cref="!:CertificateParsingException"/>.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractCertificateParsingException.#ctor(System.String)">
            <summary>
            Creates new wrapper instance for <see cref="!:CertificateParsingException"/>.
            The abstract class constructor gets executed from a derived class.
            </summary>
            <param name="message">Exception message</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Security.AbstractGeneralSecurityException">
            <summary>
            This class represents the wrapper for GeneralSecurityException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractGeneralSecurityException.#ctor">
            <summary>
            Base constructor for <see cref="!:Org.BouncyCastle.Security.GeneralSecurityException"/>.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractGeneralSecurityException.#ctor(System.String)">
            <summary>
            Creates new wrapper instance for <see cref="!:Org.BouncyCastle.Security.GeneralSecurityException"/>.
            The abstract class constructor gets executed from a derived class.
            </summary>
            <param name="format">Exception message</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Security.AbstractSecurityUtilityException">
            <summary>
            This class represents the wrapper for SecurityUtilityException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractSecurityUtilityException.#ctor">
            <summary>
            Base constructor for <see cref="!:SecurityUtilityException"/>.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractSecurityUtilityException.#ctor(System.String)">
            <summary>
            Creates new wrapper instance for <see cref="!:SecurityUtilityException"/>.
            The abstract class constructor gets executed from a derived class.
            </summary>
            <param name="message">Exception message</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Security.AbstractCrlException">
            <summary>
            This class represents the wrapper for the CrlException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractCrlException.#ctor">
            <summary>
            Base constructor for <see cref="!:CertificateExpiredException"/>.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Security.AbstractCrlException.#ctor(System.String)">
            <summary>
            Creates new wrapper instance for <see cref="T:iText.Commons.Bouncycastle.Security.AbstractCrlException"/>.
            The abstract class constructor gets executed from a derived class.
            </summary>
            <param name="message">Exception message</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Tsp.AbstractTSPException">
            <summary>
            This class represents the wrapper for TSPException that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Tsp.ITimeStampRequest">
            <summary>
            This interface represents the wrapper for TimeStampRequest that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampRequest.GetEncoded">
            <summary>
            Calls actual
            <c>getEncoded</c>
            method for wrapped TimeStampRequest object.
            </summary>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampRequest.GetNonce">
            <summary>
            Calls actual
            <c>getNonce</c>
            method for wrapped TimeStampRequest object.
            </summary>
            <returns>nonce value.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Tsp.ITimeStampRequestGenerator">
            <summary>
            This interface represents the wrapper for TimeStampRequestGenerator that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampRequestGenerator.SetCertReq(System.Boolean)">
            <summary>
            Calls actual
            <c>setCertReq</c>
            method for the wrapped TimeStampRequestGenerator object.
            </summary>
            <param name="var1">the value to be set</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampRequestGenerator.SetReqPolicy(System.String)">
            <summary>
            Calls actual
            <c>setReqPolicy</c>
            method for the wrapped TimeStampRequestGenerator object.
            </summary>
            <param name="reqPolicy">the value to be set</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampRequestGenerator.Generate(iText.Commons.Bouncycastle.Asn1.IDerObjectIdentifier,System.Byte[],iText.Commons.Bouncycastle.Math.IBigInteger)">
            <summary>
            Calls actual
            <c>generate</c>
            method for the wrapped TimeStampRequestGenerator object.
            </summary>
            <param name="objectIdentifier">ASN1ObjectIdentifier wrapper</param>
            <param name="imprint">byte array</param>
            <param name="nonce">BigInteger</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Tsp.ITimeStampRequest"/>
            the wrapper for generated TimeStampRequest object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Tsp.ITimeStampResponse">
            <summary>
            This interface represents the wrapper for TimeStampResponse that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampResponse.Validate(iText.Commons.Bouncycastle.Tsp.ITimeStampRequest)">
            <summary>
            Calls actual
            <c>validate</c>
            method for the wrapped TimeStampResponse object.
            </summary>
            <param name="request">TimeStampRequest wrapper</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampResponse.GetFailInfo">
            <summary>
            Calls actual
            <c>getFailInfo</c>
            method for the wrapped TimeStampResponse object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Cmp.IPkiFailureInfo"/>
            the wrapper for the received PKIFailureInfo object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampResponse.GetTimeStampToken">
            <summary>
            Calls actual
            <c>getTimeStampToken</c>
            method for the wrapped TimeStampResponse object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Tsp.ITimeStampToken"/>
            the wrapper for the received TimeStampToken object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampResponse.GetStatusString">
            <summary>
            Calls actual
            <c>getStatusString</c>
            method for the wrapped TimeStampResponse object.
            </summary>
            <returns>status string.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampResponse.GetEncoded">
            <summary>
            Calls actual
            <c>getEncoded</c>
            method for the wrapped TimeStampResponse object.
            </summary>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Tsp.ITimeStampResponseGenerator">
            <summary>
            This interface represents the wrapper for TimeStampResponseGenerator that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampResponseGenerator.Generate(iText.Commons.Bouncycastle.Tsp.ITimeStampRequest,iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime)">
            <summary>
            Calls actual
            <c>generate</c>
            method for the wrapped TimeStampResponseGenerator object.
            </summary>
            <param name="request">the wrapper for request this response is for</param>
            <param name="bigInteger">serial number for the response token</param>
            <param name="date">generation time for the response token</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Tsp.ITimeStampResponse"/>
            the wrapper for the generated TimeStampResponse object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Tsp.ITimeStampToken">
            <summary>
            This interface represents the wrapper for TimeStampToken that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampToken.GetTimeStampInfo">
            <summary>
            Calls actual
            <c>getTimeStampInfo</c>
            method for the wrapped TimeStampToken object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenInfo"/>
            the wrapper for the received TimeStampInfo object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampToken.GetEncoded">
            <summary>
            Calls actual
            <c>getEncoded</c>
            method for the wrapped TimeStampToken object.
            </summary>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampToken.Validate(iText.Commons.Bouncycastle.Cert.IX509Certificate)">
            <summary>
            Calls actual
            <c>validate</c>
            method for the wrapped TimeStampToken object.
            </summary>
            <param name="cert">X509Certificate wrapper</param>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenGenerator">
            <summary>
            This interface represents the wrapper for TimeStampTokenGenerator that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenGenerator.SetAccuracySeconds(System.Int32)">
            <summary>
            Calls actual
            <c>setAccuracySeconds</c>
            method for the wrapped TimeStampTokenGenerator object.
            </summary>
            <param name="i">accuracy seconds to set</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenGenerator.SetCertificates(System.Collections.Generic.IList{iText.Commons.Bouncycastle.Cert.IX509Certificate})">
            <summary>
            Creates certificates collection and calls actual
            <c>SetCertificates</c>
            method for the wrapped TimeStampTokenGenerator object.
            </summary>
            <param name="certificateChain">certificates collection to set</param>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenGenerator.Generate(iText.Commons.Bouncycastle.Tsp.ITimeStampRequest,iText.Commons.Bouncycastle.Math.IBigInteger,System.DateTime)">
            <summary>
            Calls actual
            <c>generate</c>
            method for the wrapped TimeStampTokenGenerator object.
            </summary>
            <param name="request">the originating TimeStampRequest wrapper</param>
            <param name="bigInteger">serial number for the TimeStampToken</param>
            <param name="date">token generation time</param>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Tsp.ITimeStampToken"/>
            the wrapper for the generated TimeStampToken object.
            </returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenInfo">
            <summary>
            This interface represents the wrapper for TimeStampTokenInfo that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenInfo.GetHashAlgorithm">
            <summary>
            Calls actual
            <c>getHashAlgorithm</c>
            method for the wrapped TimeStampTokenInfo object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.X509.IAlgorithmIdentifier"/>
            the wrapper for the received AlgorithmIdentifier object.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenInfo.ToASN1Structure">
            <summary>
            Calls actual
            <c>toASN1Structure</c>
            method for the wrapped TimeStampTokenInfo object.
            </summary>
            <returns>
            
            <see cref="T:iText.Commons.Bouncycastle.Asn1.Tsp.ITstInfo"/>
            TSTInfo wrapper.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenInfo.GetGenTime">
            <summary>
            Calls actual
            <c>getGenTime</c>
            method for the wrapped TimeStampTokenInfo object.
            </summary>
            <returns>
            
            <see cref="T:System.DateTime"/>
            the received genTime.
            </returns>
        </member>
        <member name="M:iText.Commons.Bouncycastle.Tsp.ITimeStampTokenInfo.GetEncoded">
            <summary>
            Calls actual
            <c>getEncoded</c>
            method for the wrapped TimeStampTokenInfo object.
            </summary>
            <returns>the default encoding for the wrapped object.</returns>
        </member>
        <member name="T:iText.Commons.Bouncycastle.X509.IX509CertificateParser">
            <summary>
            This interface represents the wrapper for X509CertificateParser that provides the ability
            to switch between bouncy-castle and bouncy-castle FIPS implementations.
            </summary>
        </member>
        <member name="M:iText.Commons.Bouncycastle.X509.IX509CertificateParser.ReadAllCerts(System.Byte[])">
            <summary>
            Calls actual
            <c>ReadAllCerts</c>
            method for the wrapped X509CertificateParser object.
            </summary>
            <param name="contentsKey">Bytes from which certificated will be read</param>
            <returns>All read certificated</returns>
        </member>
        <member name="T:iText.Commons.Datastructures.BiMap`2">
            <summary>A simple bi-directional map.</summary>
            <typeparam name="K">the type of the first key</typeparam>
            <typeparam name="V">the type of the second key</typeparam>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.#ctor">
            <summary>
            Creates a new
            <see cref="T:iText.Commons.Datastructures.BiMap`2"/>
            instance.
            </summary>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.Put(`0,`1)">
            <summary>Puts the entry into the map.</summary>
            <remarks>
            Puts the entry into the map.
            If the key already exists, the value will be overwritten.
            If the value already exists, the key will be overwritten.
            If both key and value already exist, the entry will be overwritten.
            If neither key nor value already exist, the entry will be added.
            </remarks>
            <param name="k">the key</param>
            <param name="v">the value</param>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.GetByKey(`0)">
            <summary>Gets the value by key.</summary>
            <param name="value">the key</param>
            <returns>the value</returns>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.GetByValue(`1)">
            <summary>Gets the key by value.</summary>
            <param name="key">the value</param>
            <returns>the key</returns>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.RemoveByKey(`0)">
            <summary>Removes the entry by key.</summary>
            <param name="k">the key</param>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.RemoveByValue(`1)">
            <summary>Removes the entry by value.</summary>
            <param name="v">the value</param>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.Size">
            <summary>Gets the size of the map.</summary>
            <returns>the size of the map</returns>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.Clear">
            <summary>removes all entries from the map.</summary>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.IsEmpty">
            <summary>Checks if the map is empty.</summary>
            <returns>true, if the map is empty</returns>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.ContainsKey(`0)">
            <summary>Checks if the map contains the key.</summary>
            <param name="k">the key</param>
            <returns>true, if the map contains the key</returns>
        </member>
        <member name="M:iText.Commons.Datastructures.BiMap`2.ContainsValue(`1)">
            <summary>Checks if the map contains the value.</summary>
            <param name="v">the value</param>
            <returns>true, if the map contains the value</returns>
        </member>
        <member name="T:iText.Commons.Datastructures.ConcurrentWeakMap`2">
            <summary>Concurrent weak hash map implementation.</summary>
            <typeparam name="K">type of the keys</typeparam>
            <typeparam name="V">type of the values</typeparam>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.GetEnumerator">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.System#Collections#IEnumerable#GetEnumerator">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.Add(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.Clear">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
            <summary><inheritDoc/></summary>
        </member>
        <member name="P:iText.Commons.Datastructures.ConcurrentWeakMap`2.Count">
            <summary><inheritDoc/></summary>
        </member>
        <member name="P:iText.Commons.Datastructures.ConcurrentWeakMap`2.IsReadOnly">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.ContainsKey(`0)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.Add(`0,`1)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.Remove(`0)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Commons.Datastructures.ConcurrentWeakMap`2.TryGetValue(`0,`1@)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="P:iText.Commons.Datastructures.ConcurrentWeakMap`2.Item(`0)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="P:iText.Commons.Datastructures.ConcurrentWeakMap`2.Keys">
            <summary><inheritDoc/></summary>
        </member>
        <member name="P:iText.Commons.Datastructures.ConcurrentWeakMap`2.Values">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Commons.Datastructures.NullableContainer`1">
            <summary>A simple container that can hold a value.</summary>
            <remarks>
            A simple container that can hold a value.
            This is class is used to make the autoporting of primitive types easier.
            For example autoporting enums will convert them to non nullable types.
            But if you embed them in a NullableContainer, the autoporting will convert them to nullable types.
            </remarks>
        </member>
        <member name="M:iText.Commons.Datastructures.NullableContainer`1.#ctor(`0)">
            <summary>
            Creates a new
            <see cref="T:iText.Commons.Datastructures.NullableContainer`1"/>
            instance.
            </summary>
            <param name="value">the value</param>
        </member>
        <member name="M:iText.Commons.Datastructures.NullableContainer`1.GetValue">
            <summary>Gets the value.</summary>
            <returns>the value</returns>
        </member>
        <member name="T:iText.Commons.Datastructures.Tuple2`2">
            <summary>Simple tuple container that holds two elements.</summary>
            <typeparam name="T1">type of the first element</typeparam>
            <typeparam name="T2">type of the second element</typeparam>
        </member>
        <member name="M:iText.Commons.Datastructures.Tuple2`2.#ctor(`0,`1)">
            <summary>
            Creates a new instance of
            <see cref="T:iText.Commons.Datastructures.Tuple2`2"/>
            with given elements.
            </summary>
            <param name="first">the first element</param>
            <param name="second">the second element</param>
        </member>
        <member name="M:iText.Commons.Datastructures.Tuple2`2.GetFirst">
            <summary>Get the first element.</summary>
            <returns>the first element</returns>
        </member>
        <member name="M:iText.Commons.Datastructures.Tuple2`2.GetSecond">
            <summary>Get the second element.</summary>
            <returns>the second element</returns>
        </member>
        <member name="M:iText.Commons.Datastructures.Tuple2`2.ToString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Commons.Digest.IMessageDigest">
            <summary>
            This interface should be implemented to provide applications the functionality of a message digest algorithm.
            </summary>
        </member>
        <member name="M:iText.Commons.Digest.IMessageDigest.Digest(System.Byte[])">
            <summary>
            Performs a final update on the digest using the specified array of bytes,
            then completes the digest computation.
            </summary>
            <param name="enc">the input to be updated before the digest is completed</param>
            <returns>The array of bytes for the resulting hash value</returns>
        </member>
        <member name="M:iText.Commons.Digest.IMessageDigest.Digest">
            <summary>
            Completes the hash computation by performing final operations such as padding.
            Leaves the digest reset.
            </summary>
            <returns>The array of bytes for the resulting hash value</returns>
        </member>
        <member name="M:iText.Commons.Digest.IMessageDigest.GetDigestLength">
            <summary>
            Gets byte length of wrapped digest algorithm.
            </summary>
            <returns>The length of the digest in bytes.</returns>
        </member>
        <member name="M:iText.Commons.Digest.IMessageDigest.Update(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Updates the digest using the specified array of bytes, starting at the specified offset.
            </summary>
            <param name="buf">byte array buffer</param>
            <param name="off">the offset to start from in the array of bytes</param>
            <param name="len">the number of bytes to use, starting at offset</param>
        </member>
        <member name="M:iText.Commons.Digest.IMessageDigest.Update(System.Byte[])">
            <summary>
            Updates the digest using the specified array of bytes.
            </summary>
            <param name="buf">byte array buffer</param>
        </member>
        <member name="M:iText.Commons.Digest.IMessageDigest.Reset">
            <summary>Resets the digest for further use.</summary>
        </member>
        <member name="M:iText.Commons.Digest.IMessageDigest.GetAlgorithmName">
            <summary>
            Returns a string that identifies the algorithm, independent of implementation details.
            </summary>
            <returns>The name of the algorithm.</returns>
        </member>
        <member name="T:iText.Commons.Exceptions.AggregatedException">
            <summary>Composite exception class.</summary>
        </member>
        <member name="F:iText.Commons.Exceptions.AggregatedException.ERROR_DURING_EVENT_PROCESSING">
            <summary>Notifies that event processing failed.</summary>
        </member>
        <member name="M:iText.Commons.Exceptions.AggregatedException.#ctor(System.Collections.Generic.IList{System.Exception})">
            <summary>Creates an instance of aggregated exception based on the collection of exceptions.</summary>
            <param name="aggregatedExceptions">is a list of aggregated exceptions</param>
        </member>
        <member name="M:iText.Commons.Exceptions.AggregatedException.#ctor(System.String,System.Collections.Generic.IList{System.Exception})">
            <summary>Creates an instance of aggregated exception based on the collection of exceptions.</summary>
            <param name="message">the detail message</param>
            <param name="aggregatedExceptions">is a list of aggregated exceptions</param>
        </member>
        <member name="P:iText.Commons.Exceptions.AggregatedException.Message">
            <summary>
            Builds message for the exception including its own message and all messages from the
            aggregated exceptions.
            </summary>
            <returns>aggregated message</returns>
        </member>
        <member name="M:iText.Commons.Exceptions.AggregatedException.GetAggregatedExceptions">
            <summary>Gets a list of aggregated exceptions.</summary>
            <returns>aggregated exceptions</returns>
        </member>
        <member name="T:iText.Commons.Exceptions.CommonsExceptionMessageConstant">
            <summary>Class that bundles all the error message templates as constants.</summary>
        </member>
        <member name="F:iText.Commons.Exceptions.CommonsExceptionMessageConstant.ELEMENT_ALREADY_HAS_IDENTIFIER">
            <summary>Message warns about overriding of the identifier of identifiable element.</summary>
            <remarks>
            Message warns about overriding of the identifier of identifiable element. List of params:
            <list type="bullet">
            <item><description>0th is an original element identifier;
            </description></item>
            <item><description>1st is a new element identifier;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="T:iText.Commons.Exceptions.ITextException">
            <summary>General iText exception.</summary>
            <remarks>
            General iText exception.
            <para />
            Important note, not all iText exceptions are extended from ITextException.
            </remarks>
        </member>
        <member name="M:iText.Commons.Exceptions.ITextException.#ctor">
            <summary>Creates a new ITextException with no error message and cause.</summary>
        </member>
        <member name="M:iText.Commons.Exceptions.ITextException.#ctor(System.String)">
            <summary>Creates a new ITextException.</summary>
            <param name="message">the detail message</param>
        </member>
        <member name="M:iText.Commons.Exceptions.ITextException.#ctor(System.Exception)">
            <summary>Creates a new ITextException.</summary>
            <param name="cause">
            the cause (which is saved for later retrieval by
            <see cref="P:System.Exception.InnerException"/>
            method)
            </param>
        </member>
        <member name="M:iText.Commons.Exceptions.ITextException.#ctor(System.String,System.Exception)">
            <summary>Creates a new ITextException.</summary>
            <param name="message">the detail message</param>
            <param name="cause">
            the cause (which is saved for later retrieval by
            <see cref="P:System.Exception.InnerException"/>
            method)
            </param>
        </member>
        <member name="T:iText.Commons.Exceptions.ProductEventHandlerRepeatException">
            <summary>The class represents a signal to the event handler that it is necessary to repeat the handling of the current event.
                </summary>
        </member>
        <member name="M:iText.Commons.Exceptions.ProductEventHandlerRepeatException.#ctor(System.String)">
            <summary>
            Creates a new instance of
            <see cref="T:iText.Commons.Exceptions.ProductEventHandlerRepeatException"/>
            based on message.
            </summary>
            <param name="message">the detail message</param>
        </member>
        <member name="T:iText.Commons.Exceptions.UnknownProductException">
            <summary>An exception notifies that unknown product was found in iText ecosystem.</summary>
        </member>
        <member name="F:iText.Commons.Exceptions.UnknownProductException.UNKNOWN_PRODUCT">
            <summary>Message notifies that event corresponding to unknown product was met.</summary>
            <remarks>
            Message notifies that event corresponding to unknown product was met. It is a parametrized
            message. List of params:
            <list type="bullet">
            <item><description>the name of unknown product
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="M:iText.Commons.Exceptions.UnknownProductException.#ctor(System.String)">
            <summary>Creates a new instance of the exception.</summary>
            <param name="message">the detail message</param>
        </member>
        <member name="T:iText.Commons.ITextLogManager">
            <summary>
            iText static log manager working with the <c>Microsoft.Extensions.Logging</c> framework. Use it to add iText
            logs to your application. Call <see cref = "M:iText.Commons.ITextLogManager.SetLoggerFactory(Microsoft.Extensions.Logging.ILoggerFactory)" /> to set up a logger factory that will
            receive iText log messages.
            </summary>
        </member>
        <member name="M:iText.Commons.ITextLogManager.SetLoggerFactory(Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Sets the implementation of <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> to be used for creating <see cref="T:Microsoft.Extensions.Logging.ILogger"/>
            objects.
            </summary>
            <param name="factory">The factory.</param>
        </member>
        <member name="M:iText.Commons.ITextLogManager.GetLoggerFactory">
            <summary>
            Gets an instance of the used logger factory.
            </summary>
            <returns>The factory.</returns>
        </member>
        <member name="M:iText.Commons.ITextLogManager.GetLogger(System.Type)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instance using the full name of the given type.
            </summary>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="T:iText.Commons.Logs.CommonsLogMessageConstant">
            <summary>Class that bundles all the error message templates as constants.</summary>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.BASE_64_EXCEPTION">
            <summary>Message notifies that base64 encoding or decoding failed.</summary>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.INVALID_STATISTICS_NAME">
            <summary>Message notifies that an invalid statistics name was received, because there is no aggregator for it.
                </summary>
            <remarks>
            Message notifies that an invalid statistics name was received, because there is no aggregator for it.
            <list type="bullet">
            <item><description>0th is a statistics name which is invalid;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.LOCAL_FILE_COMPRESSION_FAILED">
            <summary>Message notifies that files archiving operation failed.</summary>
            <remarks>
            Message notifies that files archiving operation failed.
            <list type="bullet">
            <item><description>0th is a message of thrown exception;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.RATIO_IS_HIGHLY_SUSPICIOUS">
            <summary>
            Message notifies that archive is suspicious to be a zip bomb due to large ratio between the compressed and
            uncompressed archive entry.
            </summary>
            <remarks>
            Message notifies that archive is suspicious to be a zip bomb due to large ratio between the compressed and
            uncompressed archive entry.
            <list type="bullet">
            <item><description>0th is a threshold ratio;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.TOO_MUCH_ENTRIES_IN_ARCHIVE">
            <summary>
            Message notifies that archive is suspicious to be a zip bomb because the number of file entries extracted from
            the archive is greater than a predefined threshold.
            </summary>
            <remarks>
            Message notifies that archive is suspicious to be a zip bomb because the number of file entries extracted from
            the archive is greater than a predefined threshold.
            <list type="bullet">
            <item><description>0th is a threshold number of file entries in the archive;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.UNABLE_TO_DESERIALIZE_JSON">
            <summary>Message notifies that some exception has been thrown during json deserialization from object.</summary>
            <remarks>
            Message notifies that some exception has been thrown during json deserialization from object.
            List of params:
            <list type="bullet">
            <item><description>0th is a class name of thrown exception;
            </description></item>
            <item><description>1st is a message of thrown exception;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.UNABLE_TO_SERIALIZE_OBJECT">
            <summary>Message notifies that some exception has been thrown during json serialization to object.</summary>
            <remarks>
            Message notifies that some exception has been thrown during json serialization to object.
            List of params:
            <list type="bullet">
            <item><description>0th is a class name of thrown exception;
            </description></item>
            <item><description>1st is a message of thrown exception;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.UNCOMPRESSED_DATA_SIZE_IS_TOO_MUCH">
            <summary>Message notifies that archive is suspicious to be a zip bomb due to large total size of the uncompressed data.
                </summary>
            <remarks>
            Message notifies that archive is suspicious to be a zip bomb due to large total size of the uncompressed data.
            <list type="bullet">
            <item><description>0th is a threshold size;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.UNKNOWN_PLACEHOLDER_WAS_IGNORED">
            <summary>
            Message notifies that unknown placeholder was ignored during parsing of the producer line
            format.
            </summary>
            <remarks>
            Message notifies that unknown placeholder was ignored during parsing of the producer line
            format. List of params:
            <list type="bullet">
            <item><description>0th is a name of ignored placeholder;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="F:iText.Commons.Logs.CommonsLogMessageConstant.UNREPORTED_EVENT">
            <summary>Message warns that some event is at confirmation stage but it is not known.</summary>
            <remarks>
            Message warns that some event is at confirmation stage but it is not known. Probably some processing has failed.
            List of params:
            <list type="bullet">
            <item><description>0th is a name of product for which event was reported;
            </description></item>
            <item><description>1st is an event type;
            </description></item>
            </list>
            </remarks>
        </member>
        <member name="T:iText.Commons.Utils.DateTimeUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.GetUtcMillisFromEpoch(System.Nullable{System.DateTime})">
            <summary>
            Gets the date time as UTC milliseconds from the epoch.
            </summary>
            <param name="dateTime">date to be converted to millis</param>
            <returns>the date as UTC milliseconds from the epoch</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.GetCalendar(System.DateTime)">
            <summary>
            Gets the calendar date and time of a day.
            </summary>
            <param name="dateTime">the date to be returned as calendar</param>
            <returns>the calendar date and time of a day</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.GetCurrentTime">
            <summary>
            Gets the current time in the default time zone with the default locale.
            </summary>
            <returns>the current time in the default time zone with the default locale</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.GetCurrentUtcTime">
            <summary>
            Gets the current time consistently.
            </summary>
            <returns>the time at which it was allocated, measured to the nearest millisecond</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.IsInPast(System.DateTime)">
            <summary>
            Defines if date is in past.
            </summary>
            <returns>true if given date is in past, false instead</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.GetRelativeTime(System.DateTime)">
            <summary>
            Gets the number of milliseconds since January 1, 1970, 00:00:00 GMT represented by specified date.
            </summary>
            <param name="date">the specified date to get time</param>
            <returns>the number of milliseconds since January 1, 1970, 00:00:00 GMT represented by the specified date</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.AddMillisToDate(System.DateTime,System.Int64)">
            <summary>
            Adds provided number of milliseconds to the DateTime.
            </summary>
            <param name="date">DateTime to increase</param>
            <param name="millis">number of milliseconds to add</param>
            <returns>updated DateTime</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.ParseWithDefaultPattern(System.String)">
            <summary>
            Parses passing date with default {@code yyyy-MM-dd} pattern.
            </summary>
            <param name="date">date is date to be parse</param>
            <returns>parse date</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.Parse(System.String,System.String)">
            <summary>
            Parses passing date with specified format.
            </summary>
            <param name="date">the date to be parsed</param>
            <param name="format">the format of parsing the date</param>
            <returns>parsed date</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.FormatWithDefaultPattern(System.DateTime)">
            <summary>
            Format passing date with default yyyy-MM-dd pattern.
            </summary>
            <param name="date">the date to be formatted</param>
            <returns>formatted date</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.Format(System.DateTime,System.String)">
            <summary>
            Format passing date with specified pattern.
            </summary>
            <param name="date">the date to be formatted</param>
            <param name="pattern">pattern for format</param>
            <returns>formatted date</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.GetCurrentTimeZoneOffset(System.DateTime)">
            <summary>
            Gets the offset of time zone from UTC at the specified date.
            </summary>
            <param name="date">the date represented in milliseconds since January 1, 1970 00:00:00 GMT</param>
            <returns>the offset of time zone from UTC at the specified date adjusted with the amount of daylight saving.</returns>
        </member>
        <member name="M:iText.Commons.Utils.DateTimeUtil.DateToString(System.DateTime)">
            <summary>
            Converts date to string of "yyyy.MM.dd HH:mm:ss z" format.
            </summary>
            <param name="date">date to convert.</param>
            <returns>string date value.</returns>
        </member>
        <member name="T:iText.Commons.Utils.DIContainer">
            <summary>A simple dependency injection container.</summary>
            <remarks>
            A simple dependency injection container.
            <para />
            The container is thread-safe.
            </remarks>
        </member>
        <member name="M:iText.Commons.Utils.DIContainer.#ctor">
            <summary>
            Creates a new instance of
            <see cref="T:iText.Commons.Utils.DIContainer"/>.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.DIContainer.RegisterDefault(System.Type,System.Func{System.Object})">
            <summary>Registers a default instance for a class.</summary>
            <param name="clazz">the class</param>
            <param name="supplier">supplier of the instance</param>
        </member>
        <member name="M:iText.Commons.Utils.DIContainer.Register(System.Type,System.Object)">
            <summary>Registers an instance for a class.</summary>
            <param name="clazz">the class</param>
            <param name="inst">the instance</param>
        </member>
        <member name="M:iText.Commons.Utils.DIContainer.GetInstance``1">
            <summary>Gets an instance of a class.</summary>
            <param name="clazz">the class</param>
            <typeparam name="T">the type of the class</typeparam>
            <returns>the instance</returns>
        </member>
        <member name="M:iText.Commons.Utils.DIContainer.IsRegistered(System.Type)">
            <summary>Checks if an instance is registered for a class.</summary>
            <remarks>
            Checks if an instance is registered for a class.
            If the class is registered but the value is null, it will still return
            <see langword="true"/>.
            </remarks>
            <param name="clazz">the class</param>
            <returns>
            
            <see langword="true"/>
            if an instance is registered,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="T:iText.Commons.Utils.EncodingUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.EncodingUtil.ConvertToBytes(System.Char[],System.String)">
            <summary>
            Converts to byte array an array of chars, taking the provided encoding into account.
            </summary>
            <param name="chars">an array of chars to be converted to bytes</param>
            <param name="encoding">the encoding to be taken into account while converting the provided array of chars</param>
            <returns>the resultant array of bytes</returns>
        </member>
        <member name="M:iText.Commons.Utils.EncodingUtil.ConvertToString(System.Byte[],System.String)">
            <summary>
            Converts to String an array of bytes, taking the provided encoding into account.
            </summary>
            <param name="chars">an array of bytes to be converted to String</param>
            <param name="encoding">the encoding to be taken into account while converting the provided bytes</param>
            <returns>the resultant string</returns>
        </member>
        <member name="T:iText.Commons.Utils.FileUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.GetFontsDir">
            <summary>
            Gets the default windows font directory.
            </summary>
            <returns>the default windows font directory</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.FileExists(System.String)">
            <summary>
            Checks whether there is a file at the provided path.
            </summary>
            <param name="path">the path to the file to be checked on existence</param>
            <returns><CODE>true</CODE> if such a file exists, otherwise <CODE>false</CODE></returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.IsFileNotEmpty(System.String)">
            <summary>
            Checks whether is provided file not empty.
            </summary>
            <param name="path">the path to the file to be checked on emptiness</param>
            <returns><CODE>true</CODE> if such file is not empty, <CODE>false</CODE> otherwise</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.DirectoryExists(System.String)">
            <summary>
            Checks whether there is a directory at the provided path.
            </summary>
            <param name="path">the path to the directory to be checked on existence</param>
            <returns>true if such a directory exists, otherwise false</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.ListFilesInDirectory(System.String,System.Boolean)">
            <summary>
            Lists all the files located at the provided directory.
            </summary>
            <param name="path">path to the directory</param>
            <param name="recursive">if <CODE>true</CODE>, files from all the subdirectories will be returned</param>
            <returns>all the files located at the provided directory</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.ListFilesInDirectoryByFilter(System.String,iText.Commons.Utils.FileUtil.IFileFilter)">
            <summary>
            Lists all the files located at the provided directory, which are accepted by the provided filter.
            </summary>
            <param name="path">path to the directory</param>
            <param name="filter">filter to accept files to be listed</param>
            <returns>all the files located at the provided directory, which are accepted by the provided filter</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.ListFilesInDirectoryByFilter(System.String,System.Boolean,iText.Commons.Utils.FileUtil.IFileFilter)">
            <summary>
            Lists all the files located at the provided directory, which are accepted by the provided filter.
            </summary>
            <param name="path">path to the directory</param>
            <param name="recursive">if <CODE>true</CODE>, files from all the subdirectories will be returned</param>
            <param name="filter">filter to accept files to be listed</param>
            <returns>all the files located at the provided directory, which are accepted by the provided filter</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.CreateTempFile(System.String)">
            <summary>
            Creates a temporary file at the provided path.
            </summary>
            <remarks>
            <para />
            Note, that this method creates temporary file with provided file's prefix and postfix using
            <see cref="!:File.createTempFile(string, string)"/>
            </remarks>
            <param name="path">path to the temporary file to be created. If it is a directory,
            then the temporary file will be created at this directory</param>
            <returns>the created temporary file</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.CreateDirectories(System.String)">
            <summary>
            Creates a directory at the provided path.
            </summary>
            <param name="outPath">path to the directory to be created</param>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.DeleteFile(System.IO.FileInfo)">
            <summary>
            Deletes a file and returns whether the operation succeeded.
            </summary>
            <remarks>
            Deletes a file and returns whether the operation succeeded.
            Note that only *files* are supported, not directories.
            </remarks>
            <param name="file">file to be deleted</param>
            <returns>true if file was deleted successfully, false otherwise</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.ParentDirectory(System.Uri)">
            <summary>
            Returns an URL of the parent directory for the resource.
            </summary>
            <param name="url">of resource</param>
            <returns>parent directory path| the same path if a catalog`s url is passed;</returns>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.CreateTempCopy(System.String,System.String,System.String)">
            <summary>Creates a temporary copy of a file.</summary>
            <remarks>
            <para />
            Note, that this method creates temporary file with provided file's prefix and postfix using
            <see cref="M:System.IO.File.Copy(System.String,System.String,System.Boolean)"/>
            </remarks>
            <param name="file">the path to the file to be copied</param>
            <param name="tempFilePrefix">the prefix of the copied file's name</param>
            <param name="tempFilePostfix">the postfix of the copied file's name</param>
            <returns>the path to the copied file</returns>
            <exception cref="T:System.IO.IOException">signals that an I/O exception has occurred.</exception>
        </member>
        <member name="M:iText.Commons.Utils.FileUtil.CreateTempDirectory(System.String)">
            <summary>Creates a temporary directory.</summary>
            <remarks>
            <para />
            Note, that this method creates temporary directory with provided directory prefix using
            <see cref="M:System.IO.Directory.CreateDirectory(System.String)"/>
            </remarks>
            <param name="tempFilePrefix">the prefix of the temporary directory's name</param>
            <returns>the path to the temporary directory</returns>
            <exception cref="T:System.IO.IOException">signals that an I/O exception has occurred.</exception>
        </member>
        <member name="T:iText.Commons.Utils.FilterReader">
            <summary>
            Abstract class for reading filtered character streams.
            The abstract class <code>FilterReader</code> itself
            provides default methods that pass all requests to
            the contained stream. Subclasses of <code>FilterReader</code>
            should override some of these methods and may also provide
            additional methods and fields.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.FilterReader.Read">
            <summary>
            Reads a single character.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.FilterReader.Read(System.Char[],System.Int32,System.Int32)">
            <summary>
            Reads characters into a portion of an array.
            </summary>
        </member>
        <member name="T:iText.Commons.Utils.FunctionalInterfaceAttribute">
            <summary>
            A mapping for java's FunctionalInterface attribute to allow clean code in java.
            </summary>
        </member>
        <member name="T:iText.Commons.Utils.IanaEncodings">
            Translates a IANA encoding name to a Java encoding.
        </member>
        <member name="F:iText.Commons.Utils.IanaEncodings.map">
            The object that maps IANA to Java encodings. 
        </member>
        <member name="T:iText.Commons.Utils.JavaCollectionsUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="T:iText.Commons.Utils.JavaUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="T:iText.Commons.Utils.JsonUtil">
            <summary>Utility class for JSON serialization and deserialization operations. Not for public use.</summary>
        </member>
        <member name="M:iText.Commons.Utils.JsonUtil.AreTwoJsonObjectEquals(System.String,System.String)">
            <summary>
            Compares two json strings without considering the order of the elements.
            </summary>
            <param name="expectedString">expected json string</param>
            <param name="toCompare">string for comparison</param>
            <returns>true if two json string are equals, false otherwise</returns>
        </member>
        <member name="M:iText.Commons.Utils.JsonUtil.SerializeToString(System.Object)">
            <summary>
            Serializes passed object to JSON string.
            </summary>
            <param name="value">the object which will be serialized</param>
            <returns>the JSON string representation of passed object or null if it is impossible to serialize to JSON</returns>
        </member>
        <member name="M:iText.Commons.Utils.JsonUtil.SerializeToMinimalString(System.Object)">
            <summary>
            Serializes passed object to minimal JSON string without spaces and line breaks.
            </summary>
            <param name="value">the object which will be serialized</param>
            <returns>the minimal JSON string representation of passed object or null if it is impossible to serialize to JSON</returns>
        </member>
        <member name="M:iText.Commons.Utils.JsonUtil.DeserializeFromStream``1(System.IO.Stream)">
            <summary>
            Deserializes passed JSON stream to object with passed type.
            </summary>
            <param name="content">the JSON stream which represent object</param>
            <param name="objectType">the type of object which will be deserialized</param>
            <typeparam name="T">the type of object which will be deserialized</typeparam>
            <returns>the deserialized object or null if operation of deserialization is impossible</returns>
        </member>
        <member name="M:iText.Commons.Utils.JsonUtil.DeserializeFromString``1(System.String)">
            <summary>
            Deserializes passed JSON string to object with passed type.
            </summary>
            <param name="content">the JSON string which represent object</param>
            <param name="objectType">the type of object which will be deserialized</param>
            <typeparam name="T">the type of object which will be deserialized</typeparam>
            <returns>the deserialized object or null if operation of deserialization is impossible</returns>
        </member>
        <member name="T:iText.Commons.Utils.MapUtil">
            <summary>Utility class for work with collections.</summary>
            <remarks>Utility class for work with collections. Not for public use.</remarks>
        </member>
        <member name="M:iText.Commons.Utils.MapUtil.Equals``2(System.Collections.Generic.IDictionary{``0,``1},System.Collections.Generic.IDictionary{``0,``1})">
            <summary>
            Checks if two
            <see cref="!:System.Collections.IDictionary&lt;K, V&gt;">maps</see>
            are equal: the are of the same types and has equal number of stored
            entries and both has the same set of keys ans each key is associated with an appropriate
            value.
            </summary>
            <param name="m1">is the first map</param>
            <param name="m2">is the second map</param>
            <typeparam name="K">is a type of keys</typeparam>
            <typeparam name="V">is a type of values</typeparam>
            <returns>
            
            <see langword="true"/>
            if maps are equal and
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Commons.Utils.MapUtil.Merge``2(System.Collections.Generic.IDictionary{``0,``1},System.Collections.Generic.IDictionary{``0,``1},System.Func{``1,``1,``1})">
            <summary>Merges data from source Map into destination Map using provided function if key exists in both Maps.
                </summary>
            <remarks>
            Merges data from source Map into destination Map using provided function if key exists in both Maps.
            If key doesn't exist in destination Map in will be putted directly.
            </remarks>
            <param name="destination">Map to which data will be merged.</param>
            <param name="source">Map from which data will be taken.</param>
            <param name="valuesMerger">function which will be used to merge Maps values.</param>
            <typeparam name="K">is a type of keys</typeparam>
            <typeparam name="V">is a type of values</typeparam>
        </member>
        <member name="M:iText.Commons.Utils.MapUtil.GetHashCode``2(System.Collections.Generic.IDictionary{``0,``1})">
            <summary>
            Calculates the hash code of the
            <see cref="!:System.Collections.IDictionary&lt;K, V&gt;">map</see>.
            </summary>
            <param name="m1">is the map</param>
            <typeparam name="K">is a type of keys</typeparam>
            <typeparam name="V">is a type of values</typeparam>
            <returns>
            the hash code of the
            <see cref="!:System.Collections.IDictionary&lt;K, V&gt;">map</see>.
            </returns>
        </member>
        <member name="M:iText.Commons.Utils.MapUtil.PutIfNotNull``2(System.Collections.Generic.IDictionary{``0,``1},``0,``1)">
            <summary>Puts value to map if the value is not null.</summary>
            <param name="map">the map in which value can be pushed</param>
            <param name="key">the key</param>
            <param name="value">the value</param>
            <typeparam name="K">is a type of key</typeparam>
            <typeparam name="V">is a type of value</typeparam>
        </member>
        <member name="T:iText.Commons.Utils.MessageFormatUtil">
            <summary>This file is a helper class for internal usage only.</summary>
            <remarks>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </remarks>
        </member>
        <member name="T:iText.Commons.Utils.Pair`2">
            <summary>Class which represents a pair of key and value.</summary>
            <typeparam name="K">key parameter type.</typeparam>
            <typeparam name="V">value parameter type.</typeparam>
        </member>
        <member name="M:iText.Commons.Utils.Pair`2.#ctor(`0,`1)">
            <summary>Creates key-value pair.</summary>
            <param name="key">key parameter</param>
            <param name="value">value parameter</param>
        </member>
        <member name="M:iText.Commons.Utils.Pair`2.GetKey">
            <summary>Gets key parameter.</summary>
            <returns>key parameter.</returns>
        </member>
        <member name="M:iText.Commons.Utils.Pair`2.GetValue">
            <summary>Gets value parameter.</summary>
            <returns>value parameter.</returns>
        </member>
        <member name="T:iText.Commons.Utils.PlaceHolderTextUtil">
            <summary>This class is used to generate placeholder text for the examples and tests.</summary>
            <remarks>
            This class is used to generate placeholder text for the examples and tests.
            It is not used anywhere in the actual code.
            This provides a better way than using Lorem Ipsum text as it is more readable.
            </remarks>
        </member>
        <member name="M:iText.Commons.Utils.PlaceHolderTextUtil.GetPlaceHolderText(iText.Commons.Utils.PlaceHolderTextUtil.PlaceHolderTextBy,System.Int32)">
            <summary>Gets the placeholder text.</summary>
            <param name="by">if you want to get the placeholder text by words or by characters</param>
            <param name="amount">the number of words or characters</param>
            <returns>the placeholder text</returns>
        </member>
        <member name="T:iText.Commons.Utils.PlaceHolderTextUtil.PlaceHolderTextBy">
            <summary>The enum Place holder text by.</summary>
            <remarks>
            The enum Place holder text by.
            This enum is used to get the placeholder text by words or by characters
            </remarks>
        </member>
        <member name="T:iText.Commons.Utils.ProcessInfo">
            <summary>Class contains a process information, such as process exit code and process output.</summary>
        </member>
        <member name="M:iText.Commons.Utils.ProcessInfo.#ctor(System.Int32,System.String,System.String)">
            <summary>
            Create a new instance, containing a process information,
            such as process exit code, process standard and error outputs.
            </summary>
            <param name="exitCode">exit code of the process.</param>
            <param name="processStdOutput">the standard output of the process.</param>
            <param name="processErrOutput">the error output of the process.</param>
        </member>
        <member name="M:iText.Commons.Utils.ProcessInfo.GetExitCode">
            <summary>Getter for a process exit code.</summary>
            <returns>Returns a process exit code.</returns>
        </member>
        <member name="M:iText.Commons.Utils.ProcessInfo.GetProcessStdOutput">
            <summary>Getter for a standard process output.</summary>
            <returns>Returns a process standard output string.</returns>
        </member>
        <member name="M:iText.Commons.Utils.ProcessInfo.GetProcessErrOutput">
            <summary>Getter for an error process output.</summary>
            <returns>Returns a process error output string.</returns>
        </member>
        <member name="T:iText.Commons.Utils.Properties">
            <summary>
            Summary description for Properties.
            </summary>
        </member>
        <member name="T:iText.Commons.Utils.PushbackReader">
            <summary>
            A character-stream reader that allows characters to be pushed back into the
            stream.
            
            <AUTHOR> Reinhold
            @since       JDK1.1
            </summary>
        </member>
        <member name="F:iText.Commons.Utils.PushbackReader._buf">
            <summary>
            Pushback buffer </summary>
        </member>
        <member name="F:iText.Commons.Utils.PushbackReader._pos">
            <summary>
            Current position in buffer </summary>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.#ctor(System.IO.TextReader,System.Int32)">
            <summary>
            Creates a new pushback reader with a pushback buffer of the given size.
            </summary>
            <param name="in">   The reader from which characters will be read </param>
            <param name="size"> The size of the pushback buffer </param>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.#ctor(System.IO.TextReader)">
            <summary>
            Creates a new pushback reader with a one-character pushback buffer.
            </summary>
            <param name="in">  The reader from which characters will be read </param>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.EnsureOpen">
            <summary>
            Checks to make sure that the stream has not been closed. </summary>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.Read">
            <summary>
            Reads a single character.
            </summary>
            <returns>     The character read, or -1 if the end of the stream has been
                        reached
            </returns>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.Read(System.Char[],System.Int32,System.Int32)">
            <summary>
            Reads characters into a portion of an array.
            </summary>
            <param name="cbuf">  Destination buffer </param>
            <param name="off">   Offset at which to start writing characters </param>
            <param name="len">   Maximum number of characters to read
            </param>
            <returns>     The number of characters read, or -1 if the end of the
                        stream has been reached
            </returns>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.Unread(System.Int32)">
            <summary>
            Pushes back a single character by copying it to the front of the
            pushback buffer. After this method returns, the next character to be read
            will have the value <code>(char)c</code>.
            </summary>
            <param name="c">  The int value representing a character to be pushed back
            </param>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.Unread(System.Char[],System.Int32,System.Int32)">
            <summary>
            Pushes back a portion of an array of characters by copying it to the
            front of the pushback buffer.  After this method returns, the next
            character to be read will have the value <code>cbuf[off]</code>, the
            character after that will have the value <code>cbuf[off+1]</code>, and
            so forth.
            </summary>
            <param name="cbuf">  Character array </param>
            <param name="off">   Offset of first character to push back </param>
            <param name="len">   Number of characters to push back
            </param>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.Unread(System.Char[])">
            <summary>
            Pushes back an array of characters by copying it to the front of the
            pushback buffer.  After this method returns, the next character to be
            read will have the value <code>cbuf[0]</code>, the character after that
            will have the value <code>cbuf[1]</code>, and so forth.
            </summary>
            <param name="cbuf">  Character array to push back
            </param>
        </member>
        <member name="M:iText.Commons.Utils.PushbackReader.Dispose(System.Boolean)">
            <summary>
            Closes the stream and releases any system resources associated with
            it. Once the stream has been closed, further read(),
            unread(), ready(), or skip() invocations will throw an IOException.
            Closing a previously closed stream has no effect.
            </summary>
        </member>
        <member name="T:iText.Commons.Utils.SortUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.SortUtil.MergeSort``1(``0[],System.Int32,System.Int32,System.Collections.Generic.IComparer{``0})">
            <summary>
            
            </summary>
            <param name="from">the index of the first element, inclusive, to be sorted</param>
            <param name="to">the index of the last element, exclusive, to be sorted</param>
        </member>
        <member name="T:iText.Commons.Utils.StringUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="T:iText.Commons.Utils.SystemUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.SystemUtil.GetRelativeTimeMillis">
            <summary>
            Should be used in relative constructs (for example to check how many milliseconds have passed).
            Shouldn't be used in the DateTime creation since the nanoseconds are expected there.
            </summary>
            <returns>relative time in milliseconds</returns>
        </member>
        <member name="M:iText.Commons.Utils.SystemUtil.GetEnvironmentVariable(System.String)">
            <summary>
            Gets environment variable with given name.
            </summary>
            <param name="name">the name of environment variable.</param>
            <returns>variable value or null if there is no such.</returns>
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.COMMON">
            Unicode script "Common".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.LATIN">
            Unicode script "Latin".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.GREEK">
            Unicode script "Greek".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.CYRILLIC">
            Unicode script "Cyrillic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.ARMENIAN">
            Unicode script "Armenian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.HEBREW">
            Unicode script "Hebrew".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.ARABIC">
            Unicode script "Arabic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SYRIAC">
            Unicode script "Syriac".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.THAANA">
            Unicode script "Thaana".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.DEVANAGARI">
            Unicode script "Devanagari".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BENGALI">
            Unicode script "Bengali".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.GURMUKHI">
            Unicode script "Gurmukhi".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.GUJARATI">
            Unicode script "Gujarati".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.ORIYA">
            Unicode script "Oriya".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TAMIL">
            Unicode script "Tamil".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TELUGU">
            Unicode script "Telugu".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.KANNADA">
            Unicode script "Kannada".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.MALAYALAM">
            Unicode script "Malayalam".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SINHALA">
            Unicode script "Sinhala".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.THAI">
            Unicode script "Thai".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.LAO">
            Unicode script "Lao".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TIBETAN">
            Unicode script "Tibetan".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.MYANMAR">
            Unicode script "Myanmar".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.GEORGIAN">
            Unicode script "Georgian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.HANGUL">
            Unicode script "Hangul".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.ETHIOPIC">
            Unicode script "Ethiopic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.CHEROKEE">
            Unicode script "Cherokee".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.CANADIAN_ABORIGINAL">
            Unicode script "Canadian_Aboriginal".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.OGHAM">
            Unicode script "Ogham".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.RUNIC">
            Unicode script "Runic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.KHMER">
            Unicode script "Khmer".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.MONGOLIAN">
            Unicode script "Mongolian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.HIRAGANA">
            Unicode script "Hiragana".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.KATAKANA">
            Unicode script "Katakana".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BOPOMOFO">
            Unicode script "Bopomofo".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.HAN">
            Unicode script "Han".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.YI">
            Unicode script "Yi".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.OLD_ITALIC">
            Unicode script "Old_Italic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.GOTHIC">
            Unicode script "Gothic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.DESERET">
            Unicode script "Deseret".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.INHERITED">
            Unicode script "Inherited".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TAGALOG">
            Unicode script "Tagalog".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.HANUNOO">
            Unicode script "Hanunoo".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BUHID">
            Unicode script "Buhid".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TAGBANWA">
            Unicode script "Tagbanwa".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.LIMBU">
            Unicode script "Limbu".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TAI_LE">
            Unicode script "Tai_Le".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.LINEAR_B">
            Unicode script "Linear_B".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.UGARITIC">
            Unicode script "Ugaritic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SHAVIAN">
            Unicode script "Shavian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.OSMANYA">
            Unicode script "Osmanya".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.CYPRIOT">
            Unicode script "Cypriot".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BRAILLE">
            Unicode script "Braille".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BUGINESE">
            Unicode script "Buginese".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.COPTIC">
            Unicode script "Coptic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.NEW_TAI_LUE">
            Unicode script "New_Tai_Lue".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.GLAGOLITIC">
            Unicode script "Glagolitic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TIFINAGH">
            Unicode script "Tifinagh".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SYLOTI_NAGRI">
            Unicode script "Syloti_Nagri".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.OLD_PERSIAN">
            Unicode script "Old_Persian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.KHAROSHTHI">
            Unicode script "Kharoshthi".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BALINESE">
            Unicode script "Balinese".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.CUNEIFORM">
            Unicode script "Cuneiform".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.PHOENICIAN">
            Unicode script "Phoenician".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.PHAGS_PA">
            Unicode script "Phags_Pa".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.NKO">
            Unicode script "Nko".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SUNDANESE">
            Unicode script "Sundanese".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BATAK">
            Unicode script "Batak".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.LEPCHA">
            Unicode script "Lepcha".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.OL_CHIKI">
            Unicode script "Ol_Chiki".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.VAI">
            Unicode script "Vai".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SAURASHTRA">
            Unicode script "Saurashtra".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.KAYAH_LI">
            Unicode script "Kayah_Li".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.REJANG">
            Unicode script "Rejang".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.LYCIAN">
            Unicode script "Lycian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.CARIAN">
            Unicode script "Carian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.LYDIAN">
            Unicode script "Lydian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.CHAM">
            Unicode script "Cham".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TAI_THAM">
            Unicode script "Tai_Tham".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TAI_VIET">
            Unicode script "Tai_Viet".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.AVESTAN">
            Unicode script "Avestan".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.EGYPTIAN_HIEROGLYPHS">
            Unicode script "Egyptian_Hieroglyphs".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SAMARITAN">
            Unicode script "Samaritan".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.MANDAIC">
            Unicode script "Mandaic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.LISU">
            Unicode script "Lisu".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BAMUM">
            Unicode script "Bamum".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.JAVANESE">
            Unicode script "Javanese".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.MEETEI_MAYEK">
            Unicode script "Meetei_Mayek".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.IMPERIAL_ARAMAIC">
            Unicode script "Imperial_Aramaic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.OLD_SOUTH_ARABIAN">
            Unicode script "Old_South_Arabian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.INSCRIPTIONAL_PARTHIAN">
            Unicode script "Inscriptional_Parthian".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.INSCRIPTIONAL_PAHLAVI">
            Unicode script "Inscriptional_Pahlavi".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.OLD_TURKIC">
            Unicode script "Old_Turkic".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.BRAHMI">
            Unicode script "Brahmi".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.KAITHI">
            Unicode script "Kaithi".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.MEROITIC_HIEROGLYPHS">
            Unicode script "Meroitic Hieroglyphs".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.MEROITIC_CURSIVE">
            Unicode script "Meroitic Cursive".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SORA_SOMPENG">
            Unicode script "Sora Sompeng".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.CHAKMA">
            Unicode script "Chakma".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.SHARADA">
            Unicode script "Sharada".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.TAKRI">
            Unicode script "Takri".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.MIAO">
            Unicode script "Miao".
        </member>
        <member name="F:iText.Commons.Utils.UnicodeScript.UNKNOWN">
            Unicode script "Unknown".
        </member>
        <member name="T:iText.Commons.Utils.UnicodeScriptUtil">
            <summary>
            This file is a helper class for internal usage only.
            Be aware that its API and functionality may be changed in future.
            </summary>
        </member>
        <member name="M:iText.Commons.Utils.UnicodeScriptUtil.Of(System.Int32)">
                    * Returns the enum constant representing the Unicode script of which
                    * the given character (Unicode code point) is assigned to.
                     *
                    * @param   codePoint the character (Unicode code point) in question.
                    * @return  The {@code UnicodeScript} constant representing the
                    *          Unicode script of which this character is assigned to.
                    *
                    * @exception IllegalArgumentException if the specified
                    * {@code codePoint} is an invalid Unicode code point.
                    * @see Character#isValidCodePoint(int)
                    *
        </member>
        <member name="T:iText.Commons.Utils.ZipFileReader">
            <summary>Allows reading entries from a zip file.</summary>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileReader.#ctor(System.String)">
            <summary>
            Creates an instance for zip file reading.
            </summary>
            <param name="archivePath">the path to the zip file to read</param>
            <exception cref="T:System.IO.IOException">if some I/O exception occurs</exception>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileReader.GetFileNames">
            <summary>
            Get all file entries paths inside the reading zip file.
            </summary>
            <returns>the {@link Set} of all file entries paths</returns>
            <exception cref="T:System.IO.IOException">if some I/O exception occurs</exception>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileReader.ReadFromZip(System.String)">
            <summary>
            Read single file from zip.
            </summary>
            <param name="fileName">the file path inside zip to read</param>
            <returns>the {@link InputStream} represents read file content</returns>
            <exception cref="T:System.IO.IOException">if some I/O exception occurs</exception>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileReader.SetThresholdSize(System.Int32)">
            <summary>
            Sets the maximum total uncompressed data size to prevent a Zip Bomb Attack.
            Default value is 1 GB (1000000000).
            </summary>
            <param name="thresholdSize"> the threshold for maximum total size of the uncompressed data</param>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileReader.SetThresholdEntries(System.Int32)">
            <summary>
            Sets the maximum number of file entries in the archive to prevent a Zip Bomb Attack. Default value is 10000.
            </summary>
            <param name="thresholdEntries"> maximum number of file entries in the archive</param>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileReader.SetThresholdRatio(System.Double)">
            <summary>
            Sets the maximum ratio between compressed and uncompressed data to prevent a Zip Bomb Attack. In general
            the data compression ratio for most of the legit archives is 1 to 3. Default value is 10.
            </summary>
            <param name="thresholdRatio"> maximum ratio between compressed and uncompressed data</param>
        </member>
        <member name="T:iText.Commons.Utils.ZipFileWriter">
            <summary>Allows writing entries into a zip file.</summary>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileWriter.#ctor(System.String)">
            <summary>
            Creates an instance for zip file writing.
            </summary>
            <param name="archivePath">the path to the zip file to write</param>
            <exception cref="T:System.IO.IOException">if some I/O exception occurs</exception>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileWriter.AddEntry(System.String,System.IO.FileInfo)">
            <summary>
            Add file from disk into zip archive.
            </summary>
            <param name="fileName">the target name of the file inside zip after writing</param>
            <param name="file">the path to the file on disk to archive</param>
            <exception cref="T:System.IO.IOException">if some I/O exception occurs</exception>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileWriter.AddEntry(System.String,System.IO.Stream)">
            <summary>
            Add file into zip archive with data from stream.
            </summary>
            <param name="fileName">the target name of the file inside zip after writing</param>
            <param name="inputStream">the input stream to archive</param>
            <exception cref="T:System.IO.IOException">if some I/O exception occurs</exception>
        </member>
        <member name="M:iText.Commons.Utils.ZipFileWriter.AddJsonEntry(System.String,System.Object)">
            <summary>
            Add file into zip archive with object serialized as JSON.
            </summary>
            <param name="fileName">the target name of the file inside zip after writing</param>
            <param name="objectToAdd">the object to serialize as JSON</param>
            <exception cref="T:System.IO.IOException">if some I/O exception occurs</exception>
        </member>
    </members>
</doc>
